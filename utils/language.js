import { getIpInfo } from "../redux/action/common";
import queryString from "query-string";

export const setLanguage = async (router, cookies, setLang) => {
  const searchParams = new URLSearchParams(window.location.search);
  let language = searchParams.get("lang");

  if (language) {
    let s = window.location.search;
    const parsed = queryString.parse(s);
    delete parsed.lang;
    localStorage.setItem("language", language);
    cookies.set("language", language, {
      path: "/",
      maxAge: 1000000000000,
    });
    let url = window.location.origin + window.location.pathname;
    if (queryString.stringify(parsed)) {
      url = url + "?" + queryString.stringify(parsed);
    }
    window.location.href = url;
  } else {
    if (!localStorage.getItem("language")) {
      getLangInfo(router, cookies, setLang);
    }
  }
};

const getLangInfo = async (router, cookies, setLang) => {
  //setIsFetching(true)
  let lang = "de";
  try {
    //let response = await getIpInfo("************"); //Czech ip for testing
    let response = await getIpInfo();
    false;
    if (response?.data?.country?.toLowerCase() == "de") {
      lang = "de";
    } else if (response?.data?.country?.toLowerCase() == "at") {
      lang = "de";
    } else if (response?.data?.country?.toLowerCase() == "ch") {
      lang = "de";
    } else if (response?.data?.country?.toLowerCase() == "cz") {
      lang = "cz";
    // } else if (response?.data?.country?.toLowerCase() == "fr") {
    //   lang = "fr";
    } else {
      lang = "de";
    }
    localStorage.setItem("language", lang);
    cookies.set("language", lang, {
      path: "/",
      maxAge: 1000000000000,
    });
    router.reload();
  } catch (err) {
    // localStorage.setItem("language", "en");
    // cookies.set("language", "en", {
    //   path: "/",
    //   maxAge: 1000000000000,
    // });

    router.reload();
    false;
  }
};
