const eye = document.querySelectorAll(".eye-icon");
if (eye.length > 0) {
  eye.forEach((eye) => {
    eye.addEventListener("click", myFunction);
    function myFunction() {
      this.classList.toggle("open");
    }
  });
}

// login tabs start

// Define variables
var tabLabels = document.querySelectorAll("#tabs li .tab-link");
var tabPanes = document.getElementsByClassName("tab-contents");
function activateTab(e) {
  e.preventDefault();

  // Deactivate all tabs
  tabLabels.forEach(function (label) {
    label.classList.remove("active");
  });
  [].forEach.call(tabPanes, function (pane) {
    pane.classList.remove("active");
  });

  // Activate current tab
  e.target.classList.add("active");
  var clickedTab = e.target.getAttribute("href");
  document.querySelector(clickedTab).classList.add("active");
}

// Apply event listeners
tabLabels.forEach(function (label) {
  label.addEventListener("click", activateTab);
});

// login tabs ends

var header = `<div class="logo-wrpr">
<img src="images/logo.png" alt="KUBY" class="img-fluid">
</div>
<div class="d-flex align-center header-btn-wrpr">
<select class="language-select">
<option value="en">En</option>
<option value="en">en</option>
<option value="en">en</option>
<option value="en">en</option>
</select>
<a href="#" class="sm btn-accent">Book a meeting</a>
<!--        before login buttons start         -->
<div class="d-flex align-center header-btn-inner">
    <a href="login.html" class="sm btn"><span class="icon login-icon"></span>Login</a>
    <a href="login.html" class="sm btn"><span class="icon register-icon"></span>New Registration</a>
</div>
 <!--        before login buttons end         -->
  <!--        after login buttons start         -->
<ul class="d-flex align-center header-btn-inner header-btn-list">
    <li class="header-btn-item">
        <a href="#" class="header-btn"><span class="icon noti-icon"></span></a>
    </li>
    <li class="header-btn-item">
        <a href="#" class="header-btn"><span class="icon wishlist-icon"></span></a>
    </li>
    <li class="header-btn-item">
        <a href="#" class="header-btn"><span class="icon cart-icon"></span></a>
    </li>
    <li class="header-btn-item">
        <a href="#" class="header-btn profile-btn">
            <img src="images/user-img.jpg" alt="" class="cover-img profile-img"/>
            <p class="fw500 text-dark-gray user-name">Welcome Frank</p>
        </a>
        <div class="prof-dropdown-menu">
            <div class="prof-menu-wrpr welcome-block">
                <p class="text-grey-7 fw500 dropdown-lbl">Welcome Frank</p>
                <a href="profile.html" class="dropdown-link"><span class="icon profile-icon"></span>Profile details</a>
            </div>
            <div class="prof-menu-wrpr history-block">
                <p class="text-grey-7 fw500 dropdown-lbl">History</p>
                <a href="#" class="dropdown-link"><span class="icon meeting-icon"></span>Meeting history</a>
                <a href="#" class="dropdown-link"><span class="icon soulwriting-icon"></span>Soulwriting history</a>
                <a href="#" class="dropdown-link"><span class="icon order-icon"></span>Order history</a>
                <a href="#" class="dropdown-link"><span class="icon payment-icon"></span>Payments</a>
            </div>
            <div class="prof-menu-wrpr logout-block">
                <a href="#" class="dropdown-link"><span class="icon logout-icon"></span>Logout</a>
            </div>
        </div>
    </li>
</ul>
 <!--        after login buttons end         -->
</div>`;

var leftSidebar = `<ul class="d-flex fd-col nav-list">
<li class="nav-item">
    <a href="#" class="text-grey-5 d-flex align-center nav-link active"><span class="nav-icon reception"></span>Reception</a>
</li>
<li class="nav-item">
    <a href="#" class="text-grey-5 d-flex align-center nav-link"><span class="nav-icon practice"></span>Self practice</a>
</li>
<li class="nav-item">
    <a href="companion.html" class="text-grey-5 d-flex align-center nav-link"><span class="nav-icon companions"></span>KUBYcompanions</a>
</li>
<li class="nav-item">
    <a href="#" class="text-grey-5 d-flex align-center nav-link"><span class="nav-icon soul"></span>Soulwriting</a>
</li>
<li class="nav-item">
    <a href="shop.html" class="text-grey-5 d-flex align-center nav-link"><span class="nav-icon shop"></span>KUBYshop</a>
</li>
</ul>`;

const headerwrpr = document.querySelectorAll(".header");
if (headerwrpr.length) {
  headerwrpr.forEach((headerwrpr) => {
    headerwrpr.innerHTML = header;
  });
}

const sidebar = document.querySelectorAll(".left-sidebar");
if (sidebar.length) {
  sidebar.forEach((sidebar) => {
    sidebar.innerHTML = leftSidebar;
  });
}

const profilebtn = document.querySelectorAll(".profile-btn");
const profiledrop = document.querySelector(".prof-dropdown-menu");
if (profilebtn.length > 0) {
  profilebtn.forEach((profilebtn) => {
    profilebtn.addEventListener("click", myFunction);
    function myFunction() {
      profiledrop.classList.toggle("active");
    }
  });
}

const schedule = document.querySelectorAll("#schedule_btn");
const profile = document.getElementById("profileModal");

if (schedule.length > 0) {
  schedule.forEach((schedule) => {
    schedule.addEventListener("click", myFunction);
    function myFunction() {
      profile.classList.add("show");
    }
  });
}

var overlaydiv = document.getElementById("modalOverlay");

overlaydiv.addEventListener("click", overlay);
function overlay(e) {
  e.target.parentElement.classList.remove("show");
}
