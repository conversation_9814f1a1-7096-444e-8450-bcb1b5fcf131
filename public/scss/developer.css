.camcel-meeting-modal .camcel-modal-content {
  max-height: 90vh;
  max-width: 400px;
}

.cancel_meeting_btn {
  justify-content: center;
  /* margin-top: 45px; */
  gap: 20px;
}

.label_cancel {
  margin-left: 40px;
}

.accordion-items.active .accordion-content {
  display: block;
}

.accordion-items.active .arrow-icon {
  transform: rotate(180deg);
  transition: rotate 0.5s;
}

.sub-lessson {
  cursor: pointer;
}

.reception-card-block .img-name-card {
  cursor: pointer;
}

.loader-wrpr {
  min-width: 100%;
}

.loaderView_sublesson {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  min-width: 22px;
  height: 22px;
  margin-left: 4px;
}

.sekeleton-video-wrpr {
  width: 100%;
  height: 500px;
}

div.loader-svg svg {
  margin: 0 auto;
  display: block;
}

.rating_modal-content {
  width: 520px;
}

.rating_modal .modal-header {
  padding: 0px;
}

.rating_modal .modal-body {
  padding: 36px;
}

.rating_modal .modal-body .f-label {
  font-weight: 500;
  font-size: 24px;
  line-height: 1.16;
  letter-spacing: -0.5px;
  color: #404040;
}

.rating_modal .rate-comment-sec {
  border: none;
  margin-top: 0px;
}

.form-rating-btns-wrpr {
  margin-top: 110px;
}

.rating_modal-body {
  padding: 8px 49px;
}

.rating_form {
  padding-top: 80px;
}

.rating-label {
  display: inline-block;
  margin-bottom: 25px;
}

.add-comment-wrpr .form-in {
  flex: 1;
}

.star_error {
  display: flex;
  align-items: center;
  margin-top: 7px;
  color: #d8090d;
}

.edit-comment-wrpr {
  cursor: pointer;
}

.btn-margin {
  margin-top: 20px;
}

.product-card {
  cursor: pointer;
}

/* .product-details-wrpr {
  flex: 0 0 calc(100% - 210px);
} */
.cart_wrpr {
  flex: 1;
  padding-right: 26px;
  padding-bottom: 30px;
  overflow: auto;
}

.cart_wrpr>table {
  width: 100%;
  margin-top: 20px;
}

thead th {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #616161;
  text-align: left;
  min-width: 140px;
  padding: 14px 12px;
}

thead th.actions {
  min-width: 60px;
}

tbody {
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
}

tr>td {
  font-weight: 400;
  font-size: 14px;
  padding: 12px;
  line-height: 16px;
  color: black;
  border-top: 1px solid #e0e8f2;
  vertical-align: middle;
}

tr>td .prod-img {
  width: 60px;
  min-width: 60px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
}

.prod-details-wrpr .audio-opt-wrpr .form-group {
  position: relative;
}

/* .prod-details-wrpr .audio-opt-wrpr .form-group .audio-input{
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
} */

.prod-details-wrpr .audio-opt-wrpr .form-group.disable .language-label {
  border: 1px dashed #969696;
  opacity: 0.3;
}

.prod-details-wrpr .audio-opt-wrpr .form-group .language-label {
  background: #f0f0f0;
  border-radius: 10px;
  padding: 5px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "Inter";
  font-weight: 600;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: -0.12px;
  color: #616161;
  cursor: pointer;
}

.prod-details-wrpr .audio-opt-wrpr .form-group .audio-input:checked+.language-label {
  background: #499557;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.cart_list {
  margin-left: 20px;
}

.cart_total__amount_wrpr {
  flex: 0 0 430px;
  padding-left: 26px;
  border-left: 1px solid #f0f0f0;
}

.coupon-from {
  gap: 12px;
  margin-top: 20px;
}

.coupon-from .btn {
  white-space: pre;
  padding: 12px 21px;
  font-size: 14px;
}

.coupon-from .form-control {
  max-width: 63%;
  padding: 10px;
}

.cart_total_wrpr {
  margin-top: 50px;
}

.cart_total_wrpr .h5 {
  margin-bottom: 24px;
}

.price-table>div {
  margin-bottom: 10px;
}

.cart-wrap span {
  border-radius: 99px;
  font: bold 11px/16px Arial;
  line-height: 18px;
  width: 18px;
  padding: 0 1px;
  box-shadow: 0 5px 12px rgb(0 0 0 / 20%);
}

.cart-wrap span {
  visibility: visible;
}

.cart-wrap {
  margin-right: 0;
  top: -5px;
  right: -9px;
  color: linear-gradient(90deg, #499557, #499557);
  font-size: 11px;
}

.cart-icon-wrap .icon-salient-cart {
  font-size: 22px;
}

.cart-icon-wrap {
  width: auto;
  height: 20px;
}

.cart-empty {
  font-family: Roboto;
  text-transform: none;
  letter-spacing: 0px;
  font-size: 42px;
  line-height: 58px;
  font-weight: 700;
  padding-bottom: 25px;
  padding-top: 10%;
  text-align: center;
}

.vertical-center {
  margin: 0;
  position: absolute;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.container {
  /* height: 100px; */
  position: relative;
}

.cart_empty {
  margin-left: 484px;
  margin-top: 20px;
}

.cart_cross_icon {
  cursor: pointer;
}

.cart-count {
  position: absolute;
  top: 5px;
  right: 7px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15px;
  height: 15px;
  background-color: #499557;
  color: #ffffff;
  font-size: 10px;
  border-radius: 10px;
  font-weight: 700;
}

div.cart_loader-svg svg {
  margin: 0 auto;
  display: block;
  margin-top: 200px;
  width: 138px;
}

.css-df17o1 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  /* font-size: 1.2em; */
  color: #499557 !important;
  background: rgba(255, 255, 255, 0.5) !important;
  /* background: rgba(0,0,0,0.7); */
  z-index: 800;
  -webkit-transition: opacity 500ms ease-in;
  transition: opacity 500ms ease-in;
  opacity: 1;
}

.MyLoader_wrapper {
  background: rgba(255, 255, 255, 0.5) !important;
}

#ds24cart_added_container {
  display: none !important;
}

.thanku-inner .h3 {
  margin-bottom: 16px;
}

.thanku-inner .p {
  margin-bottom: 40px;
}

.wishlist-img {
  position: relative;
  width: 81.18px;
  height: 81.18px;
  margin: auto;

  background: #ffffff;
}

.empty-screen {
  margin-top: 100px;
}

.cart_icon-img {
  position: relative;
  width: 40px;
  height: 27.5px;
  margin: auto;
  margin-bottom: 28px;
  border: 1.75px solid #094e26;
}

.prod-details-wrpr .cart_wrpr_outer {
  height: calc(100% - 75px);
}

.prod-details-wrpr .cart_wrpr_outer .cart_wrpr {
  height: 100%;
  overflow: auto;
}

.total_order {
  /* font-family: General sans-serif; */
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.5 px;
}

.order_header_bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.react-datetime-picker__wrapper {
  border: none !important;
}

.css-1s2u09g-control,
.react-datetime-picker,
.css-1pahdxg-control {
  padding: 10px 16px !important;
}

.table_form_in {
  position: relative;
  /* margin-right: 35px;
  margin-top: 20px; */
}

.reception-seminar .img-name-card {
  /* max-width: 180px; */
  min-width: 150px;
  width: 100%;
}

.reception-kubystudy .img-name-card {
  max-width: 50%;
}

.order-history {
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  margin-top: 40px;
}

.order-history tbody {
  box-shadow: none;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
}

.pagination .active a {
  background: #eef7f1;
  border: 0.2px solid #499557;
}

.pagination a {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #ffffff;
  color: #7e7e7e;
  border: 0.2px solid #ffffff;
  box-shadow: 0px 1px 12px rgba(2, 17, 8, 0.06);
}

.paginatate {
  padding: 30px 70px 30px 0;
}

.order_wrpr {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order_wrpr .prod-img {
  width: 60px;
  height: 80px;
  left: 0px;
  top: 0px;
  border-radius: 6px;
}

.order_header {
  font-family: "General Sans";
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #262626;
}

.order-details .order_wrpr {
  width: 100%;
  align-items: flex-start;
  cursor: pointer;
}

.order-details .product_desc {
  display: flex;
  gap: 16px;
}

.order-details .product_desc .category_wrp .category-image {
  max-width: 30px;
  margin-right: 8px;
}

.order-details .product_desc .category_wrp {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.product_domp p {
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #262626;
  display: block;
  margin-bottom: 11px;
}

.order_total {
  margin-top: 50px;
  margin-left: 27px;
  margin-right: 24px;
}

.text-gray-10 {
  color: #07080a;
  font-family: "General Sans";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
}

.notification-modal .order_content {
  border-radius: unset;
}

.noti-list .order-item {
  padding: 14px 40px;
  border-bottom: none;
}

.order-list {
  border-bottom: 1px solid #f0f0f0;
  padding-top: 24px;
}

.order_info {
  margin-top: 13px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
}

.invoice_wrp {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.invoice_wrp .invoice_btn {
  width: 46px;
  height: 20px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #499557;
}

.similar-prod-row {
  cursor: pointer;
}

.ck.ck-editor__top {
  display: none;
}

.character_item {
  cursor: pointer;
}

.choose_character .modal-header {
  /* border: none; */
}

.highlighter {
  background: #fbf2d2;
}

.bridge_chars {
  margin-top: 10px;
  background: #fbf2d2;
  border: 1px solid #fef6dc;
  border-radius: 8px;
  padding: 8px 16px;
  margin-bottom: 12px;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.43;
  color: #616161;
}

.collpase-btn {
  background: #eef7f1;
  border-radius: 12px;
  width: 100%;
  padding: 26px;
  border: none;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  color: #094e26;
}

.textAreaClass {
  height: 100px;
  resize: none;
}

.camcel-modal-content .modal-header {
  padding: 20px 40px;
}

.form-btns-wrpr {
  padding-top: 22px;
  gap: 10px;
  justify-content: center;
  align-items: center;
}

.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 0px !important;
}

.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 0px !important;
}

.bridgeHighlight {
  background-color: #fbf2d2;
}

.soul-table .soul-tcol:nth-child(3),
.soul-table .soul-tcol:nth-child(4),
.soul-table .soul-tcol:nth-child(5),
.soul-table .soul-tcol:nth-child(1) {
  min-width: 120px;
  /* width: 80px; */
  width: 120px;
}

.ck.ck-widget__selection-handler {
  display: none;
}

.save_pdf {
  height: 0px;
  bottom: 0px;
  position: fixed;
  z-index: -1;
}

.pdf_font p,
.pdf_font h6 {
  font-size: 24px;
}

.script-view-wrpr {
  padding: 20px 32px;
}

.script-view-wrpr .view-table {
  width: 100%;
}

.script-view-wrpr .view-table .line_td {
  width: 20px !important;
}

.script-view-wrpr .view-table .character_td {
  width: 50px !important;
}

.script-view-wrpr h5.step-title {
  margin-bottom: 12px !important;
}

.version_cls {
  margin-left: 6px;
}

.h-100 {
  height: 100%;
}

.over-auto {
  overflow: auto;
}

.feedback {
  background-color: #f8f8f8;
  height: calc(100vh - 83px);
}

.ConversationFeedback {
  height: 100%;
  padding-top: 32px;
  padding-bottom: 32px;
  overflow: auto;
}

.feedback .thanku-inner .img-card-title {
  margin-top: 28px;
  margin-bottom: 40px !important;
  color: #404040;
}

.feedback .us-name {
  font-weight: 500;
  font-size: 20px;
  line-height: 1;
  margin-bottom: 16px;
  color: #07080a;
}

.feedback .feedback-content {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  color: #616161;
  max-width: 710px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 48px;
  font-family: "Inter";
}

.ConvrstionFedbak-info {
  font-family: "GS-medium";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.08px;
  color: #616161;
  max-width: 1024px;
  margin-top: 12px;
  margin-bottom: 40px;
}

.ConversationFeedback .list-group-item {
  border-bottom: 1px solid #eef7f1;
  padding-top: 10px;
  padding-bottom: 10px;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #262626;
  font-family: "GS-medium";
  width: 100%;
}

.ConversationFeedback .list-group {
  gap: 0px;
}

.ConversationFeedback .list-group-item:first-child {
  padding-top: 0px;
}

.ConversationFeedback .list-group-item:last-child {
  border-bottom: 0px;
  padding-bottom: 0px;
}

.ConversationFeedback .actionLink {
  margin-top: 28px;
  text-align: center;
}

ul.list-group {
  background-color: white;
  border-radius: 8px;
  padding: 30px 30px;
  gap: 20px;
  display: flex;
  flex-direction: column;
}

li.list-group-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-feedback {
  color: #094e26;
}

.rate {
  width: 100%;
}

.faq-p {
  margin-top: -7px;
  font-size: 14px;
  margin-bottom: 10px;
  padding-left: 15px;
  text-align: left;
}

div#writesoul {
  display: flex;
  align-items: center;
}

.feedback_link {
  color: #499557 !important;
  cursor: pointer;
  font-weight: 700;
  margin-left: 4px;
}

.form-in.countryCode {
  width: 100%;
  max-width: 90px;
}

.prod-desc p {
  padding: 5px 0 5px 0;
}

.prod-desc h1,
.prod-desc h2,
.prod-desc h3,
.prod-desc h4 {
  color: #262626;
}

.search-wrpr .form-control {
  padding: 10px;
  padding-right: 86px;
}

.highlights-wrapper p {
  color: #262626;
}

.highlights-wrapper strong {
  font-weight: 600;
}

#video_overview ul {
  list-style: disc;
  margin: 5px;
}

#video_overview ol {
  list-style: auto;
  margin-left: 15px;
  margin-top: 10px;
}

#video_overview li {
  margin: 5px 10px;
}

#video_overview strong {
  font-weight: 600;
}

.shop-card-wrpr .shop-card.card1 {
  background: none;
}

.product_detail_video {
  width: 100%;
  max-width: 800px;
}

.cover-img {
  object-position: top !important;
}

.loader_container {
  height: 500px;
  width: 100%;
  position: relative;
  border: 1px solid #ccc;
  background: #fff;
}

.loader_container img {
  width: 100px;
  height: 100px;
  position: absolute;
  top: 42%;
  background: #fff;
}

@media all and (max-width: 767px) {
  .loader_container {
    height: 300px;
    width: 100%;
    position: relative;
    border: 1px solid #ccc;
    background: #fff;
  }

  .loader_container img {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 30%;
    background: #fff;
  }
}

.video_title_container {
  position: relative;
  width: 100%;
  float: left;
}

.next_video_button_container {
  position: absolute;
  right: 18px;
  top: -21px;
}

.audio_player {
  margin-bottom: 20px;
}

.lesson-cd-card {
  width: 100%;
}

@media (max-width: 992px) {
  .show-btn {
    margin-top: 10px;
    display: none;
  }
}

.comp-reception-row {
  padding: 20px 32px 0px !important;
}

#my-products-container {
  margin-top: 16px;
}

.companions-wrpr-full-wdith.com-row-left {
  max-width: 100%;
  flex: 0 0 100%;
}

.checkbox_tooltip .form-check {
  float: left;
}

.checkbox_tooltip .q-mark-icon {
  margin-top: 3px;
  cursor: pointer;
}

.checkbox_tooltip .q-mark-icon a {
  width: 100%;
  height: 100%;
  display: block;
}

.checkbox_tooltip label.form-check-label {
  padding-right: 5px !important;
}

.react-calendar {
  width: auto !important;
  border: none !important;
}

.react-calendar__month-view__weekdays {
  text-transform: none !important;
  font-size: 16px !important;
  font-weight: normal o !important;
}

.meeting-schedule-calendar .react-calendar__navigation {
  text-align: right !important;
  margin: 10px auto !important;
  display: block !important;
}

.react-calendar__navigation button {
  min-width: auto !important;
}

#continue {
  font-weight: 600;
  padding: 9px 22px;
}

.continue-wrap {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  padding: 0px 0px;
}

.video-description {
  padding: 0 0px 10px;
}

.video-modal .modal-content {
  width: 800px !important;
}

.view_video {
  min-height: 460px;
}

.view_video iframe {
  width: 100%;
}

.video-modal .modal-content .modal-dialog {
  padding: 0px;
  width: 100% !important;
  box-shadow: none;
  max-width: 720px;
  min-height: fit-content !important;
}

.video-description .desc {
  padding: 10px 0px;
  max-height: 216px;
  overflow: auto;
  margin-top: 10px;
}

.playSvg {
  background-image: url(/images/Play.svg) !important;
  background-position: 2px 3px !important;
  background-size: 18px;
  cursor: pointer;
}

.step-btn.false {
  pointer-events: none;
  cursor: default;
}

.relatedProduct .prod-img {
  width: 100%;
  height: 100%;
  max-width: 210px;
  min-width: 180px;
  max-height: 240px;
  border-radius: 5px;
  overflow: hidden;
  padding: 12px 0px;
  margin: auto;
}

.relatedProduct .prod-img .cover-img {
  object-fit: contain;
}

.prod-details-wrpr .similar-prod-row.relatedProduct .similar-prod {
  flex: 0 0 200px;
}

.undo_btn {
  max-width: 880px;
  text-align: right;
}

.undo_btn button {
  background: transparent;
  border: none;
}

.bridge-table-wrpr .bridge-block .add-bridge-card {
  padding: 0 !important;
  height: 37px;
}

.new_bridge {
  width: 100%;
}

.bridge-table-wrpr .bridge-block.bridge-past-block .bridge-list-item::before {
  width: 43px !important;
}

.ck-editor__editable {
  border: 1px #ccc solid !important;
}

.ck-powered-by-balloon {
  display: none !important;
}

.label {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 0px;
}

.termsAndCondn {
  background: #fff;
  color: #616161;
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 6px;
  /* padding: 9px 20px; */
  cursor: pointer;
  font-size: 14px;
  line-height: 1.3;
  height: 31px;
  font-weight: 400;
}

.protogonist-opt-list .protogonist-opt .protogonist-content {
  text-align: left !important;
}

.appt-form-wrpr .termsConditions .error {
  position: absolute;
  bottom: -28px;
  min-width: 220px;
}

.termsConditions .form-check {
  width: 25px;
  margin-right: 10px;
}

.termsConditions .form-check input[type="checkbox"]+.form-check-label {
  padding-left: 10px;
}

@media screen and (max-width: 767px) {
  .termsAndCondn {
    height: auto;
  }

  .checkbox_tooltip label.form-check-label {
    height: auto;
  }
}

@media all and (max-width: 480px) {
  ul.feedback_questions label.form-check-label {
    height: auto !important;
  }
}

.player-wrapper {
  position: relative;
  padding-top: 56.25%;
  /* 720 / 1280 = 0.5625 */
}

.react-player {
  position: absolute;
  top: 0;
  left: 0;
}

.bridge_wrp,
.soulwriting-forms-wrpr,
.soul-table-wrpr,
.project_tool_list,
.tab-list-wrpr {
  scrollbar-width: auto !important;
  scrollbar-color: #499557 #ffffff !important;
}

.tool-tip {
  /* position: relative; */
  cursor: pointer;
}

/* Tooltip text */
.tool-tip::after {
  content: attr(data-title);
  /* Use data-title as tooltip content */
  position: absolute;

  /*transform: translateX(-50%);*/
  background-color: rgba(0, 0, 0, 0.75);
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
  width: 400px;
  /* Set a max-width for multiline */
  white-space: normal;
  /* Allow wrapping */
  opacity: 0;
  left: 5px;
  visibility: hidden;
  transition: opacity 0.3s;
  pointer-events: none;
  font-size: 14px;
  z-index: 99999;
  line-height: 1.4;
  /* Adjust line-height for readability */
}

/* Show tooltip on hover or focus */
.tool-tip:focus::after,
.tool-tip:hover::after {
  opacity: 1;
  visibility: visible;
}

.react-calendar .calender-day-tile.react-calendar__tile--active[disabled],
.calender-day-tile[disabled] {
  background: #f0f0f0 !important;
  border: 2px solid !important;

  opacity: 0.4;
}

.calender-day-tile[disabled] abbr {
  color: #a2a2a2 !important;
}

.calender-day-tile {
  background: #499557 !important;
  border: 2px solid #499557 !important;
  opacity: .8;
}

.react-calendar .calender-day-tile.react-calendar__tile--active {
  background: green !important;
  color: white !important;
  border: 2px solid #026a02;
  opacity: 1;
}

.calender-day-tile abbr {
  color: #fff !important;

}

.right-section.expand {
  flex: unset;
}

.calendar-container-div {
  position: relative;
}

.waiting-list-container {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  z-index: 999999;
  top: 0;
}

.waiting-list-inner {
  max-width: 370px;
  margin: 0px auto;
  width: 100%;
  text-align: center;
}

.wt_figure {
  width: 102px;
  height: 102px;
  margin: 0px auto;
  display: flex;
  align-content: center;
  justify-content: center;
}

.waiting-wrap-content {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.waiting-wrap-title {
  font-size: 24px;
  line-height: 1;
  font-weight: 600;
  color: #07080A;
  font-family: "GS-medium";
}

.waiting-wrap-text {
  margin: 19px 0px;
  font-size: 16px;
  font-weight: 500;
  color: #616161;
}

.notify_btn .st_mail {
  font-weight: 600;
  font-family: "GS-medium";
}

strong {
  font-weight: 600;
}

@media (max-width: 767px) {

  .soulwriting-main-wrpr .soulwriting-forms-wrpr .soul-form-header .project-name {
    min-width: 100px;
  }

  .soulwriting-main-wrpr .soulwriting-forms-wrpr .soul-form-header {
    overflow: scroll;
  }

  .soulwriting-main-wrpr .soulwriting-forms-wrpr .soul-form-header .origin-date {
    min-width: 150px;
  }

  .soulwriting-main-wrpr .soulwriting-forms-wrpr .soul-form-header .header-filter {
    min-width: 130px;
  }
}

.email_change_message {
  background: #469146;
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #469146;
  margin: 10px 0;
  font-size: 15px;
}
.email_change_message strong{
  font-weight: 600;
  text-decoration: underline;
}
.comp-inner-header.modal-open{
  z-index: -1;
  visibility:hidden;
}

.soulwriting-purchase-modal-body p {
  text-align: left;
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 20px;
}
.soulwriting-purchase-modal-body ul{
  width: fit-content;
  text-align: center;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;

}

.soulwriting-purchase-modal-body ul li{
  display: flex;
  align-items: start;
  gap: 0px;
}

.soulwriting-purchase-modal-body ul .li-content{
  display: flex;
  align-items: start;
  flex-direction: column;
  gap: 10px;
}

.soulwriting-purchase-modal-body ul span{
  font-size: 18px;
  font-weight: 500;
  text-align: start;
}

.soulwriting-purchase-modal-body ul strong{
  font-size: 18px;
  font-weight: 600;
}

.soulwriting-purchase-modal-body .errorMessage {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: center;
  flex-direction: row !important;
}

.soulwriting-purchase-modal-body .btn-accent.sm {
  padding: 16px 22px;
}

.soulwriting-purchase-modal-body .btn-accent.footer-btn.saveLeave{
  background-image: unset;
  color: #499557;
}

.soulwriting-purchase-modal-content .close-btn {
  top: 46px;
  right: 36px;
}

.soulwriting-purchase-modal-content.modal-content {
  /* max-height: fit-content !important; */
  /* height: fit-content !important; */
  width: 100%;
  max-width: 620px;
  margin: 10px;
}

.soulwriting-purchase-modal-content .modal-dialog {
  border-radius: 32px;
}

.soulwriting-purchase-modal-content .modal-header {
  padding: 44px 40px 10px 40px;
  border-bottom: none;
}

.soulwriting-purchase-modal-content .tick{
  position: relative;
  height: 19px;
  width: 11px;
  border-bottom: 4px solid green;
  border-right: 4px solid green;
  transform: rotate(45deg);
  margin-right: 20px;
}


@media only screen and (max-width:768px){
  .soulwriting-purchase-modal-body p {
    text-align: left;
    font-size: 17px;
  }

  .soulwriting-purchase-modal-body ul span{
    font-size: 16px;
    text-align: start;
  }
  
  .soulwriting-purchase-modal-body ul strong{
    font-size: 16px;
  }

  .soulwriting-purchase-modal-content .schedule-top .h4 {
    text-align: left;
    font-size: 22px !important;
  }
  .soulwriting-purchase-modal-content .modal-header{
    padding: 20px 30px;
  }

  .soulwriting-purchase-modal-content .modal-body{
    padding: 20px 30px;
  }

  .soulwriting-purchase-modal-content .close-btn {
    top: 22px;
  }
}
button span{
  font-family: inherit;
}
.soul-step-outer{
  scroll-margin: 60px;
}

@media screen and (max-width: 767px){
  .soulwriting-main-wrpr .soulwriting-forms-wrpr{
    max-height: calc(100vh - 190px) !important;
  }
  .right-section.right-section_soulwriting {
    height: calc(100vh - 177px);
  }
  .soulwriting-footer{
    position: fixed;
  }
}

@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-form-header {
    position: relative;
  }
}

.modal-body.custom-scrollbar{
  overflow-y: 'auto';
  max-height: calc(100vh) !important;
  padding-top: '0';
  height: calc(80vh - 100px) !important;
}

@media (max-width: 767px) {
  .modal-body.custom-scrollbar{
    max-height: calc(100vh - 100px) !important;
  }
}

.soul-step-outer {
  touch-action: none;
}

/**************** 2025 *******************/

.cr_title{
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  font-family: "GS-medium";
  letter-spacing: 0.7px;
  color: #213547;
  margin-bottom: 15px;
}
.flex {
  display: flex;
}

.as_card_list{
  width: 100%;
}
.hs_card {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 16px;
  border-radius: 8px;
  transition: box-shadow 0.3s;
  /* cursor: pointer; */
  align-items: center;
  margin-bottom: 15px;
}

.hs_card .image-container{
  min-width: 65px;
}


.hs_card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.hs_card.active-star {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-left: 4px solid #16a34a;
}

.hs_card.active-star.student {
  border-left: 1px solid #e5e7eb;
  background-color: #f9fafb;
}


.hs_card.bg-gray {
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
}

.cr_flex {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.image-container {
  position: relative;

}

.image-container img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  object-fit: cover;
}
.active-star .image-container img{
  border: 2px solid #10b981;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #16a34a;
  border-radius: 50%;
  padding: 4px;
}

.badge svg {
  width: 16px;
  height: 16px;
  color: white;
}
.title {
  display: flex;   
  gap: .5rem;
}
.title h3{
  font-size: 1.125rem;
  font-weight: 500;
  max-width: 99%;
}
.truncate {
  /* overflow: hidden; */
  text-overflow: ellipsis;
  white-space: nowrap;
}

.st_wrap{
  display: flex; 
  align-items: center; 
  gap: 0.25rem; 
  background-color: #f3f4f6;
  color: #4b5563;
  padding: 0.125rem 0.5rem; 
  border-radius: 0.375rem; 
  font-size: 0.875rem;
}

.active-star .st_wrap{
  background-color: #f0fdf4; 
  color: #047857; 
}

.student_status .st_wrap{
  background-color: #F3F4F6 !important;
  color: #4B5563;
}

.active-star .st_wrap{
  background-color: #f0fdf4; 
  color: #047857; 
}

.st_wrap svg{
  width: .875rem;
  height: .875rem;
}

.st_wrap span{
  font-weight: 500;
  font-size: .875rem;
  line-height: 1.25rem;
  font-family: "GS-medium";
  color: #4B5563;
}


.price {
  font-size: 1rem;
  color: #4b5563;
  font-weight: 500;
  font-family: "GS-medium";
  margin-top: 5px;
}
.active-star .price.companion{
  color: #15803d;
}

.cr_time {
  text-align: center;
}

.cr_time .hours {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 2rem;
  font-family: "GS-medium";
}



.cr_time .label {
  font-size: .875rem;
  line-height: 1.25rem;
  color: #6b7280;
  justify-content: center;
}

.act_btn {
  display: inline-block;
  text-align: center;
  border-radius: 4px;
  padding: 8px 16px;
  color: white;
  background-color: #16a34a;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s;
  border: 1px solid #16a34a;
  min-width: 126px;
}

.act_btn:hover {
  background-color: #15803d;
}

.btn-load-more {
  width: 100%;
  margin-top: 0.5rem;
  padding: 0.75rem 1rem;
  color: #4b5563;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s ease, color 0.2s ease; 
  border: none;
  font-size: 1.025rem;
  margin-bottom: 20px;
}

.btn-load-more span {
  font-weight: 600;
  font-size: 1.4rem;
}

.btn-load-more:hover {
  color: #111827;
  background-color: #f3f4f6;
}
.btn-load-more img{
  transition: all 0.6s;
}
.arrow-up img{
  transform: rotate(180deg);
  transition: all 0.6s;
} 

.icon-chevron-down {
  height: 1.25rem;
  width: 1.25rem;
}

@media (min-width: 768px) {
  .hs_card {
    flex-direction: row;
  }

  .flex.column {
    flex-direction: column;
  }

  .title h3{
    font-size: 1.125rem;
    font-family: "GS-medium";
    font-weight: 500;
    color: #213547;
  }

  .price {
    font-size: 1.125rem;
  }

  .cr_time .hours {
    font-size: 1.5rem;
  }
}

@media (min-width: 768px) and (max-width: 979px) {
  .title{
    flex-wrap: wrap;
  }
  .cr_time .hours{
    font-size: 1.3rem;
  }
}

@media (max-width: 767px) {
  .act_btn{
    width: 100%;
  }
  .cr_flex{
    width: 100%;
  }
  
}
@media (max-width: 540px) {
  .cr_flex{
    /* flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center; */
  }
  .cr_flex .title_price .title {
    flex-direction: column;
    align-content: flex-start;
    justify-content: flex-start;
  }
  .cr_flex .title_price {
    max-width: calc(100% - 160px);
  }
  .cr_flex .title_price .title .student_status{
    display: flex;
  }

}

.price_per_min div{
  float: left;
}

span.is_certified{
  color:#15803d;
}

.video-modal .view_video{
  margin-top: 10px;
}

.sub-heading-description{
  /* margin-left: 32px;
  margin-right: 32px; */
  max-width: 48rem;
}

.sub-heading-description p{
  font-family: "GS-medium";
  
}

.sub-heading-description p.large{
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: #4b5563;
}

.sub-heading-description p.small{
  font-size: .875rem;
  margin: 10px 0 30px;
  color: #4b5563;
}

.student-card-message-container{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}



.soul-step-comment-col{
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  flex-wrap: wrap;
}


.profile-card{
  margin: 10px 0px;
}
.profile-card .comp-img{
  width: 50px;
  min-width: 50px;
  height: 50px;
}

.create_Bridge .step-right {
  padding-top: 100px;
}
.col-Project .step-right {
  padding-top: 30px;
}


@media screen and (min-width: 768px) and (max-width: 1024px) {
  .create_Bridge .step-right {
    padding-top: 10px;
  }
  .col-Project .step-right {
    padding-top: 10px;
  }
}

@media screen and (max-width: 767px) {
  .soulwriting-forms-wrpr .soul-step1 {
    padding-top: 250px;
    position: relative;
  }
  .getCompanion {
    position: absolute;
    top: 0px;
    width: 100%;
    z-index: 11;
  }
  .soulwriting-forms-wrpr .soul-step1 .step-right {
    position: initial;
  }
  .getCompanion .sendToCompanionMobile .btn-accent {
    width: 100%;
  }
  .create_Bridge .step-right {
    padding-top: 10px;
  }
  .col-Project .step-right {
    padding-top: 10px;
  }
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .first-col{
    top: 48px;
    bottom: inherit;
  }

}

.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Small Profile Picture */
.profile-pic {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s;
}

.profile-pic:hover {
  transform: scale(1.1);
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3344444;
}

/* Enlarged Profile Picture */
.modal-image {
  margin: 20ox;
  width: 400px;
  max-width: 90%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.5);
}

button.calender-day-tile{
  flex: 0 0 13.586% !important;
  max-width: calc(13.6% - 1px);
}

@media screen and (min-width: 760px) {
  button.calender-day-tile{
    flex: 0 0 13.586% !important;
    max-width: 13.586%;
  }
}

@media screen and (max-width: 575px) {
  button.calender-day-tile{
    max-width: calc(13.5% - 1px);
  }
}
@media screen and (max-width: 508px) {
  button.calender-day-tile{
    max-width: calc(13.4% - 1px);
  }
}

@media screen and (max-width: 456px) {
  button.calender-day-tile{
    max-width: calc(13.2% - 1px);
  }
}

@media screen and (max-width: 380px) {
  button.calender-day-tile{
    max-width: calc(13.1% - 1px);
  }
}

@media all and (min-width: 767px) {
  .highlighted-draggable-container {
    border: 1px solid #ccc !important; 
    padding: 5px 17px 5px 17px;
    margin-left: -20px;
    margin: 2px -17px 2px -17px;

    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  }
  .steps-wrpr .soul-step-outer-redline{
    position: relative;
    gap: 36px;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 10px 0px;
    margin-left: 50px;
  }
}



.soul-step-inner .react-calendar {
  border: 1px solid #eee !important;
  margin-top: 7px;
}
.soul-step-inner .react-calendar__month-view__days{
  gap: 0px;
}

.btn-loader-green {
  pointer-events: none;
  opacity: 0.8;
  transition: none !important;

}

.btn-loader-green:after {
  content: "";
  position: absolute;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(73, 149, 87, 1);
  border-radius: 50%;
  margin-left: 0px;
  border-right-color: white;  
  animation: loader 600ms linear 0ms infinite;
  animation-direction: alternate-reverse;
  z-index: 20;
}