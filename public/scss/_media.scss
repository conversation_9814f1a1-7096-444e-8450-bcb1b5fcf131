//////////////////////////////         Reset css          /////////////////////////////////////
* {
  box-sizing: border-box;
}
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}

$phonesm: 360px;
$phonev: 480px;
$phone: 767px;
$tabv: 992px;
$tab: 1200px;
$desk: 1400px;
$sm-screen: 800px;

@mixin phonesm {
  @media (max-width: #{$phonesm}) {
    @content;
  }
}

@mixin phonev {
  @media (max-width: #{$phonev}) {
    @content;
  }
}

@mixin phone {
  @media (max-width: #{$phone}) {
    @content;
  }
}

@mixin tabv {
  @media (max-width: #{$tabv}) {
    @content;
  }
}

@mixin tab {
  @media (max-width: #{$tab}) {
    @content;
  }
}

@mixin desk {
  @media (max-width: #{$desk}) {
    @content;
  }
}

@mixin phone-m {
  @media (min-width: #{$phone}) {
    @content;
  }
}

@mixin phonev-m {
  @media (min-width: #{$phonev}) {
    @content;
  }
}

@mixin tabv-m {
  @media (min-width: #{$tabv}) {
    @content;
  }
}

@mixin tab-m {
  @media (min-width: #{$tab}) {
    @content;
  }
}

@mixin desk-m {
  @media (min-width: #{$desk}) {
    @content;
  }
}

@mixin sm-screen {
  @media (max-height: #{$sm-screen}) {
    @content;
  }
}
