@import "_media.scss";
@import "_variables.scss";
@import "_typo.scss";

/////////////////////////          display properties        ////////////////////

.d-none {
  display: none;
}
.d-block {
  display: block;
}
.d-flex {
  display: flex;
}
.d-inblock {
  display: inline-block;
}

/////////////////////////          flex properties        ////////////////////

.flex-wrap {
  flex-wrap: wrap;
}
.fd-row-r {
  flex-direction: row-reverse;
}
.fd-col {
  flex-direction: column;
}
.fd-col-r {
  flex-direction: column-reverse;
}
.align-center {
  align-items: center;
}
.align-start {
  align-items: flex-start;
}
.align-end {
  align-items: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-end {
  justify-content: flex-end;
}

///////////////////////////           buttons style             /////////////////////////////////////////////////

@mixin btn(
  $padding: 16px 22px,
  $fs: 16px,
  $fw: 500,
  $bg: transparent,
  $color: $accent,
  $bor: $accent,
  $box: 0px 0px 0px rgba(0, 0, 0, 0.04),
  $hc: $accent,
  $hbg: #f2fbf6,
  $hbor: #b7debe
) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: $fw;
  font-size: $fs;
  font-family: $gs-m;
  padding: $padding;
  background: $bg;
  color: $color;
  box-shadow: $box;
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid $bor;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;

  @include phonev {
    width: 100%;
    font-size: 14px;
  }

  // &:focus{
  //   outline: none;
  //   background: $hbg !important;
  //   box-shadow: none;
  //   color: $hc;
  //   border-color: $hbor;
  // }

  &:active,
  &:hover {
    outline: none;
    color: $hc;
    background: $hbg;
    box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
    border-color: $hbor;
  }
  &.sm {
    padding: 8px 20px;
  }
}

.btn {
  @include btn();
  &.btn-loader {
    &::after {
      border: 3px solid rgba($accent, 0.5);
      border-right-color: $accent;
    }
  }
}

.btn-accent {
  @include btn(
    $bg: linear-gradient(90deg, $accent, $accent),
    $color: $white,
    $bor: transparent,
    $hbg: linear-gradient(315.88deg, #037231 9.27%, #6ca11f 90.54%),
    $hbor: transparent,
    $hc: $white
  );
  background-size: 102% 105%;
  background-position: center;
  background-repeat: no-repeat;
  &:hover {
    background-size: 102% 105%;
    background-position: center;
    background-repeat: no-repeat;
  }
  &.yellow {
    background: #cbcb4a;
  }
  &.black {
    background: #000000;
  }
  &.blue {
    background: #0000ff;
  }
  &.orange {
    background: #ffa500;
  }
}

.btn-accent-lite {
  @include btn(
    $bg: #f2fbf6,
    $color: $accent,
    $bor: $accent,
    $hbg: $white,
    $hbor: $accent,
    $hc: $accent
  );
}

.btn-tertiary {
  @include btn(
    $bg: #eeeef7,
    $color: #2e3a39,
    $bor: #eeeef7,
    $hbg: #fff,
    $hbor: $accent,
    $hc: #2e3a39
  );
  &:hover,
  &.active {
    box-shadow: none;
  }
}

.btn-secondary {
  @include btn(
    $bg: $white,
    $color: $grey-6,
    $bor: #f0f0f0,
    $hbg: $white,
    $hbor: #c0e0c6,
    $hc: $accent
  );
  &:hover,
  &.active {
    box-shadow: none;
  }
}

.btn-loader {
  pointer-events: none;
  opacity: 0.8;
  // text-indent: -999px !important;
  transition: none !important;
  &::after {
    content: "";
    position: absolute;
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(white, 0.5);
    border-radius: 50%;
    margin-left: 0px;
    border-right-color: white;
    animation: loader 600ms linear 0ms infinite;
    animation-direction: alternate-reverse;
    z-index: 20;
  }
  .btn-text-wrpr {
    opacity: 0;
    transition: all ease 250ms;
  }
}
.btn-loader-pdf {
  pointer-events: none;
  opacity: 0.8;
  // text-indent: -999px !important;
  transition: none !important;
  &::after {
    content: "";
    position: absolute;
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(white, 0.5);
    border-radius: 50%;
    margin-left: 0px;
    border-right-color: green;
    animation: loader 600ms linear 0ms infinite;
    animation-direction: alternate-reverse;
    left: 35%;
    z-index: 20;
    outline: none;
  }
  .btn-text-wrpr {
    opacity: 0;
    transition: all ease 250ms;
  }
}
@keyframes loader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.btn-text-wrpr {
  opacity: 1;
  transition: all ease 250ms;
}
///////////////////////////////     other properties     ////////////////////////////////

.w-100 {
  width: 100%;
}
.w-full {
  width: 100vw;
}
.w-50 {
  width: 50%;
}
.w-45 {
  width: 45%;
}

.w-43 {
  width: 43%;
}

.w-40 {
  width: 40%;
}
.h-100 {
  height: 100%;
}
.h-full {
  height: 100vh;
}

.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.img-fluid {
  width: 100%;
}
.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.contain-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}

.container {
  max-width: 1200px;
  width: 100%;
  padding: 0px 15px;
  margin: 0 auto;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.over-hidden {
  overflow: hidden;
}
.over-auto {
  overflow: auto;
}

//////////////////////////////   margins   /////////////////////////////

.m-auto {
  margin: auto !important;
}
.m-0 {
  margin: 0px !important;
}
.ml-0 {
  margin-left: 0px !important;
}
.mr-0 {
  margin-right: 0px !important;
}
.mt-0 {
  margin-top: 0px !important;
}
.mb-0 {
  margin-bottom: 0px !important;
}
.mb-10 {
  margin-bottom: 10px !important;
}
.mb-15 {
  margin-bottom: 15px !important;
}
.mb-20 {
  margin-bottom: 20px !important;
}

.soulwriting-choose-companion {
  padding: 10px 40px !important;
  overflow: hidden !important;
}
.companion-choose-modal-soulwriting {
  .modal-header {
    border-bottom: none !important;
    padding: 24px 40px 10px 40px;
  }
  .modal-body {
    height: calc(100% - 120px) !important;
  }
  .modal-footer {
    padding: 16px 40px !important;
  }

  .companion-card-row {
    .companion-card {
      @include tabv {
        width: 100%;
        max-width: 100%;
      }
    }
  }
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.ml-auto {
  margin-left: auto;
  margin-right: 0px;
}
.mr-auto {
  margin-left: 0px;
  margin-right: auto;
}
.mt-auto {
  margin-top: auto;
  margin-bottom: 0px;
}
.mb-auto {
  margin-top: 0px;
  margin-bottom: auto;
}
.mt-10 {
  margin-top: 10px;
}

//////////////////////////////   padding   /////////////////////////////

.p-0 {
  padding: 0px;
}
.pl-0 {
  padding-left: 0px;
}
.pr-0 {
  padding-right: 0px;
}
.pt-0 {
  padding-top: 0px;
}
.pb-0 {
  padding-bottom: 0px;
}

//////////////////////////////   border radius   /////////////////////////////

.br-0 {
  border-radius: 0px;
}
.br-10 {
  border-radius: 10px;
}
.br-20 {
  border-radius: 20px;
}
.br-circle {
  border-radius: 50%;
}

//////////////////////////////   common  /////////////////////////////

* {
  @include no-scrollbar;
}
///////////      page loader start    ////////////////

.page-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  z-index: -9999;
  &.active {
    opacity: 1;
    visibility: visible;
    z-index: 9999;
    .loader {
      width: 150px;
      height: 150px;
      display: flex;
      gap: 20px;
      position: relative;
      &.sm {
        transform: scale(0.5);
      }
      .loader-item {
        min-width: 26px;
        width: 26px;
        height: 26px;
        background-color: $accent;
        display: block;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 0;
        animation: ploader 1.6s linear infinite forwards;
        &.loader1 {
          top: 0%;
          left: 43%;
          animation-delay: 0s !important;
        }
        &.loader2 {
          top: 16%;
          left: 72%;
          opacity: 0.9;
          animation-delay: 0.2s !important;
        }
        &.loader3 {
          top: 42%;
          left: auto;
          right: 0;
          opacity: 0.85;
          animation-delay: 0.4s !important;
        }
        &.loader4 {
          top: 70%;
          left: 72%;
          opacity: 0.72;
          animation-delay: 0.6s !important;
        }
        &.loader5 {
          top: auto;
          bottom: 0px;
          left: 43%;
          opacity: 0.5;
          animation-delay: 0.8s !important;
        }
        &.loader6 {
          top: 69%;
          left: 14%;
          opacity: 0.4;
          animation-delay: 1s !important;
        }
        &.loader7 {
          top: 42%;
          left: 0;
          opacity: 0.3;
          animation-delay: 1.2s !important;
        }
        &.loader8 {
          top: 16%;
          left: 14%;
          opacity: 0.2;
          animation-delay: 1.4s !important;
        }
      }
    }
  }
}
.img-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  z-index: -9999;
  transform: scale(0.5);
  &.active {
    opacity: 1;
    visibility: visible;
    z-index: 9999;
    .loader {
      width: 45px;
      height: 45px;
      display: flex;
      gap: 20px;
      position: relative;
      margin-left: -6px;
      .loader-item {
        min-width: 8px;
        width: 8px;
        height: 8px;
        background-color: $accent;
        display: block;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 0;
        animation: ploader 1.6s linear infinite forwards;
        &.loader1 {
          top: 0%;
          left: 43%;
          animation-delay: 0s !important;
        }
        &.loader2 {
          top: 16%;
          left: 72%;
          opacity: 0.9;
          animation-delay: 0.2s !important;
        }
        &.loader3 {
          top: 42%;
          left: auto;
          right: 0;
          opacity: 0.85;
          animation-delay: 0.4s !important;
        }
        &.loader4 {
          top: 70%;
          left: 72%;
          opacity: 0.72;
          animation-delay: 0.6s !important;
        }
        &.loader5 {
          top: auto;
          bottom: 0px;
          left: 43%;
          opacity: 0.5;
          animation-delay: 0.8s !important;
        }
        &.loader6 {
          top: 69%;
          left: 14%;
          opacity: 0.4;
          animation-delay: 1s !important;
        }
        &.loader7 {
          top: 42%;
          left: 0;
          opacity: 0.3;
          animation-delay: 1.2s !important;
        }
        &.loader8 {
          top: 16%;
          left: 14%;
          opacity: 0.2;
          animation-delay: 1.4s !important;
        }
      }
    }
  }
}
@keyframes ploader {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
///////////      page loader end     //////////////
.disabled {
  opacity: 0.4;
  pointer-events: none;
  user-select: none;
  cursor: not-allowed;
}
.icon {
  display: inline-block;
  width: 20px;
  min-width: 20px;
  height: 20px;
  background-image: url(../images/all-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  &.lock-icon {
    background-position: -59px -2px;
  }
  &.unlock-icon {
    background-position: -59px -2px;
  }
  &.eye-icon {
    background-position: 0px -114px;
    &.open {
      background-position: -28px -114px;
    }
  }
  &.info-icon {
    background-position: -87px -2px;
    width: 24px;
    height: 24px;
    min-width: 24px;
  }
  &.info-icon-grey {
    background-position: -153px -89px;
  }
  &.login-icon {
    background-position: 2px -2px;
    margin-right: 9px;
  }
  &.register-icon {
    background-position: -29px -2px;
    margin-right: 9px;
  }
  &.soul-icon {
    background-position: -25px -52px;
    width: 30px;
    height: 30px;
    min-width: 30px;
  }
  &.noti-icon {
    background-position: -122px -30px;
  }
  &.cart-icon {
    background-position: -122px -2px;
  }
  &.cart-icon-green {
    background-position: -153px -2px;
  }
  &.cart-icon-white {
    background-position: -153px -29px;
  }
  &.gray-back {
    background-position: 0px -94px;
  }
  &.shop-icon {
    background-position: -26px -89px;
  }
  &.cat-icon {
    background-position: -60px -91px;
  }
  &.sort-icon {
    background-position: -89px -91px;
  }
  &.q-mark-icon {
    background-position: -120px -57px;
    width: 24px;
    height: 24px;
    min-width: 24px;
  }
  &.clock-icon {
    background-position: -155px -57px;
  }
  &.wishlist-icon-green {
    background-position: -22px -196px;
  }
  &.wishlist-icon-green-fill {
    background-position: 1px -196px;
  }
  &.wishlist-icon {
    background-position: -189px -2px;
  }
  &.play-icon {
    background-position: -120px -91px;
  }
  &.companion-icon {
    background-position: -187px -88px;
  }
  &.user-icon {
    background-position: -215px -2px;
  }
  &.video-icon {
    background-position: -215px -27px;
  }
  &.video-chat {
    background-position: -213px -54px;
    width: 24px;
    height: 24px;
    min-width: 24px;
  }
  &.soul-icon-white {
    background-position: -212px -88px;
    width: 24px;
    height: 24px;
    min-width: 24px;
  }
  &.icon-calender {
    background-position: -158px -119px;
  }
  &.icon-exp {
    background-position: -188px -117px;
  }
  &.video-chat-green {
    background-position: -215px -117px;
  }
  &.profile-icon {
    background-position: -245px -2px;
  }
  &.meeting-icon {
    background-position: -246px -26px;
  }
  &.soulwriting-icon {
    background-position: -246px -52px;
  }
  &.order-icon {
    background-position: -246px -77px;
  }
  &.payment-icon {
    background-position: -246px -101px;
  }
  &.logout-icon {
    background-position: -247px -124px;
  }
  &.close-icon {
    background-position: 0px -136px;
  }
  &.upload-grey {
    background-position: -89px -119px;
  }
  &.edit-icon-green {
    background-position: -60px -113px;
  }
  &.arrow-icon {
    background-position: -88px -59px;
  }
  &.change-pass-green {
    background-position: -274px -176px;
  }
  &.plus-icon {
    background-position: -58px -140px;
  }
  &.edit-icon-grey {
    background-position: -87px -143px;
  }
  &.delete-icon {
    background-position: -120px -146px;
  }
  &.address-icon {
    background-position: -246px -148px;
  }
  &.change-pass-icon {
    background-position: -246px -176px;
  }
  &.check-icon-white {
    background-position: -88px -30px;
  }
  &.check-icon-grey {
    background-position: -157px -145px;
  }
  &.rate-icon {
    background-position: -216px -143px;
  }
  &.overview-icon {
    background-position: 0px -169px;
  }
  &.download-icon {
    background-position: -59px -169px;
  }
  &.comment-icon {
    background-position: -117px -171px;
  }
  &.search-icon {
    background-position: -189px -144px;
  }
  &.status-info-icon {
    background-position: -51px -196px;
  }
  &.plus-icon-white {
    background-position: -83px -196px;
  }
  &.add-user-icon {
    background-position: -115px -195px;
  }
  &.soul-comment-icon {
    background-position: -149px -196px;
  }
  &.swap-icon {
    background-position: -183px -196px;
  }
  &.leave-icon {
    background-position: -213px -200px;
  }
  &.draft-icon {
    background-position: -245px -200px;
  }
  &.send-icon {
    background-position: -274px -203px;
  }
  &.cal-icon {
    background-position: -1px -224px;
  }
  &.drag-icon {
    background-position: -26px -223px;
  }
  &.send-icon-grey {
    background-position: -275px -228px;
  }
}
.audio-icon {
  display: inline-block;
  width: 45px;
  height: 35px;
  background-image: url(../images/audio-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  &.dvd-icon {
    background-position: 7px -41px;
  }
  &.stream-icon {
    background-position: -50px -41px;
  }
}

.main-wraper {
  min-height: 100vh;
  max-width: 100vw;
  overflow: hidden;
  @include phone {
    position: relative;
  }
  &.inner {
    min-height: calc(100vh - 80px);
  }
  &.soulwriting {
    max-height: calc(100vh - 55px);
  }
}
.logo-wrpr {
  max-width: 100px;
}
.onboarding-form-wrpr {
  max-width: 460px;
  width: 460px;
  margin: 0 auto;
  padding: 20px 0px;
  justify-content: center;

  @include sm-screen {
    justify-content: inherit;
    padding-top: 40px;
  }

  .logo-wrpr {
    max-width: 150px;
    margin: 0px auto 36px;
  }
  .form-title-wrpr:has(.form-desc) .form-head {
    margin-bottom: 12px;
  }
  .form-head {
    font-size: 24px;
  }
  .frgt-pass {
    margin-top: 0px;
    position: absolute;
    top: 95px;
    right: 0;
  }
  .form-check {
    margin-bottom: 24px;
  }
}
.slick-track {
  display: flex;
}
.resend-msg-wrpr {
  margin-top: 32px;
  .resend-msg {
    font-weight: 400;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.08px;
  }
}
.back-to-login-wrpr {
  margin-top: 32px;
  .back-to-login {
    font-family: $gs-m;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.08px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    &:hover {
      .gray-back {
        background-position: 0px -75px;
      }
    }
  }
}
.link-gray {
  color: $grey-5;
  &:hover {
    color: $accent;
  }
}
.succesfull-msg-wrpr {
  margin-bottom: 32px;
}
.right-bottom-wrpr {
  padding: 32px;
  overflow: auto;
  max-height: calc(100% - 110px);
  @include phone {
    padding: 28px 16px;
  }
}
.verification-status {
  max-width: 70px;
  margin: 32px auto;
}
.rating-stars {
  gap: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  &.f-start {
    justify-content: flex-start;
  }
}
//////////////////////     tabs    //////////////////////////

.tabs {
  text-align: center;
  border-bottom: 1px solid #e0ede5;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;

  .tab-item {
    margin-bottom: -1.5px;
    .tab-link {
      display: block;
      height: 100%;
      text-align: center;
      padding: 12px;
      font-size: 14px;
      line-height: 1;
      color: $grey-4;
      position: relative;
      &.active {
        color: $black-1;
        &:after {
          content: "";
          position: absolute;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 2px;
          border-radius: 10px;
          background-color: $black-1;
        }
      }
    }
  }
}

.onboard-tab-wrpr {
  .tab-contents-wrpr {
    padding-bottom: 30px;
    .tab-contents {
      padding-top: 32px;
      display: none;
      &.active {
        display: block;
      }
    }
  }
}
.right-half {
  padding: 0px 15px;
}

//////////////////////////////      form fields        ///////////////////////////////////
.form-wrpr {
  margin-top: 24px;
  .form-in {
    margin-bottom: 24px;
  }
}
.f-row {
  gap: 28px;
  @include phonev {
    flex-direction: column;
    gap: 0px;
  }
}
.f-in {
  position: relative;
  &.right-icon {
    .form-control {
      padding-right: 30px;
    }
    .icon {
      cursor: pointer;
      position: absolute;
      top: 17px;
      right: 10px;
    }
  }
  &.left-icon {
    .form-control {
      padding-left: 30px;
    }
    .icon {
      cursor: pointer;
      position: absolute;
      top: 17px;
      left: 10px;
    }
  }
  &.radio-buttons {
    display: flex;
    gap: 60px;
    .form-control {
      display: none;
      &:checked {
        & + .r-label {
          color: $black-1;
          &::before {
            border-color: $accent;
            background-color: $accent;
            box-shadow: 0px 2px 4px rgba(4, 96, 41, 0.34);
            transition: all 400ms ease;
          }
          &:after {
            transform: translateY(-50%) scale(1);
            transition: all 400ms ease;
          }
        }
      }
    }
    .r-label {
      cursor: pointer;
      position: relative;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.43;
      color: #a2a2a2;
      font-family: $gs-m;
      padding-left: 28px;

      &::before {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        background-color: transparent;
        border: 1px solid $grey-7;
        border-radius: 50%;
        top: 50%;
        left: 0px;
        transform: translateY(-50%);
        transition: all 400ms ease;
      }
      &::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        background-color: $white;
        border-radius: 50%;
        top: 50%;
        left: 4px;
        transform: translateY(-50%) scale(0);
        transition: all 400ms ease;
      }
    }
  }
}

.profile-pic-wrpr {
  width: fit-content;
  gap: 16px;
  flex-direction: column;
  margin: 0 auto;

  .upload-img {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    margin-bottom: 0px;
    .upload-dp {
      width: 188px;
      height: 188px;
      margin-bottom: 40px;
      background-image: url(../images/icons/camera-icon1.svg);
      background-repeat: no-repeat;
      background-position: 35px 35px;
      background-color: #fbfbfb;
      border-radius: 50%;
      overflow: hidden;
      display: block;
      position: relative;
    }
    .upload-dp-in {
      width: 100%;
      height: 100%;
      border-radius: 0px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0;
      cursor: pointer;
    }
    .upload-label {
      background: #f0f0f0;
      border: 1px solid #eeeef6;
      border-radius: 8px;
      padding: 10px 16px;
      font-size: 12px;
      line-height: 1.67;
      display: flex;
      align-items: center;
      width: 100%;
    }
  }
}

.filled-input {
  font-size: 20px;
  line-height: 1.4;
}
.form-in {
  position: relative;
}
.f-error {
  .error {
    display: flex;
  }
  .form-control {
    border: 1px solid $red !important;
  }
}
.error {
  color: $red;
  font-size: 12px;
  display: none;
  margin-top: 10px;
  // position: absolute;
}
.f-label {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: $grey-5;
  margin-bottom: 8px;
  display: block;
  width: 100%;
  font-family: $gs-m;
}
.form-control,
.css-1s2u09g-control,
.css-1pahdxg-control,
.kuby-react-select__control {
  background: #ffffff;
  border: 1px solid #eaebec !important;
  border-radius: 8px !important;
  padding: 18px 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  width: 100%;
  color: $black-1;
  transition: all 0.3s ease-in-out;
  box-shadow: none !important;
  &::placeholder {
    color: $grey-7;
  }
  &:focus,
  &:focus-within {
    outline: none;
    transition: all 0.3s ease-in-out;
    border-color: rgba($accent, 0.2) !important;
  }
  &.sm {
    padding: 12px 16px;
  }
}

.css-1s2u09g-control *,
.css-1pahdxg-control *,
.kuby-react-select__control {
  padding: 0px !important;
  margin: 0px !important;
}
.kuby-react-select__control .kuby-react-select__value-container {
  padding: 12px;
}
.sm .kuby-react-select__control .kuby-react-select__value-container {
  padding: 6.5px;
}
.css-9gakcf-option {
  background-color: $accent !important;
}
.css-yt9ioa-option:hover {
  background-color: rgba($accent, 0.5) !important;
}
.form-check {
  position: relative;
  padding: 0px;
  width: fit-content;

  .form-check-label {
    position: relative;
    background: #fff;
    color: $grey-5;
    width: 100%;
    display: flex;
    align-items: center;
    border-radius: 6px;
    padding: 9px 20px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.3;
    height: 31px;
    font-weight: 600;
    @include phonev {
      height: 0px;
    }

    @include tab {
      padding: 9px 11px;
      flex-wrap: wrap;
    }
    .link {
      z-index: 20;
    }
  }
  .form-check-input {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 11;
    opacity: 0;
    cursor: pointer;
    margin: 0px;
  }

  input[type="checkbox"] + .form-check-label {
    padding-left: 30px;

    &:after {
      content: "";
      position: absolute;
      top: 6px;
      left: 0px;
      width: 22px;
      height: 22px;
      text-align: center;
      background-color: $accent;
      border-radius: 4px;
      background: url(../images/all-icons.svg) no-repeat;
      background-position: -87px -29px;
      transform: scale(0);
      transform-origin: center;
      transition: all 0.3s ease;
    }
    &::before {
      content: "";
      position: absolute;
      top: 6px;
      left: 0;
      width: 20px;
      height: 20px;
      text-align: center;
      background: none;
      border: 1px solid #bdbfd1;
      border-radius: 4px;
      transform-origin: center;
      transition: all 0.3s ease;
    }
  }

  input[type="checkbox"]:checked + .form-check-label:after {
    background: url(../images/all-icons.svg) no-repeat;
    background-position: -87px -29px;
    transform: scale(1);
    transform-origin: center;
    background-color: $accent;
  }
}
.form-btn-wrpr,
.form-blk {
  margin-top: 56px;
}
.filter-select {
  color: $grey-6;
  font-family: $gs-m;
}

///////////////////////////////////     header       ////////////////////////////////

.header {
  padding: 20px 30px;
  border: 1px solid #e7e8f2;
  z-index: 999;
  @include phone {
    padding: 15px;
  }
  .left-wrpr {
    gap: 15px;
  }
  &.soulwriting_header_bar {
    padding: 6px 30px;
  }
  .header-btn-wrpr {
    gap: 12px;
    .header-btn-inner {
      gap: 12px;
      &.header-btn-list {
        gap: 5px;
      }
      .btn {
        @include phone {
          padding: 8px;
          gap: 0;
          .icon {
            margin-right: 0px;
          }
          .btn-text {
            display: none;
          }
        }
      }
      .header-btn-item {
        position: relative;
        &.notification,
        &.wishlist,
        &.cart {
          @include phone {
            display: none;
          }
        }
      }
      .header-btn {
        background: none;
        border: 1px solid $white;
        width: 40px;
        height: 40px;
        overflow: hidden;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50px;
        transition: all 0.3s $transs;
        &:hover,
        &.active {
          .icon {
            &.noti-icon {
              background-position: -122px -120px;
            }
            &.wishlist-icon {
              background-position: -189px -28px;
            }
            &.cart-icon {
              background-position: -153px -2px;
            }
          }
        }
        &.profile-btn {
          overflow: visible;
          position: relative;
          width: auto;
          word-break: keep-all;
          white-space: pre;
          .user-name {
            font-size: 14px;
            line-height: 1.43;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            max-width: 130px;
            @include phone {
              display: none;
            }
          }
          .profile-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 8px;
          }
          &::after {
            content: "";
            position: absolute;
            top: 12px;
            right: -18px;
            width: 15px;
            height: 15px;
            background: url(../images/all-icons.svg) no-repeat;
            background-position: -90px -61px;
            transform: scale(0.7);
            @include phone {
              right: -8px;
            }
          }
        }
      }
    }
  }
}
.prof-dropdown-menu {
  background: #fff;
  box-shadow: 4px 4px 36px rgba(19, 61, 48, 0.1);
  border-radius: 8px;
  margin-top: 10px;
  position: absolute;
  top: auto;
  right: 0;
  min-width: 232px;
  visibility: hidden;
  opacity: 0;
  z-index: -99999;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
  &.active {
    visibility: visible;
    opacity: 1;
    z-index: 1000;
    pointer-events: all;
    transition: all 0.2s ease-in-out;
  }
  .prof-menu-wrpr {
    padding: 8px 0px;
    border-bottom: 1px solid #eef7f1;
    &:last-child {
      border: none;
    }
    .dropdown-lbl {
      font-size: 12px;
      margin-bottom: 8px;
      padding: 0px 24px;
    }
    .dropdown-link {
      padding: 10px 24px;
      font-size: 14px;
      line-height: 1;
      font-family: $gs-m;
      font-weight: 500;
      color: $grey-5;
      display: block;
      display: flex;
      align-items: center;
      &:hover {
        color: $accent;
        background-color: $accent-l;
        .icon {
          background-position-x: -274px;
          &.profile-icon {
            background-position-x: -215px;
          }
          &.logout-icon {
            background-position-x: -275px;
          }
        }
      }
      .icon {
        margin-right: 5px;
      }
    }
  }
}

.language-select {
  border: none;
  padding: 12px;
  color: $grey-6;
  cursor: pointer;
  outline: none !important;
}
.inner-header {
  padding: 24px 0px;
  margin-left: 32px;
  margin-right: 32px;
  border-bottom: 1px solid #e0ede5;
  gap: 20px;
  &.b-0 {
    border: none;
  }
  @include tab {
    padding: 15px 0px;
    gap: 15px;
    margin-left: 15px;
    margin-right: 15px;
  }
  .title-wrpr {
    gap: 12px;
    .page-title {
      font-size: 24px;
      letter-spacing: -0.5px;
      color: #394445;
      @include desk {
        font-size: 20px;
      }
      @include phone {
        font-size: 18px;
      }
    }
    .title-icon {
      background-color: #e9f6f5;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      &.soul-icon {
        background-position: -26px -56px;
      }
    }
  }
  .inner-head-btns-wrpr {
    position: relative;
    .filter-btn {
      display: none;
      @include tab {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        border-radius: 40px;
        border: 1px solid $accent;
        background: rgba($accent, 0.2);
      }
      @include phonev {
        width: 34px;
        height: 34px;
      }
    }
  }
  .inner-header-btns {
    gap: 14px;
    @include tab {
      width: 240px;
      height: 0px;
      padding: 0px;
      overflow: hidden;
      position: absolute;
      right: 0px;
      flex-direction: column;
      background: $white;
      align-items: flex-start;
      border: none;
      border-radius: 10px;
      z-index: 1;
      transition: all 0.3s ease-in-out;
    }
    &.active {
      height: auto;
      padding: 30px 15px;
      border: 1px solid $grey-7;
      transition: all 0.3s ease-in-out;
    }
  }
  .header-filter {
    background: #ffffff;
    box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
    border-radius: 4px;
    padding: 10px 14px;
    gap: 8px;
    @media all and (max-width: 1350px) and (min-width: 1201px) {
      padding: 8px 5px;
    }
  }
  .filter-select {
    border: none;
    outline: none !important;
    cursor: pointer;
  }
}

///////////////////////////////////     sidebar       ////////////////////////////////

.left-sidebar {
  flex: 0 0 230px;
  padding: 32px 17px;
  border-right: 1px solid #e7e8f2;
  transition: all 0.3s ease-in-out;
  position: relative;
  z-index: 3;
  @include phone {
    position: absolute;
    z-index: 999;
    background: #fff;
    width: 100%;
    height: 100%;
    transform: translateX(-100%);
  }

  .nav-list {
    gap: 10px;

    .nav-link {
      padding: 10px;
      // max-width: fit-content;
      width: 100%;
      position: relative;
      transition: all 0.3s ease-in-out;

      &:before {
        content: "";
        position: absolute;
        top: 11px;
        left: 10px;
        right: 0;
        bottom: 0;
        width: 24px;
        z-index: 1;
        height: 24px;
        background: #f4faf9;
        border-radius: 4px;
        opacity: 0;
        transition: all 0.2s $transs;
      }
      &.active,
      &:hover {
        color: $accent !important;
        font-weight: 500;
        &::before {
          opacity: 1;
          transition: all 0.2s $transs;
        }
        .nav-icon {
          background-position-x: -30px !important;
        }
      }
      .nav-link-text {
        width: fit-content;
        opacity: 1;
        transition: all 0.2s ease-in-out;
      }
    }
  }
  &.closed {
    flex: 0 0 78px;
    padding: 32px 12px;
    transition: all 0.3s ease-in-out;
    .nav-link {
      // text-indent: -99999px;

      margin: 0 auto;
      min-width: 44px;
      width: 44px !important;
      height: 44px;
      transition: all 0.3s ease-in-out;
      &:hover,
      &.active {
        // background: url(../images/link-bg.svg) no-repeat;
        // background-position: center;
        // background-size: 100%;
        background-color: #daece1;
        border-radius: 8px;
      }
      .nav-link-text {
        width: 0px;
        opacity: 0;
        transition: all 0.2s ease-in-out;
      }
      .nav-icon {
        margin-right: 0px;
      }
    }
  }
  &.active {
    transform: translateX(0);
  }
}
.nav-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-image: url(../images/nav-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  margin-right: 10px;
  z-index: 2;
  &.reception {
    background-position: 0px 0px;
  }
  &.practice {
    background-position: 0px -28px;
  }
  &.calender {
    background-position: 0px -57px;
  }
  &.companions {
    background-position: 0px -85px;
  }
  &.soul {
    background-position: 0px -113px;
  }
  &.shop {
    background-position: 0px -141px;
  }
  &.meeting {
    background-position: 0px -197px;
  }
}

////////////////////////////////////   right section   //////////////////////////////

.right-section {
  flex: 1 1 calc(100% - 230px);
  background-color: #fcfcfc;
  // max-width: calc(100% - 230px);
  width: 100%;
  // height: 100%;
  height: calc(100vh - 82px);
  position: relative;
  transition: all 0.3s ease-in-out;

  &.expand {
    flex: 0 0 calc(100% - 78px);
    max-width: calc(100% - 78px);
    transition: all 0.3s ease-in-out;
    &.right-sidebar-fullwidth {
      flex: 1 1 100%;
      max-width: 100%;
    }
    @include phone {
      flex: 1 1 auto;
      max-width: 100%;
    }
  }
  &.right-section_soulwriting {
    height: calc(100vh - 55px);
    @include phone {
      height: calc(100vh - 55px);
    }
  }
}

.lock-icon-wrpr {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  &.top {
    .lock {
      position: absolute;
      top: 8px;
      right: 8px;
    }
  }
  .lock {
    width: 40px;
    height: 40px;
    background: rgba(172, 65, 57, 0.8);
    backdrop-filter: blur(2px);
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(../images/all-icons.svg);
    background-position: -49px -50px;
    background-repeat: no-repeat;
    &.unlock {
      background-position: -18px -133px;
    }
    &.check {
      background: #499557b8;
      background-image: url(../images/all-icons.svg);
      background-repeat: no-repeat;
      background-position: -88px -30px;
      width: 20px;
      height: 20px;
      scale: 2;
      right: 18px;
      top: 18px;
    }
  }
}

//////////////    shop page start   /////////////////////

.product-wrpr {
  gap: 22px;
  .product-card {
    background: #ffffff;
    box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
    border-radius: 8px;
    padding: 0px;
    gap: 20px;
    flex: 1 1 calc(50% - 24px);
    max-width: calc(50% - 12px);

    @media (max-width: 1300px) {
      flex-direction: column;
      gap: 0;
      flex: 1 1 calc(50% - 12px);
      max-width: calc(50% - 12px);
    }
    @include phone {
      flex-direction: column;
      flex: 1 1 100%;
      max-width: unset;
    }
    .product-image-wrpr {
      width: 100%;
      height: 100%;
      max-width: 210px;
      min-width: 180px;
      max-height: 240px;
      border-radius: 5px;
      overflow: hidden;
      padding: 12px;
      @include tab {
        max-width: unset;
        max-height: 200px;
        padding: 12px;
      }
      img {
        filter: drop-shadow(0px 2px 4px #aaa);
      }
    }
    .product-details-wrpr {
      flex: 1 1 100%;
      display: flex;
      flex-direction: column;
      height: 100%;
      align-items: flex-start;
      gap: 20px;
      padding: 22px 0px;
      justify-content: space-between;
      @include tab {
        width: 100%;
        flex: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0px 12px 12px;
      }
    }
    .product-top-wrpr {
      gap: 10px;
      .product-cat {
        font-family: "inter", sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.67;
        .cat-name {
          font-weight: 500;
          color: #003b4d;
          font-family: "inter-semibold", sans-serif;
        }
      }
      .product-title {
        font-size: 18px;
        line-height: 22px;
      }
      .product-desc {
        font-family: "inter", sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.22;
      }
    }
    .product-bottom-wrpr {
      // margin-top: 34px;
      gap: 12px;
      width: 100%;
      @include phone {
        width: 100%;
        margin-top: 10px;
      }
      .product-price {
        font-size: 24px;
        line-height: 1.17;
        color: #666773;
      }
    }
  }
}
.prod-btn-wrpr {
  gap: 12px;
  // flex-wrap: wrap;
  @include phone {
    width: 100%;
    // flex-direction: column;
  }
  .prod-btn {
    padding: 10px;
    white-space: pre;
    // @include phone{
    //   width: 100% !important;
    // }
  }
  .cart-btn {
    gap: 5px;
  }
  .wishlist-btn {
    gap: 5px;
  }
}

///////////////// shop details page start  /////////////////
.prod-details-wrpr {
  padding: 0px 32px;
  @include phone {
    padding: 0px 15px;
  }
  .prod-main-section {
    margin-top: 10px;
    margin-bottom: 40px;
    @include phone {
      flex-direction: column;
      margin-bottom: 10px;
    }
    .product-main-img {
      flex: 0 0 20%;
      padding-right: 20px;
      @include tabv {
        flex: 0 0 33%;
      }
      @include phone {
        flex: 1 1 auto;
        padding-right: 0px;
        overflow: hidden;
        width: 300px;
        max-height: 350px;
        margin: 0 auto;
      }
    }
    .prod-right-block {
      flex: 1 1 auto;
      padding-left: 32px;
      padding-right: 50px;
      @include tabv {
        padding: 0px 10px;
      }
      @include phone {
        padding: 0px;
        margin-top: 10px;
      }
    }
    .audio-option-block {
      .label {
        margin-bottom: 8px;
      }
    }
  }
  .audio-opt-wrpr {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    .form-group {
      .audio-input {
        opacity: 0;
        position: absolute;
        &:checked {
          & + .audio-label {
            background: $accent;
            color: $white;
            font-size: 14px;
            font-weight: 600;
            .dvd-icon {
              background-position: 7px 2px;
            }
            .stream-icon {
              background-position: -50px 2px;
            }
          }
        }
      }
      .audio-label {
        background: #f0f0f0;
        border-radius: 10px;
        padding: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 6px;
        width: 72px;
        height: 88px;
        font-family: "inter", sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.5;
        letter-spacing: -0.12px;
        color: $grey-5;
        cursor: pointer;
      }
    }
  }
  .product-desc-block {
    margin-bottom: 20px;
    .prod-title {
      margin-bottom: 10px;
      color: #262626;
      @include phone {
        display: none;
      }
    }
    .prod-desc {
      font-family: "inter", sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.43;
      color: #404040;
    }
  }
  .price-block {
    margin-bottom: 20px;
    .price-text {
      font-size: 14px;
      line-height: 1.43;
      color: $grey-5;
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      .prod-price {
        margin-left: 6px;
        font-size: 24px;
        line-height: 1.17;
        color: $black-1;
      }
    }
    .price-btn-quantity {
      gap: 40px;
      .prod-btn {
        min-width: 120px;
      }
    }
  }
  .version-wrpr {
    margin-bottom: 32px;
    .version-text {
      color: #404040;
      margin-bottom: 5px;
    }
  }
  .other-language-wrpr {
    margin-bottom: 32px;
    .p {
      margin-bottom: 10px;
    }
    .language-link {
      font-size: 14px;
      line-height: 1.43;
    }
  }
  .tags-wrpr {
    margin-bottom: 10px;
    .p {
      .prod-tags {
        color: #003b4d;
      }
    }
  }
  .prod-tabs-wrpr {
    margin-top: 24px;
    .tab-list-wrpr {
      gap: 30px;
      border-bottom: 1px solid #daece1;
      margin-bottom: 20px;
      .tab-item {
        &:first-child {
          .tab-link {
            padding-left: 0px;
          }
        }
      }
      .tab-link {
        padding: 12px 10px;
        color: $grey-6;
        font-size: 24px;
        font-family: "Inter", sans-serif;
        display: block;
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: auto;
          bottom: -1px;
          width: 100%;
          height: 2px;
          background: $black-1;
          border-radius: 10px;
          display: none;
        }
        &.active {
          color: $grey-1;
          font-weight: 600;
          &::before {
            display: block;
          }
        }
      }
    }
  }
  .prod-video-wrpr {
    gap: 24px;
    margin-bottom: 72px;
    .prod-video-block {
      background: #ffffff;
      box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
      border-radius: 8px;
      padding: 8px 8px 20px;
      .prod-video {
        height: 200px;
        position: relative;
      }
      .prod-video-title {
        padding-top: 12px;
        font-size: 16px;
        line-height: 1.25;
        color: #404040;
      }
    }
  }
  .desc-wrpr {
    margin-bottom: 75px;
    .prod-desc {
      font-family: "inter", sans-serif;
      font-size: 16px;
      line-height: 1.25;
      color: #616161;
      margin-bottom: 30px;
    }
  }
  .title {
    font-size: 18px;
    line-height: 1.25;
    letter-spacing: -0.06px;
  }
  .similar-prod-row {
    gap: 30px;
    margin: 30px 0px;
    .similar-prod {
      background: #ffffff;
      box-shadow: 0px 3.65448px 25.5814px rgba(102, 111, 167, 0.08);
      border-radius: 8px;
      padding: 8px 8px 20px;
      flex: 0 0 20%;
      .prod-title {
        margin-top: 12px;
        font-size: 16px;
        line-height: 1.25;
      }
    }
  }
}
.play-icon-wrpr {
  position: absolute;
  top: 40%;
  left: 36%;
  max-width: 100%;
  max-height: 100%;
  background-color: rgba(94, 179, 94, 0.8);
  border: 1px solid rgba(94, 179, 94, 0.4);
  box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.16);
  backdrop-filter: blur(2px);
  border-radius: 8px;
  width: 60px;
  height: 46px;
}
///////////////////    breadcumb start    /////////////////////
.cd-breadcrumb {
  padding: 24px 0px;
  @include phone {
    flex-wrap: wrap;
    display: none;
  }
  .breadcumb-item {
    padding-right: 5px;
    &.current {
      color: $accent;
      font-size: 12px;
      line-height: 1.33;
      font-family: $gs-m;
      top: 1px;
      position: relative;
    }

    .breadcumb-link {
      padding-right: 20px;
      position: relative;
      font-size: 12px;
      line-height: 1.33;
      color: $grey-7;
      font-family: $gs-m;
      white-space: pre;
      &:before {
        content: "";
        position: absolute;
        top: 0px;
        right: 0;
        bottom: 0;
        background: url(../images/all-icons.svg) no-repeat;
        width: 15px;
        height: 15px;
        background-position: -91px -60px;
        rotate: -90deg;
      }
    }
  }
}

///////////////////    quantity box start    /////////////////////
.quantity-wrpr {
  background: #f0f0f0;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  .quantity-btn {
    width: 40px;
    height: 34px;
    font-size: 20px;
    border: none;
    outline: none;
    background-color: #fff;
    border-radius: 8px;
    cursor: pointer;
  }
  .quantity-value {
    min-width: 45px;
    text-align: center;
    font-size: 20px;
  }
}

///////////////////    Companion section start  ////////////////////

.companion-tab-wrpr {
  padding: 0px 32px;
  @include phone {
    padding: 0px 15px;
  }
  .companion-tab {
    justify-content: start;
    gap: 30px;
    .tab-item {
      &:first-child {
        .tab-link {
          padding-left: 0px;
        }
      }
      .tab-link {
        font-family: $gs-m;
        font-weight: 500;
        color: $grey-6;
        &.active {
          color: $grey-1;
        }
      }
    }
  }
  .tab-contents {
    padding-top: 40px;
    display: none;
    &.active {
      display: flex;
    }
    @include tab {
      padding-top: 24px;
    }
  }
}

/////////////////////    companion card start    /////////////////////

.companion-card-row {
  gap: 12px;
  flex-wrap: wrap;
  // justify-content: space-between;
}

.companion-choose-modal {
  max-width: 1100px !important;
}

.comment-text {
  strong {
    font-weight: bold;
  }
  i {
    font-style: italic;
  }
  ul,
  ol {
    padding-left: 20px;
  }
}
.companion-card {
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  padding: 8px 8px 24px;
  flex: 0 0 32%;
  max-width: 32%;

  @include tab {
    flex: 0 0 48%;
    max-width: 48%;
  }
  @include tabv {
    flex: 0 0 100%;
    max-width: unset;
  }
  .companion-details {
    background: #fafafc;
    border-radius: 8px;
    padding: 20px 16px;
    margin-bottom: 12px;
    @include phonev {
      padding: 12px 8px;
    }
    .comp-img {
      width: 124px;
      height: 124px;
      border-radius: 50%;
      margin: 0 auto 10px;
      overflow: hidden;
    }
    .btn-tertiary-wrpr {
      gap: 12px;
      margin-top: 24px;
      .btn-tertiary {
        padding: 10px 16px;
        font-size: 12px;
        font-weight: 500;
        @include phonev {
          padding: 10px 10px;
        }
      }
    }
  }
  .comp-card-button-wrpr {
    padding: 0px 24px;
    gap: 12px;
    display: flex;
    flex-direction: column;
    @include phonev {
      padding: 0px 15px;
    }
  }
}
.comp-name-wrpr {
  gap: 8px;
  margin-bottom: 4px;
}

//////////////////////////             common modal start           //////////////////////////////////

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;

  &.show {
    opacity: 1;
    visibility: visible;
    z-index: 999999;
  }
  .overlay-div {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .calendar-overlay-div {
    content: "";
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .modal-dialog {
    background: $white;
    box-shadow: 4px 4px 36px rgba(2, 23, 17, 0.1);
    border-radius: 6px;
    max-width: 840px;
    margin: 0 auto;
    z-index: 2001;
    position: relative;
    height: 100%;
  }
  &.change-companion-confirmation {
    .modal-dialog {
      max-width: 600px;
    }
    p {
      line-height: 2;
      margin-bottom: 10px;
    }
  }
}
.close-btn {
  background: transparent;
  border-radius: 50px;
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  .close-icon {
    transform: scale(1.5);
  }
}
.modal-content {
  max-height: 90vh;
  height: 100%;
  @include phone {
    max-height: 90vh;
  }
}
.modal-body {
  height: calc(100% - 304px);
  overflow: auto;
  padding: 24px 40px;
  @include phone {
    padding: 15px 20px;
  }
}
.schedule-modal {
  .modal-content {
    min-width: 850px;
    @include tab {
      min-width: unset;
      max-width: 90vw;
    }
  }
  .modal-body {
    height: calc(100% - 224px);
    @include phone {
      height: calc(100% - 185px);
    }
  }
}
.video-modal {
  padding: 0px 20px;
  .view_video {
    max-height: 340px;
    min-height: 340px;
    margin-bottom: 10px;
    > div {
      height: 340px;

      iframe {
        height: 100%;
      }
    }
  }
  .modal-content {
    max-height: unset;
    height: auto;
    min-width: 600px;
    z-index: 1;
    max-height: unset;
    height: auto;
    min-width: 600px;
    z-index: 1;
    background: #fff;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    @include phone {
      align-items: baseline;
    }
    .modal-body {
      width: 100%;
    }
    @include phone {
      min-width: 100%;
    }
    .view_video {
      @include phone {
        min-height: 210px;
        height: 210px;
        border-radius: 0;
        > div {
          height: 100%;
        }
      }
      iframe {
        @include phone {
          height: 100%;
          // background: #000;
        }
      }
    }
    .continue-wrap {
      @include phone {
        padding: 0px 10px;
      }
    }
    .video-description {
      @include phone {
        padding: 10px;
      }
    }

    .video-title {
      padding: 0px 0px 40px;
      position: relative;
      padding-top: 20px;
      @include phone {
        overflow: hidden;
        padding-top: 20px;
      }
      &::before {
        content: "";
        position: absolute;
        left: -40px;
        height: 1px;
        width: calc(100% + 80px);
        background: #eee;
        bottom: 18px;
      }
      h3 {
        text-align: center;
        letter-spacing: 0.5px;
        @include phone {
          font-size: 20px;
        }
      }
    }

    .video-skeleton {
      max-width: 720px;
      height: 340px;
      background: #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      width: 100%;
      @include phone {
        height: 360px;
      }
    }
  }
}

.soulIntro {
  .modal-content {
    @include phone {
      height: calc(100vh - 85px);
    }
    .soulIntro_video {
      iframe {
        width: 100%;
        @include phone {
          width: 100%;
          background: #000;
          height: 380px;
        }
      }
    }
    .video-description {
      .desc {
        @include phone {
          max-height: 200px;
        }
      }
    }
  }
}
.soul-vedio {
  iframe {
    width: 100%;
    @include phone {
      // width: 100%;
      // background: #000;
      // height: 380px;
    }
  }
}
.modal-header {
  padding: 24px 40px;
  border-bottom: 1px solid #daece1;
  @include phone {
    padding: 15px 20px;
  }
}
.profile-top {
  .profile-details-wrpr {
    padding: 0px 12px;
    gap: 12px;
    @include phone {
      flex-wrap: wrap;
    }
    .icon {
      margin-right: 8px;
    }
  }
}
.profile-card {
  .comp-img {
    width: 80px;
    min-width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
  }
}
.modal-footer {
  padding: 24px 40px;
  border-top: 1px solid #f0f0f0;
  @include phone {
    flex-direction: column;
    padding: 8px;
    gap: 4px;
  }
  .modal-btn-wrpr {
    gap: 16px;
    .btn-accent {
      white-space: pre;
      word-break: keep-all;
      overflow: inherit;
    }
  }
}
.comp-about-title {
  margin-bottom: 8px;
  font-size: 16px;
  line-height: 1.25;
  font-weight: 500;
  font-family: $gs-m;
}
.comp-about {
  margin-bottom: 32px;
}

.profile-modal {
  .modal-content {
    height: auto;
    @include phone {
      max-width: 90vw;
    }
    .modal-body {
      max-height: calc(100vh - 400px);
      @include phone {
        max-height: calc(100vh - 540px);
      }
    }
  }
}
.soulwriting-modal .modal-content {
  height: auto;
  max-width: 90vw;
}
.more-info-modal {
  @include phone {
    padding: 0px 20px;
  }
  .modal-content {
    max-height: 80vh;
    height: 100%;
    @include phone {
      width: 100%;
    }
  }
  .modal-body {
    max-height: 100%;
    height: calc(100% - 75px);
  }
}

//////////////////////////             common modal end           //////////////////////////////////

.profile-info-wrpr {
  background: #fafafc;
  border-radius: 8px;
  padding: 20px 16px;
  margin-bottom: 24px;
  @include phone {
    flex-direction: column;
    gap: 10px;
  }
}
.prof-details-wrpr {
  padding: 0px 32px;
  overflow: auto;
  height: auto !important;
  min-height: calc(100% - 80px);
  @include tab {
    padding: 0px 20px;
  }
}
.prof-tabs-wrpr {
  .tab-contents {
    margin-top: 40px;
    padding-bottom: 40px;
    display: none;
    &.active {
      display: block;
    }
  }
  .profile-tab-wrpr {
    box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
    border-radius: 8px;
    padding: 6px 40px;
    @include tab {
      padding: 6px 20px;
    }
    .profile-header {
      border-bottom: 1px solid #e0e0e0;
      padding-top: 18px;
      padding-bottom: 18px;
      gap: 20px;
      .title {
        color: $black-1;
      }
      .profile-btns {
        gap: 20px;
        flex-wrap: wrap;
        justify-content: flex-end;
      }
    }
    .profile-info-wrpr {
      margin: 32px 0px;
    }
  }
  .prof-change-pass {
    margin-top: 46px;
    display: flex;
    align-items: center;
  }
  .join-date-wrpr {
    background: #eeeef7;
    border: 1px solid #eeeef6;
    border-radius: 8px;
    min-width: 284px;
    padding: 12px;
  }
  .profile-others {
    padding-bottom: 30px;
    gap: 60px;
    .other-details {
      min-width: 300px;
      max-width: 300px;
      &:last-child {
        max-width: unset;
      }
      .prof-label {
        margin-bottom: 2px;
      }
      .lang-block {
        flex-wrap: wrap;
        .lang-wrpr {
          gap: 8px;
        }
      }
    }
  }
  .profile-form {
    .form-wrpr {
      padding-bottom: 20px;
      @include tabv {
        flex-direction: column;
      }
    }
  }
  .form-btns-wrpr {
    padding: 38px 0px;
    border-top: 1px solid #f0f0f0;
    .btn-wrpr {
      gap: 16px;
    }
  }
}
.profile-form {
  .profile-form-left {
    flex: 0 0 30%;
  }
  .profile-form-right {
    flex: 1;
    .f-row {
      gap: 12px;
    }
  }
}
.tab-list-wrpr {
  gap: 18px;
  margin-left: -10px;
  border-bottom: 1px solid #daece1;
  padding-bottom: 13px;
  margin-top: 10px;
  overflow: auto;
  @include tabv {
    border-bottom: 0px;
  }
  @include phone {
    margin-left: 0px;
  }

  .tab-link {
    font-family: $gs-m;
    font-weight: 500;
    font-size: 14px;
    line-height: 1;
    color: $grey-6;
    padding: 10px;
    position: relative;
    white-space: pre;
    &:after {
      content: "";
      position: absolute;
      top: auto;
      bottom: 3px;
      left: 0;
      right: 0;
      width: 100%;
      height: 2px;
      background: black;
      opacity: 0;
    }
    &:hover,
    &.active {
      color: $black-1;
      &:after {
        opacity: 1;
      }
    }
  }
}
.remove-img {
  width: 100%;
  text-align: center;
  padding: 10px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}
.change-pass-wrpr {
  gap: 40px;
  @include tab {
    flex-direction: column;
    gap: 10px;
    .w-50 {
      width: 100%;
    }
  }
}
/////////////      recepetion page start     /////////////////

.comp-reception-row {
  padding: 40px 32px 0px;
  @include tab {
    flex-direction: column;
  }
  @include phone {
    padding: 30px 20px 0px;
  }
  .com-row-left {
    max-width: 75%;
    flex: 0 0 75%;
    padding-right: 16px;
    @include tab {
      max-width: 100%;
      flex: 0 0 100%;
      padding-right: 0px;
    }
  }
  .com-row-right {
    max-width: 25%;
    flex: 0 0 25%;
    padding-left: 16px;
    @include tab {
      max-width: 60%;
      flex: 0 0 100%;
      padding-left: 0px;
    }
    @include phonev {
      max-width: 100%;
    }
  }
}
.reception-row {
  padding: 6px 32px;
  @include tab {
    flex-direction: column;
    padding: 6px 20px;
  }
  .row-left,
  .row-right {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 28px;
    @include tab {
      max-width: 100%;
      flex: 0 0 100%;
      padding-right: 0px;
    }
  }
  .row-right {
    padding-left: 28px;
    padding-right: 0px;
    @include tabv {
      padding-left: 0px;
    }
  }
}
.comp_slider {
  width: 100%;
  margin: 0px -8px;
  position: relative;
  .slick-slide {
    padding: 0px 8px;
  }
  .cstm_slick_arrow {
    background: #ffffff;
    box-shadow: 0px 4px 20px rgba(8, 55, 47, 0.08);
    border-radius: 8px;
    border: none;
    position: absolute;
    top: 40%;
    left: -10px;
    z-index: 99;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &.slick_next {
      left: auto;
      right: -10px;
      transform: rotate(180deg);
    }
  }
  .slick-list {
    overflow: hidden;
    padding: 24px 0px 32px;
  }
  .comp-card {
    background: #ffffff;
    box-shadow: 0px 4.10869px 28.7608px rgba(102, 111, 167, 0.08);
    border-radius: 8px;
    padding: 18px;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    text-align: center;
    // margin: 0px 8px;
    .comp-img {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      overflow: hidden;
      margin: 0px auto 12px;
    }
    .comp-name {
      font-size: 14px;
      line-height: 18px;
      text-align: center;
      color: #07080a;
      margin-bottom: 4px;
    }
    .comp-title {
      font-weight: 400;
      font-size: 12px;
      line-height: 14px;
      color: #404040;
      margin-bottom: 8px;
    }
    .flag-main-wrpr {
      gap: 8px;
      margin-bottom: 26px;
    }
    .comp-price {
      display: flex;
      align-items: center;
      color: #666773;
      font-size: 16px;
      .price-value {
        margin-right: 3px;
        font-weight: 600;
      }
    }
    .comp-price-details {
      margin-top: 10px;
    }
  }
}

.meeting-block {
  padding-top: 56px;
  position: relative;
  @include tab {
    padding-top: 20px;
  }
}
.meeting-card-wrpr {
  gap: 8px;
  box-shadow: 0px 4.10869px 28.7608px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 8px 4px 4px;
  margin-bottom: 26px;
  position: relative;

  .heading-row {
    padding: 0px 15px;
    .img-card-title {
      font-size: 12px;
    }
  }
  .meeting-card {
    gap: 16px;
    background: #fafafc;
    border-radius: 4px;
    padding: 10px;
    .meeting-name {
      font-size: 12px;
    }
    .meeting-desc {
      font-size: 10px;
    }
    .meeting-mnth {
      padding: 0px 10px;
      background: #538137;
      font-size: 12px;
      border-radius: 3px 3px 0px 0px;
    }
    .meeting-date {
      background: #6e9d51;
      box-shadow: 0px 2.72134px 19.0494px rgba(102, 111, 167, 0.08);
      transform: skewX(5deg) translateX(1px);
      border-radius: 0px 0px 3px 3px;
      font-size: 16px;
      padding: 2px 12px;
    }
  }
  .meeting-date-wrpr {
    border-radius: 5px;
    background: linear-gradient(0deg, #163007, #163007), #163007;
    box-shadow: 0px 2.72134px 19.0494px rgba(102, 111, 167, 0.08);
    text-align: center;
  }
  .lock-icon-wrpr {
    backdrop-filter: blur(12px);
    border-radius: 8px;
  }
}

.reception-card-block {
  margin-top: 24px;
  gap: 16px;
  flex-wrap: wrap;
  @include desk {
    flex-wrap: wrap;
  }
  .img-name-card {
    padding: 10px 10px 20px;
    background: #ffffff;
    box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
    border-radius: 8px;
    flex: 0 0 calc(33% - 10px);
    max-width: 220px;
    @include desk {
      max-width: 100 !important;
      flex: 0 0 47%;
    }
    @include phone {
      max-width: 100% !important;
      flex: 0 0 calc(50% - 10px);
    }
    @include phonev {
      max-width: 100% !important;
      flex: 0 0 100%;
    }
    .h6 {
      min-height: 43px;
    }
    .img-card {
      margin-bottom: 12px;
      height: 200px;
      border-radius: 8px;
      overflow: hidden;
      .cover-img {
        object-fit: contain;
        width: 100%;
        height: 100%;
      }
    }
  }
}

.shop-title-row {
  padding-top: 40px;
  padding-bottom: 30px;
}
.shop-card-wrpr {
  gap: 55px 2px;
  margin-top: 30px;
  justify-content: flex-start;
  align-items: baseline;
  flex-wrap: nowrap;
  @include desk {
    flex-wrap: wrap;
  }
  @include phone {
    justify-content: center;
  }
  .shop-card {
    border-radius: 24px;
    /* width: 16%; */
    flex: 0 0 calc(100% / 6);
    max-width: 230px;
    min-width: 180px;
    @include desk {
      flex-wrap: wrap;
      width: 31%;
    }
    @include tab {
      width: 48%;
      flex: 1;
    }
    @include phone {
      width: 100%;
      flex: 1;
    }
    &.card1 {
      background: #ebdede;
    }
    &.card2 {
      background: #e0e5eb;
    }
    &.card3 {
      background: #ececf2;
    }
    .prod-img {
      filter: drop-shadow(-4.54819px 2.18313px 7.27711px rgba(0, 0, 0, 0.25));
      margin: -30px 21px 0px;
      border-radius: 8px 8px 0px 0px;
      overflow: hidden;
      height: 200px;
    }
    .prod-title {
      font-size: 14px;
      line-height: 1.43;
      padding: 13px 21px 17px;
      background-color: #fbfcfb;
    }
  }
}

/////////////      recepetion page end     /////////////////

.css-1okebmr-indicatorSeparator {
  display: none;
}
.css-1rhbuit-multiValue {
  padding: 4px 5px 4px 8px !important;
  margin-right: 7px !important;
  border-radius: 4px !important;
  margin-bottom: 4px !important;
  margin-top: 4px !important;
  .css-xb97g8 {
    position: relative;
    right: -2px;
  }
}
.schedule-list {
  margin-top: 30px;
  @include phone {
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
  }

  .schedule-points {
    &.active {
      .schedule-item {
        color: $dark-grey;
        font-weight: 500;
        font-family: $gs-m;
        .point {
          background: $accent-l;
          border: 1.2px solid $accent;
          color: $black-1;
        }
      }
    }
    &.complete {
      .schedule-item {
        color: $dark-grey;
        font-weight: 500;
        font-family: $gs-m;
        .point {
          background-color: $accent;
          border: 1.2px solid $accent;
          color: $accent;
          text-indent: -999px;
          background-image: url(../images/all-icons.svg);
          background-repeat: no-repeat;
          background-position: -87px -29px;
        }
      }
    }
    .schedule-item {
      .point {
        min-width: 24px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f0f0;
        border-radius: 50px;
        color: $grey-6;
        margin-right: 8px;
        overflow: hidden;
      }
    }
  }
  .schedule-points-gap {
    max-width: 114px;
    width: 100%;
    margin: 0px 12px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
  }
}

.calendar,
.timeing-block {
  display: flex;
  width: 100%;
  gap: 24px;
  position: relative;
}
.timeing-block {
  max-height: 200px;
  .slick-list {
    overflow: hidden;
    overflow-y: auto;
  }
  .cstm_slick_arrow {
    position: absolute;
    top: -64px;
    left: auto;
    right: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    &.slick_next {
      right: 0px;
      rotate: 180deg;
      top: -67px;
    }
  }
}
.day {
  position: sticky;
  top: 0px;
  text-align: center;
  width: 100%;
  z-index: 99;
  background-color: $white;
}
.cal-row {
  display: flex !important;
  flex-direction: column;
  gap: 8px;
}
.time {
  // width: 80px;
  padding: 9px;
  margin-bottom: 8px;
  margin-right: 0px;
  text-align: center;
  background: rgba($accent, 0.7);
  border: 1px solid $accent;
  border-radius: 8px;
  font-weight: 500;
  font-family: $gs-m;
  color: $white;
  position: relative;

  &.disabled {
    background: #f0f0f0;
    color: #a2a2a2;
  }
  &.active {
    background: $accent;
    color: $white;
  }
  .time-link {
    @include str;
  }
}
.modal-prof-card {
  margin-bottom: 32px;
  @include phone {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 15px;
  }
  .profile-card {
    margin-top: 12px;
    @include phone {
      width: 100%;
    }
  }
  .rating-stars {
    justify-content: flex-start;
  }
}
.appointment-calender-wrpr {
  margin-top: 24px;
  .appointment-cal-block {
    margin-top: 20px;
  }
  .calendar {
    background: #ffffff;
    border: 1px solid #eef7f1;
    border-radius: 8px;
    padding: 16px 20px;
    overflow: auto;
    .slick-list {
      height: 100%;
      .slick-slide {
        height: 100%;
      }
    }
  }
}
.slick-track {
  margin-left: 0px !important;
}
.arrow-wrpr {
  .arrow-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    transform: rotate(90deg);
    &.next {
      transform: rotate(270deg);
    }
  }
}
.appt-date-price {
  gap: 80px;
  @include phone {
    width: 100%;
    gap: unset;
    justify-content: space-between;
  }
}
.name-flag-wrpr {
  display: flex;
  flex-flow: row wrap;
  align-content: center;
  gap: 5px;
}
.appt-form-wrpr {
  margin-top: 30px;
  .appt-textarea-wrpr {
    margin-bottom: 20px;
    .f-in {
      margin-top: 8px;
    }
  }
}

.addresses-wrpr {
  gap: 30px;
  margin: 32px 0px;
  .address-card {
    flex: auto;
    background: #fafafc;
    border: 1px solid #eef7f1;
    border-radius: 8px;
    padding: 16px 20px;
    .address-icon {
      gap: 16px;
    }
  }
}

/////////////////////     notification modal     //////////////////

.notification-modal {
  position: fixed;
  top: 0;
  left: auto;
  right: 0;
  width: 100vw;
  height: 100vh;
  justify-content: flex-end;
  .modal-content {
    display: block;
    width: 100%;
    max-width: 540px;
    background: white;
    z-index: 100;
    height: 100vh;
    max-height: 100vh;
    border-radius: 8px 0px 0px 8px;
    overflow: auto;
    box-shadow: -6px 0px 26px rgba(0, 0, 20, 0.37);
    transition: all 0.3s ease;
  }
  &.show {
    transition: all 0.3s ease;
  }
  .modal-header {
    .close-btn {
      right: 10px;
      top: 10px;
      background: transparent;
    }
  }
  .modal-body {
    max-height: calc(100% - 81px);
    overflow: auto;
    height: 100%;
    padding: 0px;
  }
}

.noti-list {
  .noti-item {
    padding: 24px 40px;
    border-bottom: 1px solid #f0f0f0;
    .noti-img-wrpr {
      margin-right: 16px;

      .noti-img {
        display: block;
        width: 48px;
        height: 48px;
        background: #ebfbd7;
        border-radius: 50%;
      }
    }
    .noti-text {
      margin-bottom: 10px;
    }
  }
}

.slick-slider {
  width: 100%;
}

///////////////////////        Calender page        /////////////////////////

.calender-page-wrpr {
  width: 100%;
  padding: 32px;
  @include phone {
    padding: 30px 20px;
  }
  .calender-title {
    gap: 12px;
    .icon {
      background-color: #eef6f1;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      background-position: -156px -116px;
    }
  }
  .calender-wrpr {
    margin-top: 40px;
    background: white;
    border-radius: 10px;
    padding: 16px;
    filter: drop-shadow(4px 4px 28px rgba(19, 61, 48, 0.06));
  }
}
.fc {
  margin-top: 24px;
  .fc-header-toolbar {
    .fc-toolbar-chunk {
      &:first-child {
        div {
          display: flex;
          align-items: center;

          .fc-toolbar-title {
            font-weight: 600;
            color: $grey-1;
            font-size: 20px;
            min-width: 200px;
            @include tabv {
              font-size: 16px;
              min-width: 140px;
            }
          }
          .fc-button {
            background-color: transparent;
            border: none;
            text-transform: capitalize;
            .fc-icon {
              color: $grey-6;
            }
            &:hover,
            &:focus {
              border: none;
              box-shadow: none;
              outline: none;
            }
            &.fc-today-button {
              background-color: $accent;
              border-color: $accent;
              font-weight: 600;
              margin-right: 20px;
              text-transform: capitalize;
              padding: 4px 9px;
              &:hover,
              &:focus {
                border: none;
                box-shadow: none;
                outline: none;
              }
            }
            &.fc-prev-button,
            &.fc-next-button {
              padding: 0px;
            }
          }
        }
      }
      &:last-child {
        .fc-button-group {
          background: #ffffff;
          box-shadow: 0px 4.05602px 28.3922px rgb(102 111 167 / 8%);
          border-radius: 8.11204px;
          padding: 4px;
          .fc-button {
            background: transparent;
            color: #7e7e7e;
            font-weight: 600;
            font-size: 12.1681px;
            line-height: 1.33;
            font-family: "inter", sans-serif;
            border: none;
            padding: 8px 14px;
            border-radius: 4px;
            text-transform: capitalize;
            &:hover,
            &:focus {
              border: none;
              box-shadow: none;
              outline: none;
            }
            &.fc-button-active {
              background: #094e26;
              color: $white;
            }
          }
        }
      }
    }
  }
  .fc-daygrid-day-frame {
    overflow: hidden;
  }
  .fc-timegrid-divider {
    padding: 0;
    display: none;
  }
  .fc-day-today {
    background-color: transparent !important;
  }
  .fc-event {
    background: transparent;
    border: none;
    &:hover,
    &:focus {
      background: transparent;
    }
  }
  .fc-timegrid-more-link {
    background: #409993;
    color: #ffffff;
  }
  .fc-popover {
    border-color: #409993 !important;
    border-radius: 4px;
    overflow: hidden;
    .fc-popover-header {
      background: #d4f0ee;
    }
  }
}
.meeting-card {
  background: #d4efee;
  border-radius: 4px;
  padding: 6px 3px;
  width: 100%;
  overflow: hidden;
  .p-name {
    font-size: 10px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 3px;
  }
  .meeting-icon {
    display: block;
    width: 10px;
    height: 10px;
    background: #289893;
    border-radius: 10px;
  }
}
.meeting-info-modal {
  .modal-content {
    display: block;
    position: fixed;
    width: 100%;
    max-width: 540px;
    background: white;
    z-index: 100;
    top: 0;
    left: auto;
    right: 0;
    height: 100vh;
    max-height: 100vh;
    padding: 30px 40px 50px;
    box-shadow: -6px 0px 26px hsl(0deg 0% 20% / 37%);
  }
  .modal-header {
    border-bottom: 0px !important;
    padding: 0px 0px 20px;
    border-bottom: 1px solid #daece1;
  }
  .modal-body {
    height: calc(100vh - 176px);
    overflow: auto;
    padding: 0;
  }
  .cal-dsc {
    padding: 24px 0px 0px;
  }
  .modal-btn-wrpr {
    // gap: 52px;
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0;
    padding: 15px 40px;
    border-top: 1px solid #f0f0f0;
    justify-content: space-between;
    .btn-secondary {
      color: #811f18;
    }
  }
  .schedule-appointment-wrpr {
    margin-top: 32px;
  }
}
.meeting-status {
  background: #ffeed3;
  border: 1px solid #fcd79f;
  border-radius: 4px;
  padding: 8px 10px;
  font-family: "General Sans";
  display: inline-block;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.14;
  color: #ea8c00;
}
.view_video {
  border-radius: 12px;
  overflow: hidden;
}

.meeting_success_modal {
  .modal-content {
    max-height: 65vh;
    height: auto;
  }
  .modal-header {
    border: none;
    .close-btn {
      top: 10px;
      right: 14px;
    }
  }
  .modal-body {
    max-height: unset;
    height: 90%;
    padding-bottom: 60px;
  }
  .successs-gif-wrpr {
    width: 250px;
    height: 250px;
    text-align: center;
    margin: 0 auto 20px;
  }
}

.calender_meeting_schedule {
  gap: 11px;
}
.schedule_close_btn {
  background: #f0f0f0;
  border-radius: 50px;
  position: absolute;
  top: 30px;
  right: 30px;
  bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
.self-practice-wrpr {
  padding: 40px 32px;
  overflow: auto;
  min-height: calc(100vh - 210px);
  @include phone {
    padding: 30px 20px;
  }
  .seminar-wrpr {
    gap: 34px;
    @include tab {
      gap: 26px;
    }
  }
}

.seminar-card-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.img-name-card.seminar-card {
  // flex: 1;
  .h6 {
    min-height: unset;
  }
  .img-card {
    min-height: 214px;
    .cover-img {
      object-fit: contain;
    }
  }
  .seminar-name {
    margin-bottom: 4px;
  }
  .seminar-card-bottom {
    margin-top: 14px;
  }
  .seminar-card-btns {
    gap: 12px;
    flex: 0 0 60%;
    .btn {
      padding: 8px 9px;
      white-space: pre;
    }
  }
  .in-progress {
    margin-top: 14px;
  }
}
.study-row {
  margin-top: 56px;
  .study-wrpr {
    gap: 24px;
    .seminar-card-btns {
      flex: 0 0 80%;
    }
  }
}
.coming-soon-text {
  font-weight: 500;
  line-height: 1.2;
  color: #1387c8;
  text-align: center;
  width: 100%;
}
.loaderView {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  &.sm {
    transform: scale(0.7);
  }
}

.self-practice-lessons {
  .tab-left-aside {
    flex: 0 0 28%;
    background: #fafafc;
    padding: 12px 16px;
    overflow: auto;
    @include tabv {
      position: absolute;
      width: 100%;
      top: 0;
      left: -100%;
      z-index: 1;
      &.active {
        left: 0px;
      }
    }
  }
  .tab-right-aside {
    flex: 1 0 72%;
    padding-left: 19px;
    overflow: auto;
    @include tabv {
      padding: 15px;
    }
  }
}
.product-list-wrap {
  display: flex;
  width: 100%;
  background: #ffffff;
  border: 0.2px solid #fafafd;
  box-shadow: 0px 1px 12px rgba(11, 24, 93, 0.08);
  border-radius: 8px;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin-bottom: 8px;
  .wrap-heading {
    display: flex;
    justify-content: space-between;
    width: 100%;
    cursor: pointer;
    padding: 14px 16px;
    background-color: #fff;
    position: relative;
    z-index: 999;
    &.active {
      .arrow-icon {
        transform: rotate(180deg);
        transition: rotate 0.5s;
      }
      ~ .accordion-content {
        margin-bottom: 40px;
      }
    }
    .lesson-name {
      padding-right: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .lesson-date {
      font-family: "inter", sans-serif;
      background: #ffffff;
      border: 0.5px solid #e0e0e0;
      border-radius: 4px;
      padding: 4px 12px;
      margin-right: 5px;
    }
    .action-wrap {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .counts {
      font-family: "inter-semibold", sans-serif;
      font-style: normal;
      font-weight: 600;
      font-size: 16px;
      line-height: 18px;
      text-align: right;
      letter-spacing: -0.02em;
      color: #14182f;
    }
    .arrow-icon {
      transform: rotate(0deg);
      transition: rotate 0.5s;
    }
    .arrow-action {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
    }
  }
}
.accordion-content {
  display: none;
  color: #7f8fa4;
  font-size: 13px;
  line-height: 1.5;
  margin-top: 8px;
  .wrap-content-accordion {
    padding: 5px 15px 15px 15px;
  }
}
.sub-lessson-list {
  .sub-lessson {
    margin-bottom: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    transition: all 0.2s ease;
    &.active {
      background: rgba(73, 149, 87, 0.2);
      transition: all 0.2s ease;
    }
    .icon {
      &.active {
        background-color: $accent;
        border-radius: 50%;
      }
    }
  }
}
.cd-card-wrpr {
  margin-top: 30px;
  flex-wrap: wrap;
  gap: 12px;
  .lesson-cd-card {
    gap: 12px;
    // flex: 0 0 24%;
    // max-width: 220px;
    .cd-img {
      width: 48px;
      height: 56px;
      border-radius: 6px;
      filter: drop-shadow(0px 4px 28px rgba(102, 111, 167, 0.08));
      position: relative;
      overflow: hidden;
    }
  }
}
.lesson-auther-details {
  margin-top: 48px;
  padding-right: 40px;
  .auther-card {
    .auther-img {
      width: 48px;
      height: 48px;
      border-radius: 50px;
      overflow: hidden;
      margin-right: 12px;
    }
    .auther-name {
      padding-right: 12px;
      margin-right: 12px;
      border-right: 1px solid #d8d8d8;
    }
  }
}

.doc-icon {
  width: 32px;
}
.selfpractice-tab-wrpr {
  margin-top: 10px;
  .tabs {
    justify-content: flex-start;
    .tab-link {
      display: flex;
      align-items: center;
      gap: 9px;
      &:hover,
      &.active {
        .icon.overview-icon {
          background-position: -28px -169px;
        }
        .icon.download-icon {
          background-position: -88px -169px;
        }
        .icon.comment-icon {
          background-position: -147px -171px;
        }
      }
    }
  }
  .tab-contents {
    display: none;
    &.active {
      display: block;
    }
    .overview-title {
      margin-bottom: 8px;
    }
  }
}
.tab-overview,
.tab-downloads {
  padding: 36px 0px;
}

.download-card {
  gap: 45px;
  margin-bottom: 12px;
  .file-wrpr {
    gap: 14px;
  }
}

.comment-block-inner {
  flex: 1;
}
.rate-comment-sec {
  background: #ffffff;
  border: 0.5px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px 16px 24px;
  margin-top: 32px;
  position: relative;
  .edit-comment-wrpr {
    position: absolute;
    top: 21px;
    right: 30px;
  }
  .f-label {
    color: $grey-7;
  }
  .rating-section {
    margin-bottom: 34px;
  }
}
.rating-star-wrpr {
  gap: 5px;
  margin-bottom: 4px;
  display: flex;
  margin-right: 10px;
  .star-img {
    max-width: 16px;
  }
}
.add-comment-wrpr {
  gap: 20px;
  align-items: flex-start;
  .form-control {
    padding: 12px 10px;
  }
  .btn-accent {
    white-space: pre;
    padding: 10px 30px;
  }
}
.search-comment-wrpr {
  gap: 20px;
  .form-control {
    padding: 10px 10px;
  }
  .search-wrpr {
    position: relative;
    width: 100%;
    .icon {
      position: absolute;
      top: 10px;
      left: 10px;
      width: 20px;
      height: 20px;
    }
    .form-control {
      padding-left: 32px;
    }
  }
  .filter-select {
    background: #ffffff;
    box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
    border-radius: 4px;
    padding: 10px 27px;
    border: none;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: $grey-3;
    cursor: pointer;
    outline: none !important;
  }
}
.comment-card-wrpr {
  margin-top: 30px;
  .comment-card {
    width: 100%;
    margin-bottom: 30px;
    .comment-date {
      margin-left: 10px;
    }
  }
  .comment-user-img {
    width: 40px;
    min-width: 40px;
    height: 40px;
    min-height: 40px;
    border-radius: 40px;
    overflow: hidden;
    margin-right: 12px;
  }
}
.comment-wrpr {
  padding-right: 20px;
}
.rating-wrap {
  padding: 30px 24px 0px;
  max-width: 40%;
  .rating-pt {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .rating-star {
    background: $white;
    border: 0.8px solid #c6c6c6;
    border-radius: 8px;
    padding: 14px 22px;
    min-width: 144px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    .rt-star {
      width: 30px;
      height: 30px;
    }
  }
  .as-rating {
    font-weight: 600;
    font-size: 36px;
    line-height: 1.17;
    letter-spacing: -0.035em;
    color: #14182f;
  }
  .rating-list {
    padding-top: 55px;
    width: 100%;

    .ul-rating {
      width: 100%;
      .li-rating {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        &:last-child {
          margin-bottom: 0px;
        }
        .star-rating {
          display: flex;
          align-items: center;
          gap: 5px;
          min-width: 45px;
        }
        .star-count {
          font-style: normal;
          font-weight: 600;
          font-size: 14px;
          line-height: 16px;
          color: #14182f;
          min-width: 9px;
          text-align: center;
        }
        .star-img {
          width: 15px;
          position: relative;
          top: -1px;
        }
        .review-content {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 16px;
          color: #797f99;
        }
        .rating-bar {
          width: 60%;
          display: flex;
        }
        .rating-reviews {
          display: flex;
          min-width: 85px;
          justify-content: end;
        }
      }
    }
  }
}
.progress-bar {
  height: 7px;
  overflow: hidden;
  background-color: #d5d7e4;
  border-radius: 11.3055px;
  width: 100%;
  display: inline-block;
}
.progress-data-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
  border-radius: 11.3055px;
  &.bar-green {
    background: #11996c;
  }
  &.bar-red {
    background: #d60537;
  }
  &.bar-yellow {
    background: #fcc607;
  }
}
.line-side {
  color: #d5d7e4;
  font-style: inherit;
}

.sales-page-wrpr {
  padding: 0px 32px;
  @include phone {
    padding: 0px 15px;
  }
  .btn-accent {
    padding: 18px 54px;
  }
  .sales-page-ttl {
    font-family: "General Sans";
    font-style: normal;
    font-weight: 600;
    font-size: 60px;
    line-height: 1.07;
    letter-spacing: -1.72px;
    margin-bottom: 32px;
    @include phone {
      font-size: 32px;
      margin-bottom: 10px;
      letter-spacing: 0px;
    }
  }
  .banner-wrpr {
    padding: 50px;
    @include phone {
      padding: 15px 0px;
    }
    .banner-video {
      margin: 40px 0px;
      border-radius: 10px;
      overflow: hidden;
      height: 500px;
      @include phone {
        height: auto;
      }
    }
  }
  .banner-text-wrpr {
    .banner-subhead {
      margin-bottom: 14px;
      font-size: 18px;
    }
    .banner-btn {
      margin-top: 32px;
      margin-bottom: 20px;
      @include phone {
        margin-top: 20px;
      }
    }
    .h3 {
      font-size: 30px;
      line-height: 1.27;
      letter-spacing: -1.32px;
      @include phone {
        font-size: 24px;
        letter-spacing: 0px;
      }
    }
  }
  .seminar-avail-card {
    padding: 50px;
    @include phone {
      padding: 15px 0px;
    }
    .img-wrpr {
      height: 100%;
      max-height: 500px;
      overflow: hidden;
      border-radius: 22px;
      margin-bottom: 64px;
      @include phone {
        margin-bottom: 34px;
      }
    }
    .seminar-card-content {
      max-width: 810px;
      margin: 0 auto;
      .p {
        font-size: 18px;
        margin-bottom: 56px;
      }
    }
  }
  .online-seminar-card {
    border-radius: 10px;
    overflow: hidden;
    gap: 68px;
    padding: 50px;
    @include phone {
      padding: 15px 0px;
      gap: 24px;
      flex-direction: column;
    }
    .img-wrpr {
      border-radius: 22px;
      max-height: 500px;
      height: 100%;
      flex: 1 1 50%;
      overflow: hidden;
    }
    .online-seminar-ttl {
      margin-bottom: 32px;
    }
    .online-seminar-item {
      gap: 12px;
      margin-bottom: 16px;
      font-weight: 400;
      font-size: 16px;
      line-height: 1.5;

      .chevron-icon {
        min-width: 20px;
        width: 20px;
        margin-top: 2px;
        filter: drop-shadow(0px 1px 4px rgba(4, 96, 41, 0.26));
      }
    }
  }
  .watch-buy-seminar-wrpr {
    background: linear-gradient(
        138.43deg,
        rgba(242, 239, 227, 0.7) 0%,
        rgba(198, 224, 215, 0.7) 100.36%
      ),
      #ffffff;
    box-shadow: 0px 4px 28px rgb(102 111 167 / 8%);
    border-radius: 22px;
    padding: 48px;
    gap: 28px;
    margin: 50px;
    @include phone {
      margin: 15px 0px;
      flex-direction: column;
      padding: 14px;
    }
    .watch-buy-inner {
      background: rgba(255, 255, 255, 0.7);
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
      backdrop-filter: blur(6px);
      border-radius: 22px;
      padding: 52px;
      gap: 32px;
      @include phone {
        padding: 24px;
        text-align: center;
      }
      .watch-buy-img-wrpr {
        background: #d8edd9;
        border-radius: 22px;
        width: 104px;
        height: 104px;
        padding: 28px;
      }
    }
  }
  .logos-section {
    padding: 50px;
    @include phone {
      padding: 15px;
      margin-top: 25px;
    }
    .p {
      font-size: 18px;
      line-height: 1.4;
    }
    .kuby-logos-wrpr {
      gap: 24px;
      margin-top: 64px;
      @include phone {
        margin-top: 24px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
      }
      .kuby-logos {
        background: $white;
        padding: 12px;
        box-shadow: 0px 4px 28px rgba(144, 173, 150, 0.06);
        border-radius: 12px;
      }
    }
  }
  .testimonial-section {
    padding: 50px;
    @include phone {
      padding: 15px 0px;
    }
    .testimonial-slider {
      margin-top: 64px;
    }
    .p {
      font-size: 18px;
      line-height: 1.4;
    }
  }
  .testimonial-slide {
    background: #ffffff;
    box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
    border-radius: 12px;
    padding: 20px;
    .testimonial-auther-name {
      text-align: left;
      .auther-name {
        font-weight: 600;
        font-size: 18px;
        line-height: 1.33;
        margin-bottom: 4px;
      }
      .auther-loc {
        font-weight: 500;
        font-size: 14px;
        line-height: 1.43;
      }
    }
    .testi-star-wrpr {
      gap: 2px;
      .img-fluid {
        width: 14px;
      }
    }
    .testimonial-bottom {
      text-align: left;
      margin-top: 25px;
      .quote-icon-wrpr {
        width: 16px;
        display: inline-block;
      }
      .testimonial-text {
        font-size: 14px;
        line-height: 1.71;
        letter-spacing: 0.16px;
        margin-top: 14px;
      }
    }
  }
  .steps-section {
    padding: 50px;
    @include phone {
      padding: 15px 0px;
    }
    .step-sec-content {
      width: 100%;
      max-width: 600px;
      margin: 0 auto 64px;
      .p {
        font-size: 18px;
        line-height: 1.44;
      }
    }
    .steps-block {
      gap: 22px;
      margin-bottom: 80px;
      @include phone {
        flex-direction: column;
      }
      .step {
        background: $accent-l;
        border-radius: 12px;
        position: relative;
        padding: 38px 30px;
        text-align: left;
        max-width: 33.33%;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5;
        letter-spacing: -0.32px;
        color: #536459;
        @include phone {
          max-width: 100%;
        }
        &:after {
          content: "";
          position: absolute;
          width: 4px;
          height: 30px;
          left: 0;
          top: 36%;
          background: $accent;
          border-radius: 0px 14px 14px 0px;
        }
      }
    }
  }
  .faq-section {
    margin: 50px 0px;
    padding: 48px 55px;
    background: rgb(255, 255, 255);
    box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
    border-radius: 8px;
    @include phone {
      padding: 15px;
    }
    .faq-block {
      margin-top: 80px;
      margin-bottom: 64px;
      .product-list-wrap {
        background-color: transparent;
        box-shadow: none;
        border: none;
        border-bottom: 1px solid #e0ede5;
        border-radius: 0px;
        &.active {
          .faq-icon {
            background-position: -182px -174px;
          }
        }
        .wrap-heading {
          padding: 36px 16px;
          @include phone {
            padding: 15px;
            text-align: left;
          }
        }
      }
      .faq-icon {
        background-position: -182px -174px;
      }
    }
  }
}
.progress-data-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  transition: width 0.6s ease;
  border-radius: 11.3055px;

  &.bar-5 {
    background: #11996c;
  }

  &.bar-4 {
    background: #46ac8a;
  }

  &.bar-2 {
    background: #d62b05;
  }

  &.bar-1 {
    background: #d60537;
  }

  &.bar-3 {
    background: #fcc607;
  }
}

///*****              soulwriting style start          ***********//
.soulwriting-wrpr {
  margin-left: 24px;
  margin-right: 24px;
  padding: 20px;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  gap: 32px;
  @include phone {
    gap: 0px !important;
  }
  .soul-vedio {
    flex: 0 0 35%;
    max-width: 390px;
    // box-shadow: inset 0px 7px 72px rgba(1, 3, 17, 0.08);
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    min-height: 220px;
    justify-content: center;
    max-height: 220px;
    @include phone {
      min-height: 160px !important;
      max-height: 160px !important;
    }
    > div {
      width: 100%;
      height: 100%;
    }
    iframe {
      @include phone-m {
        height: 100%;
      }
    }
  }
  .soul-intro {
    margin-bottom: 10px;
    @include phone {
      margin-top: 10px !important;
    }
  }
  .status-info {
    font-weight: 500;
    font-size: 12px;
    line-height: 1.33;
    color: #5374ca;
    .status-info-icon {
      margin-left: 5px;
    }
  }
}

.soul-table-wrpr {
  margin: 0px 24px 50px;
  padding: 0px 10px;
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  overflow: auto;
  max-height: 500px;
  overflow: auto;
  .soul-table {
    width: 100%;
    overflow: auto;
  }
}
.soul-tcol {
  background: #fff;
  padding: 16px 9px;
  text-align: left;
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  letter-spacing: -0.25px;
  color: #262626;
  &.th {
    font-family: $gs-m;
    font-weight: 500;
    white-space: pre;
    color: $grey-7;
  }
  &:first-child {
    min-width: 150px;
  }
  &:nth-child(2) {
    min-width: 200px;
    @include phone {
      min-width: 140px;
    }
  }
  &:last-child {
    white-space: pre;
    word-break: keep-all;
    min-width: 130px;
    .link {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 20px;
    }
  }
  .link {
    display: block;
    width: 100%;
    text-align: right;
    text-decoration: underline;
  }
}
.soul-trow {
  position: relative;
}
.soul-status {
  background: #eaeef9;
  border: 1px solid #c5d2f5;
  border-radius: 4px;
  padding: 6px 8px;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.2;
  letter-spacing: -0.35px;
  color: #3a69ea;
  font-family: "inter-medium", sans-serif;
  display: flex;
  align-items: center;
  &.submitted {
    border-color: #b2d9b9;
    color: #09771d;
    background: #ecfaef;
  }
  &.recieved {
    border-color: #fae0c2;
    color: #ed8e00;
    background: #fcf9ee;
  }
}

//////////////////     create soulwriting         ////////////////////

.soulwriting-main-wrpr {
  padding: 10px 0px 10px 32px;
  gap: 24px;
  .soulwriting-steps-wrpr-outer {
    position: relative;
    display: flex;
    .toggle-sidebar {
      z-index: 1;
      right: -23px;
      &.closed {
        right: 0px;
      }
    }
  }
  .soulwriting-steps-wrpr {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 128px);
    overflow: auto;
    flex: 0 0 200px;
    max-width: 200px;
    gap: 16px;
    border-right: 1px solid #e7e8f2;
    padding-right: 15px;
    margin-right: 15px;
    transition: all 0.3s ease;
    &.closed {
      flex: 0 0 67px;
      max-width: 67px;
      transition: all 0.3s ease;
      .step-text {
        display: none;
      }
    }
    .step-btn {
      background: #f0f0f0;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      padding: 12px 8px;
      gap: 4px;
      display: flex;
      align-items: center;
      position: relative;
      cursor: pointer;
      font-size: 16px;
      line-height: 1.25;
      letter-spacing: 0.12px;
      color: $dark-grey;
      font-weight: 400;
      outline: none;
      box-shadow: none;
      &:after {
        content: "";
        position: absolute;
        top: 100%;
        left: 9px;
        height: 16px;
        width: 10px;
        background-image: url(../images/dashed-line.svg);
        background-repeat: no-repeat;
        background-position: bottom;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      .soul-step {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50px;
        background-color: rgba(#d8d8d8, 0.32);
        background-image: url(../images/soulwriting-icons.svg);
        background-position: 0px 0px;
        background-repeat: no-repeat;
        &.step1 {
          background-position: 3px 4px;
        }
        &.step2 {
          background-position: -34px 4px;
        }
        &.step3 {
          background-position: -72px 4px;
        }
        &.step4 {
          background-position: -110px 4px;
        }
        &.step5 {
          background-position: -148px 4px;
        }
        &.step6 {
          background-position: -186px 4px;
        }
        &.step7 {
          background-position: -224px 4px;
        }
        &.step8 {
          background-position: -262px 4px;
        }
        &.step9 {
          background-position: -300px 4px;
        }
        &.step10 {
          background-position: -338px 4px;
        }
        &.step11 {
          background-position: -377px 4px;
        }
        &.step12 {
          background-position: -413px 4px;
        }
      }
      &.active {
        background-color: $white;
        &.btn1 {
          border-color: #dcd7fc;
        }
        &.btn2 {
          border-color: #abbded;
        }
        &.btn3 {
          border-color: #a8e5ff;
        }
        &.btn4 {
          border-color: #8fcec8;
        }
        &.btn5 {
          border-color: #7aba94;
        }
        &.btn6 {
          border-color: #b5d289;
        }
        &.btn7 {
          border-color: #d1d445;
        }
        &.btn8 {
          border-color: #fad824;
        }
        &.btn9 {
          border-color: #febf60;
        }
        &.btn10 {
          border-color: #fe9c56;
        }
        &.btn11 {
          border-color: #ffa3a3;
        }
        &.btn12 {
          border-color: #dc665d;
        }
        .soul-step {
          background-position-y: -32px;
        }
      }
    }
  }
  .soulwriting-forms-wrpr {
    position: relative;
    flex: 1 1 calc(100% - 224px);
    overflow: auto;
    max-height: calc(100vh - 128px);
  }
}
.soul-form-header {
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  background: #fff;
  padding: 10px;
  z-index: 99;
  box-shadow: 0px 2px 5px #eee;
  border-radius: 0px 0px 4px 4px;
  .header-filter {
    padding: 4px 12px;
    position: relative;
    display: flex;
    gap: 4px;
    background: transparent;
    margin-left: 24px;
    &:after {
      content: "";
      position: absolute;
      top: 4px;
      right: -12px;
      width: 1px;
      height: 70%;
      background: $grey-7;
    }
    &:last-child {
      &:after {
        display: none;
      }
    }
    .filter-select {
      border: none;
      outline: none;
      box-shadow: none;
      background: transparent;
    }
    .filter-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      background: transparent;
      outline: none;
      box-shadow: none;
      border: none;
      font-size: 14px;
      line-height: 1.43;
      color: $grey-6;
    }
    .filter-icon {
      width: 18px;
    }
  }
}

/*****  soulwriting  responsive *****/
.soulwriting-main-wrpr {
  @include phone {
    padding: 10px;
  }
  .soulwriting-forms-wrpr {
    @include phone {
      max-height: calc(100vh - 140px);
    }
  }
  .soulwriting-steps-wrpr-outer {
    @include phone {
      display: none;
    }
  }
  .soul-form-header {
    @include phone {
      // flex-direction: column;
      align-items: baseline;
      gap: 5px;
      // display: none;
    }
  }
  .soul-header-right {
    @include phone {
      // flex-direction: column;
      width: 100%;
      align-items: center;
      gap: 5px;
    }
    .origin-date {
      @include phone {
        width: 100%;
      }
    }
    .header-filter {
      @include phone {
        margin-left: 0px;
        padding-left: 0;
      }
      &::after {
        @include phone {
          display: none;
        }
      }
    }
    .filter-btn {
      @include phone {
        padding-left: 0px;
      }
    }
  }
  .soul-step-outer {
    @include phone {
      flex-direction: column;
      &::before {
        display: none;
      }
    }
    .step-left {
      @include tabv {
        width: 100%;
        flex: inherit !important;
      }
      @include phone {
        width: 100%;
      }
      .project-field-wrpr .form-in {
        @include phone {
          width: 100%;
        }
      }
    }
    .step-right {
      @include phone {
        width: 100%;
        flex: inherit !important;
      }
    }
    .protagonist-card {
      @include phonev {
        overflow: auto;
      }
      .protagonist-details {
        @include phone {
          gap: 10px;
        }
        p {
          @include phonev {
            overflow: hidden;
            white-space: nowrap;
            display: block;
            text-overflow: ellipsis;
            padding-right: 0px;
            max-width: 99%;
            width: 100%;
            min-width: 100px;
          }
        }
      }
      .status-badge {
        @include phonev {
          min-width: 80px;
          text-align: center;
        }
      }
    }
    .person-card {
      @include phone {
        width: 35px;
        height: 35px;
        font-size: 13px;
      }
    }
    .soul-step-inner {
      @include tabv {
        flex: inherit;
        max-width: 100%;
      }
      .bridge-subtitle {
        @include tabv {
          font-size: 14px;
        }
      }
      .date-in-wrpr {
        .form-in {
          @include tabv {
            width: 100%;
          }
        }
      }
      @include phone {
        min-width: 100%;
      }
      .scene-row {
        @include phone {
          // flex-direction: column;
          align-items: baseline;
          flex-wrap: wrap;
          position: relative;
        }
      }
      .scene-text-wrpr {
        @include phone {
          width: 100%;
          flex: auto;
          padding-left: 25px;
        }
      }
      .bridge-table-wrpr {
        @include phone {
          flex-direction: column;
        }
        .bridge-past-block {
          @include phone {
            max-width: 100%;
          }
          .bridge-list-item::before {
            @include phone {
              display: none;
            }
          }
        }
        .bridge-present-block {
          @include phone {
            max-width: 100%;
          }
        }
      }
      .bridge-block {
        .add-bridge-card {
          @include phone {
            justify-content: center;
          }
        }
      }
      .first-col {
        @include phone {
          position: absolute;
          bottom: 24px;
          left: -4px;
        }
      }
      .second-col {
        @include phone {
          position: absolute;
          left: -20px;
          margin-top: 7px;
        }
      }
    }
  }
}

.right-section_soulwriting {
  .soulwriting-footer {
    @include tabv {
      padding: 15px;
      justify-content: center;
    }
    @include phone {
      padding: 15px;
      flex-wrap: wrap;
      gap: 10px;
    }
    .footer-btn {
      @include phone {
        width: auto;
      }
      &.saveLeave {
        background: transparent;
        color: #404040;
        border: 1px solid #404040;
      }
    }
  }
}

.right-section_soulwriting {
  .soulwriting-wrpr {
    width: calc(100% - 48px);
    @include phone {
      flex-direction: column;
    }

    .soul-vedio {
      @include phone {
        flex: 0 0 100%;
        max-width: 100%;
      }
      // @include phonev {
      //   max-height: inherit;
      //   min-height: auto;
      // }

      // iframe {
      //   @include phonev {
      //     height: 170px;
      //   }
      // }
    }

    .soul-content-wrpr {
      div.text-right {
        @include phone {
          text-align: left;
          margin-top: 15px;
        }
      }
    }
  }
  .soulwriting-table.inner-header {
    overflow: auto;
  }
}

div.protagonist-list-modal {
  @include phone {
    top: 0px;
    height: 100%;
  }
  .overlay-div {
    @include phone {
      display: block;
    }
  }
  .icon.close-icon {
    @include phone {
      background-position: 0px -142px;
    }
  }

  .modal-content {
    @include phone {
      margin: 0px 20px;
      width: 100% !important;
    }
    .modal-header {
      @include phone {
        .q-mark-icon {
          display: none;
        }
      }
    }
    .bridge-table-wrpr {
      @include phone {
        flex-direction: column;
      }
      .bridge-block {
        @include phone {
          width: 100%;
          flex: inherit;
          max-width: 100%;
        }
        .add-bridge-card {
          @include phone {
            justify-content: center;
          }
        }
        .bridge-list-item::before {
          @include phone {
            display: none;
          }
        }
      }
    }
  }
}

/*****  soulwriting  responsive *****/

.char-count {
  font-size: 12px;
  line-height: 1.67;
}
.steps-wrpr {
  box-shadow: 4px 4px 28px rgb(19 61 48 / 6%);
  border-radius: 8px;
  padding: 24px;
  // max-height: calc(100vh - 180px);
  // overflow: auto;

  .step-title {
    gap: 10px;
    margin-bottom: 12px;
  }
  .step-subtitle {
    font-size: 12px;
    min-height: 24px;
    .icon {
      transform: scale(0.8);
    }
  }
  .soul-step-outer {
    position: relative;
    gap: 36px;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 10px 0px;
    &:before {
      content: "";
      position: absolute;
      height: 100%;
      width: 3px;
      top: 0px;
      left: -24px;
      border-radius: 0px 6px 4px 0px;
    }
    &.soul-step1 {
      &::before {
        background: #af93ff;
      }
    }
    &.soul-step2 {
      padding-bottom: 24px;
      &::before {
        background: #7a9efa;
      }
    }
    &.soul-step3 {
      &::before {
        background: #8adcff;
      }
    }
    &.soul-step4 {
      &::before {
        background: #48a2ae;
      }
    }
    &.soul-step5 {
      &::before {
        background: #76a8af;
      }
    }
    &.soul-step6 {
      &::before {
        background: #2fa75f;
      }
    }
    &.soul-step7 {
      &::before {
        background: #a9cf6f;
      }
    }
    &.soul-step8 {
      &::before {
        background: #f9dd2f;
      }
    }
    &.soul-step9 {
      &::before {
        background: #ffc46b;
      }
    }
    &.soul-step10 {
      &::before {
        background: #fca05e;
      }
    }
    &.soul-step11 {
      &::before {
        background: #ffa3a3;
      }
    }
    &.soul-step12 {
      &::before {
        background: #dc665d;
      }
    }
    .step-left {
      flex: 0 0 calc(100% - 286px);
      .textarea {
        resize: none;
        height: 186px;
        overflow: auto;
      }
    }
    .step-right {
      flex: 0 0 250px;
      position: relative;
      > .p {
        margin-bottom: 12px;
      }
      .comment-section-header {
        margin-top: 44px;
        .icon {
          margin-right: 8px;
        }
        .cmnt-btn {
          padding: 4px 18px;
          &.disable {
            pointer-events: none;
            color: $grey-5;
            background-color: #c6c6c6;
            border-color: #c6c6c6;
          }
        }
      }
    }
  }
}
.soul-step-inner {
  width: 100%;
  .textarea {
    height: 110px;
    font-family: "inter", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
    resize: none;
  }
  .person-cards {
    margin-left: 24px;
    display: flex;
    align-items: center;
  }
}
.action-btns {
  gap: 36px;
  margin-top: 10px;
  .delete-btn {
    background: transparent;
    border: none;
    outline: none;
    box-shadow: none;
  }
  .add-btn {
    padding: 6px 16px;
    font-size: 12px;
  }
}
.add-btn {
  padding: 8px 16px 8px 12px;
  background: #f0f0f0;
  border: 1px solid #eeeef6;
  border-radius: 8px;
  color: $dark-grey;
}
.scene-row {
  gap: 12px;
  margin-bottom: 20px;
  &.title {
    margin-top: 30px;
  }
  .scene-text-wrpr {
    flex: 1;
  }
  .first-col {
    flex: 0 0 30px;
    text-align: center;
  }

  .comment-card-block {
    flex: 0 0 250px;
  }
  .comment-card {
    width: 250px;
  }
}
.swap-comp {
  background-color: transparent;
  border: none;
  text-decoration: underline;
  color: $accent;
  cursor: pointer;
  font-size: 14px;
  gap: 9px;
  margin-top: 22px;
}
.soulwriting-footer {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 4px 28px rgb(7 49 15 / 6%);
  padding: 10px;
  z-index: 99;
  gap: 24px;
  // padding-left: 250px;
  justify-content: space-between;
  .footer-btn {
    padding: 10px 23px;
    border-radius: 8px;
    gap: 10px;
    font-family: $gs-m;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    &.leave-btn {
      background: #ffffff;
      border: 1px solid #f2ebde;
      color: #ac4139;
    }
    &.draft-btn {
      background: #ffffff;
      border: 1px solid #f5c08e;
      color: #f0790b;
      position: relative;
      align-items: center;
      justify-content: center;
      &.btn-loader {
        &::after {
          border: 3px solid rgba($accent, 0.5);
          border-right-color: $accent;
        }
      }
    }
  }
  .ftr-btn {
    color: #000 !important;
    background: #fff;
  }
  .soulwriting-footer-left {
    display: flex;
    gap: 15px;
  }
  .soulwriting-footer-right {
    display: flex;
    gap: 15px;
  }
}
.person-card,
.person-card-count,
.person-card-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 44px;
  background: #cbecea;
  border: 1px solid #a6d9d6;
  box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 40px;
  margin-left: -23px;
  color: $grey-3;

  &:first-child {
    margin-left: 0px;
  }
}
.person-card-count {
  background: rgba(33, 87, 79, 1);
  border: 1px solid rgba(109, 156, 149, 0.3);
  box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.16);
  // backdrop-filter: blur(2px);
}
.person-card-empty {
  background: #eef7f1;
  border: 1px solid #f0f0f0;
  color: #c6c6c6;
  box-shadow: none;
}
.person-wrpr {
  margin: 6px 0px;
}

.date-in-wrpr {
  gap: 16px;
  margin-bottom: 16px;
  .form-control {
    padding: 10px;
  }
}

.protagonist-list {
  margin-top: 24px;
  .protagonist-list-item {
    margin-bottom: 16px;
  }
  .protagonist-card {
    background: #ffffff;
    border: 1px solid #f4ece2;
    border-radius: 6px;
    padding: 12px 16px;
    margin-left: 16px;
    .protagonist-details {
      gap: 20px;
      .p {
        margin-bottom: 4px;
      }
    }
    .life-status-wrpr {
      gap: 8px;
      .life-status {
        background: #ecf7f1;
        border: 1px solid #dcf0e5;
        border-radius: 4px;
        padding: 10px 18px;
        color: #094e26;

        &.passaway {
          color: #811f18;
          background: #fff4f3;
          border: 1px solid #ffeeed;
        }
      }
    }
  }
  .date-passed {
    gap: 13px;
  }
}
.upper-case {
  text-transform: uppercase;
}
.protagonist-modal {
  &.new {
    .modal-content {
      height: auto;
      @include phone {
        width: 100%;
      }
    }
    .modal-dialog {
      max-width: 620px;
    }
    .modal-footer {
      padding-bottom: 0px;
    }
  }
  @include phone {
    padding: 0px 20px;
  }
  .modal-header {
    @include phone {
      .h4 {
        text-align: left;
        font-size: 23px;
      }
    }
  }
  .modal-dialog {
    min-width: 615px;
    @include phone {
      min-width: 100%;
    }
  }
  .modal-content {
    @include phone {
      width: 100%;
    }
    .modal-body {
      @include phone {
        padding-left: 15px;
        padding-right: 15px;
      }
    }
    .protagonist-form-wrpr {
      .f-row {
        .w-50 {
          @include phone {
            width: 100%;
          }
        }
      }
    }
  }
  .protagonist-form-wrpr {
    height: 100%;
    .form-inner {
      height: calc(100% - 65px);
      overflow: auto;
      padding-left: 40px;
      padding-right: 40px;
      @include phone {
        padding-left: 0px;
        padding-right: 0px;
      }
      &.protogonist-form-inner {
        padding: 40px;
        border-radius: 24px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        background: #f8f9fb;
        margin-left: 40px;
        margin-right: 40px;
        display: flex;
        flex-direction: column;
        max-height: 50vh;
        overflow: auto;
        min-height: 230px;
        @include phone {
          min-width: 300px;
          min-height: 260px;
          margin-left: 0px;
          margin-right: 0px;
        }

        .protogonist-opt-list {
          @include phone {
            flex-direction: column;
          }
          .protogonist-opt-item {
            @include phone {
              width: 100%;
            }
          }
        }
      }
    }
    .modal-footer {
      padding-left: 40px;
      padding-right: 40px;
      padding-top: 24px;
      &.bt-0 {
        border-top: 0px;
      }
    }
  }
  .modal-body {
    height: calc(100% - 81px);
    padding-left: 0px;
    padding-right: 0px;
  }

  .modal-body-inner {
    height: calc(100% - 66px);
    overflow: auto;
  }
  .form-in {
    margin-bottom: 24px;
    .f-label {
      width: unset;
    }
  }
}
.script-modal {
  .modal-body {
    height: 100%;
  }
}
.companion-modal {
  .modal-dialog {
    @include tab {
      max-width: 90vw;
    }
  }
  .modal-body {
    height: calc(100% - 100px);
    padding: 0px;
    min-width: 950px;
    @include tabv {
      min-width: unset;
      width: 100%;
    }
    .choose-comp {
      height: calc(100% - 65px);
      overflow: auto;
      padding: 24px 40px;
    }
  }
  .modal-footer {
    padding-bottom: 0px;
  }
  .companion-card {
    max-width: 32%;
    border: 2px solid transparent;
    cursor: pointer;
    &.active {
      background: $white;
      border: 2px solid $accent;
    }
  }
}
.redline-wrpr {
  .icon {
    cursor: grab;
  }
}
.red-line {
  width: 100%;
  border: 2px solid #ff0000;
  border-radius: 2px;
  margin-left: 14px;
  &.view {
    margin-left: 0px;
    margin-bottom: 24px;
    margin-top: -26px;
  }
}
.script-inner-wrpr {
  margin-bottom: 48px;
  .h5 {
    margin-bottom: 12px;
  }
  .q-mark-icon {
    margin-left: 10px;
  }
  .view-table {
    .tr {
      margin-bottom: 10px;
      .td {
        padding: 4px 8px 14px;
        vertical-align: middle;
      }
    }
    .view-person-card {
      background: #e0e0e0;
      box-shadow: -2px 2px 8px rgb(0 0 0 / 6%);
      border-radius: 40px;
      width: 46px;
      height: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .step-subtitle {
    font-size: 12px;
    .icon {
      transform: scale(0.7);
    }
  }
  .view-date-wrpr {
    padding: 10px 0px;
    margin-top: 6px;
    margin-bottom: 24px;
    .view-date {
      color: #094e26;
      padding-left: 10px;
      border-left: 1px solid #f0f0f0;
      margin-left: 10px;
    }
  }
}
.comment-card {
  .comment-card-inner {
    background: #eeeef7;
    border-radius: 8px;
    padding: 2px;
    margin-bottom: 5px;
    .comment-name {
      padding: 2px 18px 4px;
    }
    &.companion_message {
      background-color: #ecf9ee;
    }
    &.member_message {
      background-color: #e6f6fa;
    }
    .comment-text {
      background: #ffffff;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      padding: 8px 16px;
    }
  }
  .cmnt-btn-wrpr {
    margin-bottom: 10px;
    .reply-btn {
      background: transparent;
      border: none;
      outline: none;
      font-size: 12px;
      line-height: 10px;
      color: $accent;
    }
    .send-comment {
      border: none;
      background: transparent;
      padding: 0px;
      outline: none;
      box-shadow: none;
    }
  }
  .comment-input {
    width: 100%;
    min-height: 40px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.43;
    color: #262626;
    outline: none;
    box-shadow: none;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 6px;
  .active {
    a {
      background: #eef7f1;
      border: 0.2px solid #499557;
    }
  }
  a {
    width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: #ffffff;
    color: $grey-6;
    border: 0.2px solid #ffffff;
    box-shadow: 0px 1px 12px rgba(2, 17, 8, 0.06);
  }
}

.scene-text-wrpr {
  strong {
    font-weight: bold;
  }
  i {
    font-style: italic;
  }
  ul,
  ol {
    padding-left: 20px;
  }
}

.ck-editor__main {
  strong {
    font-weight: bold;
  }
  i {
    font-style: italic;
  }
  ul,
  ol {
    padding-left: 20px;
  }
}
.bridge_wrp {
  position: absolute;
  top: 100px;
  right: 0;
  left: auto;
  width: 270px;
  height: 250px;
  padding: 10px;
  overflow: auto;
  background: #fff;
  box-shadow: 0px 0px 10px 0px #eee;
  border-radius: 7px;
  z-index: 999;
}
.bridge_wrp.companion-selected {
  top: 190px;
}
.remove_bridge {
  position: absolute;
  top: 11px;
  right: 3px;
  background: transparent;
  border: none;
}
/* ===== Scrollbar CSS ===== */
/* Firefox */
.bridge_wrp,
.soulwriting-forms-wrpr,
.soul-table-wrpr,
.project_tool_list,
.tab-list-wrpr {
  scrollbar-width: auto;
  // scrollbar-color: $accent #ffffff;
  scrollbar-color: #fff #ffffff;
}

/* Chrome, Edge, and Safari */
.bridge_wrp::-webkit-scrollbar,
.soulwriting-forms-wrpr::-webkit-scrollbar,
.soul-table-wrpr::-webkit-scrollbar,
.project_tool_list::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}

.bridge_wrp::-webkit-scrollbar-track,
.soulwriting-forms-wrpr::-webkit-scrollbar-track,
.soul-table-wrpr::-webkit-scrollbar-track,
.project_tool_list::-webkit-scrollbar-track {
  background: #ffffff;
}

.bridge_wrp::-webkit-scrollbar-thumb,
.soulwriting-forms-wrpr::-webkit-scrollbar-thumb,
.soul-table-wrpr::-webkit-scrollbar-thumb,
.project_tool_list::-webkit-scrollbar-thumb {
  background-color: $accent;
  border: 3px solid #ffffff;
}

.tab-list-wrpr::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.tab-list-wrpr::-webkit-scrollbar-track {
  background: #ffffff;
}
.tab-list-wrpr::-webkit-scrollbar-thumb {
  background-color: $accent;
  border: none;
}

.choose_character {
  .modal-content {
    max-height: unset;
    height: auto;
    @include phone {
      width: 100%;
      padding: 0px 20px;
    }
  }
  .modal-dialog {
    width: 600px;
    @include phone {
      width: 100%;
    }
    .modal-body {
      height: auto;
      overflow: auto;
      padding: 0px;
      .order-list {
        padding-top: 0px;
        border: none;
        max-height: 70vh;
        overflow: auto;
        .character_item {
          cursor: pointer;
          padding: 13px 30px !important;
          &:last-child {
            border: none;
          }
          .person-cards {
            align-items: center;
            gap: 10px;
          }
        }
      }
    }
  }
}
.privacy-policy * {
  margin-bottom: 12px;
  font-size: 16px;
}

.soul-status-draft {
  background-position: -53px -225px;
}
.soul-status-submitted {
  background-position: -110px -225px;
}
.soul-status-recieved {
  background-position: -81px -225px;
}

.project_add_modal {
  .modal-content {
    height: auto;
  }
  .modal-dialog {
    width: 540px;
    .modal-body {
      height: calc(100vh - 180px);
    }
  }

  .modal-footer {
    padding-bottom: 0px;
  }
  .list_wrapper {
    max-height: calc(100vh - 250px);
    overflow: auto;
    .action-btns {
      margin: 0px 0px 10px;
    }
  }
  .project_list {
    position: relative;
    margin-bottom: 20px;
    .delete-btn {
      background: transparent;
      border: none;
      outline: none;
      position: absolute;
      top: 15px;
      right: 4px;
    }
  }
}

.project_list_wrapper {
  background: #f4f4f4;
  margin-top: 20px;
  border-radius: 10px;
  padding: 10px 15px;
  max-height: 280px;
  overflow: auto;
  .project_name {
    margin-bottom: 5px;
  }
}
.new-proj {
  padding-top: 10px;
  padding-bottom: 10px;
  border-top: 1px solid rgba($accent, 0.2);
}
.project-field-wrpr {
  gap: 20px;
}
// .css-4ljt47-MenuList{
//   .project_tool_list{
//     max-height: 150px;
//     overflow: auto;
//   }
// }

.soulwriting-table {
  .soul-table-wrpr {
    margin: 0px 0px 50px;
  }
}

.check-list-blk {
  gap: 10px;
  @include tabv {
    flex-wrap: wrap;
    margin-top: 15px;
  }
}

.form-check .form-check-input:checked ~ .checked_highligh {
  background: #fffcf9;
  border: 1px solid #fbc87b;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  color: #262626;
}
.checked_highligh {
  padding: 10px 14px;
  border: 1px solid transparent;
  color: #616161;
  @include tabv {
    padding: 0px 6px;
  }
  .cat-icon {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    overflow: hidden;

    img {
      width: 100%;
    }
  }
}
.detail-img {
  border-radius: 5px;
  max-height: 350px;
}

.sales-page-wrpr .online-seminar-card ul li.online-seminar-list {
  gap: 12px;
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  display: flex;
  background-image: url("/images/right-chevron.svg");
  background-repeat: no-repeat;
  background-position: left top 2px;
  padding-left: 30px;
}

.comp-inner-header {
  position: sticky;
  top: 0px;
  background: #fff;
  z-index: 2;
  @include tab {
    position: relative;
  }
  .tab-list-wrpr {
    margin-left: 32px;
    margin-right: 32px;
  }
}



.cstm-wrprr {
  height: 100%;
  max-height: calc(100% - 123px);
  overflow: auto;
  padding-bottom: 30px;
}

.google-calendar-btn {
  border: 1px solid #4291ff;
  color: #4291ff;
  padding: 10px 18px;
}

.google-calendar-btn img {
  height: 20px;
  width: 20px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: transparent;
  color: #499557;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #499557;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}

.static_page_header {
  margin: 20px 0 20px 0;
}

.privacy-policy p {
  margin-bottom: 12px;
  font-size: 18px;
}

.privacy-policy ul {
  list-style: unset;
  margin-left: 20px;
}
.or-wrpr {
  position: relative;
  text-align: center;
  padding: 20px 0px;
  &:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    height: 2px;
    background-color: #eee;
    z-index: 0;
  }
  .or-content {
    width: fit-content;
    background: $white;
    position: relative;
    z-index: 0;
    padding: 0px 10px;
  }
}
.toggle-sidebar {
  border-radius: 50px;
  border: 1px solid #e7e8f2;
  width: 30px;
  height: 30px;
  background: #fff;
  display: flex;
  align-content: center;
  justify-content: center;
  position: absolute;
  top: 7px;
  left: auto;
  right: -15px;
  @include phone {
    display: none;
  }

  .arrow-icon {
    rotate: 90deg;
    top: 3px;
    position: relative;
    left: -2px;
    transition: all 0.4s ease-in-out;
  }
  &.closed {
    .arrow-icon {
      left: 1px;
      top: 3px;
      rotate: 270deg;
      transition: all 0.4s ease-in-out;
    }
  }
}
.book-meeting-btn {
  .meeting-icon {
    display: none;
  }
}
.ham-icon-wrpr {
  display: none;
  @include phone {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 6px;
  }
  .ham-bar {
    width: 20px;
    display: block;
    background: $grey-5;
    transition: all 0.3s ease;
    height: 2px;
    &.bar2 {
      width: 80%;
    }
  }
  &.open {
    .ham-bar.bar1 {
      transform: rotate(45deg) translate(5px, 5px);
      transition: all 0.3s ease;
    }
    .ham-bar.bar2 {
      display: none;
    }
    .ham-bar.bar3 {
      transform: rotate(-45deg) translate(2px, -2px);
      transition: all 0.3s ease;
    }
  }
}
.mobile-inner-icons {
  display: none;
  @include phone {
    display: block;
    padding: 8px 0px;
    border-bottom: 1px solid #eef7f1;
  }
}
.soulwriting-bill .modal-content {
  max-height: unset;
  height: auto;
}
.table.border-0 {
  border: none;
  td {
    border: none;
  }
}

.react-daterange-picker {
  padding: 10px 14px !important;
  .react-daterange-picker__wrapper {
    border: none;
  }
}
.table_form_in {
  margin: 0px;
}
.order_header_bar {
  margin-top: 20px;
}
.calender-day-tile {
  background: #fff;
  border-radius: 7px;
  margin-bottom: 5px;
  border: 1px solid green;
  flex: 0 0 13.6% !important;
  max-width: 13.6%;
  font-size: 16px;
  padding: 6px;
  &[disabled] {
    background: #f0f0f0;
    color: #a2a2a2;
    opacity: 0.4;
  }
  &.react-calendar__tile--active {
    background: $accent !important;
    color: $white !important;
  }
  @include phonev {
    flex: 0 0 12.6% !important;
  }
}

.react-calendar__month-view__days {
  gap: 5px;
}
.react-calendar__month-view__weekdays__weekday {
  text-align: center;
  text-decoration: none;
  margin-bottom: 7px;
  abbr {
    text-decoration: none;
  }
}
.meeting-schedule-calendar .react-calendar__navigation button:first-child,
.meeting-schedule-calendar .react-calendar__navigation button:last-child,
.meeting-schedule-calendar .react-calendar__navigation button:nth-child(3) {
  display: none;
}
.meeting-schedule-calendar .react-calendar__navigation__arrow {
  background: #fff;
  border: 1px solid $accent;
  border-radius: 7px;
  width: 30px;
  height: 30px;
  color: $accent;
  line-height: 0.9;
  margin: 0px 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &.react-calendar__navigation__prev-button {
    transform: rotate(90deg);
  }
  &.react-calendar__navigation__next-button {
    transform: rotate(270deg);
  }
}

.meeting-schedule-calendar .react-calendar__navigation {
  text-align: right;
  margin: 10px auto;
}
.slot-wrpr {
  .cal-row {
    flex-direction: row;
    margin-top: 10px;
    flex-wrap: wrap;
  }
}
///////////////////////////////////      responsive style start       ////////////////////////////////
@include tab {
  .onboarding-row {
    flex-direction: column;
    .w-50 {
      width: 100%;
    }
    .left-half {
      display: none;
    }
  }
  .title-img-wrpr {
    padding-top: 40px;
  }
}
@include tabv {
}
@include phone {
  .book-meeting-btn {
    position: fixed;
    top: auto;
    bottom: 20px;
    right: 15px;
    z-index: 99;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    padding: 0px !important;
    border: none;
    .meeting-text {
      display: none;
    }
    .meeting-icon {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .order_header_bar {
    flex-direction: column;
    align-items: flex-start !important;
    margin-top: 30px;
    .table_form_in {
      margin-top: 10px;
      margin-right: 0px;
    }
  }
  .fc .fc-toolbar {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 10px;
  }
}

.btn-sm {
  background: #ffffff;
  border: 1px solid #f2ebde;
  // color: #ac4139;
  padding: 5px 10px;
  font-size: 12px;
}
.question-wrpr {
  margin-bottom: 34px;
}

.protogonist-opt-list {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 20px;
  align-items: center;
  flex-wrap: wrap;
  &.two-column {
    .protogonist-opt-item {
      width: 48%;
      margin-bottom: 20px;
    }
  }
  .protogonist-opt-item {
    width: 100%;
    margin-bottom: 20px;
    &.full {
      width: 100% !important;
    }
  }
  .protogonist-opt {
    cursor: pointer;
    width: 100%;

    input[type="radio"] {
      position: absolute;
      opacity: 0;
      height: 8px;
      width: 8px;
      background: $accent;
      left: 20px;
      top: 30px;
      border-radius: 100%;
      &:checked + .protogonist-content {
        border: 2px solid $accent;
        border-radius: 12px;
        background: #fff;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
        transition: ease-in 0.3s;
      }
      &:checked + .protogonist-content:after {
        content: "";
        position: absolute;
        height: 8px;
        width: 8px;
        background: $accent;
        left: 20px;
        top: 21px;
        border-radius: 100%;
        border: 3px solid #fff;
        -webkit-box-shadow: 0px 0px 0px 2px $accent;
        box-shadow: 0px 0px 0px 2px $accent;
      }
    }
    .protogonist-content {
      display: flex;
      flex-direction: column;
      padding: 20px;
      padding-left: 50px;
      box-sizing: border-box;
      border: 2px solid #eaeaea;
      background: #ffffff;
      border-radius: 10px;
      transition: -webkit-box-shadow 0.4s;
      transition: box-shadow 0.4s;
      transition: box-shadow 0.4s, -webkit-box-shadow 0.4s;
      position: relative;
      align-items: flex-start;
      gap: 10px;
      &:hover {
        box-shadow: 0px 3px 5px 0px #e8e8e8;
      }
    }
  }
}

.protagonist-list-modal {
  position: absolute;
  right: 0;
  width: fit-content;
  margin-right: 0;
  margin-left: auto;
  height: fit-content;
  bottom: 0;
  top: auto;
  &.bridge {
    position: fixed;
    .modal-content {
      width: 637px;
      .bridge-table-wrpr .bridge-block .bridge-card {
        max-width: 230px;
        width: 100%;
      }
    }
  }

  .close-btn {
    top: 25px;
  }
  .overlay-div {
    display: none;
  }
  .modal-content {
    width: 500px;
    margin-right: 75px;
    margin-left: auto;
    margin-bottom: 0px;
    margin-top: auto;
    max-height: 75vh;
    overflow: hidden;
    height: 100%;
    .modal-body {
      height: 60vh;
      overflow: auto;
    }
    .modal-dialog {
      border: 1px solid $grey-9;
      border-radius: 10px 10px 0px 0px;
    }
  }
}
.modal-listing-wrpr {
  margin-top: 20px;
  .accordion-item {
    border-top: 1px solid $grey-9;
    &:last-child {
      border-bottom: 1px solid $grey-9;
    }
  }
  .protagonist-list {
    margin-top: 0px;
    .protagonist-card {
      margin-left: 0px;
      border: 0px;
      border-radius: 0px;
      padding: 10px 0px;
    }
  }
  .status-badge {
    font-weight: 600;
    border-radius: 50px;
    padding: 0px 8px;
    border: none;
    font-size: 14px;
    line-height: 1.43;
    &.green {
      color: $accent;
      background-color: $accent-l;
    }
    &.gray {
      color: $grey-1;
      background-color: $grey-8;
    }
  }
  .protogonist-content {
    padding-left: 66px;
    height: 0px;
    overflow: hidden;
    &.active {
      height: auto;
      padding-bottom: 15px;
    }
  }
}
.protogonist-action-btns {
  .btn {
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 8px;
  }
}

.twitter-picker span div {
  border: 1px solid rgb(220, 208, 208);
}
.ftr-row {
  max-width: 98%;
  margin: 0 auto;
  border-radius: 6px;
  padding: 30px;
  padding-right: 80px;
  background: #ececec;
  gap: 15px;
  @include tabv {
    flex-direction: column;
    padding-right: 0px;
  }
  .ftr-left,
  .ftr-right {
    @include tabv {
      flex: 0 0 auto;
      width: 100%;
    }
  }
  .ftr-links-list {
    gap: 16px;
    flex-wrap: wrap;
    @include tabv {
      justify-content: center;
      text-align: center;
    }
  }
  .ftr-link {
    font-size: 14px;
    font-weight: normal;
    color: $grey-7;
    white-space: nowrap;
    text-align: center;
    &.active {
      color: $grey-1;
    }
  }
  .social-media-list {
    gap: 16px;
    @include tabv {
      justify-content: center;
      text-align: center;
    }
    .sm-link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      .sm-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
}
.ftr-bottom-row {
  padding: 15px 0px;
}
.bridge-step-wrpr {
  .soul-step-inner {
    flex: 0 0 calc(100% - 250px);
    max-width: calc(100% - 250px);
  }
}
.bridge-table-wrpr {
  width: 100%;
  gap: 34px;
  .bridge-block {
    flex: 0 0 calc(50% - 17px);
    max-width: calc(50% - 17px);
    &.bridge-past-block {
      .bridge-list-item {
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 18px;
          right: -45px;
          left: auto;
          border: 1px dashed #e5dec1;
          width: 100px;
          z-index: 0;
        }
      }
    }
    .bridgePast {
      margin: 0;
    }
    .bridgePersent {
      font-weight: 300;
      margin: 0;
    }
    .bridge-subtitle {
      margin-bottom: 14px;
    }
    .bridge-count {
      margin-right: 3px;
      display: flex;
      min-width: 20px;
      justify-content: start;
    }
    .bridge-list-item {
      margin-bottom: 10px;
      position: relative;
      &.past {
        .bridge-card {
          max-width: calc(100% - 23px);
        }
      }
      &.prenset {
        gap: 10px;
      }
      .btn-text {
        background-color: transparent;
        border: none;
        padding: 0px;
        outline: none;
        box-shadow: none;
        cursor: pointer;
        &:hover {
          background-color: transparent;
          border: none;
          padding: 0px;
          outline: none;
        }
        &.full-delete-bridge {
          position: absolute;
          right: -30px;
          width: 20px;
          height: 20px;
        }
      }
    }
    .bridge-content {
      cursor: pointer;
      flex: 0 0 calc(100% - 60px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p.bridge-content {
      min-height: 20px;
    }
    .bridge-card {
      border-radius: 6px;
      border: 1px solid #e5dec1;
      background: #fbf9f3;
      padding: 6px 10px;
      gap: 10px;
      width: 100%;
      // max-width: 370px;
      position: relative;
      .btn-text {
        background-color: transparent;
        border: none;
        padding: 0px;
        outline: none;
        box-shadow: none;
        cursor: pointer;
        &:hover {
          background-color: transparent;
          border: none;
          padding: 0px;
          outline: none;
        }
      }
    }
    .add-bridge-card {
      border-radius: 6px;
      border: 1px dashed $accent;
      background: #fff;
      padding: 8px 10px;
      gap: 10px;
      width: 100%;
      z-index: 10;
    }
  }
}
.more-info-wrpr {
  min-width: 600px;
  .info-row {
    padding: 10px;
    border-bottom: 1px solid $grey-9;
    &:last-child {
      border-bottom-width: 0px;
    }
    .info-left {
      flex: 0 0 150px;
      max-width: 150px;
    }
    .info-right {
      flex: 0 0 auto;
      width: calc(100% - 150px);
    }
  }
}
.read-more {
  background: transparent !important;
  color: $accent !important;
  border: none !important;
  outline: none !important;
}
.cookie-popup {
  top: auto;
  margin-left: 0px;
  z-index: 9999;
  left: 46px;
  box-shadow: 0 0 14px 0 rgb(0 0 0 / 34%);
  bottom: 62px;
  position: fixed;
  background: #fff;
  padding: 20px;
  width: 100%;
  max-width: 403px;
  font-size: 13px;
  border-radius: 4px;
  text-align: center;
  h4,
  p {
    margin-bottom: 10px;
  }
  .btn-block {
    gap: 16px;
  }
  .cookie-cat-wrpr {
    text-align: left;
    padding-left: 50px;
    .cat-name {
      font-size: 16px;
      padding-bottom: 10px;
    }
    UL {
      margin-left: 10px;
    }
  }
}
.list-btn {
  display: none;
  @include tabv {
    display: block;
  }
}

.topic-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 270px;
  .icon {
    display: none;
    @include tabv {
      display: inline-block;
      margin-right: 10px;
    }
  }
}
#top_right_video {
  aspect-ratio: 16 / 9;
}

.border-none {
  border: none !important;
}

.tab-right-btn-wrpr {
  gap: 14px;
  @media all and (max-width: 480px) {
    button {
      font-size: 16px;
    }
  }
  @include tabv-m {
    justify-content: flex-end;
    padding-right: 16px;
  }
}
.modal-body {
  .errorMessage {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 30px;
  }
}
#limitModal {
  .modal-content {
    height: auto;
  }

  .modal-body {
    height: 100%;
  }
}

.audio-block-wrpr {
  display: flex;
  gap: 40px;
  margin-bottom: 5px;
  @include tabv {
    flex-direction: column;
    gap: 0px;
  }
}
.cart_wrpr_outer {
  @include tab {
    flex-direction: column;
    height: auto !important;
  }
  .cart_total__amount_wrpr {
    @include tab {
      flex: 1 1 auto;
      padding-left: 0px;
      border-left: none;
    }
  }
}
.cart_wrpr > table tr td:first-child {
  min-width: 300px;
}
.mobile-title {
  margin-bottom: 16px;
  display: none;
  @include phone {
    display: block;
  }
}

.tab-list-search-wrpr {
  padding-right: 20px;
  border-bottom: 1px solid #daece1;
}
.search-wrpr {
  position: relative;
  max-width: 400px;
  margin-top: -10px;
  width: 100%;
  @include phone {
    display: none;
  }
  .search-button {
    position: absolute;
    top: 0px;
    right: 0px;
    left: auto;
    width: 50px;
    min-width: 50px;
    height: 100%;
    background: transparent;
    border: 1px solid $grey-9;
    border-radius: 0px 9px 9px 0px;
    .icon {
      transform: scale(1.5);
    }
  }
}
.clear-button {
  border: none !important;
  background: none !important;
  position: absolute;
  left: auto;
  top: 23%;
  right: 50px;
  outline: none !important;
  box-shadow: none !important;
}

.protogonist-opt-list {
  .form-inner {
    width: 100%;
  }
}

div#affirmation-10 .ck-content {
  height: 100px;
}

.footer-btn.protagonists {
  @include phone {
    display: none;
  }
}

.footer-btn.SendCompanion {
  @include phone {
    display: none;
  }
}
.footer-btn.btn-next {
  @include phone {
    background: linear-gradient(90deg, #499557, #499557);
  }
}

.scene-text-wrpr {
  .ck-content {
    width: 100%;
    word-wrap: break-word !important;
  }
}

.mobile-only {
  display: none;
  @include phone {
    display: block;
  }
}

.desktop-only {
  @include phone {
    display: none;
  }
}
.createProject {
  @include tabv-m {
    position: absolute;
    left: 153px;
    top: 10px;
  }
}
.sendToCompanionMobile {
  margin-top: 5px;
}

.protogonist-opt-list
  .protogonist-opt
  input[type="radio"]
  + .protogonist-content:after {
  content: "";
  width: 8px;
  height: 8px;
  position: absolute;
  left: 21px;
  top: 22px;
  border-radius: 100%;
  box-shadow: 0px 0px 0px 2px #eee;
  border: 3px solid #fff;
}

.AnimatedShowMore__MainContent {
  @include phone {
    // max-height: max-content !important;
  }
}

.soulwriting-wrpr .soul-content-wrpr .soul-intro {
  line-height: 1.6 !important;
}
.bill-modal-test .companion-card-row .billing_intro {
  line-height: 1.6 !important;
}
.desc .column p {
  line-height: 1.6 !important;
}

.priceCalculation {
  margin-bottom: 20px;
}

.soul-content-open {
  .AnimatedShowMore__MainContent {
    @include phone {
      max-height: max-content !important;
    }
  }
}
// .scene-text-wrpr .ck-rounded-corners {
//   > p {
//     overflow-wrap: break-word !important;
//     word-wrap: break-word !important;
//     hyphens: auto !important;
//   }
// }

.comp-about-desc > div {
  width: 100% !important;
  margin: 0px !important;
}

.profile-modal {
  .modal-btn-wrpr {
    @include phonev {
      flex-direction: column;
    }
  }
}
.calender-day-tile abbr {
  color: green;
}
.calender-day-tile.react-calendar__tile--active abbr {
  color: #fff;
}

.project-details-container {
  width: 100%;
  border-collapse: collapse;
}

.table-header,
.table-row {
  display: flex;
  padding: 10px 0;
}

.table-header {
  background-color: #f4f4f4;
  font-weight: bold;
}

.table-row {
  border-bottom: 1px solid #ddd;
}

.serial-no-header,
.actor-header,
.text-header,
.comment-header,
.serial-no-cell,
.actor-cell,
.text-cell,
.comment-cell {
  padding: 10px;
  flex: 1;
  display: flex;
  align-items: center;
}

.text-cell {
  flex: 3;
}

.comment-cell {
  flex: 2;
}
.personProject .person-card {
  width: 160px;
  height: 32px;
}
.ckEditorOldProject .ck-content.ck-editor__editable {
  background: #fff;
}

.ckeditor-component .text-cell {
  flex-direction: column;
  background: #fff;
  border: 1px solid #ccc;
}

.fourth-col .commentContent {
  background: #fff;
  border: 1px solid #ccc;
  padding: 10px;
}

.fourth-col .commentLabel {
  font-weight: 500 !important;
  font-family: "GS-medium";
  color: #616161;
  padding: 2px 0px 5px 0px;
  display: block;
}

.fourth-col {
  width: 250px;
}
.text-cellContent {
  display: flex;
  flex-direction: column;
}

.mt-20 {
  @include phonev {
    margin-top: 20px;
  }
}

.generalComment {
  width: 700px !important;
  margin: 20px;
}
