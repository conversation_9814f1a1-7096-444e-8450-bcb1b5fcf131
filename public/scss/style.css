@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
* {
  box-sizing: border-box;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

:root {
  --white:#ffffff;
  --black:#000000;
  --text:$grey-3;
  --accent:$accent;
}

.text-white {
  color: white !important;
}

.text-black {
  color: #000000 !important;
}

.text-black-1 {
  color: #021416 !important;
}

.text-dark-grey {
  color: #404040 !important;
}

.text-grey-1 {
  color: #07080A !important;
}

.text-grey-2 {
  color: #14182F !important;
}

.text-grey-3 {
  color: #262626 !important;
}

.text-grey-4 {
  color: #5F6667 !important;
}

.text-grey-5 {
  color: #616161 !important;
}

.text-grey-6 {
  color: #7E7E7E !important;
}

.text-grey-7 {
  color: #A2A2A2 !important;
}

.text-grey-8 {
  color: #C6C6C6 !important;
}

.text-grey-9 {
  color: #E0EDE5 !important;
}

.text-accent {
  color: #499557 !important;
}

.text-red {
  color: #D8090D !important;
}

.bg-white {
  background-color: white !important;
}

.bg-black {
  background-color: #000000 !important;
}

.bg-black-1 {
  background-color: #021416 !important;
}

.bg-dark-grey {
  background-color: #404040 !important;
}

.bg-grey-1 {
  background-color: #07080A !important;
}

.bg-grey-2 {
  background-color: #14182F !important;
}

.bg-grey-3 {
  background-color: #262626 !important;
}

.bg-grey-4 {
  background-color: #5F6667 !important;
}

.bg-grey-5 {
  background-color: #616161 !important;
}

.bg-grey-6 {
  background-color: #7E7E7E !important;
}

.bg-grey-7 {
  background-color: #A2A2A2 !important;
}

.bg-grey-8 {
  background-color: #C6C6C6 !important;
}

.bg-grey-9 {
  background-color: #E0EDE5 !important;
}

.bg-accent {
  background-color: #499557 !important;
}

.bg-red {
  background-color: #D8090D !important;
}

.icon-fb {
  background: url(../images/) no-repeat;
  background-position-y: center;
  background-position-x: -44px;
  width: 34px;
  height: 34px;
  display: inline-block;
}

.icon-insta {
  background: url(../images/) no-repeat;
  background-position-y: center;
  background-position-x: 0px;
  width: 34px;
  height: 34px;
  display: inline-block;
}

.icon-linked {
  background: url(../images/) no-repeat;
  background-position-y: center;
  background-position-x: -86px;
  width: 34px;
  height: 34px;
  display: inline-block;
}

.icon-twitter {
  background: url(../images/) no-repeat;
  background-position-y: center;
  background-position-x: -132px;
  width: 34px;
  height: 34px;
  display: inline-block;
}

@font-face {
  font-family: "GS-bold";
  src: url("../fonts/GeneralSans-Bold.woff") format("woff");
  src: url("../fonts/GeneralSans-Bold.woff2") format("woff2");
}
@font-face {
  font-family: "GS-light";
  src: url("../fonts/GeneralSans-Light.woff") format("woff");
  src: url("../fonts/GeneralSans-Light.woff2") format("woff2");
}
@font-face {
  font-family: "GS-medium";
  src: url("../fonts/GeneralSans-Medium.woff") format("woff");
  src: url("../fonts/GeneralSans-Medium.woff2") format("woff2");
}
@font-face {
  font-family: "GS";
  src: url("../fonts/GeneralSans-Regular.woff") format("woff");
  src: url("../fonts/GeneralSans-Regular.woff2") format("woff2");
}
@font-face {
  font-family: "GS-semibold";
  src: url("../fonts/GeneralSans-Semibold.woff") format("woff");
  src: url("../fonts/GeneralSans-Semibold.woff2") format("woff2");
}
@font-face {
  font-family: "inter", sans-serif;
  src: url("../fonts/Inter-Regular.woff") format("woff");
}
@font-face {
  font-family: "inter-black", sans-serif;
  src: url("../fonts/Inter-Black.woff") format("woff");
}
@font-face {
  font-family: "inter-bold", sans-serif;
  src: url("../fonts/Inter-Bold.woff") format("woff");
}
@font-face {
  font-family: "inter-medium", sans-serif;
  src: url("../fonts/Inter-Medium.woff") format("woff");
}
@font-face {
  font-family: "inter-semibold", sans-serif;
  src: url("../fonts/Inter-SemiBold.woff") format("woff");
}
html,
body,
div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
img,
figure,
footer,
header,
section {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-family: "GS";
  scroll-behavior: smooth;
}

body {
  line-height: 1;
  color: #000000;
  font-family: "GS";
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

h1,
.h1 {
  font-size: 38px;
  line-height: 1.06;
  font-weight: 800;
}

h2,
.h2 {
  font-size: 28px;
  font-weight: 800;
  line-height: 1.17;
}

h3,
.h3 {
  font-size: 26px;
  font-weight: 800;
  line-height: 1.18;
}

h4,
.h4 {
  font-size: 24px;
  font-weight: 800;
  line-height: 1.33;
}

h5,
.h5 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
}

h6,
.h6 {
  font-size: 16px;
  line-height: 1.33;
  font-weight: 400;
}

p,
.p {
  font-size: 14px;
  line-height: 1.43;
  color: #262626;
}

.fs-12 {
  font-size: 12px;
  line-height: 1.43;
}

a {
  color: #499557;
  text-decoration: none;
  outline: none !important;
  box-shadow: none !important;
  cursor: pointer !important;
}
a:hover {
  color: #499557;
  text-decoration: none;
}

button {
  cursor: pointer;
}

ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}

.font-gs {
  font-family: "GS";
}

.font-inter {
  font-family: "Inter", sans-serif;
}

.fw300 {
  font-weight: 300 !important;
  font-family: "GS-light";
}

.fw400 {
  font-weight: 400 !important;
  font-family: "GS";
}

.fw500 {
  font-weight: 500 !important;
  font-family: "GS-medium";
}

.fw600 {
  font-weight: 600 !important;
  font-family: "GS-semibold";
}

.fw700 {
  font-weight: 700 !important;
  font-family: "GS-bold";
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-flex {
  display: flex;
}

.d-inblock {
  display: inline-block;
}

.flex-wrap {
  flex-wrap: wrap;
}

.fd-row-r {
  flex-direction: row-reverse;
}

.fd-col {
  flex-direction: column;
}

.fd-col-r {
  flex-direction: column-reverse;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: transparent;
  color: #499557;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #499557;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}
@media (max-width: 480px) {
  .btn {
    width: 100%;
    font-size: 14px;
  }
}
.btn:active, .btn:hover {
  outline: none;
  color: #499557;
  background: #f2fbf6;
  box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
  border-color: #b7debe;
}
.btn.sm {
  padding: 8px 20px;
}
.btn.btn-loader::after {
  border: 3px solid rgba(73, 149, 87, 0.5);
  border-right-color: #499557;
}

.btn-accent {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: linear-gradient(90deg, #499557, #499557);
  color: white;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid transparent;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
  background-size: 102% 105%;
  background-position: center;
  background-repeat: no-repeat;
}
@media (max-width: 480px) {
  .btn-accent {
    width: 100%;
    font-size: 14px;
  }
}
.btn-accent:active, .btn-accent:hover {
  outline: none;
  color: white;
  background: linear-gradient(315.88deg, #037231 9.27%, #6ca11f 90.54%);
  box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
  border-color: transparent;
}
.btn-accent.sm {
  padding: 8px 20px;
}
.btn-accent:hover {
  background-size: 102% 105%;
  background-position: center;
  background-repeat: no-repeat;
}
.btn-accent.yellow {
  background: #cbcb4a;
}
.btn-accent.black {
  background: #000000;
}
.btn-accent.blue {
  background: #0000ff;
}
.btn-accent.orange {
  background: #ffa500;
}

.btn-accent-lite {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: #f2fbf6;
  color: #499557;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #499557;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}
@media (max-width: 480px) {
  .btn-accent-lite {
    width: 100%;
    font-size: 14px;
  }
}
.btn-accent-lite:active, .btn-accent-lite:hover {
  outline: none;
  color: #499557;
  background: white;
  box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
  border-color: #499557;
}
.btn-accent-lite.sm {
  padding: 8px 20px;
}

.btn-tertiary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: #eeeef7;
  color: #2e3a39;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #eeeef7;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}
@media (max-width: 480px) {
  .btn-tertiary {
    width: 100%;
    font-size: 14px;
  }
}
.btn-tertiary:active, .btn-tertiary:hover {
  outline: none;
  color: #2e3a39;
  background: #fff;
  box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
  border-color: #499557;
}
.btn-tertiary.sm {
  padding: 8px 20px;
}
.btn-tertiary:hover, .btn-tertiary.active {
  box-shadow: none;
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: white;
  color: #7E7E7E;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #f0f0f0;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}
@media (max-width: 480px) {
  .btn-secondary {
    width: 100%;
    font-size: 14px;
  }
}
.btn-secondary:active, .btn-secondary:hover {
  outline: none;
  color: #499557;
  background: white;
  box-shadow: 0px 6px 20px rgba(55, 108, 243, 0.1);
  border-color: #c0e0c6;
}
.btn-secondary.sm {
  padding: 8px 20px;
}
.btn-secondary:hover, .btn-secondary.active {
  box-shadow: none;
}

.btn-loader {
  pointer-events: none;
  opacity: 0.8;
  transition: none !important;
}
.btn-loader::after {
  content: "";
  position: absolute;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  margin-left: 0px;
  border-right-color: white;
  animation: loader 600ms linear 0ms infinite;
  animation-direction: alternate-reverse;
  z-index: 20;
}
.btn-loader .btn-text-wrpr {
  opacity: 0;
  transition: all ease 250ms;
}

.btn-loader-pdf {
  pointer-events: none;
  opacity: 0.8;
  transition: none !important;
}
.btn-loader-pdf::after {
  content: "";
  position: absolute;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  margin-left: 0px;
  border-right-color: green;
  animation: loader 600ms linear 0ms infinite;
  animation-direction: alternate-reverse;
  left: 35%;
  z-index: 20;
  outline: none;
}
.btn-loader-pdf .btn-text-wrpr {
  opacity: 0;
  transition: all ease 250ms;
}

@keyframes loader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.btn-text-wrpr {
  opacity: 1;
  transition: all ease 250ms;
}

.w-100 {
  width: 100%;
}

.w-full {
  width: 100vw;
}

.w-50 {
  width: 50%;
}

.w-45 {
  width: 45%;
}

.w-43 {
  width: 43%;
}

.w-40 {
  width: 40%;
}

.h-100 {
  height: 100%;
}

.h-full {
  height: 100vh;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.img-fluid {
  width: 100%;
}

.cover-img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.contain-img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: center;
     object-position: center;
}

.container {
  max-width: 1200px;
  width: 100%;
  padding: 0px 15px;
  margin: 0 auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.over-hidden {
  overflow: hidden;
}

.over-auto {
  overflow: auto;
}

.m-auto {
  margin: auto !important;
}

.m-0 {
  margin: 0px !important;
}

.ml-0 {
  margin-left: 0px !important;
}

.mr-0 {
  margin-right: 0px !important;
}

.mt-0 {
  margin-top: 0px !important;
}

.mb-0 {
  margin-bottom: 0px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.soulwriting-choose-companion {
  padding: 10px 40px !important;
  overflow: hidden !important;
}

.companion-choose-modal-soulwriting .modal-header {
  border-bottom: none !important;
  padding: 24px 40px 10px 40px;
}
.companion-choose-modal-soulwriting .modal-body {
  height: calc(100% - 120px) !important;
}
.companion-choose-modal-soulwriting .modal-footer {
  padding: 16px 40px !important;
}
@media (max-width: 992px) {
  .companion-choose-modal-soulwriting .companion-card-row .companion-card {
    width: 100%;
    max-width: 100%;
  }
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.ml-auto {
  margin-left: auto;
  margin-right: 0px;
}

.mr-auto {
  margin-left: 0px;
  margin-right: auto;
}

.mt-auto {
  margin-top: auto;
  margin-bottom: 0px;
}

.mb-auto {
  margin-top: 0px;
  margin-bottom: auto;
}

.mt-10 {
  margin-top: 10px;
}

.p-0 {
  padding: 0px;
}

.pl-0 {
  padding-left: 0px;
}

.pr-0 {
  padding-right: 0px;
}

.pt-0 {
  padding-top: 0px;
}

.pb-0 {
  padding-bottom: 0px;
}

.br-0 {
  border-radius: 0px;
}

.br-10 {
  border-radius: 10px;
}

.br-20 {
  border-radius: 20px;
}

.br-circle {
  border-radius: 50%;
}

* {
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}
*::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
*::-webkit-scrollbar-track {
  background: transparent;
}
*::-webkit-scrollbar-thumb {
  background: transparent;
}
*::-webkit-scrollbar-thumb:hover {
  background: transparent;
}

.page-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  z-index: -9999;
}
.page-loader.active {
  opacity: 1;
  visibility: visible;
  z-index: 9999;
}
.page-loader.active .loader {
  width: 150px;
  height: 150px;
  display: flex;
  gap: 20px;
  position: relative;
}
.page-loader.active .loader.sm {
  transform: scale(0.5);
}
.page-loader.active .loader .loader-item {
  min-width: 26px;
  width: 26px;
  height: 26px;
  background-color: #499557;
  display: block;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  animation: ploader 1.6s linear infinite forwards;
}
.page-loader.active .loader .loader-item.loader1 {
  top: 0%;
  left: 43%;
  animation-delay: 0s !important;
}
.page-loader.active .loader .loader-item.loader2 {
  top: 16%;
  left: 72%;
  opacity: 0.9;
  animation-delay: 0.2s !important;
}
.page-loader.active .loader .loader-item.loader3 {
  top: 42%;
  left: auto;
  right: 0;
  opacity: 0.85;
  animation-delay: 0.4s !important;
}
.page-loader.active .loader .loader-item.loader4 {
  top: 70%;
  left: 72%;
  opacity: 0.72;
  animation-delay: 0.6s !important;
}
.page-loader.active .loader .loader-item.loader5 {
  top: auto;
  bottom: 0px;
  left: 43%;
  opacity: 0.5;
  animation-delay: 0.8s !important;
}
.page-loader.active .loader .loader-item.loader6 {
  top: 69%;
  left: 14%;
  opacity: 0.4;
  animation-delay: 1s !important;
}
.page-loader.active .loader .loader-item.loader7 {
  top: 42%;
  left: 0;
  opacity: 0.3;
  animation-delay: 1.2s !important;
}
.page-loader.active .loader .loader-item.loader8 {
  top: 16%;
  left: 14%;
  opacity: 0.2;
  animation-delay: 1.4s !important;
}

.img-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  z-index: -9999;
  transform: scale(0.5);
}
.img-loader.active {
  opacity: 1;
  visibility: visible;
  z-index: 9999;
}
.img-loader.active .loader {
  width: 45px;
  height: 45px;
  display: flex;
  gap: 20px;
  position: relative;
  margin-left: -6px;
}
.img-loader.active .loader .loader-item {
  min-width: 8px;
  width: 8px;
  height: 8px;
  background-color: #499557;
  display: block;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  animation: ploader 1.6s linear infinite forwards;
}
.img-loader.active .loader .loader-item.loader1 {
  top: 0%;
  left: 43%;
  animation-delay: 0s !important;
}
.img-loader.active .loader .loader-item.loader2 {
  top: 16%;
  left: 72%;
  opacity: 0.9;
  animation-delay: 0.2s !important;
}
.img-loader.active .loader .loader-item.loader3 {
  top: 42%;
  left: auto;
  right: 0;
  opacity: 0.85;
  animation-delay: 0.4s !important;
}
.img-loader.active .loader .loader-item.loader4 {
  top: 70%;
  left: 72%;
  opacity: 0.72;
  animation-delay: 0.6s !important;
}
.img-loader.active .loader .loader-item.loader5 {
  top: auto;
  bottom: 0px;
  left: 43%;
  opacity: 0.5;
  animation-delay: 0.8s !important;
}
.img-loader.active .loader .loader-item.loader6 {
  top: 69%;
  left: 14%;
  opacity: 0.4;
  animation-delay: 1s !important;
}
.img-loader.active .loader .loader-item.loader7 {
  top: 42%;
  left: 0;
  opacity: 0.3;
  animation-delay: 1.2s !important;
}
.img-loader.active .loader .loader-item.loader8 {
  top: 16%;
  left: 14%;
  opacity: 0.2;
  animation-delay: 1.4s !important;
}

@keyframes ploader {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.disabled {
  opacity: 0.4;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: not-allowed;
}

.icon {
  display: inline-block;
  width: 20px;
  min-width: 20px;
  height: 20px;
  background-image: url(../images/all-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
}
.icon.lock-icon {
  background-position: -59px -2px;
}
.icon.unlock-icon {
  background-position: -59px -2px;
}
.icon.eye-icon {
  background-position: 0px -114px;
}
.icon.eye-icon.open {
  background-position: -28px -114px;
}
.icon.info-icon {
  background-position: -87px -2px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}
.icon.info-icon-grey {
  background-position: -153px -89px;
}
.icon.login-icon {
  background-position: 2px -2px;
  margin-right: 9px;
}
.icon.register-icon {
  background-position: -29px -2px;
  margin-right: 9px;
}
.icon.soul-icon {
  background-position: -25px -52px;
  width: 30px;
  height: 30px;
  min-width: 30px;
}
.icon.noti-icon {
  background-position: -122px -30px;
}
.icon.cart-icon {
  background-position: -122px -2px;
}
.icon.cart-icon-green {
  background-position: -153px -2px;
}
.icon.cart-icon-white {
  background-position: -153px -29px;
}
.icon.gray-back {
  background-position: 0px -94px;
}
.icon.shop-icon {
  background-position: -26px -89px;
}
.icon.cat-icon {
  background-position: -60px -91px;
}
.icon.sort-icon {
  background-position: -89px -91px;
}
.icon.q-mark-icon {
  background-position: -120px -57px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}
.icon.clock-icon {
  background-position: -155px -57px;
}
.icon.wishlist-icon-green {
  background-position: -22px -196px;
}
.icon.wishlist-icon-green-fill {
  background-position: 1px -196px;
}
.icon.wishlist-icon {
  background-position: -189px -2px;
}
.icon.play-icon {
  background-position: -120px -91px;
}
.icon.companion-icon {
  background-position: -187px -88px;
}
.icon.user-icon {
  background-position: -215px -2px;
}
.icon.video-icon {
  background-position: -215px -27px;
}
.icon.video-chat {
  background-position: -213px -54px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}
.icon.soul-icon-white {
  background-position: -212px -88px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}
.icon.icon-calender {
  background-position: -158px -119px;
}
.icon.icon-exp {
  background-position: -188px -117px;
}
.icon.video-chat-green {
  background-position: -215px -117px;
}
.icon.profile-icon {
  background-position: -245px -2px;
}
.icon.meeting-icon {
  background-position: -246px -26px;
}
.icon.soulwriting-icon {
  background-position: -246px -52px;
}
.icon.order-icon {
  background-position: -246px -77px;
}
.icon.payment-icon {
  background-position: -246px -101px;
}
.icon.logout-icon {
  background-position: -247px -124px;
}
.icon.close-icon {
  background-position: 0px -136px;
}
.icon.upload-grey {
  background-position: -89px -119px;
}
.icon.edit-icon-green {
  background-position: -60px -113px;
}
.icon.arrow-icon {
  background-position: -88px -59px;
}
.icon.change-pass-green {
  background-position: -274px -176px;
}
.icon.plus-icon {
  background-position: -58px -140px;
}
.icon.edit-icon-grey {
  background-position: -87px -143px;
}
.icon.delete-icon {
  background-position: -120px -146px;
}
.icon.address-icon {
  background-position: -246px -148px;
}
.icon.change-pass-icon {
  background-position: -246px -176px;
}
.icon.check-icon-white {
  background-position: -88px -30px;
}
.icon.check-icon-grey {
  background-position: -157px -145px;
}
.icon.rate-icon {
  background-position: -216px -143px;
}
.icon.overview-icon {
  background-position: 0px -169px;
}
.icon.download-icon {
  background-position: -59px -169px;
}
.icon.comment-icon {
  background-position: -117px -171px;
}
.icon.search-icon {
  background-position: -189px -144px;
}
.icon.status-info-icon {
  background-position: -51px -196px;
}
.icon.plus-icon-white {
  background-position: -83px -196px;
}
.icon.add-user-icon {
  background-position: -115px -195px;
}
.icon.soul-comment-icon {
  background-position: -149px -196px;
}
.icon.swap-icon {
  background-position: -183px -196px;
}
.icon.leave-icon {
  background-position: -213px -200px;
}
.icon.draft-icon {
  background-position: -245px -200px;
}
.icon.send-icon {
  background-position: -274px -203px;
}
.icon.cal-icon {
  background-position: -1px -224px;
}
.icon.drag-icon {
  background-position: -26px -223px;
}
.icon.send-icon-grey {
  background-position: -275px -228px;
}

.audio-icon {
  display: inline-block;
  width: 45px;
  height: 35px;
  background-image: url(../images/audio-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
}
.audio-icon.dvd-icon {
  background-position: 7px -41px;
}
.audio-icon.stream-icon {
  background-position: -50px -41px;
}

.main-wraper {
  min-height: 100vh;
  max-width: 100vw;
  overflow: hidden;
}
@media (max-width: 767px) {
  .main-wraper {
    position: relative;
  }
}
.main-wraper.inner {
  min-height: calc(100vh - 80px);
}
.main-wraper.soulwriting {
  max-height: calc(100vh - 55px);
}

.logo-wrpr {
  max-width: 100px;
}

.onboarding-form-wrpr {
  max-width: 460px;
  width: 460px;
  margin: 0 auto;
  padding: 20px 0px;
  justify-content: center;
}
@media (max-height: 800px) {
  .onboarding-form-wrpr {
    justify-content: inherit;
    padding-top: 40px;
  }
}
.onboarding-form-wrpr .logo-wrpr {
  max-width: 150px;
  margin: 0px auto 36px;
}
.onboarding-form-wrpr .form-title-wrpr:has(.form-desc) .form-head {
  margin-bottom: 12px;
}
.onboarding-form-wrpr .form-head {
  font-size: 24px;
}
.onboarding-form-wrpr .frgt-pass {
  margin-top: 0px;
  position: absolute;
  top: 95px;
  right: 0;
}
.onboarding-form-wrpr .form-check {
  margin-bottom: 24px;
}

.slick-track {
  display: flex;
}

.resend-msg-wrpr {
  margin-top: 32px;
}
.resend-msg-wrpr .resend-msg {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: -0.08px;
}

.back-to-login-wrpr {
  margin-top: 32px;
}
.back-to-login-wrpr .back-to-login {
  font-family: "GS-medium";
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: -0.08px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.back-to-login-wrpr .back-to-login:hover .gray-back {
  background-position: 0px -75px;
}

.link-gray {
  color: #616161;
}
.link-gray:hover {
  color: #499557;
}

.succesfull-msg-wrpr {
  margin-bottom: 32px;
}

.right-bottom-wrpr {
  padding: 32px;
  overflow: auto;
  max-height: calc(100% - 110px);
}
@media (max-width: 767px) {
  .right-bottom-wrpr {
    padding: 28px 16px;
  }
}

.verification-status {
  max-width: 70px;
  margin: 32px auto;
}

.rating-stars {
  gap: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rating-stars.f-start {
  justify-content: flex-start;
}

.tabs {
  text-align: center;
  border-bottom: 1px solid #e0ede5;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
}
.tabs .tab-item {
  margin-bottom: -1.5px;
}
.tabs .tab-item .tab-link {
  display: block;
  height: 100%;
  text-align: center;
  padding: 12px;
  font-size: 14px;
  line-height: 1;
  color: #5F6667;
  position: relative;
}
.tabs .tab-item .tab-link.active {
  color: #021416;
}
.tabs .tab-item .tab-link.active:after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  border-radius: 10px;
  background-color: #021416;
}

.onboard-tab-wrpr .tab-contents-wrpr {
  padding-bottom: 30px;
}
.onboard-tab-wrpr .tab-contents-wrpr .tab-contents {
  padding-top: 32px;
  display: none;
}
.onboard-tab-wrpr .tab-contents-wrpr .tab-contents.active {
  display: block;
}

.right-half {
  padding: 0px 15px;
}

.form-wrpr {
  margin-top: 24px;
}
.form-wrpr .form-in {
  margin-bottom: 24px;
}

.f-row {
  gap: 28px;
}
@media (max-width: 480px) {
  .f-row {
    flex-direction: column;
    gap: 0px;
  }
}

.f-in {
  position: relative;
}
.f-in.right-icon .form-control {
  padding-right: 30px;
}
.f-in.right-icon .icon {
  cursor: pointer;
  position: absolute;
  top: 17px;
  right: 10px;
}
.f-in.left-icon .form-control {
  padding-left: 30px;
}
.f-in.left-icon .icon {
  cursor: pointer;
  position: absolute;
  top: 17px;
  left: 10px;
}
.f-in.radio-buttons {
  display: flex;
  gap: 60px;
}
.f-in.radio-buttons .form-control {
  display: none;
}
.f-in.radio-buttons .form-control:checked + .r-label {
  color: #021416;
}
.f-in.radio-buttons .form-control:checked + .r-label::before {
  border-color: #499557;
  background-color: #499557;
  box-shadow: 0px 2px 4px rgba(4, 96, 41, 0.34);
  transition: all 400ms ease;
}
.f-in.radio-buttons .form-control:checked + .r-label:after {
  transform: translateY(-50%) scale(1);
  transition: all 400ms ease;
}
.f-in.radio-buttons .r-label {
  cursor: pointer;
  position: relative;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #a2a2a2;
  font-family: "GS-medium";
  padding-left: 28px;
}
.f-in.radio-buttons .r-label::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: 1px solid #A2A2A2;
  border-radius: 50%;
  top: 50%;
  left: 0px;
  transform: translateY(-50%);
  transition: all 400ms ease;
}
.f-in.radio-buttons .r-label::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 4px;
  transform: translateY(-50%) scale(0);
  transition: all 400ms ease;
}

.profile-pic-wrpr {
  width: -moz-fit-content;
  width: fit-content;
  gap: 16px;
  flex-direction: column;
  margin: 0 auto;
}
.profile-pic-wrpr .upload-img {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  margin-bottom: 0px;
}
.profile-pic-wrpr .upload-img .upload-dp {
  width: 188px;
  height: 188px;
  margin-bottom: 40px;
  background-image: url(../images/icons/camera-icon1.svg);
  background-repeat: no-repeat;
  background-position: 35px 35px;
  background-color: #fbfbfb;
  border-radius: 50%;
  overflow: hidden;
  display: block;
  position: relative;
}
.profile-pic-wrpr .upload-img .upload-dp-in {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  cursor: pointer;
}
.profile-pic-wrpr .upload-img .upload-label {
  background: #f0f0f0;
  border: 1px solid #eeeef6;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 12px;
  line-height: 1.67;
  display: flex;
  align-items: center;
  width: 100%;
}

.filled-input {
  font-size: 20px;
  line-height: 1.4;
}

.form-in {
  position: relative;
}

.f-error .error {
  display: flex;
}
.f-error .form-control {
  border: 1px solid #D8090D !important;
}

.error {
  color: #D8090D;
  font-size: 12px;
  display: none;
  margin-top: 10px;
}

.f-label {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #616161;
  margin-bottom: 8px;
  display: block;
  width: 100%;
  font-family: "GS-medium";
}

.form-control,
.css-1s2u09g-control,
.css-1pahdxg-control,
.kuby-react-select__control {
  background: #ffffff;
  border: 1px solid #eaebec !important;
  border-radius: 8px !important;
  padding: 18px 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  width: 100%;
  color: #021416;
  transition: all 0.3s ease-in-out;
  box-shadow: none !important;
}
.form-control::-moz-placeholder, .css-1s2u09g-control::-moz-placeholder, .css-1pahdxg-control::-moz-placeholder, .kuby-react-select__control::-moz-placeholder {
  color: #A2A2A2;
}
.form-control::placeholder,
.css-1s2u09g-control::placeholder,
.css-1pahdxg-control::placeholder,
.kuby-react-select__control::placeholder {
  color: #A2A2A2;
}
.form-control:focus, .form-control:focus-within,
.css-1s2u09g-control:focus,
.css-1s2u09g-control:focus-within,
.css-1pahdxg-control:focus,
.css-1pahdxg-control:focus-within,
.kuby-react-select__control:focus,
.kuby-react-select__control:focus-within {
  outline: none;
  transition: all 0.3s ease-in-out;
  border-color: rgba(73, 149, 87, 0.2) !important;
}
.form-control.sm,
.css-1s2u09g-control.sm,
.css-1pahdxg-control.sm,
.kuby-react-select__control.sm {
  padding: 12px 16px;
}

.css-1s2u09g-control *,
.css-1pahdxg-control *,
.kuby-react-select__control {
  padding: 0px !important;
  margin: 0px !important;
}

.kuby-react-select__control .kuby-react-select__value-container {
  padding: 12px;
}

.sm .kuby-react-select__control .kuby-react-select__value-container {
  padding: 6.5px;
}

.css-9gakcf-option {
  background-color: #499557 !important;
}

.css-yt9ioa-option:hover {
  background-color: rgba(73, 149, 87, 0.5) !important;
}

.form-check {
  position: relative;
  padding: 0px;
  width: -moz-fit-content;
  width: fit-content;
}
.form-check .form-check-label {
  position: relative;
  background: #fff;
  color: #616161;
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 6px;
  padding: 9px 20px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.3;
  height: 31px;
  font-weight: 600;
}
@media (max-width: 480px) {
  .form-check .form-check-label {
    height: 0px;
  }
}
@media (max-width: 1200px) {
  .form-check .form-check-label {
    padding: 9px 11px;
    flex-wrap: wrap;
  }
}
.form-check .form-check-label .link {
  z-index: 20;
}
.form-check .form-check-input {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
  opacity: 0;
  cursor: pointer;
  margin: 0px;
}
.form-check input[type=checkbox] + .form-check-label {
  padding-left: 30px;
}
.form-check input[type=checkbox] + .form-check-label:after {
  content: "";
  position: absolute;
  top: 6px;
  left: 0px;
  width: 22px;
  height: 22px;
  text-align: center;
  background-color: #499557;
  border-radius: 4px;
  background: url(../images/all-icons.svg) no-repeat;
  background-position: -87px -29px;
  transform: scale(0);
  transform-origin: center;
  transition: all 0.3s ease;
}
.form-check input[type=checkbox] + .form-check-label::before {
  content: "";
  position: absolute;
  top: 6px;
  left: 0;
  width: 20px;
  height: 20px;
  text-align: center;
  background: none;
  border: 1px solid #bdbfd1;
  border-radius: 4px;
  transform-origin: center;
  transition: all 0.3s ease;
}
.form-check input[type=checkbox]:checked + .form-check-label:after {
  background: url(../images/all-icons.svg) no-repeat;
  background-position: -87px -29px;
  transform: scale(1);
  transform-origin: center;
  background-color: #499557;
}

.form-btn-wrpr,
.form-blk {
  margin-top: 56px;
}

.filter-select {
  color: #7E7E7E;
  font-family: "GS-medium";
}

.header {
  padding: 20px 30px;
  border: 1px solid #e7e8f2;
  z-index: 999;
}
@media (max-width: 767px) {
  .header {
    padding: 15px;
  }
}
.header .left-wrpr {
  gap: 15px;
}
.header.soulwriting_header_bar {
  padding: 6px 30px;
}
.header .header-btn-wrpr {
  gap: 12px;
}
.header .header-btn-wrpr .header-btn-inner {
  gap: 12px;
}
.header .header-btn-wrpr .header-btn-inner.header-btn-list {
  gap: 5px;
}
@media (max-width: 767px) {
  .header .header-btn-wrpr .header-btn-inner .btn {
    padding: 8px;
    gap: 0;
  }
  .header .header-btn-wrpr .header-btn-inner .btn .icon {
    margin-right: 0px;
  }
  .header .header-btn-wrpr .header-btn-inner .btn .btn-text {
    display: none;
  }
}
.header .header-btn-wrpr .header-btn-inner .header-btn-item {
  position: relative;
}
@media (max-width: 767px) {
  .header .header-btn-wrpr .header-btn-inner .header-btn-item.notification, .header .header-btn-wrpr .header-btn-inner .header-btn-item.wishlist, .header .header-btn-wrpr .header-btn-inner .header-btn-item.cart {
    display: none;
  }
}
.header .header-btn-wrpr .header-btn-inner .header-btn {
  background: none;
  border: 1px solid white;
  width: 40px;
  height: 40px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0, 0, 0.2, 1);
}
.header .header-btn-wrpr .header-btn-inner .header-btn:hover .icon.noti-icon, .header .header-btn-wrpr .header-btn-inner .header-btn.active .icon.noti-icon {
  background-position: -122px -120px;
}
.header .header-btn-wrpr .header-btn-inner .header-btn:hover .icon.wishlist-icon, .header .header-btn-wrpr .header-btn-inner .header-btn.active .icon.wishlist-icon {
  background-position: -189px -28px;
}
.header .header-btn-wrpr .header-btn-inner .header-btn:hover .icon.cart-icon, .header .header-btn-wrpr .header-btn-inner .header-btn.active .icon.cart-icon {
  background-position: -153px -2px;
}
.header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn {
  overflow: visible;
  position: relative;
  width: auto;
  word-break: keep-all;
  white-space: pre;
}
.header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn .user-name {
  font-size: 14px;
  line-height: 1.43;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 130px;
}
@media (max-width: 767px) {
  .header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn .user-name {
    display: none;
  }
}
.header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn .profile-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 8px;
}
.header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn::after {
  content: "";
  position: absolute;
  top: 12px;
  right: -18px;
  width: 15px;
  height: 15px;
  background: url(../images/all-icons.svg) no-repeat;
  background-position: -90px -61px;
  transform: scale(0.7);
}
@media (max-width: 767px) {
  .header .header-btn-wrpr .header-btn-inner .header-btn.profile-btn::after {
    right: -8px;
  }
}

.prof-dropdown-menu {
  background: #fff;
  box-shadow: 4px 4px 36px rgba(19, 61, 48, 0.1);
  border-radius: 8px;
  margin-top: 10px;
  position: absolute;
  top: auto;
  right: 0;
  min-width: 232px;
  visibility: hidden;
  opacity: 0;
  z-index: -99999;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
}
.prof-dropdown-menu.active {
  visibility: visible;
  opacity: 1;
  z-index: 1000;
  pointer-events: all;
  transition: all 0.2s ease-in-out;
}
.prof-dropdown-menu .prof-menu-wrpr {
  padding: 8px 0px;
  border-bottom: 1px solid #eef7f1;
}
.prof-dropdown-menu .prof-menu-wrpr:last-child {
  border: none;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-lbl {
  font-size: 12px;
  margin-bottom: 8px;
  padding: 0px 24px;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link {
  padding: 10px 24px;
  font-size: 14px;
  line-height: 1;
  font-family: "GS-medium";
  font-weight: 500;
  color: #616161;
  display: block;
  display: flex;
  align-items: center;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link:hover {
  color: #499557;
  background-color: #EEF7F1;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link:hover .icon {
  background-position-x: -274px;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link:hover .icon.profile-icon {
  background-position-x: -215px;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link:hover .icon.logout-icon {
  background-position-x: -275px;
}
.prof-dropdown-menu .prof-menu-wrpr .dropdown-link .icon {
  margin-right: 5px;
}

.language-select {
  border: none;
  padding: 12px;
  color: #7E7E7E;
  cursor: pointer;
  outline: none !important;
}

.inner-header {
  padding: 24px 0px;
  margin-left: 32px;
  margin-right: 32px;
  border-bottom: 1px solid #e0ede5;
  gap: 20px;
}
.inner-header.b-0 {
  border: none;
}
@media (max-width: 1200px) {
  .inner-header {
    padding: 15px 0px;
    gap: 15px;
    margin-left: 15px;
    margin-right: 15px;
  }
}
.inner-header .title-wrpr {
  gap: 12px;
}
.inner-header .title-wrpr .page-title {
  font-size: 24px;
  letter-spacing: -0.5px;
  color: #394445;
}
@media (max-width: 1400px) {
  .inner-header .title-wrpr .page-title {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .inner-header .title-wrpr .page-title {
    font-size: 18px;
  }
}
.inner-header .title-wrpr .title-icon {
  background-color: #e9f6f5;
  border-radius: 4px;
  width: 24px;
  height: 24px;
}
.inner-header .title-wrpr .title-icon.soul-icon {
  background-position: -26px -56px;
}
.inner-header .inner-head-btns-wrpr {
  position: relative;
}
.inner-header .inner-head-btns-wrpr .filter-btn {
  display: none;
}
@media (max-width: 1200px) {
  .inner-header .inner-head-btns-wrpr .filter-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 40px;
    border: 1px solid #499557;
    background: rgba(73, 149, 87, 0.2);
  }
}
@media (max-width: 480px) {
  .inner-header .inner-head-btns-wrpr .filter-btn {
    width: 34px;
    height: 34px;
  }
}
.inner-header .inner-header-btns {
  gap: 14px;
}
@media (max-width: 1200px) {
  .inner-header .inner-header-btns {
    width: 240px;
    height: 0px;
    padding: 0px;
    overflow: hidden;
    position: absolute;
    right: 0px;
    flex-direction: column;
    background: white;
    align-items: flex-start;
    border: none;
    border-radius: 10px;
    z-index: 1;
    transition: all 0.3s ease-in-out;
  }
}
.inner-header .inner-header-btns.active {
  height: auto;
  padding: 30px 15px;
  border: 1px solid #A2A2A2;
  transition: all 0.3s ease-in-out;
}
.inner-header .header-filter {
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 4px;
  padding: 10px 14px;
  gap: 8px;
}
@media all and (max-width: 1350px) and (min-width: 1201px) {
  .inner-header .header-filter {
    padding: 8px 5px;
  }
}
.inner-header .filter-select {
  border: none;
  outline: none !important;
  cursor: pointer;
}

.left-sidebar {
  flex: 0 0 230px;
  padding: 32px 17px;
  border-right: 1px solid #e7e8f2;
  transition: all 0.3s ease-in-out;
  position: relative;
  z-index: 3;
}
@media (max-width: 767px) {
  .left-sidebar {
    position: absolute;
    z-index: 999;
    background: #fff;
    width: 100%;
    height: 100%;
    transform: translateX(-100%);
  }
}
.left-sidebar .nav-list {
  gap: 10px;
}
.left-sidebar .nav-list .nav-link {
  padding: 10px;
  width: 100%;
  position: relative;
  transition: all 0.3s ease-in-out;
}
.left-sidebar .nav-list .nav-link:before {
  content: "";
  position: absolute;
  top: 11px;
  left: 10px;
  right: 0;
  bottom: 0;
  width: 24px;
  z-index: 1;
  height: 24px;
  background: #f4faf9;
  border-radius: 4px;
  opacity: 0;
  transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
}
.left-sidebar .nav-list .nav-link.active, .left-sidebar .nav-list .nav-link:hover {
  color: #499557 !important;
  font-weight: 500;
}
.left-sidebar .nav-list .nav-link.active::before, .left-sidebar .nav-list .nav-link:hover::before {
  opacity: 1;
  transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
}
.left-sidebar .nav-list .nav-link.active .nav-icon, .left-sidebar .nav-list .nav-link:hover .nav-icon {
  background-position-x: -30px !important;
}
.left-sidebar .nav-list .nav-link .nav-link-text {
  width: -moz-fit-content;
  width: fit-content;
  opacity: 1;
  transition: all 0.2s ease-in-out;
}
.left-sidebar.closed {
  flex: 0 0 78px;
  padding: 32px 12px;
  transition: all 0.3s ease-in-out;
}
.left-sidebar.closed .nav-link {
  margin: 0 auto;
  min-width: 44px;
  width: 44px !important;
  height: 44px;
  transition: all 0.3s ease-in-out;
}
.left-sidebar.closed .nav-link:hover, .left-sidebar.closed .nav-link.active {
  background-color: #daece1;
  border-radius: 8px;
}
.left-sidebar.closed .nav-link .nav-link-text {
  width: 0px;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}
.left-sidebar.closed .nav-link .nav-icon {
  margin-right: 0px;
}
.left-sidebar.active {
  transform: translateX(0);
}

.nav-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-image: url(../images/nav-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  margin-right: 10px;
  z-index: 2;
}
.nav-icon.reception {
  background-position: 0px 0px;
}
.nav-icon.practice {
  background-position: 0px -28px;
}
.nav-icon.calender {
  background-position: 0px -57px;
}
.nav-icon.companions {
  background-position: 0px -85px;
}
.nav-icon.soul {
  background-position: 0px -113px;
}
.nav-icon.shop {
  background-position: 0px -141px;
}
.nav-icon.meeting {
  background-position: 0px -197px;
}

.right-section {
  flex: 1 1 calc(100% - 230px);
  background-color: #fcfcfc;
  width: 100%;
  height: calc(100vh - 82px);
  position: relative;
  transition: all 0.3s ease-in-out;
}
.right-section.expand {
  flex: 0 0 calc(100% - 78px);
  max-width: calc(100% - 78px);
  transition: all 0.3s ease-in-out;
}
.right-section.expand.right-sidebar-fullwidth {
  flex: 1 1 100%;
  max-width: 100%;
}
@media (max-width: 767px) {
  .right-section.expand {
    flex: 1 1 auto;
    max-width: 100%;
  }
}
.right-section.right-section_soulwriting {
  height: calc(100vh - 55px);
}
@media (max-width: 767px) {
  .right-section.right-section_soulwriting {
    height: calc(100vh - 55px);
  }
}

.lock-icon-wrpr {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.lock-icon-wrpr.top .lock {
  position: absolute;
  top: 8px;
  right: 8px;
}
.lock-icon-wrpr .lock {
  width: 40px;
  height: 40px;
  background: rgba(172, 65, 57, 0.8);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url(../images/all-icons.svg);
  background-position: -49px -50px;
  background-repeat: no-repeat;
}
.lock-icon-wrpr .lock.unlock {
  background-position: -18px -133px;
}
.lock-icon-wrpr .lock.check {
  background: rgba(73, 149, 87, 0.7215686275);
  background-image: url(../images/all-icons.svg);
  background-repeat: no-repeat;
  background-position: -88px -30px;
  width: 20px;
  height: 20px;
  scale: 2;
  right: 18px;
  top: 18px;
}

.product-wrpr {
  gap: 22px;
}
.product-wrpr .product-card {
  background: #ffffff;
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 0px;
  gap: 20px;
  flex: 1 1 calc(50% - 24px);
  max-width: calc(50% - 12px);
}
@media (max-width: 1300px) {
  .product-wrpr .product-card {
    flex-direction: column;
    gap: 0;
    flex: 1 1 calc(50% - 12px);
    max-width: calc(50% - 12px);
  }
}
@media (max-width: 767px) {
  .product-wrpr .product-card {
    flex-direction: column;
    flex: 1 1 100%;
    max-width: unset;
  }
}
.product-wrpr .product-card .product-image-wrpr {
  width: 100%;
  height: 100%;
  max-width: 210px;
  min-width: 180px;
  max-height: 240px;
  border-radius: 5px;
  overflow: hidden;
  padding: 12px;
}
@media (max-width: 1200px) {
  .product-wrpr .product-card .product-image-wrpr {
    max-width: unset;
    max-height: 200px;
    padding: 12px;
  }
}
.product-wrpr .product-card .product-image-wrpr img {
  filter: drop-shadow(0px 2px 4px #aaa);
}
.product-wrpr .product-card .product-details-wrpr {
  flex: 1 1 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: flex-start;
  gap: 20px;
  padding: 22px 0px;
  justify-content: space-between;
}
@media (max-width: 1200px) {
  .product-wrpr .product-card .product-details-wrpr {
    width: 100%;
    flex: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0px 12px 12px;
  }
}
.product-wrpr .product-card .product-top-wrpr {
  gap: 10px;
}
.product-wrpr .product-card .product-top-wrpr .product-cat {
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.67;
}
.product-wrpr .product-card .product-top-wrpr .product-cat .cat-name {
  font-weight: 500;
  color: #003b4d;
  font-family: "inter-semibold", sans-serif;
}
.product-wrpr .product-card .product-top-wrpr .product-title {
  font-size: 18px;
  line-height: 22px;
}
.product-wrpr .product-card .product-top-wrpr .product-desc {
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.22;
}
.product-wrpr .product-card .product-bottom-wrpr {
  gap: 12px;
  width: 100%;
}
@media (max-width: 767px) {
  .product-wrpr .product-card .product-bottom-wrpr {
    width: 100%;
    margin-top: 10px;
  }
}
.product-wrpr .product-card .product-bottom-wrpr .product-price {
  font-size: 24px;
  line-height: 1.17;
  color: #666773;
}

.prod-btn-wrpr {
  gap: 12px;
}
@media (max-width: 767px) {
  .prod-btn-wrpr {
    width: 100%;
  }
}
.prod-btn-wrpr .prod-btn {
  padding: 10px;
  white-space: pre;
}
.prod-btn-wrpr .cart-btn {
  gap: 5px;
}
.prod-btn-wrpr .wishlist-btn {
  gap: 5px;
}

.prod-details-wrpr {
  padding: 0px 32px;
}
@media (max-width: 767px) {
  .prod-details-wrpr {
    padding: 0px 15px;
  }
}
.prod-details-wrpr .prod-main-section {
  margin-top: 10px;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .prod-details-wrpr .prod-main-section {
    flex-direction: column;
    margin-bottom: 10px;
  }
}
.prod-details-wrpr .prod-main-section .product-main-img {
  flex: 0 0 20%;
  padding-right: 20px;
}
@media (max-width: 992px) {
  .prod-details-wrpr .prod-main-section .product-main-img {
    flex: 0 0 33%;
  }
}
@media (max-width: 767px) {
  .prod-details-wrpr .prod-main-section .product-main-img {
    flex: 1 1 auto;
    padding-right: 0px;
    overflow: hidden;
    width: 300px;
    max-height: 350px;
    margin: 0 auto;
  }
}
.prod-details-wrpr .prod-main-section .prod-right-block {
  flex: 1 1 auto;
  padding-left: 32px;
  padding-right: 50px;
}
@media (max-width: 992px) {
  .prod-details-wrpr .prod-main-section .prod-right-block {
    padding: 0px 10px;
  }
}
@media (max-width: 767px) {
  .prod-details-wrpr .prod-main-section .prod-right-block {
    padding: 0px;
    margin-top: 10px;
  }
}
.prod-details-wrpr .prod-main-section .audio-option-block .label {
  margin-bottom: 8px;
}
.prod-details-wrpr .audio-opt-wrpr {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}
.prod-details-wrpr .audio-opt-wrpr .form-group .audio-input {
  opacity: 0;
  position: absolute;
}
.prod-details-wrpr .audio-opt-wrpr .form-group .audio-input:checked + .audio-label {
  background: #499557;
  color: white;
  font-size: 14px;
  font-weight: 600;
}
.prod-details-wrpr .audio-opt-wrpr .form-group .audio-input:checked + .audio-label .dvd-icon {
  background-position: 7px 2px;
}
.prod-details-wrpr .audio-opt-wrpr .form-group .audio-input:checked + .audio-label .stream-icon {
  background-position: -50px 2px;
}
.prod-details-wrpr .audio-opt-wrpr .form-group .audio-label {
  background: #f0f0f0;
  border-radius: 10px;
  padding: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 72px;
  height: 88px;
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: -0.12px;
  color: #616161;
  cursor: pointer;
}
.prod-details-wrpr .product-desc-block {
  margin-bottom: 20px;
}
.prod-details-wrpr .product-desc-block .prod-title {
  margin-bottom: 10px;
  color: #262626;
}
@media (max-width: 767px) {
  .prod-details-wrpr .product-desc-block .prod-title {
    display: none;
  }
}
.prod-details-wrpr .product-desc-block .prod-desc {
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  color: #404040;
}
.prod-details-wrpr .price-block {
  margin-bottom: 20px;
}
.prod-details-wrpr .price-block .price-text {
  font-size: 14px;
  line-height: 1.43;
  color: #616161;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.prod-details-wrpr .price-block .price-text .prod-price {
  margin-left: 6px;
  font-size: 24px;
  line-height: 1.17;
  color: #021416;
}
.prod-details-wrpr .price-block .price-btn-quantity {
  gap: 40px;
}
.prod-details-wrpr .price-block .price-btn-quantity .prod-btn {
  min-width: 120px;
}
.prod-details-wrpr .version-wrpr {
  margin-bottom: 32px;
}
.prod-details-wrpr .version-wrpr .version-text {
  color: #404040;
  margin-bottom: 5px;
}
.prod-details-wrpr .other-language-wrpr {
  margin-bottom: 32px;
}
.prod-details-wrpr .other-language-wrpr .p {
  margin-bottom: 10px;
}
.prod-details-wrpr .other-language-wrpr .language-link {
  font-size: 14px;
  line-height: 1.43;
}
.prod-details-wrpr .tags-wrpr {
  margin-bottom: 10px;
}
.prod-details-wrpr .tags-wrpr .p .prod-tags {
  color: #003b4d;
}
.prod-details-wrpr .prod-tabs-wrpr {
  margin-top: 24px;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr {
  gap: 30px;
  border-bottom: 1px solid #daece1;
  margin-bottom: 20px;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr .tab-item:first-child .tab-link {
  padding-left: 0px;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr .tab-link {
  padding: 12px 10px;
  color: #7E7E7E;
  font-size: 24px;
  font-family: "Inter", sans-serif;
  display: block;
  position: relative;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr .tab-link::before {
  content: "";
  position: absolute;
  top: auto;
  bottom: -1px;
  width: 100%;
  height: 2px;
  background: #021416;
  border-radius: 10px;
  display: none;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr .tab-link.active {
  color: #07080A;
  font-weight: 600;
}
.prod-details-wrpr .prod-tabs-wrpr .tab-list-wrpr .tab-link.active::before {
  display: block;
}
.prod-details-wrpr .prod-video-wrpr {
  gap: 24px;
  margin-bottom: 72px;
}
.prod-details-wrpr .prod-video-wrpr .prod-video-block {
  background: #ffffff;
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 8px 8px 20px;
}
.prod-details-wrpr .prod-video-wrpr .prod-video-block .prod-video {
  height: 200px;
  position: relative;
}
.prod-details-wrpr .prod-video-wrpr .prod-video-block .prod-video-title {
  padding-top: 12px;
  font-size: 16px;
  line-height: 1.25;
  color: #404040;
}
.prod-details-wrpr .desc-wrpr {
  margin-bottom: 75px;
}
.prod-details-wrpr .desc-wrpr .prod-desc {
  font-family: "inter", sans-serif;
  font-size: 16px;
  line-height: 1.25;
  color: #616161;
  margin-bottom: 30px;
}
.prod-details-wrpr .title {
  font-size: 18px;
  line-height: 1.25;
  letter-spacing: -0.06px;
}
.prod-details-wrpr .similar-prod-row {
  gap: 30px;
  margin: 30px 0px;
}
.prod-details-wrpr .similar-prod-row .similar-prod {
  background: #ffffff;
  box-shadow: 0px 3.65448px 25.5814px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 8px 8px 20px;
  flex: 0 0 20%;
}
.prod-details-wrpr .similar-prod-row .similar-prod .prod-title {
  margin-top: 12px;
  font-size: 16px;
  line-height: 1.25;
}

.play-icon-wrpr {
  position: absolute;
  top: 40%;
  left: 36%;
  max-width: 100%;
  max-height: 100%;
  background-color: rgba(94, 179, 94, 0.8);
  border: 1px solid rgba(94, 179, 94, 0.4);
  box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.16);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  border-radius: 8px;
  width: 60px;
  height: 46px;
}

.cd-breadcrumb {
  padding: 24px 0px;
}
@media (max-width: 767px) {
  .cd-breadcrumb {
    flex-wrap: wrap;
    display: none;
  }
}
.cd-breadcrumb .breadcumb-item {
  padding-right: 5px;
}
.cd-breadcrumb .breadcumb-item.current {
  color: #499557;
  font-size: 12px;
  line-height: 1.33;
  font-family: "GS-medium";
  top: 1px;
  position: relative;
}
.cd-breadcrumb .breadcumb-item .breadcumb-link {
  padding-right: 20px;
  position: relative;
  font-size: 12px;
  line-height: 1.33;
  color: #A2A2A2;
  font-family: "GS-medium";
  white-space: pre;
}
.cd-breadcrumb .breadcumb-item .breadcumb-link:before {
  content: "";
  position: absolute;
  top: 0px;
  right: 0;
  bottom: 0;
  background: url(../images/all-icons.svg) no-repeat;
  width: 15px;
  height: 15px;
  background-position: -91px -60px;
  rotate: -90deg;
}

.quantity-wrpr {
  background: #f0f0f0;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}
.quantity-wrpr .quantity-btn {
  width: 40px;
  height: 34px;
  font-size: 20px;
  border: none;
  outline: none;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
}
.quantity-wrpr .quantity-value {
  min-width: 45px;
  text-align: center;
  font-size: 20px;
}

.companion-tab-wrpr {
  padding: 0px 32px;
}
@media (max-width: 767px) {
  .companion-tab-wrpr {
    padding: 0px 15px;
  }
}
.companion-tab-wrpr .companion-tab {
  justify-content: start;
  gap: 30px;
}
.companion-tab-wrpr .companion-tab .tab-item:first-child .tab-link {
  padding-left: 0px;
}
.companion-tab-wrpr .companion-tab .tab-item .tab-link {
  font-family: "GS-medium";
  font-weight: 500;
  color: #7E7E7E;
}
.companion-tab-wrpr .companion-tab .tab-item .tab-link.active {
  color: #07080A;
}
.companion-tab-wrpr .tab-contents {
  padding-top: 40px;
  display: none;
}
.companion-tab-wrpr .tab-contents.active {
  display: flex;
}
@media (max-width: 1200px) {
  .companion-tab-wrpr .tab-contents {
    padding-top: 24px;
  }
}

.companion-card-row {
  gap: 12px;
  flex-wrap: wrap;
}

.companion-choose-modal {
  max-width: 1100px !important;
}

.comment-text strong {
  font-weight: bold;
}
.comment-text i {
  font-style: italic;
}
.comment-text ul,
.comment-text ol {
  padding-left: 20px;
}

.companion-card {
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  padding: 8px 8px 24px;
  flex: 0 0 32%;
  max-width: 32%;
}
@media (max-width: 1200px) {
  .companion-card {
    flex: 0 0 48%;
    max-width: 48%;
  }
}
@media (max-width: 992px) {
  .companion-card {
    flex: 0 0 100%;
    max-width: unset;
  }
}
.companion-card .companion-details {
  background: #fafafc;
  border-radius: 8px;
  padding: 20px 16px;
  margin-bottom: 12px;
}
@media (max-width: 480px) {
  .companion-card .companion-details {
    padding: 12px 8px;
  }
}
.companion-card .companion-details .comp-img {
  width: 124px;
  height: 124px;
  border-radius: 50%;
  margin: 0 auto 10px;
  overflow: hidden;
}
.companion-card .companion-details .btn-tertiary-wrpr {
  gap: 12px;
  margin-top: 24px;
}
.companion-card .companion-details .btn-tertiary-wrpr .btn-tertiary {
  padding: 10px 16px;
  font-size: 12px;
  font-weight: 500;
}
@media (max-width: 480px) {
  .companion-card .companion-details .btn-tertiary-wrpr .btn-tertiary {
    padding: 10px 10px;
  }
}
.companion-card .comp-card-button-wrpr {
  padding: 0px 24px;
  gap: 12px;
  display: flex;
  flex-direction: column;
}
@media (max-width: 480px) {
  .companion-card .comp-card-button-wrpr {
    padding: 0px 15px;
  }
}

.comp-name-wrpr {
  gap: 8px;
  margin-bottom: 4px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
}
.modal.show {
  opacity: 1;
  visibility: visible;
  z-index: 999999;
}
.modal .overlay-div {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.modal .calendar-overlay-div {
  content: "";
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.modal .modal-dialog {
  background: white;
  box-shadow: 4px 4px 36px rgba(2, 23, 17, 0.1);
  border-radius: 6px;
  max-width: 840px;
  margin: 0 auto;
  z-index: 2001;
  position: relative;
  height: 100%;
}
.modal.change-companion-confirmation .modal-dialog {
  max-width: 600px;
}
.modal.change-companion-confirmation p {
  line-height: 2;
  margin-bottom: 10px;
}

.close-btn {
  background: transparent;
  border-radius: 50px;
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
.close-btn .close-icon {
  transform: scale(1.5);
}

.modal-content {
  max-height: 90vh;
  height: 100%;
}
@media (max-width: 767px) {
  .modal-content {
    max-height: 90vh;
  }
}

.modal-body {
  height: calc(100% - 304px);
  overflow: auto;
  padding: 24px 40px;
}
@media (max-width: 767px) {
  .modal-body {
    padding: 15px 20px;
  }
}

.schedule-modal .modal-content {
  min-width: 850px;
}
@media (max-width: 1200px) {
  .schedule-modal .modal-content {
    min-width: unset;
    max-width: 90vw;
  }
}
.schedule-modal .modal-body {
  height: calc(100% - 224px);
}
@media (max-width: 767px) {
  .schedule-modal .modal-body {
    height: calc(100% - 185px);
  }
}

.video-modal {
  padding: 0px 20px;
}
.video-modal .view_video {
  max-height: 340px;
  min-height: 340px;
  margin-bottom: 10px;
}
.video-modal .view_video > div {
  height: 340px;
}
.video-modal .view_video > div iframe {
  height: 100%;
}
.video-modal .modal-content {
  max-height: unset;
  height: auto;
  min-width: 600px;
  z-index: 1;
  max-height: unset;
  height: auto;
  min-width: 600px;
  z-index: 1;
  background: #fff;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
}
@media (max-width: 767px) {
  .video-modal .modal-content {
    align-items: baseline;
  }
}
.video-modal .modal-content .modal-body {
  width: 100%;
}
@media (max-width: 767px) {
  .video-modal .modal-content {
    min-width: 100%;
  }
}
@media (max-width: 767px) {
  .video-modal .modal-content .view_video {
    min-height: 210px;
    height: 210px;
    border-radius: 0;
  }
  .video-modal .modal-content .view_video > div {
    height: 100%;
  }
}
@media (max-width: 767px) {
  .video-modal .modal-content .view_video iframe {
    height: 100%;
  }
}
@media (max-width: 767px) {
  .video-modal .modal-content .continue-wrap {
    padding: 0px 10px;
  }
}
@media (max-width: 767px) {
  .video-modal .modal-content .video-description {
    padding: 10px;
  }
}
.video-modal .modal-content .video-title {
  padding: 0px 0px 40px;
  position: relative;
  padding-top: 20px;
}
@media (max-width: 767px) {
  .video-modal .modal-content .video-title {
    overflow: hidden;
    padding-top: 20px;
  }
}
.video-modal .modal-content .video-title::before {
  content: "";
  position: absolute;
  left: -40px;
  height: 1px;
  width: calc(100% + 80px);
  background: #eee;
  bottom: 18px;
}
.video-modal .modal-content .video-title h3 {
  text-align: center;
  letter-spacing: 0.5px;
}
@media (max-width: 767px) {
  .video-modal .modal-content .video-title h3 {
    font-size: 20px;
  }
}
.video-modal .modal-content .video-skeleton {
  max-width: 720px;
  height: 340px;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  width: 100%;
}
@media (max-width: 767px) {
  .video-modal .modal-content .video-skeleton {
    height: 360px;
  }
}

@media (max-width: 767px) {
  .soulIntro .modal-content {
    height: calc(100vh - 85px);
  }
}
.soulIntro .modal-content .soulIntro_video iframe {
  width: 100%;
}
@media (max-width: 767px) {
  .soulIntro .modal-content .soulIntro_video iframe {
    width: 100%;
    background: #000;
    height: 380px;
  }
}
@media (max-width: 767px) {
  .soulIntro .modal-content .video-description .desc {
    max-height: 200px;
  }
}

.soul-vedio iframe {
  width: 100%;
}
.modal-header {
  padding: 24px 40px;
  border-bottom: 1px solid #daece1;
}
@media (max-width: 767px) {
  .modal-header {
    padding: 15px 20px;
  }
}

.profile-top .profile-details-wrpr {
  padding: 0px 12px;
  gap: 12px;
}
@media (max-width: 767px) {
  .profile-top .profile-details-wrpr {
    flex-wrap: wrap;
  }
}
.profile-top .profile-details-wrpr .icon {
  margin-right: 8px;
}

.profile-card .comp-img {
  width: 80px;
  min-width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.modal-footer {
  padding: 24px 40px;
  border-top: 1px solid #f0f0f0;
}
@media (max-width: 767px) {
  .modal-footer {
    flex-direction: column;
    padding: 8px;
    gap: 4px;
  }
}
.modal-footer .modal-btn-wrpr {
  gap: 16px;
}
.modal-footer .modal-btn-wrpr .btn-accent {
  white-space: pre;
  word-break: keep-all;
  overflow: inherit;
}

.comp-about-title {
  margin-bottom: 8px;
  font-size: 16px;
  line-height: 1.25;
  font-weight: 500;
  font-family: "GS-medium";
}

.comp-about {
  margin-bottom: 32px;
}

.profile-modal .modal-content {
  height: auto;
}
@media (max-width: 767px) {
  .profile-modal .modal-content {
    max-width: 90vw;
  }
}
.profile-modal .modal-content .modal-body {
  max-height: calc(100vh - 400px);
}
@media (max-width: 767px) {
  .profile-modal .modal-content .modal-body {
    max-height: calc(100vh - 540px);
  }
}

.soulwriting-modal .modal-content {
  height: auto;
  max-width: 90vw;
}

@media (max-width: 767px) {
  .more-info-modal {
    padding: 0px 20px;
  }
}
.more-info-modal .modal-content {
  max-height: 80vh;
  height: 100%;
}
@media (max-width: 767px) {
  .more-info-modal .modal-content {
    width: 100%;
  }
}
.more-info-modal .modal-body {
  max-height: 100%;
  height: calc(100% - 75px);
}

.profile-info-wrpr {
  background: #fafafc;
  border-radius: 8px;
  padding: 20px 16px;
  margin-bottom: 24px;
}
@media (max-width: 767px) {
  .profile-info-wrpr {
    flex-direction: column;
    gap: 10px;
  }
}

.prof-details-wrpr {
  padding: 0px 32px;
  overflow: auto;
  height: auto !important;
  min-height: calc(100% - 80px);
}
@media (max-width: 1200px) {
  .prof-details-wrpr {
    padding: 0px 20px;
  }
}

.prof-tabs-wrpr .tab-contents {
  margin-top: 40px;
  padding-bottom: 40px;
  display: none;
}
.prof-tabs-wrpr .tab-contents.active {
  display: block;
}
.prof-tabs-wrpr .profile-tab-wrpr {
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  padding: 6px 40px;
}
@media (max-width: 1200px) {
  .prof-tabs-wrpr .profile-tab-wrpr {
    padding: 6px 20px;
  }
}
.prof-tabs-wrpr .profile-tab-wrpr .profile-header {
  border-bottom: 1px solid #e0e0e0;
  padding-top: 18px;
  padding-bottom: 18px;
  gap: 20px;
}
.prof-tabs-wrpr .profile-tab-wrpr .profile-header .title {
  color: #021416;
}
.prof-tabs-wrpr .profile-tab-wrpr .profile-header .profile-btns {
  gap: 20px;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.prof-tabs-wrpr .profile-tab-wrpr .profile-info-wrpr {
  margin: 32px 0px;
}
.prof-tabs-wrpr .prof-change-pass {
  margin-top: 46px;
  display: flex;
  align-items: center;
}
.prof-tabs-wrpr .join-date-wrpr {
  background: #eeeef7;
  border: 1px solid #eeeef6;
  border-radius: 8px;
  min-width: 284px;
  padding: 12px;
}
.prof-tabs-wrpr .profile-others {
  padding-bottom: 30px;
  gap: 60px;
}
.prof-tabs-wrpr .profile-others .other-details {
  min-width: 300px;
  max-width: 300px;
}
.prof-tabs-wrpr .profile-others .other-details:last-child {
  max-width: unset;
}
.prof-tabs-wrpr .profile-others .other-details .prof-label {
  margin-bottom: 2px;
}
.prof-tabs-wrpr .profile-others .other-details .lang-block {
  flex-wrap: wrap;
}
.prof-tabs-wrpr .profile-others .other-details .lang-block .lang-wrpr {
  gap: 8px;
}
.prof-tabs-wrpr .profile-form .form-wrpr {
  padding-bottom: 20px;
}
@media (max-width: 992px) {
  .prof-tabs-wrpr .profile-form .form-wrpr {
    flex-direction: column;
  }
}
.prof-tabs-wrpr .form-btns-wrpr {
  padding: 38px 0px;
  border-top: 1px solid #f0f0f0;
}
.prof-tabs-wrpr .form-btns-wrpr .btn-wrpr {
  gap: 16px;
}

.profile-form .profile-form-left {
  flex: 0 0 30%;
}
.profile-form .profile-form-right {
  flex: 1;
}
.profile-form .profile-form-right .f-row {
  gap: 12px;
}

.tab-list-wrpr {
  gap: 18px;
  margin-left: -10px;
  border-bottom: 1px solid #daece1;
  padding-bottom: 13px;
  margin-top: 10px;
  overflow: auto;
}
@media (max-width: 992px) {
  .tab-list-wrpr {
    border-bottom: 0px;
  }
}
@media (max-width: 767px) {
  .tab-list-wrpr {
    margin-left: 0px;
  }
}
.tab-list-wrpr .tab-link {
  font-family: "GS-medium";
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: #7E7E7E;
  padding: 10px;
  position: relative;
  white-space: pre;
}
.tab-list-wrpr .tab-link:after {
  content: "";
  position: absolute;
  top: auto;
  bottom: 3px;
  left: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background: black;
  opacity: 0;
}
.tab-list-wrpr .tab-link:hover, .tab-list-wrpr .tab-link.active {
  color: #021416;
}
.tab-list-wrpr .tab-link:hover:after, .tab-list-wrpr .tab-link.active:after {
  opacity: 1;
}

.remove-img {
  width: 100%;
  text-align: center;
  padding: 10px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.change-pass-wrpr {
  gap: 40px;
}
@media (max-width: 1200px) {
  .change-pass-wrpr {
    flex-direction: column;
    gap: 10px;
  }
  .change-pass-wrpr .w-50 {
    width: 100%;
  }
}

.comp-reception-row {
  padding: 40px 32px 0px;
}
@media (max-width: 1200px) {
  .comp-reception-row {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  .comp-reception-row {
    padding: 30px 20px 0px;
  }
}
.comp-reception-row .com-row-left {
  max-width: 75%;
  flex: 0 0 75%;
  padding-right: 16px;
}
@media (max-width: 1200px) {
  .comp-reception-row .com-row-left {
    max-width: 100%;
    flex: 0 0 100%;
    padding-right: 0px;
  }
}
.comp-reception-row .com-row-right {
  max-width: 25%;
  flex: 0 0 25%;
  padding-left: 16px;
}
@media (max-width: 1200px) {
  .comp-reception-row .com-row-right {
    max-width: 60%;
    flex: 0 0 100%;
    padding-left: 0px;
  }
}
@media (max-width: 480px) {
  .comp-reception-row .com-row-right {
    max-width: 100%;
  }
}

.reception-row {
  padding: 6px 32px;
}
@media (max-width: 1200px) {
  .reception-row {
    flex-direction: column;
    padding: 6px 20px;
  }
}
.reception-row .row-left,
.reception-row .row-right {
  flex: 0 0 50%;
  max-width: 50%;
  padding-right: 28px;
}
@media (max-width: 1200px) {
  .reception-row .row-left,
  .reception-row .row-right {
    max-width: 100%;
    flex: 0 0 100%;
    padding-right: 0px;
  }
}
.reception-row .row-right {
  padding-left: 28px;
  padding-right: 0px;
}
@media (max-width: 992px) {
  .reception-row .row-right {
    padding-left: 0px;
  }
}

.comp_slider {
  width: 100%;
  margin: 0px -8px;
  position: relative;
}
.comp_slider .slick-slide {
  padding: 0px 8px;
}
.comp_slider .cstm_slick_arrow {
  background: #ffffff;
  box-shadow: 0px 4px 20px rgba(8, 55, 47, 0.08);
  border-radius: 8px;
  border: none;
  position: absolute;
  top: 40%;
  left: -10px;
  z-index: 99;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.comp_slider .cstm_slick_arrow.slick_next {
  left: auto;
  right: -10px;
  transform: rotate(180deg);
}
.comp_slider .slick-list {
  overflow: hidden;
  padding: 24px 0px 32px;
}
.comp_slider .comp-card {
  background: #ffffff;
  box-shadow: 0px 4.10869px 28.7608px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 18px;
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  text-align: center;
}
.comp_slider .comp-card .comp-img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0px auto 12px;
}
.comp_slider .comp-card .comp-name {
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: #07080a;
  margin-bottom: 4px;
}
.comp_slider .comp-card .comp-title {
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #404040;
  margin-bottom: 8px;
}
.comp_slider .comp-card .flag-main-wrpr {
  gap: 8px;
  margin-bottom: 26px;
}
.comp_slider .comp-card .comp-price {
  display: flex;
  align-items: center;
  color: #666773;
  font-size: 16px;
}
.comp_slider .comp-card .comp-price .price-value {
  margin-right: 3px;
  font-weight: 600;
}
.comp_slider .comp-card .comp-price-details {
  margin-top: 10px;
}

.meeting-block {
  padding-top: 56px;
  position: relative;
}
@media (max-width: 1200px) {
  .meeting-block {
    padding-top: 20px;
  }
}

.meeting-card-wrpr {
  gap: 8px;
  box-shadow: 0px 4.10869px 28.7608px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
  padding: 8px 4px 4px;
  margin-bottom: 26px;
  position: relative;
}
.meeting-card-wrpr .heading-row {
  padding: 0px 15px;
}
.meeting-card-wrpr .heading-row .img-card-title {
  font-size: 12px;
}
.meeting-card-wrpr .meeting-card {
  gap: 16px;
  background: #fafafc;
  border-radius: 4px;
  padding: 10px;
}
.meeting-card-wrpr .meeting-card .meeting-name {
  font-size: 12px;
}
.meeting-card-wrpr .meeting-card .meeting-desc {
  font-size: 10px;
}
.meeting-card-wrpr .meeting-card .meeting-mnth {
  padding: 0px 10px;
  background: #538137;
  font-size: 12px;
  border-radius: 3px 3px 0px 0px;
}
.meeting-card-wrpr .meeting-card .meeting-date {
  background: #6e9d51;
  box-shadow: 0px 2.72134px 19.0494px rgba(102, 111, 167, 0.08);
  transform: skewX(5deg) translateX(1px);
  border-radius: 0px 0px 3px 3px;
  font-size: 16px;
  padding: 2px 12px;
}
.meeting-card-wrpr .meeting-date-wrpr {
  border-radius: 5px;
  background: linear-gradient(0deg, #163007, #163007), #163007;
  box-shadow: 0px 2.72134px 19.0494px rgba(102, 111, 167, 0.08);
  text-align: center;
}
.meeting-card-wrpr .lock-icon-wrpr {
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  border-radius: 8px;
}

.reception-card-block {
  margin-top: 24px;
  gap: 16px;
  flex-wrap: wrap;
}
@media (max-width: 1400px) {
  .reception-card-block {
    flex-wrap: wrap;
  }
}
.reception-card-block .img-name-card {
  padding: 10px 10px 20px;
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  flex: 0 0 calc(33% - 10px);
  max-width: 220px;
}
@media (max-width: 1400px) {
  .reception-card-block .img-name-card {
    max-width: 100 !important;
    flex: 0 0 47%;
  }
}
@media (max-width: 767px) {
  .reception-card-block .img-name-card {
    max-width: 100% !important;
    flex: 0 0 calc(50% - 10px);
  }
}
@media (max-width: 480px) {
  .reception-card-block .img-name-card {
    max-width: 100% !important;
    flex: 0 0 100%;
  }
}
.reception-card-block .img-name-card .h6 {
  min-height: 43px;
}
.reception-card-block .img-name-card .img-card {
  margin-bottom: 12px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}
.reception-card-block .img-name-card .img-card .cover-img {
  -o-object-fit: contain;
     object-fit: contain;
  width: 100%;
  height: 100%;
}

.shop-title-row {
  padding-top: 40px;
  padding-bottom: 30px;
}

.shop-card-wrpr {
  gap: 55px 2px;
  margin-top: 30px;
  justify-content: flex-start;
  align-items: baseline;
  flex-wrap: nowrap;
}
@media (max-width: 1400px) {
  .shop-card-wrpr {
    flex-wrap: wrap;
  }
}
@media (max-width: 767px) {
  .shop-card-wrpr {
    justify-content: center;
  }
}
.shop-card-wrpr .shop-card {
  border-radius: 24px;
  /* width: 16%; */
  flex: 0 0 16.6666666667%;
  max-width: 230px;
  min-width: 180px;
}
@media (max-width: 1400px) {
  .shop-card-wrpr .shop-card {
    flex-wrap: wrap;
    width: 31%;
  }
}
@media (max-width: 1200px) {
  .shop-card-wrpr .shop-card {
    width: 48%;
    flex: 1;
  }
}
@media (max-width: 767px) {
  .shop-card-wrpr .shop-card {
    width: 100%;
    flex: 1;
  }
}
.shop-card-wrpr .shop-card.card1 {
  background: #ebdede;
}
.shop-card-wrpr .shop-card.card2 {
  background: #e0e5eb;
}
.shop-card-wrpr .shop-card.card3 {
  background: #ececf2;
}
.shop-card-wrpr .shop-card .prod-img {
  filter: drop-shadow(-4.54819px 2.18313px 7.27711px rgba(0, 0, 0, 0.25));
  margin: -30px 21px 0px;
  border-radius: 8px 8px 0px 0px;
  overflow: hidden;
  height: 200px;
}
.shop-card-wrpr .shop-card .prod-title {
  font-size: 14px;
  line-height: 1.43;
  padding: 13px 21px 17px;
  background-color: #fbfcfb;
}

.css-1okebmr-indicatorSeparator {
  display: none;
}

.css-1rhbuit-multiValue {
  padding: 4px 5px 4px 8px !important;
  margin-right: 7px !important;
  border-radius: 4px !important;
  margin-bottom: 4px !important;
  margin-top: 4px !important;
}
.css-1rhbuit-multiValue .css-xb97g8 {
  position: relative;
  right: -2px;
}

.schedule-list {
  margin-top: 30px;
}
@media (max-width: 767px) {
  .schedule-list {
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
  }
}
.schedule-list .schedule-points.active .schedule-item {
  color: #404040;
  font-weight: 500;
  font-family: "GS-medium";
}
.schedule-list .schedule-points.active .schedule-item .point {
  background: #EEF7F1;
  border: 1.2px solid #499557;
  color: #021416;
}
.schedule-list .schedule-points.complete .schedule-item {
  color: #404040;
  font-weight: 500;
  font-family: "GS-medium";
}
.schedule-list .schedule-points.complete .schedule-item .point {
  background-color: #499557;
  border: 1.2px solid #499557;
  color: #499557;
  text-indent: -999px;
  background-image: url(../images/all-icons.svg);
  background-repeat: no-repeat;
  background-position: -87px -29px;
}
.schedule-list .schedule-points .schedule-item .point {
  min-width: 24px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 50px;
  color: #7E7E7E;
  margin-right: 8px;
  overflow: hidden;
}
.schedule-list .schedule-points-gap {
  max-width: 114px;
  width: 100%;
  margin: 0px 12px;
  border: none;
  border-bottom: 1px solid #f0f0f0;
}

.calendar,
.timeing-block {
  display: flex;
  width: 100%;
  gap: 24px;
  position: relative;
}

.timeing-block {
  max-height: 200px;
}
.timeing-block .slick-list {
  overflow: hidden;
  overflow-y: auto;
}
.timeing-block .cstm_slick_arrow {
  position: absolute;
  top: -64px;
  left: auto;
  right: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
}
.timeing-block .cstm_slick_arrow.slick_next {
  right: 0px;
  rotate: 180deg;
  top: -67px;
}

.day {
  position: sticky;
  top: 0px;
  text-align: center;
  width: 100%;
  z-index: 99;
  background-color: white;
}

.cal-row {
  display: flex !important;
  flex-direction: column;
  gap: 8px;
}

.time {
  padding: 9px;
  margin-bottom: 8px;
  margin-right: 0px;
  text-align: center;
  background: rgba(73, 149, 87, 0.7);
  border: 1px solid #499557;
  border-radius: 8px;
  font-weight: 500;
  font-family: "GS-medium";
  color: white;
  position: relative;
}
.time.disabled {
  background: #f0f0f0;
  color: #a2a2a2;
}
.time.active {
  background: #499557;
  color: white;
}
.time .time-link {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 4;
}

.modal-prof-card {
  margin-bottom: 32px;
}
@media (max-width: 767px) {
  .modal-prof-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 15px;
  }
}
.modal-prof-card .profile-card {
  margin-top: 12px;
}
@media (max-width: 767px) {
  .modal-prof-card .profile-card {
    width: 100%;
  }
}
.modal-prof-card .rating-stars {
  justify-content: flex-start;
}

.appointment-calender-wrpr {
  margin-top: 24px;
}
.appointment-calender-wrpr .appointment-cal-block {
  margin-top: 20px;
}
.appointment-calender-wrpr .calendar {
  background: #ffffff;
  border: 1px solid #eef7f1;
  border-radius: 8px;
  padding: 16px 20px;
  overflow: auto;
}
.appointment-calender-wrpr .calendar .slick-list {
  height: 100%;
}
.appointment-calender-wrpr .calendar .slick-list .slick-slide {
  height: 100%;
}

.slick-track {
  margin-left: 0px !important;
}

.arrow-wrpr .arrow-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  transform: rotate(90deg);
}
.arrow-wrpr .arrow-btn.next {
  transform: rotate(270deg);
}

.appt-date-price {
  gap: 80px;
}
@media (max-width: 767px) {
  .appt-date-price {
    width: 100%;
    gap: unset;
    justify-content: space-between;
  }
}

.name-flag-wrpr {
  display: flex;
  flex-flow: row wrap;
  align-content: center;
  gap: 5px;
}

.appt-form-wrpr {
  margin-top: 30px;
}
.appt-form-wrpr .appt-textarea-wrpr {
  margin-bottom: 20px;
}
.appt-form-wrpr .appt-textarea-wrpr .f-in {
  margin-top: 8px;
}

.addresses-wrpr {
  gap: 30px;
  margin: 32px 0px;
}
.addresses-wrpr .address-card {
  flex: auto;
  background: #fafafc;
  border: 1px solid #eef7f1;
  border-radius: 8px;
  padding: 16px 20px;
}
.addresses-wrpr .address-card .address-icon {
  gap: 16px;
}

.notification-modal {
  position: fixed;
  top: 0;
  left: auto;
  right: 0;
  width: 100vw;
  height: 100vh;
  justify-content: flex-end;
}
.notification-modal .modal-content {
  display: block;
  width: 100%;
  max-width: 540px;
  background: white;
  z-index: 100;
  height: 100vh;
  max-height: 100vh;
  border-radius: 8px 0px 0px 8px;
  overflow: auto;
  box-shadow: -6px 0px 26px rgba(0, 0, 20, 0.37);
  transition: all 0.3s ease;
}
.notification-modal.show {
  transition: all 0.3s ease;
}
.notification-modal .modal-header .close-btn {
  right: 10px;
  top: 10px;
  background: transparent;
}
.notification-modal .modal-body {
  max-height: calc(100% - 81px);
  overflow: auto;
  height: 100%;
  padding: 0px;
}

.noti-list .noti-item {
  padding: 24px 40px;
  border-bottom: 1px solid #f0f0f0;
}
.noti-list .noti-item .noti-img-wrpr {
  margin-right: 16px;
}
.noti-list .noti-item .noti-img-wrpr .noti-img {
  display: block;
  width: 48px;
  height: 48px;
  background: #ebfbd7;
  border-radius: 50%;
}
.noti-list .noti-item .noti-text {
  margin-bottom: 10px;
}

.slick-slider {
  width: 100%;
}

.calender-page-wrpr {
  width: 100%;
  padding: 32px;
}
@media (max-width: 767px) {
  .calender-page-wrpr {
    padding: 30px 20px;
  }
}
.calender-page-wrpr .calender-title {
  gap: 12px;
}
.calender-page-wrpr .calender-title .icon {
  background-color: #eef6f1;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  background-position: -156px -116px;
}
.calender-page-wrpr .calender-wrpr {
  margin-top: 40px;
  background: white;
  border-radius: 10px;
  padding: 16px;
  filter: drop-shadow(4px 4px 28px rgba(19, 61, 48, 0.06));
}

.fc {
  margin-top: 24px;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div {
  display: flex;
  align-items: center;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-toolbar-title {
  font-weight: 600;
  color: #07080A;
  font-size: 20px;
  min-width: 200px;
}
@media (max-width: 992px) {
  .fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-toolbar-title {
    font-size: 16px;
    min-width: 140px;
  }
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button {
  background-color: transparent;
  border: none;
  text-transform: capitalize;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button .fc-icon {
  color: #7E7E7E;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button:hover, .fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button.fc-today-button {
  background-color: #499557;
  border-color: #499557;
  font-weight: 600;
  margin-right: 20px;
  text-transform: capitalize;
  padding: 4px 9px;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button.fc-today-button:hover, .fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button.fc-today-button:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button.fc-prev-button, .fc .fc-header-toolbar .fc-toolbar-chunk:first-child div .fc-button.fc-next-button {
  padding: 0px;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:last-child .fc-button-group {
  background: #ffffff;
  box-shadow: 0px 4.05602px 28.3922px rgba(102, 111, 167, 0.08);
  border-radius: 8.11204px;
  padding: 4px;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:last-child .fc-button-group .fc-button {
  background: transparent;
  color: #7e7e7e;
  font-weight: 600;
  font-size: 12.1681px;
  line-height: 1.33;
  font-family: "inter", sans-serif;
  border: none;
  padding: 8px 14px;
  border-radius: 4px;
  text-transform: capitalize;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:last-child .fc-button-group .fc-button:hover, .fc .fc-header-toolbar .fc-toolbar-chunk:last-child .fc-button-group .fc-button:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
.fc .fc-header-toolbar .fc-toolbar-chunk:last-child .fc-button-group .fc-button.fc-button-active {
  background: #094e26;
  color: white;
}
.fc .fc-daygrid-day-frame {
  overflow: hidden;
}
.fc .fc-timegrid-divider {
  padding: 0;
  display: none;
}
.fc .fc-day-today {
  background-color: transparent !important;
}
.fc .fc-event {
  background: transparent;
  border: none;
}
.fc .fc-event:hover, .fc .fc-event:focus {
  background: transparent;
}
.fc .fc-timegrid-more-link {
  background: #409993;
  color: #ffffff;
}
.fc .fc-popover {
  border-color: #409993 !important;
  border-radius: 4px;
  overflow: hidden;
}
.fc .fc-popover .fc-popover-header {
  background: #d4f0ee;
}

.meeting-card {
  background: #d4efee;
  border-radius: 4px;
  padding: 6px 3px;
  width: 100%;
  overflow: hidden;
}
.meeting-card .p-name {
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
}
.meeting-card .meeting-icon {
  display: block;
  width: 10px;
  height: 10px;
  background: #289893;
  border-radius: 10px;
}

.meeting-info-modal .modal-content {
  display: block;
  position: fixed;
  width: 100%;
  max-width: 540px;
  background: white;
  z-index: 100;
  top: 0;
  left: auto;
  right: 0;
  height: 100vh;
  max-height: 100vh;
  padding: 30px 40px 50px;
  box-shadow: -6px 0px 26px hsla(0, 0%, 20%, 0.37);
}
.meeting-info-modal .modal-header {
  border-bottom: 0px !important;
  padding: 0px 0px 20px;
  border-bottom: 1px solid #daece1;
}
.meeting-info-modal .modal-body {
  height: calc(100vh - 176px);
  overflow: auto;
  padding: 0;
}
.meeting-info-modal .cal-dsc {
  padding: 24px 0px 0px;
}
.meeting-info-modal .modal-btn-wrpr {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0;
  padding: 15px 40px;
  border-top: 1px solid #f0f0f0;
  justify-content: space-between;
}
.meeting-info-modal .modal-btn-wrpr .btn-secondary {
  color: #811f18;
}
.meeting-info-modal .schedule-appointment-wrpr {
  margin-top: 32px;
}

.meeting-status {
  background: #ffeed3;
  border: 1px solid #fcd79f;
  border-radius: 4px;
  padding: 8px 10px;
  font-family: "General Sans";
  display: inline-block;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.14;
  color: #ea8c00;
}

.view_video {
  border-radius: 12px;
  overflow: hidden;
}

.meeting_success_modal .modal-content {
  max-height: 65vh;
  height: auto;
}
.meeting_success_modal .modal-header {
  border: none;
}
.meeting_success_modal .modal-header .close-btn {
  top: 10px;
  right: 14px;
}
.meeting_success_modal .modal-body {
  max-height: unset;
  height: 90%;
  padding-bottom: 60px;
}
.meeting_success_modal .successs-gif-wrpr {
  width: 250px;
  height: 250px;
  text-align: center;
  margin: 0 auto 20px;
}

.calender_meeting_schedule {
  gap: 11px;
}

.schedule_close_btn {
  background: #f0f0f0;
  border-radius: 50px;
  position: absolute;
  top: 30px;
  right: 30px;
  bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.self-practice-wrpr {
  padding: 40px 32px;
  overflow: auto;
  min-height: calc(100vh - 210px);
}
@media (max-width: 767px) {
  .self-practice-wrpr {
    padding: 30px 20px;
  }
}
.self-practice-wrpr .seminar-wrpr {
  gap: 34px;
}
@media (max-width: 1200px) {
  .self-practice-wrpr .seminar-wrpr {
    gap: 26px;
  }
}

.seminar-card-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.img-name-card.seminar-card .h6 {
  min-height: unset;
}
.img-name-card.seminar-card .img-card {
  min-height: 214px;
}
.img-name-card.seminar-card .img-card .cover-img {
  -o-object-fit: contain;
     object-fit: contain;
}
.img-name-card.seminar-card .seminar-name {
  margin-bottom: 4px;
}
.img-name-card.seminar-card .seminar-card-bottom {
  margin-top: 14px;
}
.img-name-card.seminar-card .seminar-card-btns {
  gap: 12px;
  flex: 0 0 60%;
}
.img-name-card.seminar-card .seminar-card-btns .btn {
  padding: 8px 9px;
  white-space: pre;
}
.img-name-card.seminar-card .in-progress {
  margin-top: 14px;
}

.study-row {
  margin-top: 56px;
}
.study-row .study-wrpr {
  gap: 24px;
}
.study-row .study-wrpr .seminar-card-btns {
  flex: 0 0 80%;
}

.coming-soon-text {
  font-weight: 500;
  line-height: 1.2;
  color: #1387c8;
  text-align: center;
  width: 100%;
}

.loaderView {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}
.loaderView.sm {
  transform: scale(0.7);
}

.self-practice-lessons .tab-left-aside {
  flex: 0 0 28%;
  background: #fafafc;
  padding: 12px 16px;
  overflow: auto;
}
@media (max-width: 992px) {
  .self-practice-lessons .tab-left-aside {
    position: absolute;
    width: 100%;
    top: 0;
    left: -100%;
    z-index: 1;
  }
  .self-practice-lessons .tab-left-aside.active {
    left: 0px;
  }
}
.self-practice-lessons .tab-right-aside {
  flex: 1 0 72%;
  padding-left: 19px;
  overflow: auto;
}
@media (max-width: 992px) {
  .self-practice-lessons .tab-right-aside {
    padding: 15px;
  }
}

.product-list-wrap {
  display: flex;
  width: 100%;
  background: #ffffff;
  border: 0.2px solid #fafafd;
  box-shadow: 0px 1px 12px rgba(11, 24, 93, 0.08);
  border-radius: 8px;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin-bottom: 8px;
}
.product-list-wrap .wrap-heading {
  display: flex;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
  padding: 14px 16px;
  background-color: #fff;
  position: relative;
  z-index: 999;
}
.product-list-wrap .wrap-heading.active .arrow-icon {
  transform: rotate(180deg);
  transition: rotate 0.5s;
}
.product-list-wrap .wrap-heading.active ~ .accordion-content {
  margin-bottom: 40px;
}
.product-list-wrap .wrap-heading .lesson-name {
  padding-right: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-list-wrap .wrap-heading .lesson-date {
  font-family: "inter", sans-serif;
  background: #ffffff;
  border: 0.5px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px 12px;
  margin-right: 5px;
}
.product-list-wrap .wrap-heading .action-wrap {
  display: flex;
  align-items: center;
  gap: 8px;
}
.product-list-wrap .wrap-heading .counts {
  font-family: "inter-semibold", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 18px;
  text-align: right;
  letter-spacing: -0.02em;
  color: #14182f;
}
.product-list-wrap .wrap-heading .arrow-icon {
  transform: rotate(0deg);
  transition: rotate 0.5s;
}
.product-list-wrap .wrap-heading .arrow-action {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}

.accordion-content {
  display: none;
  color: #7f8fa4;
  font-size: 13px;
  line-height: 1.5;
  margin-top: 8px;
}
.accordion-content .wrap-content-accordion {
  padding: 5px 15px 15px 15px;
}

.sub-lessson-list .sub-lessson {
  margin-bottom: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
}
.sub-lessson-list .sub-lessson.active {
  background: rgba(73, 149, 87, 0.2);
  transition: all 0.2s ease;
}
.sub-lessson-list .sub-lessson .icon.active {
  background-color: #499557;
  border-radius: 50%;
}

.cd-card-wrpr {
  margin-top: 30px;
  flex-wrap: wrap;
  gap: 12px;
}
.cd-card-wrpr .lesson-cd-card {
  gap: 12px;
}
.cd-card-wrpr .lesson-cd-card .cd-img {
  width: 48px;
  height: 56px;
  border-radius: 6px;
  filter: drop-shadow(0px 4px 28px rgba(102, 111, 167, 0.08));
  position: relative;
  overflow: hidden;
}

.lesson-auther-details {
  margin-top: 48px;
  padding-right: 40px;
}
.lesson-auther-details .auther-card .auther-img {
  width: 48px;
  height: 48px;
  border-radius: 50px;
  overflow: hidden;
  margin-right: 12px;
}
.lesson-auther-details .auther-card .auther-name {
  padding-right: 12px;
  margin-right: 12px;
  border-right: 1px solid #d8d8d8;
}

.doc-icon {
  width: 32px;
}

.selfpractice-tab-wrpr {
  margin-top: 10px;
}
.selfpractice-tab-wrpr .tabs {
  justify-content: flex-start;
}
.selfpractice-tab-wrpr .tabs .tab-link {
  display: flex;
  align-items: center;
  gap: 9px;
}
.selfpractice-tab-wrpr .tabs .tab-link:hover .icon.overview-icon, .selfpractice-tab-wrpr .tabs .tab-link.active .icon.overview-icon {
  background-position: -28px -169px;
}
.selfpractice-tab-wrpr .tabs .tab-link:hover .icon.download-icon, .selfpractice-tab-wrpr .tabs .tab-link.active .icon.download-icon {
  background-position: -88px -169px;
}
.selfpractice-tab-wrpr .tabs .tab-link:hover .icon.comment-icon, .selfpractice-tab-wrpr .tabs .tab-link.active .icon.comment-icon {
  background-position: -147px -171px;
}
.selfpractice-tab-wrpr .tab-contents {
  display: none;
}
.selfpractice-tab-wrpr .tab-contents.active {
  display: block;
}
.selfpractice-tab-wrpr .tab-contents .overview-title {
  margin-bottom: 8px;
}

.tab-overview,
.tab-downloads {
  padding: 36px 0px;
}

.download-card {
  gap: 45px;
  margin-bottom: 12px;
}
.download-card .file-wrpr {
  gap: 14px;
}

.comment-block-inner {
  flex: 1;
}

.rate-comment-sec {
  background: #ffffff;
  border: 0.5px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px 16px 24px;
  margin-top: 32px;
  position: relative;
}
.rate-comment-sec .edit-comment-wrpr {
  position: absolute;
  top: 21px;
  right: 30px;
}
.rate-comment-sec .f-label {
  color: #A2A2A2;
}
.rate-comment-sec .rating-section {
  margin-bottom: 34px;
}

.rating-star-wrpr {
  gap: 5px;
  margin-bottom: 4px;
  display: flex;
  margin-right: 10px;
}
.rating-star-wrpr .star-img {
  max-width: 16px;
}

.add-comment-wrpr {
  gap: 20px;
  align-items: flex-start;
}
.add-comment-wrpr .form-control {
  padding: 12px 10px;
}
.add-comment-wrpr .btn-accent {
  white-space: pre;
  padding: 10px 30px;
}

.search-comment-wrpr {
  gap: 20px;
}
.search-comment-wrpr .form-control {
  padding: 10px 10px;
}
.search-comment-wrpr .search-wrpr {
  position: relative;
  width: 100%;
}
.search-comment-wrpr .search-wrpr .icon {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 20px;
  height: 20px;
}
.search-comment-wrpr .search-wrpr .form-control {
  padding-left: 32px;
}
.search-comment-wrpr .filter-select {
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 4px;
  padding: 10px 27px;
  border: none;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
  color: #262626;
  cursor: pointer;
  outline: none !important;
}

.comment-card-wrpr {
  margin-top: 30px;
}
.comment-card-wrpr .comment-card {
  width: 100%;
  margin-bottom: 30px;
}
.comment-card-wrpr .comment-card .comment-date {
  margin-left: 10px;
}
.comment-card-wrpr .comment-user-img {
  width: 40px;
  min-width: 40px;
  height: 40px;
  min-height: 40px;
  border-radius: 40px;
  overflow: hidden;
  margin-right: 12px;
}

.comment-wrpr {
  padding-right: 20px;
}

.rating-wrap {
  padding: 30px 24px 0px;
  max-width: 40%;
}
.rating-wrap .rating-pt {
  display: flex;
  align-items: center;
  justify-content: center;
}
.rating-wrap .rating-star {
  background: white;
  border: 0.8px solid #c6c6c6;
  border-radius: 8px;
  padding: 14px 22px;
  min-width: 144px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}
.rating-wrap .rating-star .rt-star {
  width: 30px;
  height: 30px;
}
.rating-wrap .as-rating {
  font-weight: 600;
  font-size: 36px;
  line-height: 1.17;
  letter-spacing: -0.035em;
  color: #14182f;
}
.rating-wrap .rating-list {
  padding-top: 55px;
  width: 100%;
}
.rating-wrap .rating-list .ul-rating {
  width: 100%;
}
.rating-wrap .rating-list .ul-rating .li-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}
.rating-wrap .rating-list .ul-rating .li-rating:last-child {
  margin-bottom: 0px;
}
.rating-wrap .rating-list .ul-rating .li-rating .star-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  min-width: 45px;
}
.rating-wrap .rating-list .ul-rating .li-rating .star-count {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 16px;
  color: #14182f;
  min-width: 9px;
  text-align: center;
}
.rating-wrap .rating-list .ul-rating .li-rating .star-img {
  width: 15px;
  position: relative;
  top: -1px;
}
.rating-wrap .rating-list .ul-rating .li-rating .review-content {
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  color: #797f99;
}
.rating-wrap .rating-list .ul-rating .li-rating .rating-bar {
  width: 60%;
  display: flex;
}
.rating-wrap .rating-list .ul-rating .li-rating .rating-reviews {
  display: flex;
  min-width: 85px;
  justify-content: end;
}

.progress-bar {
  height: 7px;
  overflow: hidden;
  background-color: #d5d7e4;
  border-radius: 11.3055px;
  width: 100%;
  display: inline-block;
}

.progress-data-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  transition: width 0.6s ease;
  border-radius: 11.3055px;
}
.progress-data-bar.bar-green {
  background: #11996c;
}
.progress-data-bar.bar-red {
  background: #d60537;
}
.progress-data-bar.bar-yellow {
  background: #fcc607;
}

.line-side {
  color: #d5d7e4;
  font-style: inherit;
}

.sales-page-wrpr {
  padding: 0px 32px;
}
@media (max-width: 767px) {
  .sales-page-wrpr {
    padding: 0px 15px;
  }
}
.sales-page-wrpr .btn-accent {
  padding: 18px 54px;
}
.sales-page-wrpr .sales-page-ttl {
  font-family: "General Sans";
  font-style: normal;
  font-weight: 600;
  font-size: 60px;
  line-height: 1.07;
  letter-spacing: -1.72px;
  margin-bottom: 32px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .sales-page-ttl {
    font-size: 32px;
    margin-bottom: 10px;
    letter-spacing: 0px;
  }
}
.sales-page-wrpr .banner-wrpr {
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .banner-wrpr {
    padding: 15px 0px;
  }
}
.sales-page-wrpr .banner-wrpr .banner-video {
  margin: 40px 0px;
  border-radius: 10px;
  overflow: hidden;
  height: 500px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .banner-wrpr .banner-video {
    height: auto;
  }
}
.sales-page-wrpr .banner-text-wrpr .banner-subhead {
  margin-bottom: 14px;
  font-size: 18px;
}
.sales-page-wrpr .banner-text-wrpr .banner-btn {
  margin-top: 32px;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .banner-text-wrpr .banner-btn {
    margin-top: 20px;
  }
}
.sales-page-wrpr .banner-text-wrpr .h3 {
  font-size: 30px;
  line-height: 1.27;
  letter-spacing: -1.32px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .banner-text-wrpr .h3 {
    font-size: 24px;
    letter-spacing: 0px;
  }
}
.sales-page-wrpr .seminar-avail-card {
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .seminar-avail-card {
    padding: 15px 0px;
  }
}
.sales-page-wrpr .seminar-avail-card .img-wrpr {
  height: 100%;
  max-height: 500px;
  overflow: hidden;
  border-radius: 22px;
  margin-bottom: 64px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .seminar-avail-card .img-wrpr {
    margin-bottom: 34px;
  }
}
.sales-page-wrpr .seminar-avail-card .seminar-card-content {
  max-width: 810px;
  margin: 0 auto;
}
.sales-page-wrpr .seminar-avail-card .seminar-card-content .p {
  font-size: 18px;
  margin-bottom: 56px;
}
.sales-page-wrpr .online-seminar-card {
  border-radius: 10px;
  overflow: hidden;
  gap: 68px;
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .online-seminar-card {
    padding: 15px 0px;
    gap: 24px;
    flex-direction: column;
  }
}
.sales-page-wrpr .online-seminar-card .img-wrpr {
  border-radius: 22px;
  max-height: 500px;
  height: 100%;
  flex: 1 1 50%;
  overflow: hidden;
}
.sales-page-wrpr .online-seminar-card .online-seminar-ttl {
  margin-bottom: 32px;
}
.sales-page-wrpr .online-seminar-card .online-seminar-item {
  gap: 12px;
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
}
.sales-page-wrpr .online-seminar-card .online-seminar-item .chevron-icon {
  min-width: 20px;
  width: 20px;
  margin-top: 2px;
  filter: drop-shadow(0px 1px 4px rgba(4, 96, 41, 0.26));
}
.sales-page-wrpr .watch-buy-seminar-wrpr {
  background: linear-gradient(138.43deg, rgba(242, 239, 227, 0.7) 0%, rgba(198, 224, 215, 0.7) 100.36%), #ffffff;
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  border-radius: 22px;
  padding: 48px;
  gap: 28px;
  margin: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .watch-buy-seminar-wrpr {
    margin: 15px 0px;
    flex-direction: column;
    padding: 14px;
  }
}
.sales-page-wrpr .watch-buy-seminar-wrpr .watch-buy-inner {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  -webkit-backdrop-filter: blur(6px);
          backdrop-filter: blur(6px);
  border-radius: 22px;
  padding: 52px;
  gap: 32px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .watch-buy-seminar-wrpr .watch-buy-inner {
    padding: 24px;
    text-align: center;
  }
}
.sales-page-wrpr .watch-buy-seminar-wrpr .watch-buy-inner .watch-buy-img-wrpr {
  background: #d8edd9;
  border-radius: 22px;
  width: 104px;
  height: 104px;
  padding: 28px;
}
.sales-page-wrpr .logos-section {
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .logos-section {
    padding: 15px;
    margin-top: 25px;
  }
}
.sales-page-wrpr .logos-section .p {
  font-size: 18px;
  line-height: 1.4;
}
.sales-page-wrpr .logos-section .kuby-logos-wrpr {
  gap: 24px;
  margin-top: 64px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .logos-section .kuby-logos-wrpr {
    margin-top: 24px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
}
.sales-page-wrpr .logos-section .kuby-logos-wrpr .kuby-logos {
  background: white;
  padding: 12px;
  box-shadow: 0px 4px 28px rgba(144, 173, 150, 0.06);
  border-radius: 12px;
}
.sales-page-wrpr .testimonial-section {
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .testimonial-section {
    padding: 15px 0px;
  }
}
.sales-page-wrpr .testimonial-section .testimonial-slider {
  margin-top: 64px;
}
.sales-page-wrpr .testimonial-section .p {
  font-size: 18px;
  line-height: 1.4;
}
.sales-page-wrpr .testimonial-slide {
  background: #ffffff;
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  border-radius: 12px;
  padding: 20px;
}
.sales-page-wrpr .testimonial-slide .testimonial-auther-name {
  text-align: left;
}
.sales-page-wrpr .testimonial-slide .testimonial-auther-name .auther-name {
  font-weight: 600;
  font-size: 18px;
  line-height: 1.33;
  margin-bottom: 4px;
}
.sales-page-wrpr .testimonial-slide .testimonial-auther-name .auther-loc {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
}
.sales-page-wrpr .testimonial-slide .testi-star-wrpr {
  gap: 2px;
}
.sales-page-wrpr .testimonial-slide .testi-star-wrpr .img-fluid {
  width: 14px;
}
.sales-page-wrpr .testimonial-slide .testimonial-bottom {
  text-align: left;
  margin-top: 25px;
}
.sales-page-wrpr .testimonial-slide .testimonial-bottom .quote-icon-wrpr {
  width: 16px;
  display: inline-block;
}
.sales-page-wrpr .testimonial-slide .testimonial-bottom .testimonial-text {
  font-size: 14px;
  line-height: 1.71;
  letter-spacing: 0.16px;
  margin-top: 14px;
}
.sales-page-wrpr .steps-section {
  padding: 50px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .steps-section {
    padding: 15px 0px;
  }
}
.sales-page-wrpr .steps-section .step-sec-content {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 64px;
}
.sales-page-wrpr .steps-section .step-sec-content .p {
  font-size: 18px;
  line-height: 1.44;
}
.sales-page-wrpr .steps-section .steps-block {
  gap: 22px;
  margin-bottom: 80px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .steps-section .steps-block {
    flex-direction: column;
  }
}
.sales-page-wrpr .steps-section .steps-block .step {
  background: #EEF7F1;
  border-radius: 12px;
  position: relative;
  padding: 38px 30px;
  text-align: left;
  max-width: 33.33%;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: -0.32px;
  color: #536459;
}
@media (max-width: 767px) {
  .sales-page-wrpr .steps-section .steps-block .step {
    max-width: 100%;
  }
}
.sales-page-wrpr .steps-section .steps-block .step:after {
  content: "";
  position: absolute;
  width: 4px;
  height: 30px;
  left: 0;
  top: 36%;
  background: #499557;
  border-radius: 0px 14px 14px 0px;
}
.sales-page-wrpr .faq-section {
  margin: 50px 0px;
  padding: 48px 55px;
  background: rgb(255, 255, 255);
  box-shadow: 0px 4px 28px rgba(102, 111, 167, 0.08);
  border-radius: 8px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .faq-section {
    padding: 15px;
  }
}
.sales-page-wrpr .faq-section .faq-block {
  margin-top: 80px;
  margin-bottom: 64px;
}
.sales-page-wrpr .faq-section .faq-block .product-list-wrap {
  background-color: transparent;
  box-shadow: none;
  border: none;
  border-bottom: 1px solid #e0ede5;
  border-radius: 0px;
}
.sales-page-wrpr .faq-section .faq-block .product-list-wrap.active .faq-icon {
  background-position: -182px -174px;
}
.sales-page-wrpr .faq-section .faq-block .product-list-wrap .wrap-heading {
  padding: 36px 16px;
}
@media (max-width: 767px) {
  .sales-page-wrpr .faq-section .faq-block .product-list-wrap .wrap-heading {
    padding: 15px;
    text-align: left;
  }
}
.sales-page-wrpr .faq-section .faq-block .faq-icon {
  background-position: -182px -174px;
}

.progress-data-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  transition: width 0.6s ease;
  border-radius: 11.3055px;
}
.progress-data-bar.bar-5 {
  background: #11996c;
}
.progress-data-bar.bar-4 {
  background: #46ac8a;
}
.progress-data-bar.bar-2 {
  background: #d62b05;
}
.progress-data-bar.bar-1 {
  background: #d60537;
}
.progress-data-bar.bar-3 {
  background: #fcc607;
}

.soulwriting-wrpr {
  margin-left: 24px;
  margin-right: 24px;
  padding: 20px;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  gap: 32px;
}
@media (max-width: 767px) {
  .soulwriting-wrpr {
    gap: 0px !important;
  }
}
.soulwriting-wrpr .soul-vedio {
  flex: 0 0 35%;
  max-width: 390px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  min-height: 220px;
  justify-content: center;
  max-height: 220px;
}
@media (max-width: 767px) {
  .soulwriting-wrpr .soul-vedio {
    min-height: 160px !important;
    max-height: 160px !important;
  }
}
.soulwriting-wrpr .soul-vedio > div {
  width: 100%;
  height: 100%;
}
@media (min-width: 767px) {
  .soulwriting-wrpr .soul-vedio iframe {
    height: 100%;
  }
}
.soulwriting-wrpr .soul-intro {
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .soulwriting-wrpr .soul-intro {
    margin-top: 10px !important;
  }
}
.soulwriting-wrpr .status-info {
  font-weight: 500;
  font-size: 12px;
  line-height: 1.33;
  color: #5374ca;
}
.soulwriting-wrpr .status-info .status-info-icon {
  margin-left: 5px;
}

.soul-table-wrpr {
  margin: 0px 24px 50px;
  padding: 0px 10px;
  background: #ffffff;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  overflow: auto;
  max-height: 500px;
  overflow: auto;
}
.soul-table-wrpr .soul-table {
  width: 100%;
  overflow: auto;
}

.soul-tcol {
  /* background: #fff; */
  padding: 16px 9px;
  text-align: left;
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  letter-spacing: -0.25px;
  color: #262626;
}
.soul-tcol.th {
  font-family: "GS-medium";
  font-weight: 500;
  white-space: pre;
  color: #A2A2A2;
}
.soul-tcol:first-child {
  min-width: 150px;
}
.soul-tcol:nth-child(2) {
  min-width: 200px;
}
@media (max-width: 767px) {
  .soul-tcol:nth-child(2) {
    min-width: 140px;
  }
}
.soul-tcol:last-child {
  white-space: pre;
  word-break: keep-all;
  min-width: 130px;
}
.soul-tcol:last-child .link {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 20px;
}
.soul-tcol .link {
  display: block;
  width: 100%;
  text-align: right;
  text-decoration: underline;
}

.soul-trow {
  position: relative;
}

.soul-status {
  background: #eaeef9;
  border: 1px solid #c5d2f5;
  border-radius: 4px;
  padding: 6px 8px;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.2;
  letter-spacing: -0.35px;
  color: #3a69ea;
  font-family: "inter-medium", sans-serif;
  display: flex;
  align-items: center;
}
.soul-status.submitted {
  border-color: #b2d9b9;
  color: #09771d;
  background: #ecfaef;
}
.soul-status.recieved {
  border-color: #fae0c2;
  color: #ed8e00;
  background: #fcf9ee;
}

.soulwriting-main-wrpr {
  padding: 10px 0px 10px 32px;
  gap: 24px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr-outer {
  position: relative;
  display: flex;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr-outer .toggle-sidebar {
  z-index: 1;
  right: -23px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr-outer .toggle-sidebar.closed {
  right: 0px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 128px);
  overflow: auto;
  flex: 0 0 200px;
  max-width: 200px;
  gap: 16px;
  border-right: 1px solid #e7e8f2;
  padding-right: 15px;
  margin-right: 15px;
  transition: all 0.3s ease;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr.closed {
  flex: 0 0 67px;
  max-width: 67px;
  transition: all 0.3s ease;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr.closed .step-text {
  display: none;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn {
  background: #f0f0f0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px 8px;
  gap: 4px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0.12px;
  color: #404040;
  font-weight: 400;
  outline: none;
  box-shadow: none;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 9px;
  height: 16px;
  width: 10px;
  background-image: url(../images/dashed-line.svg);
  background-repeat: no-repeat;
  background-position: bottom;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn:last-child::after {
  display: none;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50px;
  background-color: rgba(216, 216, 216, 0.32);
  background-image: url(../images/soulwriting-icons.svg);
  background-position: 0px 0px;
  background-repeat: no-repeat;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step1 {
  background-position: 3px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step2 {
  background-position: -34px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step3 {
  background-position: -72px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step4 {
  background-position: -110px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step5 {
  background-position: -148px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step6 {
  background-position: -186px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step7 {
  background-position: -224px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step8 {
  background-position: -262px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step9 {
  background-position: -300px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step10 {
  background-position: -338px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step11 {
  background-position: -377px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn .soul-step.step12 {
  background-position: -413px 4px;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active {
  background-color: white;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn1 {
  border-color: #dcd7fc;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn2 {
  border-color: #abbded;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn3 {
  border-color: #a8e5ff;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn4 {
  border-color: #8fcec8;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn5 {
  border-color: #7aba94;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn6 {
  border-color: #b5d289;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn7 {
  border-color: #d1d445;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn8 {
  border-color: #fad824;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn9 {
  border-color: #febf60;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn10 {
  border-color: #fe9c56;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn11 {
  border-color: #ffa3a3;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active.btn12 {
  border-color: #dc665d;
}
.soulwriting-main-wrpr .soulwriting-steps-wrpr .step-btn.active .soul-step {
  background-position-y: -32px;
}
.soulwriting-main-wrpr .soulwriting-forms-wrpr {
  position: relative;
  flex: 1 1 calc(100% - 224px);
  overflow: auto;
  max-height: calc(100vh - 128px);
}

.soul-form-header {
  margin-bottom: 10px;
  position: sticky;
  top: 0;
  background: #fff;
  padding: 10px;
  z-index: 99;
  box-shadow: 0px 2px 5px #eee;
  border-radius: 0px 0px 4px 4px;
}
.soul-form-header .header-filter {
  padding: 4px 12px;
  position: relative;
  display: flex;
  gap: 4px;
  background: transparent;
  margin-left: 24px;
}
.soul-form-header .header-filter:after {
  content: "";
  position: absolute;
  top: 4px;
  right: -12px;
  width: 1px;
  height: 70%;
  background: #A2A2A2;
}
.soul-form-header .header-filter:last-child:after {
  display: none;
}
.soul-form-header .header-filter .filter-select {
  border: none;
  outline: none;
  box-shadow: none;
  background: transparent;
}
.soul-form-header .header-filter .filter-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  outline: none;
  box-shadow: none;
  border: none;
  font-size: 14px;
  line-height: 1.43;
  color: #7E7E7E;
}
.soul-form-header .header-filter .filter-icon {
  width: 18px;
}

/*****  soulwriting  responsive *****/
@media (max-width: 767px) {
  .soulwriting-main-wrpr {
    padding: 10px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soulwriting-forms-wrpr {
    max-height: calc(100vh - 140px);
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soulwriting-steps-wrpr-outer {
    display: none;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-form-header {
    align-items: baseline;
    gap: 5px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-header-right {
    width: 100%;
    align-items: center;
    gap: 5px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-header-right .origin-date {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-header-right .header-filter {
    margin-left: 0px;
    padding-left: 0;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-header-right .header-filter::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-header-right .filter-btn {
    padding-left: 0px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer {
    flex-direction: column;
  }
  .soulwriting-main-wrpr .soul-step-outer::before {
    display: none;
  }
}
@media (max-width: 992px) {
  .soulwriting-main-wrpr .soul-step-outer .step-left {
    width: 100%;
    flex: inherit !important;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .step-left {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .step-left .project-field-wrpr .form-in {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .step-right {
    width: 100%;
    flex: inherit !important;
  }
}
@media (max-width: 480px) {
  .soulwriting-main-wrpr .soul-step-outer .protagonist-card {
    overflow: auto;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .protagonist-card .protagonist-details {
    gap: 10px;
  }
}
@media (max-width: 480px) {
  .soulwriting-main-wrpr .soul-step-outer .protagonist-card .protagonist-details p {
    overflow: hidden;
    white-space: nowrap;
    display: block;
    text-overflow: ellipsis;
    padding-right: 0px;
    max-width: 99%;
    width: 100%;
    min-width: 100px;
  }
}
@media (max-width: 480px) {
  .soulwriting-main-wrpr .soul-step-outer .protagonist-card .status-badge {
    min-width: 80px;
    text-align: center;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .person-card {
    width: 35px;
    height: 35px;
    font-size: 13px;
  }
}
@media (max-width: 992px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner {
    flex: inherit;
    max-width: 100%;
  }
}
@media (max-width: 992px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-subtitle {
    font-size: 14px;
  }
}
@media (max-width: 992px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .date-in-wrpr .form-in {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner {
    min-width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .scene-row {
    align-items: baseline;
    flex-wrap: wrap;
    position: relative;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .scene-text-wrpr {
    width: 100%;
    flex: auto;
    padding-left: 25px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-table-wrpr {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-table-wrpr .bridge-past-block {
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-table-wrpr .bridge-past-block .bridge-list-item::before {
    display: none;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-table-wrpr .bridge-present-block {
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .bridge-block .add-bridge-card {
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .first-col {
    position: absolute;
    bottom: 24px;
    left: -4px;
  }
}
@media (max-width: 767px) {
  .soulwriting-main-wrpr .soul-step-outer .soul-step-inner .second-col {
    position: absolute;
    left: -20px;
    margin-top: 7px;
  }
}

@media (max-width: 992px) {
  .right-section_soulwriting .soulwriting-footer {
    padding: 15px;
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .right-section_soulwriting .soulwriting-footer {
    padding: 15px;
    flex-wrap: wrap;
    gap: 10px;
  }
}
@media (max-width: 767px) {
  .right-section_soulwriting .soulwriting-footer .footer-btn {
    width: auto;
  }
}
.right-section_soulwriting .soulwriting-footer .footer-btn.saveLeave {
  background: transparent;
  color: #404040;
  border: 1px solid #404040;
}

.right-section_soulwriting .soulwriting-wrpr {
  width: calc(100% - 48px);
}
@media (max-width: 767px) {
  .right-section_soulwriting .soulwriting-wrpr {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  .right-section_soulwriting .soulwriting-wrpr .soul-vedio {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .right-section_soulwriting .soulwriting-wrpr .soul-content-wrpr div.text-right {
    text-align: left;
    margin-top: 15px;
  }
}
.right-section_soulwriting .soulwriting-table.inner-header {
  overflow: auto;
  padding: 24px 0 0px;
}

@media (max-width: 767px) {
  div.protagonist-list-modal {
    top: 0px;
    height: 100%;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .overlay-div {
    display: block;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .icon.close-icon {
    background-position: 0px -142px;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content {
    margin: 0px 20px;
    width: 100% !important;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content .modal-header .q-mark-icon {
    display: none;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content .bridge-table-wrpr {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content .bridge-table-wrpr .bridge-block {
    width: 100%;
    flex: inherit;
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content .bridge-table-wrpr .bridge-block .add-bridge-card {
    justify-content: center;
  }
}
@media (max-width: 767px) {
  div.protagonist-list-modal .modal-content .bridge-table-wrpr .bridge-block .bridge-list-item::before {
    display: none;
  }
}

/*****  soulwriting  responsive *****/
.char-count {
  font-size: 12px;
  line-height: 1.67;
}

.steps-wrpr {
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  border-radius: 8px;
  padding: 24px;
}
.steps-wrpr .step-title {
  gap: 10px;
  margin-bottom: 12px;
}
.steps-wrpr .step-subtitle {
  font-size: 12px;
  min-height: 24px;
}
.steps-wrpr .step-subtitle .icon {
  transform: scale(0.8);
}
.steps-wrpr .soul-step-outer {
  position: relative;
  gap: 36px;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 10px 0px;
}
.steps-wrpr .soul-step-outer:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 3px;
  top: 0px;
  left: -24px;
  border-radius: 0px 6px 4px 0px;
}
.steps-wrpr .soul-step-outer.soul-step1::before {
  background: #af93ff;
}
.steps-wrpr .soul-step-outer.soul-step2 {
  padding-bottom: 24px;
}
.steps-wrpr .soul-step-outer.soul-step2::before {
  background: #7a9efa;
}
.steps-wrpr .soul-step-outer.soul-step3::before {
  background: #8adcff;
}
.steps-wrpr .soul-step-outer.soul-step4::before {
  background: #48a2ae;
}
.steps-wrpr .soul-step-outer.soul-step5::before {
  background: #76a8af;
}
.steps-wrpr .soul-step-outer.soul-step6::before {
  background: #2fa75f;
}
.steps-wrpr .soul-step-outer.soul-step7::before {
  background: #a9cf6f;
}
.steps-wrpr .soul-step-outer.soul-step8::before {
  background: #f9dd2f;
}
.steps-wrpr .soul-step-outer.soul-step9::before {
  background: #ffc46b;
}
.steps-wrpr .soul-step-outer.soul-step10::before {
  background: #fca05e;
}
.steps-wrpr .soul-step-outer.soul-step11::before {
  background: #ffa3a3;
}
.steps-wrpr .soul-step-outer.soul-step12::before {
  background: #dc665d;
}
.steps-wrpr .soul-step-outer .step-left {
  flex: 0 0 calc(100% - 286px);
}
.steps-wrpr .soul-step-outer .step-left .textarea {
  resize: none;
  height: 186px;
  overflow: auto;
}
.steps-wrpr .soul-step-outer .step-right {
  flex: 0 0 250px;
  position: relative;
}
.steps-wrpr .soul-step-outer .step-right > .p {
  margin-bottom: 12px;
}
.steps-wrpr .soul-step-outer .step-right .comment-section-header {
  margin-top: 44px;
}
.steps-wrpr .soul-step-outer .step-right .comment-section-header .icon {
  margin-right: 8px;
}
.steps-wrpr .soul-step-outer .step-right .comment-section-header .cmnt-btn {
  padding: 4px 18px;
}
.steps-wrpr .soul-step-outer .step-right .comment-section-header .cmnt-btn.disable {
  pointer-events: none;
  color: #616161;
  background-color: #c6c6c6;
  border-color: #c6c6c6;
}

.soul-step-inner {
  width: 100%;
}
.soul-step-inner .textarea {
  height: 110px;
  font-family: "inter", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  resize: none;
}
.soul-step-inner .person-cards {
  margin-left: 24px;
  display: flex;
  align-items: center;
}

.action-btns {
  gap: 36px;
  margin-top: 10px;
}
.action-btns .delete-btn {
  background: transparent;
  border: none;
  outline: none;
  box-shadow: none;
}
.action-btns .add-btn {
  padding: 6px 16px;
  font-size: 12px;
}

.add-btn {
  padding: 8px 16px 8px 12px;
  background: #f0f0f0;
  border: 1px solid #eeeef6;
  border-radius: 8px;
  color: #404040;
}

.scene-row {
  gap: 12px;
  margin-bottom: 20px;
}
.scene-row.title {
  margin-top: 30px;
}
.scene-row .scene-text-wrpr {
  flex: 1;
}
.scene-row .first-col {
  flex: 0 0 30px;
  text-align: center;
}
.scene-row .comment-card-block {
  flex: 0 0 250px;
}
.scene-row .comment-card {
  width: 250px;
}

.swap-comp {
  background-color: transparent;
  border: none;
  text-decoration: underline;
  color: #499557;
  cursor: pointer;
  font-size: 14px;
  gap: 9px;
  margin-top: 22px;
}

.soulwriting-footer {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 4px 28px rgba(7, 49, 15, 0.06);
  padding: 10px;
  z-index: 99;
  gap: 24px;
  justify-content: space-between;
}
.soulwriting-footer .footer-btn {
  padding: 10px 23px;
  border-radius: 8px;
  gap: 10px;
  font-family: "GS-medium";
  font-weight: 500;
  font-size: 14px;
  line-height: 1.43;
}
.soulwriting-footer .footer-btn.leave-btn {
  background: #ffffff;
  border: 1px solid #f2ebde;
  color: #ac4139;
}
.soulwriting-footer .footer-btn.draft-btn {
  background: #ffffff;
  border: 1px solid #f5c08e;
  color: #f0790b;
  position: relative;
  align-items: center;
  justify-content: center;
}
.soulwriting-footer .footer-btn.draft-btn.btn-loader::after {
  border: 3px solid rgba(73, 149, 87, 0.5);
  border-right-color: #499557;
}
.soulwriting-footer .ftr-btn {
  color: #000 !important;
  background: #fff;
}
.soulwriting-footer .soulwriting-footer-left {
  display: flex;
  gap: 15px;
}
.soulwriting-footer .soulwriting-footer-right {
  display: flex;
  gap: 15px;
}

.person-card,
.person-card-count,
.person-card-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 44px;
  background: #cbecea;
  border: 1px solid #a6d9d6;
  box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 40px;
  margin-left: -23px;
  color: #262626;
}
.person-card:first-child,
.person-card-count:first-child,
.person-card-empty:first-child {
  margin-left: 0px;
}

.person-card-count {
  background: rgb(33, 87, 79);
  border: 1px solid rgba(109, 156, 149, 0.3);
  box-shadow: 0px 4px 32px rgba(0, 0, 0, 0.16);
}

.person-card-empty {
  background: #eef7f1;
  border: 1px solid #f0f0f0;
  color: #c6c6c6;
  box-shadow: none;
}

.person-wrpr {
  margin: 6px 0px;
}

.date-in-wrpr {
  gap: 16px;
  margin-bottom: 16px;
}
.date-in-wrpr .form-control {
  padding: 10px;
}

.protagonist-list {
  margin-top: 24px;
}
.protagonist-list .protagonist-list-item {
  margin-bottom: 16px;
}
.protagonist-list .protagonist-card {
  background: #ffffff;
  border: 1px solid #f4ece2;
  border-radius: 6px;
  padding: 12px 16px;
  margin-left: 16px;
}
.protagonist-list .protagonist-card .protagonist-details {
  gap: 20px;
}
.protagonist-list .protagonist-card .protagonist-details .p {
  margin-bottom: 4px;
}
.protagonist-list .protagonist-card .life-status-wrpr {
  gap: 8px;
}
.protagonist-list .protagonist-card .life-status-wrpr .life-status {
  background: #ecf7f1;
  border: 1px solid #dcf0e5;
  border-radius: 4px;
  padding: 10px 18px;
  color: #094e26;
}
.protagonist-list .protagonist-card .life-status-wrpr .life-status.passaway {
  color: #811f18;
  background: #fff4f3;
  border: 1px solid #ffeeed;
}
.protagonist-list .date-passed {
  gap: 13px;
}

.upper-case {
  text-transform: uppercase;
}

.protagonist-modal.new .modal-content {
  height: auto;
}
@media (max-width: 767px) {
  .protagonist-modal.new .modal-content {
    width: 100%;
  }
}
.protagonist-modal.new .modal-dialog {
  max-width: 620px;
}
.protagonist-modal.new .modal-footer {
  padding-bottom: 0px;
}
@media (max-width: 767px) {
  .protagonist-modal {
    padding: 0px 20px;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .modal-header .h4 {
    text-align: left;
    font-size: 23px;
  }
}
.protagonist-modal .modal-dialog {
  min-width: 615px;
}
@media (max-width: 767px) {
  .protagonist-modal .modal-dialog {
    min-width: 100%;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .modal-content {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .modal-content .modal-body {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .modal-content .protagonist-form-wrpr .f-row .w-50 {
    width: 100%;
  }
}
.protagonist-modal .protagonist-form-wrpr {
  height: 100%;
}
.protagonist-modal .protagonist-form-wrpr .form-inner {
  height: calc(100% - 65px);
  overflow: auto;
  padding-left: 40px;
  padding-right: 40px;
}
@media (max-width: 767px) {
  .protagonist-modal .protagonist-form-wrpr .form-inner {
    padding-left: 0px;
    padding-right: 0px;
  }
}
.protagonist-modal .protagonist-form-wrpr .form-inner.protogonist-form-inner {
  padding: 40px;
  border-radius: 24px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background: #f8f9fb;
  margin-left: 40px;
  margin-right: 40px;
  display: flex;
  flex-direction: column;
  max-height: 50vh;
  overflow: auto;
  min-height: 230px;
}
@media (max-width: 767px) {
  .protagonist-modal .protagonist-form-wrpr .form-inner.protogonist-form-inner {
    min-width: 300px;
    min-height: 260px;
    margin-left: 0px;
    margin-right: 0px;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .protagonist-form-wrpr .form-inner.protogonist-form-inner .protogonist-opt-list {
    flex-direction: column;
  }
}
@media (max-width: 767px) {
  .protagonist-modal .protagonist-form-wrpr .form-inner.protogonist-form-inner .protogonist-opt-list .protogonist-opt-item {
    width: 100%;
  }
}
.protagonist-modal .protagonist-form-wrpr .modal-footer {
  padding-left: 40px;
  padding-right: 40px;
  padding-top: 24px;
}
.protagonist-modal .protagonist-form-wrpr .modal-footer.bt-0 {
  border-top: 0px;
}
.protagonist-modal .modal-body {
  height: calc(100% - 81px);
  padding-left: 0px;
  padding-right: 0px;
}
.protagonist-modal .modal-body-inner {
  height: calc(100% - 66px);
  overflow: auto;
}
.protagonist-modal .form-in {
  margin-bottom: 24px;
}
.protagonist-modal .form-in .f-label {
  width: unset;
}

.script-modal .modal-body {
  height: 100%;
}

@media (max-width: 1200px) {
  .companion-modal .modal-dialog {
    max-width: 90vw;
  }
}
.companion-modal .modal-body {
  height: calc(100% - 100px);
  padding: 0px;
  min-width: 950px;
}
@media (max-width: 992px) {
  .companion-modal .modal-body {
    min-width: unset;
    width: 100%;
  }
}
.companion-modal .modal-body .choose-comp {
  height: calc(100% - 65px);
  overflow: auto;
  padding: 24px 40px;
}
.companion-modal .modal-footer {
  padding-bottom: 0px;
}
.companion-modal .companion-card {
  max-width: 32%;
  border: 2px solid transparent;
  cursor: pointer;
}
.companion-modal .companion-card.active {
  background: white;
  border: 2px solid #499557;
}

.redline-wrpr .icon {
  cursor: grab;
}

.red-line {
  width: 100%;
  border: 2px solid #ff0000;
  border-radius: 2px;
  margin-left: 14px;
}
.red-line.view {
  margin-left: 0px;
  margin-bottom: 24px;
  margin-top: -26px;
}

.script-inner-wrpr {
  margin-bottom: 48px;
}
.script-inner-wrpr .h5 {
  margin-bottom: 12px;
}
.script-inner-wrpr .q-mark-icon {
  margin-left: 10px;
}
.script-inner-wrpr .view-table .tr {
  margin-bottom: 10px;
}
.script-inner-wrpr .view-table .tr .td {
  padding: 4px 8px 14px;
  vertical-align: middle;
}
.script-inner-wrpr .view-table .view-person-card {
  background: #e0e0e0;
  box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 40px;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.script-inner-wrpr .step-subtitle {
  font-size: 12px;
}
.script-inner-wrpr .step-subtitle .icon {
  transform: scale(0.7);
}
.script-inner-wrpr .view-date-wrpr {
  padding: 10px 0px;
  margin-top: 6px;
  margin-bottom: 24px;
}
.script-inner-wrpr .view-date-wrpr .view-date {
  color: #094e26;
  padding-left: 10px;
  border-left: 1px solid #f0f0f0;
  margin-left: 10px;
}

.comment-card .comment-card-inner {
  background: #eeeef7;
  border-radius: 8px;
  padding: 2px;
  margin-bottom: 5px;
}
.comment-card .comment-card-inner .comment-name {
  padding: 2px 18px 4px;
}
.comment-card .comment-card-inner.companion_message {
  background-color: #ecf9ee;
}
.comment-card .comment-card-inner.member_message {
  background-color: #e6f6fa;
}
.comment-card .comment-card-inner .comment-text {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 8px 16px;
}
.comment-card .cmnt-btn-wrpr {
  margin-bottom: 10px;
}
.comment-card .cmnt-btn-wrpr .reply-btn {
  background: transparent;
  border: none;
  outline: none;
  font-size: 12px;
  line-height: 10px;
  color: #499557;
}
.comment-card .cmnt-btn-wrpr .send-comment {
  border: none;
  background: transparent;
  padding: 0px;
  outline: none;
  box-shadow: none;
}
.comment-card .comment-input {
  width: 100%;
  min-height: 40px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.43;
  color: #262626;
  outline: none;
  box-shadow: none;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 6px;
}
.pagination .active a {
  background: #eef7f1;
  border: 0.2px solid #499557;
}
.pagination a {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #ffffff;
  color: #7E7E7E;
  border: 0.2px solid #ffffff;
  box-shadow: 0px 1px 12px rgba(2, 17, 8, 0.06);
}

.scene-text-wrpr strong {
  font-weight: bold;
}
.scene-text-wrpr i {
  font-style: italic;
}
.scene-text-wrpr ul,
.scene-text-wrpr ol {
  padding-left: 20px;
}

.ck-editor__main strong {
  font-weight: bold;
}
.ck-editor__main i {
  font-style: italic;
}
.ck-editor__main ul,
.ck-editor__main ol {
  padding-left: 20px;
}

.bridge_wrp {
  position: absolute;
  top: 100px;
  right: 0;
  left: auto;
  width: 270px;
  height: 250px;
  padding: 10px;
  overflow: auto;
  background: #fff;
  box-shadow: 0px 0px 10px 0px #eee;
  border-radius: 7px;
  z-index: 999;
}

.bridge_wrp.companion-selected {
  top: 190px;
}

.remove_bridge {
  position: absolute;
  top: 11px;
  right: 3px;
  background: transparent;
  border: none;
}

/* ===== Scrollbar CSS ===== */
/* Firefox */
.bridge_wrp,
.soulwriting-forms-wrpr,
.soul-table-wrpr,
.project_tool_list,
.tab-list-wrpr {
  scrollbar-width: auto;
  scrollbar-color: #fff #ffffff;
}

/* Chrome, Edge, and Safari */
.bridge_wrp::-webkit-scrollbar,
.soulwriting-forms-wrpr::-webkit-scrollbar,
.soul-table-wrpr::-webkit-scrollbar,
.project_tool_list::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}

.bridge_wrp::-webkit-scrollbar-track,
.soulwriting-forms-wrpr::-webkit-scrollbar-track,
.soul-table-wrpr::-webkit-scrollbar-track,
.project_tool_list::-webkit-scrollbar-track {
  background: #ffffff;
}

.bridge_wrp::-webkit-scrollbar-thumb,
.soulwriting-forms-wrpr::-webkit-scrollbar-thumb,
.soul-table-wrpr::-webkit-scrollbar-thumb,
.project_tool_list::-webkit-scrollbar-thumb {
  background-color: #499557;
  border: 3px solid #ffffff;
}

.tab-list-wrpr::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.tab-list-wrpr::-webkit-scrollbar-track {
  background: #ffffff;
}

.tab-list-wrpr::-webkit-scrollbar-thumb {
  background-color: #499557;
  border: none;
}

.choose_character .modal-content {
  max-height: unset;
  height: auto;
}
@media (max-width: 767px) {
  .choose_character .modal-content {
    width: 100%;
    padding: 0px 20px;
  }
}
.choose_character .modal-dialog {
  width: 600px;
}
@media (max-width: 767px) {
  .choose_character .modal-dialog {
    width: 100%;
  }
}
.choose_character .modal-dialog .modal-body {
  height: auto;
  overflow: auto;
  padding: 0px;
}
.choose_character .modal-dialog .modal-body .order-list {
  padding-top: 0px;
  border: none;
  max-height: 70vh;
  overflow: auto;
}
.choose_character .modal-dialog .modal-body .order-list .character_item {
  cursor: pointer;
  padding: 13px 30px !important;
}
.choose_character .modal-dialog .modal-body .order-list .character_item:last-child {
  border: none;
}
.choose_character .modal-dialog .modal-body .order-list .character_item .person-cards {
  align-items: center;
  gap: 10px;
}

.privacy-policy * {
  margin-bottom: 12px;
  font-size: 16px;
}

.soul-status-draft {
  background-position: -53px -225px;
}

.soul-status-submitted {
  background-position: -110px -225px;
}

.soul-status-recieved {
  background-position: -81px -225px;
}

.project_add_modal .modal-content {
  height: auto;
}
.project_add_modal .modal-dialog {
  width: 540px;
}
.project_add_modal .modal-dialog .modal-body {
  height: calc(100vh - 180px);
}
.project_add_modal .modal-footer {
  padding-bottom: 0px;
}
.project_add_modal .list_wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}
.project_add_modal .list_wrapper .action-btns {
  margin: 0px 0px 10px;
}
.project_add_modal .project_list {
  position: relative;
  margin-bottom: 20px;
}
.project_add_modal .project_list .delete-btn {
  background: transparent;
  border: none;
  outline: none;
  position: absolute;
  top: 15px;
  right: 4px;
}

.project_list_wrapper {
  background: #f4f4f4;
  margin-top: 20px;
  border-radius: 10px;
  padding: 10px 15px;
  max-height: 280px;
  overflow: auto;
}
.project_list_wrapper .project_name {
  margin-bottom: 5px;
}

.new-proj {
  padding-top: 10px;
  padding-bottom: 10px;
  border-top: 1px solid rgba(73, 149, 87, 0.2);
}

.project-field-wrpr {
  gap: 20px;
}

.soulwriting-table .soul-table-wrpr {
  margin: 0px 0px 50px;
}

.check-list-blk {
  gap: 10px;
}
@media (max-width: 992px) {
  .check-list-blk {
    flex-wrap: wrap;
    margin-top: 15px;
  }
}

.form-check .form-check-input:checked ~ .checked_highligh {
  background: #fffcf9;
  border: 1px solid #fbc87b;
  box-shadow: 4px 4px 28px rgba(19, 61, 48, 0.06);
  color: #262626;
}

.checked_highligh {
  padding: 10px 14px;
  border: 1px solid transparent;
  color: #616161;
}
@media (max-width: 992px) {
  .checked_highligh {
    padding: 0px 6px;
  }
}
.checked_highligh .cat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  overflow: hidden;
}
.checked_highligh .cat-icon img {
  width: 100%;
}

.detail-img {
  border-radius: 5px;
  max-height: 350px;
}

.sales-page-wrpr .online-seminar-card ul li.online-seminar-list {
  gap: 12px;
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  display: flex;
  background-image: url("/images/right-chevron.svg");
  background-repeat: no-repeat;
  background-position: left top 2px;
  padding-left: 30px;
}

.comp-inner-header {
  position: sticky;
  top: 0px;
  background: #fff;
  z-index: 2;
}
@media (max-width: 1200px) {
  .comp-inner-header {
    position: relative;
  }
}
.comp-inner-header .tab-list-wrpr {
  margin-left: 32px;
  margin-right: 32px;
}

.cstm-wrprr {
  height: 100%;
  max-height: calc(100% - 123px);
  overflow: auto;
  padding-bottom: 30px;
}

.google-calendar-btn {
  border: 1px solid #4291ff;
  color: #4291ff;
  padding: 10px 18px;
}

.google-calendar-btn img {
  height: 20px;
  width: 20px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  font-family: "GS-medium";
  padding: 16px 22px;
  background: transparent;
  color: #499557;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.04);
  transition: all ease 250ms;
  line-height: 1.38;
  border-radius: 8px;
  outline: none !important;
  border: 1px solid #499557;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  gap: 4px;
}

.static_page_header {
  margin: 20px 0 20px 0;
}

.privacy-policy p {
  margin-bottom: 12px;
  font-size: 18px;
}

.privacy-policy ul {
  list-style: unset;
  margin-left: 20px;
}

.or-wrpr {
  position: relative;
  text-align: center;
  padding: 20px 0px;
}
.or-wrpr:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background-color: #eee;
  z-index: 0;
}
.or-wrpr .or-content {
  width: -moz-fit-content;
  width: fit-content;
  background: white;
  position: relative;
  z-index: 0;
  padding: 0px 10px;
}

.toggle-sidebar {
  border-radius: 50px;
  border: 1px solid #e7e8f2;
  width: 30px;
  height: 30px;
  background: #fff;
  display: flex;
  align-content: center;
  justify-content: center;
  position: absolute;
  top: 7px;
  left: auto;
  right: -15px;
}
@media (max-width: 767px) {
  .toggle-sidebar {
    display: none;
  }
}
.toggle-sidebar .arrow-icon {
  rotate: 90deg;
  top: 3px;
  position: relative;
  left: -2px;
  transition: all 0.4s ease-in-out;
}
.toggle-sidebar.closed .arrow-icon {
  left: 1px;
  top: 3px;
  rotate: 270deg;
  transition: all 0.4s ease-in-out;
}

.book-meeting-btn .meeting-icon {
  display: none;
}

.ham-icon-wrpr {
  display: none;
}
@media (max-width: 767px) {
  .ham-icon-wrpr {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 6px;
  }
}
.ham-icon-wrpr .ham-bar {
  width: 20px;
  display: block;
  background: #616161;
  transition: all 0.3s ease;
  height: 2px;
}
.ham-icon-wrpr .ham-bar.bar2 {
  width: 80%;
}
.ham-icon-wrpr.open .ham-bar.bar1 {
  transform: rotate(45deg) translate(5px, 5px);
  transition: all 0.3s ease;
}
.ham-icon-wrpr.open .ham-bar.bar2 {
  display: none;
}
.ham-icon-wrpr.open .ham-bar.bar3 {
  transform: rotate(-45deg) translate(2px, -2px);
  transition: all 0.3s ease;
}

.mobile-inner-icons {
  display: none;
}
@media (max-width: 767px) {
  .mobile-inner-icons {
    display: block;
    padding: 8px 0px;
    border-bottom: 1px solid #eef7f1;
  }
}

.soulwriting-bill .modal-content {
  max-height: unset;
  height: auto;
}

.table.border-0 {
  border: none;
}
.table.border-0 td {
  border: none;
}

.react-daterange-picker {
  padding: 10px 14px !important;
}
.react-daterange-picker .react-daterange-picker__wrapper {
  border: none;
}

.table_form_in {
  margin: 0px;
}

.order_header_bar {
  margin-top: 20px;
}

.calender-day-tile {
  background: #fff;
  border-radius: 7px;
  margin-bottom: 5px;
  border: 1px solid green;
  flex: 0 0 13.6% !important;
  max-width: 13.6%;
  font-size: 16px;
  padding: 6px;
}
.calender-day-tile[disabled] {
  background: #f0f0f0;
  color: #a2a2a2;
  opacity: 0.4;
}
.calender-day-tile.react-calendar__tile--active {
  background: #499557 !important;
  color: white !important;
}
@media (max-width: 480px) {
  .calender-day-tile {
    flex: 0 0 12.6% !important;
  }
}

.react-calendar__month-view__days {
  gap: 5px;
}

.react-calendar__month-view__weekdays__weekday {
  text-align: center;
  text-decoration: none;
  margin-bottom: 7px;
}
.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
}

.meeting-schedule-calendar .react-calendar__navigation button:first-child,
.meeting-schedule-calendar .react-calendar__navigation button:last-child,
.meeting-schedule-calendar .react-calendar__navigation button:nth-child(3) {
  display: none;
}

.meeting-schedule-calendar .react-calendar__navigation__arrow {
  background: #fff;
  border: 1px solid #499557;
  border-radius: 7px;
  width: 30px;
  height: 30px;
  color: #499557;
  line-height: 0.9;
  margin: 0px 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.meeting-schedule-calendar .react-calendar__navigation__arrow.react-calendar__navigation__prev-button {
  transform: rotate(90deg);
}
.meeting-schedule-calendar .react-calendar__navigation__arrow.react-calendar__navigation__next-button {
  transform: rotate(270deg);
}

.meeting-schedule-calendar .react-calendar__navigation {
  text-align: right;
  margin: 10px auto;
}

.slot-wrpr .cal-row {
  flex-direction: row;
  margin-top: 10px;
  flex-wrap: wrap;
}

@media (max-width: 1200px) {
  .onboarding-row {
    flex-direction: column;
  }
  .onboarding-row .w-50 {
    width: 100%;
  }
  .onboarding-row .left-half {
    display: none;
  }
  .title-img-wrpr {
    padding-top: 40px;
  }
}
@media (max-width: 767px) {
  .book-meeting-btn {
    position: fixed;
    top: auto;
    bottom: 20px;
    right: 15px;
    z-index: 99;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    padding: 0px !important;
    border: none;
  }
  .book-meeting-btn .meeting-text {
    display: none;
  }
  .book-meeting-btn .meeting-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .order_header_bar {
    flex-direction: column;
    align-items: flex-start !important;
    margin-top: 30px;
  }
  .order_header_bar .table_form_in {
    margin-top: 10px;
    margin-right: 0px;
  }
  .fc .fc-toolbar {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 10px;
  }
}
.btn-sm {
  background: #ffffff;
  border: 1px solid #f2ebde;
  padding: 5px 10px;
  font-size: 12px;
}

.question-wrpr {
  margin-bottom: 34px;
}

.protogonist-opt-list {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  border-radius: 20px;
  align-items: center;
  flex-wrap: wrap;
}
.protogonist-opt-list.two-column .protogonist-opt-item {
  width: 48%;
  margin-bottom: 20px;
}
.protogonist-opt-list .protogonist-opt-item {
  width: 100%;
  margin-bottom: 20px;
}
.protogonist-opt-list .protogonist-opt-item.full {
  width: 100% !important;
}
.protogonist-opt-list .protogonist-opt {
  cursor: pointer;
  width: 100%;
}
.protogonist-opt-list .protogonist-opt input[type=radio] {
  position: absolute;
  opacity: 0;
  height: 8px;
  width: 8px;
  background: #499557;
  left: 20px;
  top: 30px;
  border-radius: 100%;
}
.protogonist-opt-list .protogonist-opt input[type=radio]:checked + .protogonist-content {
  border: 2px solid #499557;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
  transition: ease-in 0.3s;
}
.protogonist-opt-list .protogonist-opt input[type=radio]:checked + .protogonist-content:after {
  content: "";
  position: absolute;
  height: 8px;
  width: 8px;
  background: #499557;
  left: 20px;
  top: 21px;
  border-radius: 100%;
  border: 3px solid #fff;
  box-shadow: 0px 0px 0px 2px #499557;
}
.protogonist-opt-list .protogonist-opt .protogonist-content {
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-left: 50px;
  box-sizing: border-box;
  border: 2px solid #eaeaea;
  background: #ffffff;
  border-radius: 10px;
  transition: box-shadow 0.4s;
  position: relative;
  align-items: flex-start;
  gap: 10px;
}
.protogonist-opt-list .protogonist-opt .protogonist-content:hover {
  box-shadow: 0px 3px 5px 0px #e8e8e8;
}

.protagonist-list-modal {
  position: absolute;
  right: 0;
  width: -moz-fit-content;
  width: fit-content;
  margin-right: 0;
  margin-left: auto;
  height: -moz-fit-content;
  height: fit-content;
  bottom: 0;
  top: auto;
}
.protagonist-list-modal.bridge {
  position: fixed;
}
.protagonist-list-modal.bridge .modal-content {
  width: 637px;
}
.protagonist-list-modal.bridge .modal-content .bridge-table-wrpr .bridge-block .bridge-card {
  max-width: 230px;
  width: 100%;
}
.protagonist-list-modal .close-btn {
  top: 25px;
}
.protagonist-list-modal .overlay-div {
  display: none;
}
.protagonist-list-modal .modal-content {
  width: 500px;
  margin-right: 75px;
  margin-left: auto;
  margin-bottom: 0px;
  margin-top: auto;
  max-height: 75vh;
  overflow: hidden;
  height: 100%;
}
.protagonist-list-modal .modal-content .modal-body {
  height: 60vh;
  overflow: auto;
}
.protagonist-list-modal .modal-content .modal-dialog {
  border: 1px solid #E0EDE5;
  border-radius: 10px 10px 0px 0px;
}

.modal-listing-wrpr {
  margin-top: 20px;
}
.modal-listing-wrpr .accordion-item {
  border-top: 1px solid #E0EDE5;
}
.modal-listing-wrpr .accordion-item:last-child {
  border-bottom: 1px solid #E0EDE5;
}
.modal-listing-wrpr .protagonist-list {
  margin-top: 0px;
}
.modal-listing-wrpr .protagonist-list .protagonist-card {
  margin-left: 0px;
  border: 0px;
  border-radius: 0px;
  padding: 10px 0px;
}
.modal-listing-wrpr .status-badge {
  font-weight: 600;
  border-radius: 50px;
  padding: 0px 8px;
  border: none;
  font-size: 14px;
  line-height: 1.43;
}
.modal-listing-wrpr .status-badge.green {
  color: #499557;
  background-color: #EEF7F1;
}
.modal-listing-wrpr .status-badge.gray {
  color: #07080A;
  background-color: #C6C6C6;
}
.modal-listing-wrpr .protogonist-content {
  padding-left: 66px;
  height: 0px;
  overflow: hidden;
}
.modal-listing-wrpr .protogonist-content.active {
  height: auto;
  padding-bottom: 15px;
}

.protogonist-action-btns .btn {
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding: 8px;
}

.twitter-picker span div {
  border: 1px solid rgb(220, 208, 208);
}

.ftr-row {
  max-width: 98%;
  margin: 0 auto;
  border-radius: 6px;
  padding: 30px;
  padding-right: 80px;
  background: #ececec;
  gap: 15px;
}
@media (max-width: 992px) {
  .ftr-row {
    flex-direction: column;
    padding-right: 0px;
  }
}
@media (max-width: 992px) {
  .ftr-row .ftr-left,
  .ftr-row .ftr-right {
    flex: 0 0 auto;
    width: 100%;
  }
}
.ftr-row .ftr-links-list {
  gap: 16px;
  flex-wrap: wrap;
}
@media (max-width: 992px) {
  .ftr-row .ftr-links-list {
    justify-content: center;
    text-align: center;
  }
}
.ftr-row .ftr-link {
  font-size: 14px;
  font-weight: normal;
  color: #A2A2A2;
  white-space: nowrap;
  text-align: center;
}
.ftr-row .ftr-link.active {
  color: #07080A;
}
.ftr-row .social-media-list {
  gap: 16px;
}
@media (max-width: 992px) {
  .ftr-row .social-media-list {
    justify-content: center;
    text-align: center;
  }
}
.ftr-row .social-media-list .sm-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
.ftr-row .social-media-list .sm-link .sm-icon {
  width: 20px;
  height: 20px;
}

.ftr-bottom-row {
  padding: 15px 0px;
}

.bridge-step-wrpr .soul-step-inner {
  flex: 0 0 calc(100% - 250px);
  max-width: calc(100% - 250px);
}

.bridge-table-wrpr {
  width: 100%;
  gap: 34px;
}
.bridge-table-wrpr .bridge-block {
  flex: 0 0 calc(50% - 17px);
  max-width: calc(50% - 17px);
}
.bridge-table-wrpr .bridge-block.bridge-past-block .bridge-list-item {
  position: relative;
}
.bridge-table-wrpr .bridge-block.bridge-past-block .bridge-list-item::before {
  content: "";
  position: absolute;
  top: 18px;
  right: -45px;
  left: auto;
  border: 1px dashed #e5dec1;
  width: 100px;
  z-index: 0;
}
.bridge-table-wrpr .bridge-block .bridgePast {
  margin: 0;
}
.bridge-table-wrpr .bridge-block .bridgePersent {
  font-weight: 300;
  margin: 0;
}
.bridge-table-wrpr .bridge-block .bridge-subtitle {
  margin-bottom: 14px;
}
.bridge-table-wrpr .bridge-block .bridge-count {
  margin-right: 3px;
  display: flex;
  min-width: 20px;
  justify-content: start;
}
.bridge-table-wrpr .bridge-block .bridge-list-item {
  margin-bottom: 10px;
  position: relative;
}
.bridge-table-wrpr .bridge-block .bridge-list-item.past .bridge-card {
  max-width: calc(100% - 23px);
}
.bridge-table-wrpr .bridge-block .bridge-list-item.prenset {
  gap: 10px;
}
.bridge-table-wrpr .bridge-block .bridge-list-item .btn-text {
  background-color: transparent;
  border: none;
  padding: 0px;
  outline: none;
  box-shadow: none;
  cursor: pointer;
}
.bridge-table-wrpr .bridge-block .bridge-list-item .btn-text:hover {
  background-color: transparent;
  border: none;
  padding: 0px;
  outline: none;
}
.bridge-table-wrpr .bridge-block .bridge-list-item .btn-text.full-delete-bridge {
  position: absolute;
  right: -30px;
  width: 20px;
  height: 20px;
}
.bridge-table-wrpr .bridge-block .bridge-content {
  cursor: pointer;
  flex: 0 0 calc(100% - 60px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bridge-table-wrpr .bridge-block p.bridge-content {
  min-height: 20px;
}
.bridge-table-wrpr .bridge-block .bridge-card {
  border-radius: 6px;
  border: 1px solid #e5dec1;
  background: #fbf9f3;
  padding: 6px 10px;
  gap: 10px;
  width: 100%;
  position: relative;
}
.bridge-table-wrpr .bridge-block .bridge-card .btn-text {
  background-color: transparent;
  border: none;
  padding: 0px;
  outline: none;
  box-shadow: none;
  cursor: pointer;
}
.bridge-table-wrpr .bridge-block .bridge-card .btn-text:hover {
  background-color: transparent;
  border: none;
  padding: 0px;
  outline: none;
}
.bridge-table-wrpr .bridge-block .add-bridge-card {
  border-radius: 6px;
  border: 1px dashed #499557;
  background: #fff;
  padding: 8px 10px;
  gap: 10px;
  width: 100%;
  z-index: 10;
}

.more-info-wrpr {
  min-width: 600px;
}
.more-info-wrpr .info-row {
  padding: 10px;
  border-bottom: 1px solid #E0EDE5;
}
.more-info-wrpr .info-row:last-child {
  border-bottom-width: 0px;
}
.more-info-wrpr .info-row .info-left {
  flex: 0 0 150px;
  max-width: 150px;
}
.more-info-wrpr .info-row .info-right {
  flex: 0 0 auto;
  width: calc(100% - 150px);
}

.read-more {
  background: transparent !important;
  color: #499557 !important;
  border: none !important;
  outline: none !important;
}

.cookie-popup {
  top: auto;
  margin-left: 0px;
  z-index: 9999;
  left: 46px;
  box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.34);
  bottom: 62px;
  position: fixed;
  background: #fff;
  padding: 20px;
  width: 100%;
  max-width: 403px;
  font-size: 13px;
  border-radius: 4px;
  text-align: center;
}
.cookie-popup h4,
.cookie-popup p {
  margin-bottom: 10px;
}
.cookie-popup .btn-block {
  gap: 16px;
}
.cookie-popup .cookie-cat-wrpr {
  text-align: left;
  padding-left: 50px;
}
.cookie-popup .cookie-cat-wrpr .cat-name {
  font-size: 16px;
  padding-bottom: 10px;
}
.cookie-popup .cookie-cat-wrpr UL {
  margin-left: 10px;
}

.list-btn {
  display: none;
}
@media (max-width: 992px) {
  .list-btn {
    display: block;
  }
}

.topic-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 270px;
}
.topic-name .icon {
  display: none;
}
@media (max-width: 992px) {
  .topic-name .icon {
    display: inline-block;
    margin-right: 10px;
  }
}

#top_right_video {
  aspect-ratio: 16/9;
}

.border-none {
  border: none !important;
}

.tab-right-btn-wrpr {
  gap: 14px;
}
@media all and (max-width: 480px) {
  .tab-right-btn-wrpr button {
    font-size: 16px;
  }
}
@media (min-width: 992px) {
  .tab-right-btn-wrpr {
    justify-content: flex-end;
    padding-right: 16px;
  }
}

.modal-body .errorMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 30px;
}

#limitModal .modal-content {
  height: auto;
}
#limitModal .modal-body {
  height: 100%;
}

.audio-block-wrpr {
  display: flex;
  gap: 40px;
  margin-bottom: 5px;
}
@media (max-width: 992px) {
  .audio-block-wrpr {
    flex-direction: column;
    gap: 0px;
  }
}

@media (max-width: 1200px) {
  .cart_wrpr_outer {
    flex-direction: column;
    height: auto !important;
  }
}
@media (max-width: 1200px) {
  .cart_wrpr_outer .cart_total__amount_wrpr {
    flex: 1 1 auto;
    padding-left: 0px;
    border-left: none;
  }
}

.cart_wrpr > table tr td:first-child {
  min-width: 300px;
}

.mobile-title {
  margin-bottom: 16px;
  display: none;
}
@media (max-width: 767px) {
  .mobile-title {
    display: block;
  }
}

.tab-list-search-wrpr {
  padding-right: 20px;
  border-bottom: 1px solid #daece1;
}

.search-wrpr {
  position: relative;
  max-width: 400px;
  margin-top: -10px;
  width: 100%;
}
@media (max-width: 767px) {
  .search-wrpr {
    display: none;
  }
}
.search-wrpr .search-button {
  position: absolute;
  top: 0px;
  right: 0px;
  left: auto;
  width: 50px;
  min-width: 50px;
  height: 100%;
  background: transparent;
  border: 1px solid #E0EDE5;
  border-radius: 0px 9px 9px 0px;
}
.search-wrpr .search-button .icon {
  transform: scale(1.5);
}

.clear-button {
  border: none !important;
  background: none !important;
  position: absolute;
  left: auto;
  top: 23%;
  right: 50px;
  outline: none !important;
  box-shadow: none !important;
}

.protogonist-opt-list .form-inner {
  width: 100%;
}

div#affirmation-10 .ck-content {
  height: 100px;
}

@media (max-width: 767px) {
  .footer-btn.protagonists {
    display: none;
  }
}

@media (max-width: 767px) {
  .footer-btn.SendCompanion {
    display: none;
  }
}

@media (max-width: 767px) {
  .footer-btn.btn-next {
    background: linear-gradient(90deg, #499557, #499557);
  }
}

.scene-text-wrpr .ck-content {
  width: 100%;
  word-wrap: break-word !important;
}

.mobile-only {
  display: none;
}
@media (max-width: 767px) {
  .mobile-only {
    display: block;
  }
}

@media (max-width: 767px) {
  .desktop-only {
    display: none;
  }
}

@media (min-width: 992px) {
  .createProject {
    position: absolute;
    left: 153px;
    top: 10px;
  }
}

.sendToCompanionMobile {
  margin-top: 5px;
}

.protogonist-opt-list .protogonist-opt input[type=radio] + .protogonist-content:after {
  content: "";
  width: 8px;
  height: 8px;
  position: absolute;
  left: 21px;
  top: 22px;
  border-radius: 100%;
  box-shadow: 0px 0px 0px 2px #eee;
  border: 3px solid #fff;
}

.soulwriting-wrpr .soul-content-wrpr .soul-intro {
  line-height: 1.6 !important;
}

.bill-modal-test .companion-card-row .billing_intro {
  line-height: 1.6 !important;
}

.desc .column p {
  line-height: 1.6 !important;
}

.priceCalculation {
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  .soul-content-open .AnimatedShowMore__MainContent {
    max-height: -moz-max-content !important;
    max-height: max-content !important;
  }
}

.comp-about-desc > div {
  width: 100% !important;
  margin: 0px !important;
}

@media (max-width: 480px) {
  .profile-modal .modal-btn-wrpr {
    flex-direction: column;
  }
}

.calender-day-tile abbr {
  color: green;
}

.calender-day-tile.react-calendar__tile--active abbr {
  color: #fff;
}

.project-details-container {
  width: 100%;
  border-collapse: collapse;
}

.table-header,
.table-row {
  display: flex;
  padding: 10px 0;
}

.table-header {
  background-color: #f4f4f4;
  font-weight: bold;
}

.table-row {
  border-bottom: 1px solid #ddd;
}

.serial-no-header,
.actor-header,
.text-header,
.comment-header,
.serial-no-cell,
.actor-cell,
.text-cell,
.comment-cell {
  padding: 10px;
  flex: 1;
  display: flex;
  align-items: center;
}

.text-cell {
  flex: 3;
}

.comment-cell {
  flex: 2;
}

.personProject .person-card {
  width: 160px;
  height: 32px;
}

.ckEditorOldProject .ck-content.ck-editor__editable {
  background: #fff;
}

.ckeditor-component .text-cell {
  flex-direction: column;
  background: #fff;
  border: 1px solid #ccc;
}

.fourth-col .commentContent {
  background: #fff;
  border: 1px solid #ccc;
  padding: 10px;
}

.fourth-col .commentLabel {
  font-weight: 500 !important;
  font-family: "GS-medium";
  color: #616161;
  padding: 2px 0px 5px 0px;
  display: block;
}

.fourth-col {
  width: 250px;
}

.text-cellContent {
  display: flex;
  flex-direction: column;
}

@media (max-width: 480px) {
  .mt-20 {
    margin-top: 20px;
  }
}

.generalComment {
  width: 700px !important;
  margin: 20px;
}/*# sourceMappingURL=style.css.map */