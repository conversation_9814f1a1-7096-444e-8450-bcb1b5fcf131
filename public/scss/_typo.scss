@font-face {
  font-family: "GS-bold";
  src: url("../fonts/GeneralSans-Bold.woff") format("woff");
  src: url("../fonts/GeneralSans-Bold.woff2") format("woff2");
}

@font-face {
  font-family: "GS-light";
  src: url("../fonts/GeneralSans-Light.woff") format("woff");
  src: url("../fonts/GeneralSans-Light.woff2") format("woff2");
}

@font-face {
  font-family: "GS-medium";
  src: url("../fonts/GeneralSans-Medium.woff") format("woff");
  src: url("../fonts/GeneralSans-Medium.woff2") format("woff2");
}

@font-face {
  font-family: "GS";
  src: url("../fonts/GeneralSans-Regular.woff") format("woff");
  src: url("../fonts/GeneralSans-Regular.woff2") format("woff2");
}

@font-face {
  font-family: "GS-semibold";
  src: url("../fonts/GeneralSans-Semibold.woff") format("woff");
  src: url("../fonts/GeneralSans-Semibold.woff2") format("woff2");
}

@font-face {
  font-family: "inter", sans-serif;
  src: url("../fonts/Inter-Regular.woff") format("woff");
}

@font-face {
  font-family: "inter-black", sans-serif;
  src: url("../fonts/Inter-Black.woff") format("woff");
}

@font-face {
  font-family: "inter-bold", sans-serif;
  src: url("../fonts/Inter-Bold.woff") format("woff");
}

@font-face {
  font-family: "inter-medium", sans-serif;
  src: url("../fonts/Inter-Medium.woff") format("woff");
}

@font-face {
  font-family: "inter-semibold", sans-serif;
  src: url("../fonts/Inter-SemiBold.woff") format("woff");
}

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
$gs: "GS";
$gs-l: "GS-light";
$gs-m: "GS-medium";
$gs-sm: "GS-semibold";
$gs-b: "GS-bold";

html,
body,
div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
img,
figure,
footer,
header,
section {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-family: $gs;
  scroll-behavior: smooth;
}
body {
  line-height: 1;
  color: $black;
  font-family: $gs;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

h1,
.h1 {
  font-size: 38px;
  line-height: 1.06;
  font-weight: 800;
}

h2,
.h2 {
  font-size: 28px;
  font-weight: 800;
  line-height: 1.17;
}

h3,
.h3 {
  font-size: 26px;
  font-weight: 800;
  line-height: 1.18;
}

h4,
.h4 {
  font-size: 24px;
  font-weight: 800;
  line-height: 1.33;
}

h5,
.h5 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
}

h6,
.h6 {
  font-size: 16px;
  line-height: 1.33;
  font-weight: 400;
}

p,
.p {
  font-size: 14px;
  line-height: 1.43;
  color: $grey-3;
}
.fs-12 {
  font-size: 12px;
  line-height: 1.43;
}
a {
  color: $accent;
  text-decoration: none;
  outline: none !important;
  box-shadow: none !important;
  cursor: pointer !important;
  &:hover {
    color: $accent;
    text-decoration: none;
  }
}
button {
  cursor: pointer;
}
ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
}
.font-gs {
  font-family: $gs;
}
.font-inter {
  font-family: "Inter", sans-serif;
}
.fw300 {
  font-weight: 300 !important;
  font-family: $gs-l;
}
.fw400 {
  font-weight: 400 !important;
  font-family: $gs;
}
.fw500 {
  font-weight: 500 !important;
  font-family: $gs-m;
}
.fw600 {
  font-weight: 600 !important;
  font-family: $gs-sm;
}
.fw700 {
  font-weight: 700 !important;
  font-family: $gs-b;
}
