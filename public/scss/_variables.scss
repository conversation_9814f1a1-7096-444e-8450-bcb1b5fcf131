$white: white;
$black: #000000;
$black-1: #021416;
$dark-grey: #404040;
$grey-1:#07080A;
$grey-2:#14182F;
$grey-3:#262626;
$grey-4:#5F6667;
$grey-5:#616161;
$grey-6:#7E7E7E;
$grey-7:#A2A2A2;
$grey-8:#C6C6C6;
$grey-9:#E0EDE5;
$accent:#499557;
$accent-l:#EEF7F1;
$red:#D8090D;


:root{
  --white:#ffffff;
  --black:#000000;
  --text:$grey-3;
  --accent:$accent;
}

$themeColours: (
    "white": $white,
    "black": $black,
    "black-1": $black-1,
    "dark-grey": $dark-grey,
    "grey-1": $grey-1,
    "grey-2": $grey-2,
    "grey-3": $grey-3,
    "grey-4": $grey-4,
    "grey-5": $grey-5,
    "grey-6": $grey-6,
    "grey-7": $grey-7,
    "grey-8": $grey-8,
    "grey-9": $grey-9,
    "accent": $accent,
    "red": $red
);

@each $themeColour, $i in $themeColours {
  .text-{
    &#{$themeColour} {
        color: $i !important;
    }	
  }
}


@each $bgColour, $bg in $themeColours {
  .bg-{
    &#{$bgColour} {
        background-color: $bg !important;
    }	
  }
}

$transs:  cubic-bezier(0, 0, 0.2, 1);

$icons: (
    "fb": -44px,
    "insta": 0px,
    "linked": -86px,
    "twitter": -132px,
);

@each $icon, $i in $icons {
  .icon-{
    &#{$icon} {
        background: url(../images/) no-repeat;
        background-position-y: center;
        background-position-x: #{$i};
        width: 34px;
        height: 34px;
        display: inline-block;
    }	
  }
}

@mixin str{
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 4;
}

@mixin no-scrollbar{
  /* width */
  &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
      background: transparent;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
      background: transparent;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
      background: transparent;
  }
}
