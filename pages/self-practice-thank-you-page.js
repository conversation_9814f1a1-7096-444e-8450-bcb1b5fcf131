import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import lottie<PERSON>son from "../animation.json";
import { payMentSelfPractice } from "../redux/action/kuby-courses";
import dynamic from "next/dynamic";
import { useCookie } from "next-cookie";
import { checklanguage, getLanguages } from "../constant";

const SelfPracticeThankYouPage = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });
  const router = useRouter();
  const { query } = router;
  const cookies = useCookie();
  useEffect(() => {
    const jwtToken = cookies.get("jwtToken");
    if (typeof jwtToken !== "undefined" && jwtToken !== null) {
      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
    }
    if (
      Object.keys(query)?.length > 0 &&
      query?.order_id &&
      query?.order_item_id
    )
      createPaymentForSelfPractice();
  }, [query]);

  const createPaymentForSelfPractice = async () => {
    try {
      let response = await payMentSelfPractice({
        data: router?.asPath?.split("?")[1],
      });
      router.push(router.pathname, "", { scroll: false });
    } catch (err) {}
  };

  return (
    <div className="h-100 d-flex align-center justify-center thanku-wrpr">
      <div className="text-center thanku-inner">
        <div className="thanku-gif">
          <Lottie
            loop
            animationData={lottieJson}
            play
            style={{
              width: 120,
              height: 120,
              marginLeft: "auto",
              marginRight: "auto",
            }}
          />
        </div>
        <h3 className="h3 text-dark-grey fw500">
          {getLanguages(checklanguage, "paymentCompleted")}
          
        </h3>
        <p className="p text-grey-5">{getLanguages(checklanguage, "selfPracticeThankyouPageDetailMessage", [], [], true)}</p>
        {isLoggedIn ? (
          <button onClick={() => router.push("/")} className="btn-accent">
            {getLanguages(checklanguage, "goToHome")}
          </button>
        ) : (
          <>
            <p className="p text-grey-5">
              {getLanguages(checklanguage, "thankyouSelf")}

            </p>
            <button
              onClick={() => router.push("/signin")}
              className="btn-accent"
            >
              {getLanguages(checklanguage, "loginNow")}
            </button>
          </>
        )}
        
      </div>
      <img
        src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 1"
      />
      <img
        src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 2"
      />
    </div>
  );
};

export default SelfPracticeThankYouPage;
