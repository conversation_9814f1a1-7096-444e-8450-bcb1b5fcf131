import React, { useEffect, useState } from 'react'
import { useCookie } from 'next-cookie'
import { useRouter } from 'next/router'
import ProductShop from '../../components/Common/ProductShop'
import { getWisLists ,addToWishLists } from "../../redux/action/product"
import { addProductToCart, USER_CART } from "../../redux/action/cart"
import _ from 'lodash'
import { useDispatch, useSelector } from 'react-redux'
import EmptyScreen from '../../components/Common/EmptyScreen'
const Wishlist = () => {
    const [WishListProduct, setWishListProduct] = useState(null);
    const cookies = useCookie();
    const router = useRouter();
    const dispatch = useDispatch();
    const userCart = useSelector(state => state.cart);
    let [IsFetching, setIsFetching] = useState(false)
    useEffect(() => {
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            getProductAddedInWatchList()
        }
        else {
            router.push('/')
        }
    }, [])

    const getProductAddedInWatchList = async () => {
        setIsFetching(true)
        try {
            let response = await getWisLists();
            setWishListProduct(response?.data?.responseData);
            setIsFetching(false)

        }
        catch (err) {

        }
    }
    const addToCart = async (product, index) => {
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            try {
                let response = await addProductToCart({
                    productId: product?.id,
                    count: 1
                })
                dispatch({
                    type: USER_CART,
                    payload: response?.data?.responseData
                })
            }
            catch (err) {

            }
        }
        else {
            router.push(`/signin?redirectTourl=/dashboard/shop`)
        }
    }
    const checkProductInCart = (id) => {
        let userCartData = { ...userCart }
        let findProduct = _.find(userCartData?.userCart?.records, ["productId", id])
        if (findProduct != undefined) {
            return true
        }
        else {
            return false
        }
    }
    const addToWishList = async (id, index, WishLists) => {
        try {
            let response = await addToWishLists({ productId: id })
            let updateresponseObject = { ...WishListProduct }
            if (WishLists?.length > 0) {
                updateresponseObject.products.splice(index ,1)
            }
            setWishListProduct(updateresponseObject)

        }
        catch (err) {

        }
    }
    return (
        <>
        <ProductShop isWisList = {1} IsFetching={IsFetching} ProductLists={WishListProduct} checkProductInCart={checkProductInCart} addToCart={addToCart} addToWishList={addToWishList} />
        {WishListProduct?.products?.length === 0 && <EmptyScreen productType={2} />}
        </>
        
    )
}

export default Wishlist