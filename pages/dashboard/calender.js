import moment from "moment";
import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import CalendarComponent from "../../components/Common/CalenderComponent";
import MeetingSuccessModal from "../../components/Common/Modals/MeetingSuccessModal";
import { getEventsCalendar } from "../../redux/action/kuby-companion"
import { getLanguages, checklanguage } from "../../constant";

function Calender() {
  const cookies = useCookie();
  const router = useRouter();
  const [calendarDate, setcalendarDate] = useState({
    startDate: "",
    endDate: ""
  })
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [meetingPopupSuccess, setmeetingPopupSuccess] = useState(false);
  const [successMeetingType, setsuccessMeetingType] = useState(1)
  const [initialDate, setInitialDate] = useState(router?.query?.s || null)

  const [allevents, setsetAllEvents] = useState(null)
  useEffect(() => {
    if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
      if (startDate != null && endDate != null) {
        calendarMeeting()
      }
    }
    else {
      router.push(`/signin?redirectTourl=/dashboard/calender`)

    }
  }, [startDate, endDate, meetingPopupSuccess])

  useEffect(() => {

  }, []);

  const calendarMeeting = async () => {
    try {
      let response = await getEventsCalendar({ startDate: startDate, endDate: endDate, status: 1 })
      setsetAllEvents(response?.data?.responseData?.meetings)
    }
    catch (err) {

    }

  }
  return (
    <>
      <div className="calender-page-wrpr">
        <div className="title-text">
          <h4 className="h4 text-dark-grey d-flex align-center calender-title"><span className="icon icon-calender"></span>{getLanguages(checklanguage, "calendar")}</h4>
          <p className="p text-grey-5">
          { getLanguages(checklanguage, "calendarPageInstructions") }
          </p>
        </div>
        {/* <div className="calender-wrpr"> */}
        <CalendarComponent
          calendarDate={calendarDate}
          appointments={allevents}
          setcalendarDate={setcalendarDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          meetingPopupSuccess={meetingPopupSuccess}
          setmeetingPopupSuccess={setmeetingPopupSuccess}
          setsuccessMeetingType={setsuccessMeetingType}
          successMeetingType={successMeetingType}
          initialDate={initialDate}
        />
        {/* </div> */}
      </div>
      {
        meetingPopupSuccess &&  
        <MeetingSuccessModal
          show={meetingPopupSuccess}
          onHide={() => setmeetingPopupSuccess(false)}
          resccheduleMeeting={successMeetingType}
        />
      }
    </>
  );
}

export default Calender;
