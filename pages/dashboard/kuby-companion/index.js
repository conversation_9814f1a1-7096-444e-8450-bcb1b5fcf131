import CompanionCard from "../../../components/Common/Cards/CompanionCard";
import Filters from "../../../components/Common/Filters";
import {
  checklanguage,
  COMPANION_NAV_LABELS,
  getLanguages,
  COMPANION_TYPE,
} from "../../../constant";
import CommonNavigation from "../../../helpers/CommonNavigation";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import {
  getAllCompanions,
  getMyCompanions,
} from "../../../redux/action/kuby-companion";
import { useCookie } from "next-cookie";
import CompanionCardSkeleton from "../../../components/Common/SkeletonLoader/CompanionCardSkeleton";
import InfiniteScroll from "react-infinite-scroll-component";
import { InfinitySpin } from "react-loader-spinner";
import { getAllLanguages } from "../../../redux/action/auth";
import LimitReachedModal from "../../../components/Common/Modals/LimitReachedModal";
import CompanionCurrentAppointments from "../../../components/KubyCompanion/CurrentAppointments";

function KubyCompanions({ languageArray, queryData }) {
  const cookies = useCookie();
  const router = useRouter();

  const { pathname, query } = router;
  const [CompanionList, setCompanionList] = useState(null);
  const [isFetching, setisFetching] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNext, setHasNext] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [shwoMycontanct, setShowMyContact] = useState(false);
  const [companionNav, setCompanionNav] = useState();
  const [modalOpen, setModalOpen] = useState(false);

  useEffect(() => {
    if(query?.tabstatus == "currentAppointments"){
      setisFetching(false);

    }else{
      getCompanionList();
    }
    
  }, [currentPage]);

  const fetchMyCompanions = async () => {
    const checkMyCompanions = await getMyCompanions({
      pageNumber: currentPage,
      limit: 100,
      onlyMyCompanions: 1,
    });
    if (checkMyCompanions?.data?.responseData?.users?.length === 0) {
      const fileterNavLables = COMPANION_NAV_LABELS.filter((i) => i.tab !== 3);
      setCompanionNav(fileterNavLables);
    } else {
      setCompanionNav(COMPANION_NAV_LABELS);
    }
  };

  useEffect(() => {
    const jwtToken = cookies.get("jwtToken");
    if (jwtToken) {
      fetchMyCompanions();
    } else {
      const fileterNavLables = COMPANION_NAV_LABELS.filter((i) => i.tab !== 3);
      setCompanionNav(fileterNavLables);
    }
  }, []);

  const getCompanionList = async () => {
    setisFetching(true);
    try {
      let dataQuery = { ...query };
      dataQuery.language = localStorage.getItem("language") ?? "de";
      delete dataQuery.tabstatus;
      delete dataQuery.companionId;
      delete dataQuery.type;

      let response = null;
      if (query?.tabstatus === "companions") {
        delete dataQuery.language;
        const jwtToken = cookies.get("jwtToken");
        if (jwtToken) {
          response = await getMyCompanions({
            pageNumber: currentPage,
            limit: 100,
            onlyMyCompanions: 1,
            ...dataQuery,
            random: 1,
          });
        }
      } else {
        response = await getAllCompanions({
          pageNumber: currentPage,
          limit: 100,
          role: COMPANION_TYPE[query?.tabstatus || queryData.tabstatus],
          meetingProductId: 1,
          ...dataQuery,
          random: 1,
        });
      }

      if (currentPage > 1) {
        setCompanionList((prevList) => ({
          ...prevList,
          users: [...prevList.users, ...response?.data?.responseData?.users],
        }));
      } else {
        setCompanionList(response?.data?.responseData);
      }

      setisFetching(false);
      setHasNext(response?.data?.responseData?.loadMore);
    } catch (err) {
      setisFetching(false);
      console.error(err);
    }
  };

  const fetchData = () => {
    // console.log(`fetchData`);
    if (CompanionList?.totalPages > currentPage) {
      setTimeout(() => {
        setCurrentPage((prev) => prev + 1);
      }, 600);
    }
  };

  const hideErrorModal = () => {
    setErrorModal(false);
  };
  useEffect(() => {
   //alert(modalOpen)
  }, [modalOpen])

  return (
    <>
      <div className="h-100 companion-wrpr">
        <div className={`comp-inner-header ${modalOpen ? "modal-open" : ""} `}>
          <div className="d-flex align-center justify-between b-0 inner-header">
            <div className="d-flex align-center title-wrpr">
              <span className="icon title-icon companion-icon"></span>
              <h4 className="fw500 page-title">
                {getLanguages(checklanguage, "kubycompanion")}
              </h4>
              
            </div>
          </div>
          {/* <div className="sub-heading-description">
            <p className="large">{getLanguages(checklanguage, "companionSelectionDescriptionText1")}</p>
            <p className="small">{getLanguages(checklanguage, "companionSelectionDescriptionText2")}</p>
          </div> */}
          <CommonNavigation data={companionNav} kubynavbarStatus={1} />
        </div>
        <div id="scrollableDiv" className="companion-tab-wrpr cstm-wrprr">
          <InfiniteScroll
            style={{ flex: 1, overflow: "auto" }}
            dataLength={CompanionList ? CompanionList.users.length : 0}
            next={fetchData}
            hasMore={hasNext}
            // scrollThreshold={1.0}
            scrollableTarget="scrollableDiv"
            loader={
              <div className="loader-svg">
                <InfinitySpin width="200" color="#248BFF" />
              </div>
            }
          >
            <div className="tab-companion tab-contents companion-card-row active">
              {(isFetching || CompanionList == null) && query?.tabstatus != "currentAppointments" ? (
                <CompanionCardSkeleton listsToRender={3} />
              ) : (
                (() => {
                  switch (query.tabstatus) {
                    case "currentAppointments":
                      return (
                        <CompanionCurrentAppointments
                          tab={query.tabstatus}
                          CompanionList={CompanionList}
                          fetchData={fetchData}
                          hasNext={hasNext}
                          setModalOpen={setModalOpen}
                        />
                      );
                    case "professionals":
                      return (
                        <CompanionCard
                          tab={query.tabstatus}
                          CompanionList={CompanionList}
                          fetchData={fetchData}
                          hasNext={hasNext}
                          setModalOpen={setModalOpen}
                        />
                      );
                    case "students":
                      return (
                        <>
                          {/* <p style={{ background: "white", padding: "10px" }}>
                            {getLanguages(checklanguage, "companionLimitText1")}
                          </p> */}
                          <div className="sub-heading-description">
                            <p className="large">{getLanguages(checklanguage, "companionLimitText1")}</p>
                            <p className="small">{getLanguages(checklanguage, "companionLimitText2")}</p>
                          </div>
                          <div className="student-card-message-container">
                          <CompanionCard
                            tab={query.tabstatus}
                            CompanionList={CompanionList}
                            fetchData={fetchData}
                            hasNext={hasNext}
                            errorModal={errorModal}
                            hideErrorModal={hideErrorModal}
                            setErrorModal={setErrorModal}
                            setModalOpen={setModalOpen}
                          />
                          </div>
                        </>
                      );
                    case "companions":
                      return (
                        <CompanionCard
                          tab={query.tabstatus}
                          CompanionList={CompanionList}
                          fetchData={fetchData}
                          hasNext={hasNext}
                          setModalOpen={setModalOpen}
                        />
                      );
                    default:
                      return <></>;
                  }
                })()
              )}
            </div>
          </InfiniteScroll>
        </div>
      </div>
      {errorModal && (
        <LimitReachedModal show={errorModal} onHide={hideErrorModal} />
      )}
    </>
  );
}

export async function getServerSideProps(ctx) {
  try {
    let response = await getAllLanguages();
    return {
      props: {
        languageArray: response?.data?.responseData,
        queryData: ctx.query,
      },
    };
  } catch (err) {
    console.error(err);
    return {
      props: {
        languageArray: [],
        queryData: ctx.query,
      },
    };
  }
}

export default KubyCompanions;
