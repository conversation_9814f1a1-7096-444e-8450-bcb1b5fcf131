import React, { useEffect, useState } from "react";
import LessonComponent from "../../../components/LessonComponent";
import SelfPracticeTab from "../../../components/SelfPractice/SelfPracticeTab";
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import VimeoPlayer from "../../../components/VimeoPlayer";
import ReactVideoPlayer from "../../../components/ReactVideoPlayer";
import { getChapters } from "../../../redux/action/kuby-courses";
import { useRouter } from "next/router";
import {
  LessonSkeleten,
  SkeletonVemio,
} from "../../../components/Common/SkeletonLoader/SeminarSkeleton";
import ReactStarModal from "../../../components/Common/Modals/RatingStarModal";
import RatingStar from "../../../components/Common/Rating";
import { useCookie } from "next-cookie";
import { getLanguages, checklanguage } from "../../../constant";
import AudioPlayerComponent from "../../../components/Common/AudioPlayerComponent";
import { isMobile } from "react-device-detect";
const SelfPracticeDetail = (props) => {
  const urlParams = new URLSearchParams(window.location.search);
  const playerType = urlParams.get("player");
  const router = useRouter();
  const [initialVideoToPlay, setInitialVideoToPlay] = useState([0, 0]);
  const cookies = useCookie();
  const [lessonNavigation, setLessonNavigation] = useState(false);
  const [getChapterLists, setgetChapterLists] = useState(null);
  const [SubVideosLesson, setSubVideosLesson] = useState(null);
  const [commentsList, setCommentsList] = useState(null);
  const [isFetching, setisFetching] = useState(true);
  const [Play, setPlay] = useState(false);
  const [OverAllRating, setOverAllRating] = useState(null);
  const [ViewData, setViewData] = useState({
    description: "",
    attachment: null,
  });
  const [subLessonId, setsubLessonId] = useState("");
  const [ratingPopUp, setratingPopUp] = useState(false);
  const [UpdateIndex, setUpdateIndex] = useState({
    parentIndex: 0,
    childIndex: 0,
  });
  useEffect(() => {
    if (
      props?.id &&
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      getChapterList(props?.id);
    } else {
      router.push("/");
    }
  }, [props, OverAllRating]);

  const getChapterList = async (id) => {
    if (OverAllRating === 2) {
      setisFetching(false);
    } else if (OverAllRating === 1) {
      setisFetching(false);
    } else {
      setisFetching(true);
    }
    try {
      let response = await getChapters({ topicId: id });
      setisFetching(false);
      if (OverAllRating === 2 || OverAllRating === 1) {
        for (
          let i = 0;
          i < response?.data?.responseData?.topicList?.length;
          i++
        ) {
          for (
            let j = 0;
            j < response?.data?.responseData?.topicList?.[i]?.subVideos?.length;
            j++
          ) {
            response.data.responseData.topicList[i].subVideos[j].attachments = [
              ...response?.data?.responseData?.topicList?.[i]?.subVideos[j]
                .attachments,
              ...response?.data?.responseData?.topicList[i]?.attachments,
            ];
          }
        }
        let updateRating = { ...response?.data?.responseData };
        updateRating.topicList[UpdateIndex?.parentIndex].subVideos[
          UpdateIndex?.childIndex
        ].rating =
          response?.data?.responseData?.topicList?.[
            UpdateIndex?.parentIndex
          ]?.subVideos[UpdateIndex?.childIndex].rating;
        setgetChapterLists(updateRating);
        setSubVideosLesson(
          response?.data?.responseData?.topicList?.[UpdateIndex?.parentIndex]
            ?.subVideos[UpdateIndex?.childIndex]
        );
        setOverAllRating(1);
      } else if (OverAllRating == null) {
        let lastWatchedVideoId =
          response?.data?.responseData?.topicDetails?.lastWatchedId;
        //let lastWatchedVideoId = 5247;
        for (
          let i = 0;
          i < response?.data?.responseData?.topicList?.length;
          i++
        ) {
          for (
            let j = 0;
            j < response?.data?.responseData?.topicList?.[i]?.subVideos?.length;
            j++
          ) {
            if (
              lastWatchedVideoId &&
              lastWatchedVideoId ==
                response?.data?.responseData?.topicList?.[i]?.subVideos[j].id
            ) {
              setInitialVideoToPlay([i, j]);
            }
            response.data.responseData.topicList[i].subVideos[j].attachments = [
              ...response?.data?.responseData?.topicList?.[i]?.subVideos[j]
                .attachments,
              ...response?.data?.responseData?.topicList[i]?.attachments,
            ];
          }
        }
        setgetChapterLists(response?.data?.responseData);
        setsubLessonId(
          response?.data?.responseData?.topicList?.[0]?.subVideos?.[0]?.id
        );
        let object = {};
        (object.description =
          response?.data?.responseData?.topicList?.[0]?.subVideos[0]?.description),
          (object.attachment =
            response?.data?.responseData?.topicList?.[0]?.subVideos[0]?.attachments);
        setViewData({ ...object });
        setSubVideosLesson(
          response?.data?.responseData?.topicList?.[0]?.subVideos?.[0]
        );
      }
    } catch (err) {
      setisFetching(false);
      router.push("/dashboard/self-practice");
    }
  };
  //    console.log('SubVideosLesson', SubVideosLesson)
  const playNextLesson = () => {
    //find lesson div which is active
    let nextLessonIndex = parseInt(UpdateIndex?.parentIndex) + 1;
    let nextSubLessonIndex = parseInt(UpdateIndex?.childIndex) + 1;
    //check if next sublesson exists
    if (getChapterLists?.topicList?.length) {
      if (getChapterLists?.topicList?.[UpdateIndex?.parentIndex]) {
        if (
          getChapterLists?.topicList?.[UpdateIndex?.parentIndex]?.subVideos[
            nextSubLessonIndex
          ]
        ) {
          document
            .getElementById(
              "sub_lesson_" +
                UpdateIndex?.parentIndex +
                "_" +
                nextSubLessonIndex
            )
            .click();
        } else if (
          getChapterLists?.topicList?.[nextLessonIndex]?.subVideos[0]
        ) {
          document.getElementById("lesson_" + nextLessonIndex).click();
          document
            .getElementById("sub_lesson_" + nextLessonIndex + "_0")
            .click();
        }
      }
    }
  };

  return (
    <>
      <div className="h-100 w-100 d-flex self-practice-lessons">
        <div
          className={`${"h-100 tab-left-aside"} ${
            lessonNavigation ? "active" : ""
          }`}
        >
          <div className="d-flex align-center justify-between">
            <h5 className="h5 fw600 text-grey-2 topic-name">
              <span
                className="icon gray-back"
                onClick={() => {
                  setLessonNavigation(false);
                }}
              ></span>
              {getChapterLists?.topicDetails?.name}
            </h5>
            {isFetching ? (
              <></>
            ) : (
              getChapterLists?.topicDetails?.percentageWatched != null && (
                <div className="loaderView sm">
                  <CircularProgressbarWithChildren
                    styles={buildStyles({
                      pathColor: "#FC7900",
                      trailColor: "#FEF0DB",
                      strokeLinecap: "butt",
                      textColor: "#404040",
                    })}
                    text={`${getChapterLists?.topicDetails?.percentageWatched?.toFixed(
                      0
                    )}%`}
                    value={getChapterLists?.topicDetails?.percentageWatched?.toFixed(
                      0
                    )}
                  ></CircularProgressbarWithChildren>
                </div>
              )
            )}
          </div>
          {isFetching ? (
            <LessonSkeleten />
          ) : (
            <LessonComponent
              initialVideoToPlay={initialVideoToPlay}
              setLessonNavigation={setLessonNavigation}
              getChapterLists={getChapterLists}
              setSubVideosLesson={setSubVideosLesson}
              setPlay={setPlay}
              setViewData={setViewData}
              subLessonId={subLessonId}
              setsubLessonId={setsubLessonId}
              Play={Play}
              setUpdateIndex={setUpdateIndex}
            />
          )}
        </div>
        {isFetching ? (
          <SkeletonVemio />
        ) : (
          <div className="h-100 tab-right-aside">
            <div className="d-flex tab-right-btn-wrpr">
              <button
                className="btn mb-10 list-btn"
                onClick={() => {
                  setLessonNavigation(true);
                }}
              >
                {getLanguages(checklanguage, "lessonList")}
              </button>
              <button
                className="btn-accent mb-10 list-btn"
                onClick={() => {
                  playNextLesson(true);
                }}
              >
                {getLanguages(checklanguage, "next")}
              </button>
            </div>
            
              {/* <button
                className="btn mb-10 list-btn"
                onClick={() => {
                  setLessonNavigation(true);
                }}
              >
                {getLanguages(checklanguage, "next")}
              </button> */}
              {SubVideosLesson != null && (
                // <>
                // {
                //   (!playerType)
                //   ?
                //   <VimeoPlayer
                //   SubVideosLesson={SubVideosLesson}
                //   Play={Play}
                //   setPlay={setPlay}
                //   getChapterLists={getChapterLists}
                //   setgetChapterLists={setgetChapterLists}
                // />
                // :
                // <ReactVideoPlayer
                //   SubVideosLesson={SubVideosLesson}
                //   Play={Play}
                //   setPlay={setPlay}
                //   getChapterLists={getChapterLists}
                //   setgetChapterLists={setgetChapterLists}
                // />

                // }
                
                
                // </>
                <ReactVideoPlayer
                  SubVideosLesson={SubVideosLesson}
                  Play={Play}
                  setPlay={setPlay}
                  getChapterLists={getChapterLists}
                  setgetChapterLists={setgetChapterLists}
                />
              )}
           
            <div className="d-flex align-center cd-card-wrpr">
              <div className="lesson-cd-card">
                {SubVideosLesson?.audioLink != null && (
                  <AudioPlayerComponent
                    path={
                      SubVideosLesson?.audioLink?.indexOf("http") === 0
                        ? SubVideosLesson?.audioLink
                        : process.env.NEXT_PUBLIC_API_BASE_URL +
                          "/" +
                          SubVideosLesson?.audioLink
                    }
                  />
                )}
                {SubVideosLesson?.name ? (
                  <div className="video_title_container">
                    <h5>{SubVideosLesson?.name}</h5>

                    <div className="next_video_button_container">
                      <button
                        className="btn-accent mb-10 show-btn"
                        onClick={() => {
                          playNextLesson(true);
                        }}
                      >
                        {getLanguages(checklanguage, "next")}
                      </button>
                    </div>
                  </div>
                ) : (
                  <></>
                )}
                {/* <div className="cd-img">
                                        <img src="/images/product-image.png" className="cover-img" />
                                    </div>
                                    <div className="cd-content">
                                        <p className="p text-grey-5 cd-name">Live Your Movie (DVD)</p>
                                        <a href="#" className="p fw600 text-dark-grey">Buy Now</a>
                                    </div> */}
              </div>
            </div>
            {/* <div className="d-flex align-center justify-between lesson-auther-details"> */}
            {/* <div className="d-flex align-center auther-card">
                                    <div className="auther-img">
                                        <img src="/images/user-image.jpg" className="cover-img" />
                                    </div>
                                    <h6 className="h6 text-grey-1 fw600 auther-name">Clemens Kuby</h6>
                                    <p className="fs12 text-dark-grey">{getLanguages(checklanguage, 'professional')}</p>
                                </div> */}
            {/* {
                                    SubVideosLesson?.userReview?.rating != null ?
                                        <RatingStar count={SubVideosLesson?.userReview?.rating} iconsCount={5} />
                                        :
                                        <button className="btn sm" onClick={() => setratingPopUp(true)}>
                                            <span className="icon rate-icon"></span>
                                            Rate
                                        </button>
                                } */}

            {/* </div> */}
            <SelfPracticeTab
              SubVideosLesson={SubVideosLesson}
              show={ratingPopUp}
              setSubVideosLesson={setSubVideosLesson}
              setOverAllRating={setOverAllRating}
            />
          </div>
        )}
      </div>
      {ratingPopUp && (
        <ReactStarModal
          commentsList={commentsList}
          show={ratingPopUp}
          setCommentsList={setCommentsList}
          SubVideosLesson={SubVideosLesson}
          setSubVideosLesson={setSubVideosLesson}
          setOverAllRating={setOverAllRating}
          onHide={() => {
            setratingPopUp(false);
          }}
        />
      )}
    </>
  );
};
export async function getServerSideProps(context) {
  return {
    props: context.query, // will be passed to the page component as props
  };
}

export default SelfPracticeDetail;
