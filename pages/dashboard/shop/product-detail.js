import React, { useEffect, useState } from 'react'
import { getCookie } from 'cookies-next';
import SimilarProduct from '../../../components/Common/SimilarProduct'
import ProductQuickDetail from '../../../components/Product/ProductQuickDetail';
import { getSimilarProduct } from '../../../redux/action/product';

const ProductDetail = ({ detail, id }) => {
     const [similarProduct ,setsimilarProduct] = useState(null)
     useEffect(()=>{
      getsimilarProduct();
     },[])

    const getsimilarProduct = async () => {
      try {
        let response = await getSimilarProduct({id : id});
        setsimilarProduct(response?.data?.responseData)
      }
      catch(err) {

      }
     
    }

  return (
    <div className="prod-details-wrpr">
      <ProductQuickDetail pData={detail} id={id} />
      {similarProduct?.length > 0 && <SimilarProduct similarProduct={similarProduct} /> }
    </div>
  )
}
export async function getServerSideProps(ctx) {

  let jwtToken = getCookie('jwtToken', { req: ctx.req, res: ctx.res });
  let headers = {
    'Content-Type': 'application/json',
    'accept': 'application/json'
  }
  if (jwtToken) {
    headers = Object.assign(headers, { 'Authorization': `Bearer ${jwtToken}` })
  }
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/product-details?id=${ctx.query.pid}`, {
      method: 'GET',
      headers
    })
    const json = await response.json()
    let updateResponseData = { ...json?.responseData }
    updateResponseData.allProducts.count = 1
    return {
      props: {
        detail: updateResponseData,
        id: ctx.query.pid
      },
    }
  }
  catch (err) {
    return { props: {} }
  }
}
export default ProductDetail