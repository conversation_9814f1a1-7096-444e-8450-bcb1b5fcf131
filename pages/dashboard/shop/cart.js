import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import CartProduct from '../../../components/CartProduct/CartProduct';
import { Number, Currency } from "react-intl-number-format";

import { getCartData, USER_CART } from "../../../redux/action/cart"
import { currencyFormat, getLanguages, checklanguage } from '../../../constant';
import { useRouter } from 'next/router';
import { useCookie } from 'next-cookie';
import { CartScreenLoader , OverLayLoader} from '../../../components/FieldLoader';
import EmptyScreen from '../../../components/Common/EmptyScreen';
const GetAllCart = () => {
    const { userInfo: { User } } = useSelector((state) => state.user);
    const cookies = useCookie();
    const dispatch = useDispatch();
    const { userCart, total } = useSelector(state => state.cart)
    console.log(userCart, 'userCart')
    const router = useRouter();
    const [IsFetching, setIsFetching] = useState(false);
    const [Loading, setLoading] = useState(false)
    useEffect(() => {
        // window.digistoreCart()
        window.digistoreCart(`tracking=${User?.id}`)
    
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            getAllUserCartData()
        }
        else {
            router.push('/')
        }
    }, [])
    const getAllUserCartData = async () => {
        setIsFetching(true)
        try {
            let response = await getCartData()
            setIsFetching(false)
            dispatch({
                type: USER_CART,
                payload: response?.data?.responseData
            })
        }
        catch (err) {

        }
    }
    const checkout =  () => {
        window.alert = function () { };
        setLoading(true)
        let delayTime = 0;
        window.ds24cart_clear();
        for( let i = 0; i < userCart?.records.length; i++){
            delayTime += 1200;
            setTimeout(() => {
                window.ds24cart_add(parseInt(userCart?.records[i].Product.dgStoreId), userCart?.records[i].count);
            }, delayTime);
        }
        delayTime += 1200;
        setTimeout(() => {   
           window.location.href = "https://www.digistore24.com/checkout/"+cookies.get("ds24cart_id");
        }, delayTime);
    }

    return (
        <>
            {
                IsFetching ?
                  <CartScreenLoader/>
                    :

                    userCart?.records?.length === 0 ?
                    <EmptyScreen productType = {3}/>
                        // <div className="container main-content">
                        //     <div className="row">

                        //     </div><p className="cart-empty">Your cart is currently empty.</p>	<p className="return-to-shop">
                        //     </p>
                        //     <div className="container">
                        //         <div className="vertical-center">
                        //             <button className='btn btn-accent cart_empty' onClick={() => router.push('/dashboard/shop')}>Return to KUBYshop</button>
                        //         </div>
                        //     </div>
                        // </div>
                        :
                        <OverLayLoader Loading ={Loading}>
                        <div className="h-100 prod-details-wrpr">
                            <ul className="d-flex align-center cd-breadcrumb">
                                <li className="breadcumb-item"><a href="javascript:;" onClick={() => router.push('/dashboard/shop')} className="breadcumb-link">KUBYshop</a></li>
                                <li className="breadcumb-item"><a href="javascript:;" className="breadcumb-link">Cart</a></li>
                            </ul>
                            <div className='d-flex cart_wrpr_outer'>
                                <div className='cart_wrpr'>
                                    <h2 className='text-grey-1 fw600'>Cart</h2>
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th>{getLanguages(checklanguage, 'product')}</th>
                                                <th>{getLanguages(checklanguage, 'category')}</th>
                                                <th>{getLanguages(checklanguage, 'totalQuantity')}</th>
                                                <th>{getLanguages(checklanguage, 'totalAmount')}</th>
                                                <th className='actions'></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {
                                                userCart != null &&
                                                Array.isArray(userCart?.records) &&
                                                userCart?.records?.map((product, index) => (
                                                    <CartProduct
                                                        product={product}
                                                        key={index}
                                                        setLoading={setLoading}
                                                    />
                                                ))
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div className='cart_total__amount_wrpr'>
                                    {/* <h5 className='h5 text-grey-1'>Coupon <span className='icon q-mark-icon'></span></h5>
                                    <div className='d-flex coupon-from'>
                                        <input type='text' className="form-control w-50" />
                                        <button className='btn btn-accent'>Apply voucher</button>
                                    </div> */}
                                    <div className='cart_total_wrpr'>
                                        <h5 className='h5 text-grey-1'>{getLanguages(checklanguage, 'cartTotal')}</h5>
                                        <div className='price-table'>
                                            <div className='d-flex align-center justify-between w-100'>
                                                <p className="h6 text-grey-5">{getLanguages(checklanguage, 'subTotal')} ({userCart?.records?.length}) :</p> <span className='text-grey-1 fw500'><Currency locale={checklanguage} currency={"EUR"}>{total.toFixed(2)}</Currency></span>
                                            </div>
                                            {/* <div className='d-flex align-center justify-between w-100'>
                                                <p className="h6 text-grey-5">Shipment:</p> <span className='text-grey-1 fw500'>€ 0.00</span>
                                            </div> */}
                                            {/* <div className='d-flex align-center justify-between w-100'>
                                                <p className="h6 text-grey-5">Address:</p> <span className='text-grey-1 fw500'>Shipping to India</span>
                                            </div> */}
                                            <div className='d-flex align-center justify-between w-100'>
                                                <p className="h6 text-grey-5">{getLanguages(checklanguage, 'total')}:</p> <span className='text-grey-1 fw500'><Currency locale={checklanguage} currency={"EUR"}>{total.toFixed(2)}</Currency></span>
                                            </div>
                                        </div>
                                    </div>
                                    <button className='btn btn-accent w-100' onClick={() => checkout()}>{getLanguages(checklanguage, 'proceedToCheckout')}</button>
                                </div>
                            </div>           
                        </div>
                        </OverLayLoader>
            }
          
        </>
    )
}

export default GetAllCart