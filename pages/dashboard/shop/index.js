import React, { useEffect } from "react";
import Kubyshop from "../../../components/Product/Shop";
import axios from "axios";
import { getCategoryLists } from "../../../redux/action/product";

const Shop = ({ CategoryList }) => {
  return <Kubyshop CategoryList={CategoryList} />;
};

// const {data} = await axios.post('/user', document.querySelector('#my-form'), {
//   headers: {
//     'Content-Type': 'application/json'
//   }
// })

export async function getServerSideProps(ctx) {
  // console.log(process.env.NEXT_PUBLIC_API_BASE_URL+'/category-list?orderByParameter=ordering&orderByValue=asc')
  try {
    //let response = await getCategoryLists();
    let response = await axios.get(
      process.env.NEXT_PUBLIC_API_BASE_URL + "/category-list",
      {
        params: {
          orderByParameter: "ordering",
          orderByValue: "ASC",
        },
        headers: {
          language: ctx?.req?.cookies?.language ?? "de",
        },
      }
    );
    return {
      props: {
        CategoryList: response?.data?.responseData?.category,
      },
    };
  } catch (err) {
    console.log(err?.response?.data?.errors);
    return {
      props: {},
    };
  }
}
export default Shop;
