import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import lottie<PERSON>son from "../../animation.json";
import {
  meetingPayment,
  meetingPaymentEnd,
} from "../../redux/action/kuby-companion";
import MeetingSuccessModal from "../../components/Common/Modals/MeetingSuccessModal";
import { getLanguages, checklanguage } from "../../constant";
import dynamic from "next/dynamic";
import moment from "moment";

const MeetingSuccess = () => {
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });

  const [meetingPopupSuccess, setmeetingPopupSuccess] = useState(false);
  const [paymentType, setPaymentType] = useState(null);
  const [companionName, setCompanionName] = useState();
  const [date, setDate] = useState();
  const router = useRouter();
  const { query } = router;

  const getFormattedDate = (date) => {
    const language = localStorage.getItem("language");

    if (language === "en") {
      return moment(date).format("dddd, MMMM D, YYYY [at] HH:mm");
    } else {
      return moment(date).format("dddd, D. MMM YYYY [um] HH:mm");
    }
  };

  useEffect(() => {
    if (!query) return;
    if (Object.keys(query)?.length > 0) {
      if (query?.custom?.indexOf("booking") !== -1) {
        if (query?.custom != "booking-completed") {
          bookingPayment();
        } else {
          setCompanionName(query?.companionName);
          setDate(query?.startDate);
        }
        setPaymentType("booking");
      } else if (query?.custom?.indexOf("payment") !== -1) {
        if (query?.custom != "payment") {
          meetingCompletionPayment();
        }
        setPaymentType("booking-completion");
      }
    } else {
      setmeetingPopupSuccess(true);
    }
  }, [query]);

  const bookingPayment = async () => {
    try {
      let response = await meetingPayment({
        data: router?.asPath?.split("?")[1],
      });
      if (response) {
        const companionInfo =
          response?.data?.responseData?.companionInfo?.firstName;
        const eventStartDate = response?.data?.responseData?.event?.startDate;
        setCompanionName(companionInfo);
        setDate(eventStartDate);
        router.push(
          {
            pathname: router.pathname,
            query: {
              custom: "booking-completed",
              companionName: JSON.stringify(companionInfo),
              startDate: eventStartDate,
            },
          },
          undefined,
          { scroll: false }
        );
      }
    } catch (err) {}
  };

  const meetingCompletionPayment = async () => {
    try {
      let response = await meetingPaymentEnd({
        data: router?.asPath?.split("?")[1],
      });
      router.push(
        {
          pathname: router.pathname,
          query: { custom: "payment" },
        },
        undefined,
        { scroll: false }
      );
      setPaymentType("booking-completion");
    } catch (err) {
      router.push(
        {
          pathname: router.pathname,
          query: { custom: "payment" },
        },
        undefined,
        { scroll: false }
      );
      setPaymentType("booking-completion");
    }
  };

  useEffect(() => {
    if (query?.companionName) {
      setCompanionName(JSON.parse(query.companionName));
    }
    if (query?.startDate) {
      setDate(query.startDate);
    }
  }, [query]);

  return (
    <>
      <div className="h-100 d-flex align-center justify-center thanku-wrpr">
        <div className="text-center thanku-inner">
          <div className="thanku-gif">
            <Lottie
              loop
              animationData={lottieJson}
              play
              style={{
                width: 120,
                height: 120,
                marginLeft: "auto",
                marginRight: "auto",
              }}
            />
          </div>
          {paymentType === "booking" && (
            <>
              <h3 className="h3 text-dark-grey fw500">
                {getLanguages(checklanguage, "successfullyBooked")}
              </h3>
              <p className="p text-grey-5">
                {getLanguages(checklanguage, "meetingBooked")}
              </p>
              <p className="p text-grey-5">
                {getLanguages(
                  checklanguage,
                  "metingTakePlace",
                  ["date", "companion"],
                  [`${getFormattedDate(date)}`, `${companionName} `],
                  true
                )}
              </p>
              <p className="p text-grey-5">
                {getLanguages(checklanguage, "metingEmail")}
              </p>
              <button
                onClick={() => router.push("/user/upcoming-meeting")}
                className="btn-accent"
              >
                {getLanguages(checklanguage, "viewMyMeting")}
              </button>
              <img
                src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
                style={{
                  visibility: "hidden",
                  height: 0,
                  width: 0,
                  border: "none",
                }}
                alt="invisible tracker 1"
              />
              <img
                src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
                style={{
                  visibility: "hidden",
                  height: 0,
                  width: 0,
                  border: "none",
                }}
                alt="invisible tracker 2"
              />
            </>
          )}

          {paymentType === "booking-completion" && (
            <>
              <h3 className="h3 text-dark-grey fw500">
                {getLanguages(checklanguage, "successfullyPaid")}
              </h3>
              <p className="p text-grey-5">
                {getLanguages(checklanguage, "thankyouForPayment")}
              </p>
              <button
                onClick={() => router.push("/user/meeting-history")}
                className="btn-accent"
              >
                {getLanguages(checklanguage, "viewMyMeting")}
              </button>
              <img
                src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
                style={{
                  visibility: "hidden",
                  height: 0,
                  width: 0,
                  border: "none",
                }}
                alt="invisible tracker 1"
              />
              <img
                src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
                style={{
                  visibility: "hidden",
                  height: 0,
                  width: 0,
                  border: "none",
                }}
                alt="invisible tracker 2"
              />
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default MeetingSuccess;
