import { useRouter } from "next/router";
import React, { useEffect } from "react";
import lottie<PERSON>son from "../../animation.json";
import { meetingPaymentEnd } from "../../redux/action/kuby-companion";
import dynamic from "next/dynamic";

const MeetingEnd = () => {
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });
  const router = useRouter();
  const { query } = router;
  useEffect(() => {
    if (Object.keys(query)?.length) {
      createPayment();
    }
  }, [query]);

  const createPayment = async () => {
    try {
      let response = await meetingPaymentEnd({
        data: router?.asPath?.split("?")[1],
      });
      router.push(router.pathname, "", { scroll: false });
    } catch (err) {}
  };

  return (
    <div className="h-100 d-flex align-center justify-center thanku-wrpr">
      <div className="text-center thanku-inner">
        <div className="thanku-gif">
          <Lottie
            loop
            animationData={lottieJson}
            play
            style={{
              width: 120,
              height: 120,
              marginLeft: "auto",
              marginRight: "auto",
            }}
          />
        </div>
        <h3 className="h3 text-dark-grey fw500">Thank you for payment</h3>
        {/* <p className='p text-grey-5'>Your meeting has been successfully schedulled</p> */}
        <button
          onClick={() => router.push("/dashboard/calender")}
          className="btn-accent"
        >
          Back to Calender
        </button>
      </div>
      <img
        src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 1"
      />
      <img
        src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 2"
      />
    </div>
  );
};

export default MeetingEnd;
