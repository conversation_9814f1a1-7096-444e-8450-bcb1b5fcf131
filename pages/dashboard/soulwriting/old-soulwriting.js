import React, { useEffect, useState } from "react";
import moment from "moment";
import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import { getLanguages, checklanguage } from "../../../constant";
import { getOldSoulWriting } from "../../../redux/action/soul-writing";

const OldSoulwriting = () => {
  const [oldProjects, setOldProjects] = useState([]);
  const cookies = useCookie();
  const router = useRouter();

  useEffect(() => {
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push("/signin?redirectTourl=/user/old-soulwriting");
    } else {
      fetchOldProjects();
    }
  }, []);

  const fetchOldProjects = async () => {
    try {
      const response = await getOldSoulWriting();

      setOldProjects(response?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching old projects:", error);
    }
  };
  // console.log()
  return (
    <div className="h-100 prof-details-wrpr">
      <ul className="d-flex align-center cd-breadcrumb">
        <li className="breadcumb-item">
          <Link href={"/"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item current">
          <em>{getLanguages(checklanguage, "oldSoulWritingProjects")}</em>
        </li>
      </ul>
      <div className="inner-header">
        <h4 className="fw500 page-title">
          {getLanguages(checklanguage, "oldSoulWritingProjects")}
        </h4>
      </div>
      <div className="soulwriting-table inner-header">
        <table className="soul-table w-100">
          <thead>
            <tr className="tr soul-trow">
              <th className="th soul-tcol" style={{ width: "175px" }}>
                {getLanguages(checklanguage, "project")}
              </th>
              <th className="th soul-tcol">
                {getLanguages(checklanguage, "dateOfOrigin")}
              </th>
              <th className="th soul-tcol">
                {getLanguages(checklanguage, "companion")}
              </th>
              <th className="th soul-tcol">
                {getLanguages(checklanguage, "characters")}
              </th>
              <th className="th soul-tcol">
                {getLanguages(checklanguage, "status")}
              </th>
              <th className="th soul-tcol">
                {/* {getLanguages(checklanguage, "action")} */}
              </th>
            </tr>
          </thead>
          <tbody>
            {oldProjects.map((project, index) => (
              <tr key={index} className="tr soul-trow">
                <td className="td soul-tcol">{project.projectDetails.title}</td>
                <td className="td soul-tcol">
                  {moment(project.projectDetails.createTimeStamp.$date).format(
                    "MMMM D, YYYY"
                  )}
                </td>
                <td className="td soul-tcol">
                  {project?.companion?.UserProfile?.firstName
                    ? ` ${project?.companion?.UserProfile?.firstName} ${project?.companion?.UserProfile?.lastName}`
                    : ""}
                </td>
                <td className="td soul-tcol">
                  {project.contentDetails[0].billableCharacterCount}
                </td>
                <td className="td soul-tcol">
                  {getLanguages(
                    checklanguage,
                    `${project.projectDetails.status}`
                  )}
                </td>
                <td className="td soul-tcol">
                  <Link
                    href={`/dashboard/soulwriting/projectDetails?projectId=${project?.id}`}
                  >
                    <a className="link">
                      {getLanguages(checklanguage, "view")}
                    </a>
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OldSoulwriting;
