import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import ReactPagination from "../../../components/Common/ReactPagination";
import Soulwritingtable from "../../../components/SoulWriting/Soulwritingtable";
import {
  getOldSoulWriting,
  getSetting,
  getSoulWritingProject,
} from "../../../redux/action/soul-writing";
import { getLanguages, checklanguage } from "../../../constant";
import Vimeo from "@u-wave/react-vimeo";
import AnimatedShowMore from "react-animated-show-more";

const SoulWriting = ({ json }) => {
  const [projectList, setProjectList] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFullIntro, setShowFullIntro] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [vimeoOpen, setVimeoOpen] = useState(false);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [vimeDesc, setVimeoDesc] = useState();
  const [showOldSoulwriting, setShowSoulWriting] = useState(false);
  const router = useRouter();
  const cookies = useCookie();
  const [open, setOpen] = useState("");

  useEffect(() => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      getAllProjects();
      getSettings();
      fetchOldProjects();
    } else {
      router.push(`/signin?redirectTourl=/dashboard/soulwriting`);
    }
  }, [currentPage]);

  const getAllProjects = async () => {
    try {
      let response = await getSoulWritingProject({
        pageNumber: currentPage,
        limit: 10,
      });
      setProjectList(response?.data?.responseData);
    } catch (err) {}
  };

  const fetchOldProjects = async () => {
    try {
      const response = await getOldSoulWriting();
      if (response?.data?.responseData?.records?.length > 0) {
        setShowSoulWriting(true);
      }
    } catch (error) {
      console.error("Error fetching old projects:", error);
    }
  };

  const getSettings = async () => {
    try {
      const response = await getSetting();
      const settings = response?.data?.responseData?.records;
      const language = localStorage.getItem("language") || "de";
      const vimeoVideoKey = `SOUL_WRITING_VIDEO_${language.toUpperCase()}`;
      const vimeoDescKey = `SOUL_WRITING_DESCRIPTION_${language.toUpperCase()}`;

      const vimeoVideo = settings.find(
        (item) => item.key === vimeoVideoKey
      )?.value;
      const vimeoDesc = settings.find(
        (item) => item.key === vimeoDescKey
      )?.value;

      setVimeoSrc(vimeoVideo);
      setVimeoDesc(vimeoDesc);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  const handleVideoIconClick = () => {
    setShowVideo(true);
    setVimeoOpen(true);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
  });

  const handleResize = () => {
    let element = document.querySelector(
      ".soul-content-wrpr .showmore .font-inter .soul-intro div[aria-hidden=true]"
    );
    if (element != null) {
      element.parentNode.querySelector(".AnimatedShowMore__MainContent").style = element.offsetHeight + "px";
    }
  };

  return (
    <>
      <div className="d-flex align-center justify-between b-0 inner-header">
        <div className="d-flex align-center title-wrpr">
          <span className="icon title-icon soul-icon"></span>
          <h4 className="fw500 page-title">
            {getLanguages(checklanguage, "soulwriting_title")}
          </h4>
        </div>
      </div>
      <div className="bg-white d-flex soulwriting-wrpr">
        <div className="relative soul-vedio">
          {vimeoSrc != null && (
            <Vimeo video={vimeoSrc} width={500} height={"auto"} />
          )}
        </div>
        
        <div className={`${open} soul-content-wrpr`}>
          <AnimatedShowMore
            height={100}
            className="showmore"
            toggle={({ isOpen }) => {
              if (isOpen) {
                setOpen("soul-content-open");
                setTimeout(() => {
                  handleResize();
                }, 500);
              } else {
                setOpen("");
              }
              return isOpen ? (
                <a href="javascript:void(0);" className="link-blue showLess">
                  {getLanguages(checklanguage, "showLess")}
                </a>
              ) : (
                <a href="javascript:void(0);" className="link-blue readMore">
                  {getLanguages(checklanguage, "readMore")}
                </a>
              );
            }}
            speed={200}
          >
            <p className="p text-dark-grey font-inter soul-intro">
              {getLanguages(
                checklanguage,
                "soulWritingIntroText",
                null,
                null,
                true
              )}
            </p>
          </AnimatedShowMore>

          <div className="text-right w-100">
            <Link href={"/dashboard/soulwriting/create-soulwriting"}>
              <a href="javascript:void(0)" className="btn-accent">
                <span className="icon plus-icon-white"></span>
                {getLanguages(checklanguage, "createNewSoulWriting")}
              </a>
            </Link>
          </div>
        </div>
      </div>
      <div className="d-flex align-center justify-between b-0 inner-header">
        <div
          className="d-flex align-center justify-between title-wrpr"
          style={{ width: "100%" }}
        >
          <h4 className="fw500 page-title">
            {getLanguages(checklanguage, "mySoulWriting")}
          </h4>
          {showOldSoulwriting && (
            <Link href={"/dashboard/soulwriting/old-soulwriting"}>
              <a className="btn btn-primary ml-3">
                {getLanguages(checklanguage, "viewOldSoulwriting")}
              </a>
            </Link>
          )}
        </div>
      </div>
      <Soulwritingtable
        projectData={projectList}
        showOldSoulwriting={showOldSoulwriting}
      />
      {projectList?.totalPages > 1 && (
        <div className="paginatate">
          <ReactPagination
            currentPage={currentPage}
            setcurrentPage={setCurrentPage}
            totalItemsCount={
              projectList?.totalRecords ? projectList?.totalRecords : 10
            }
            totalPages={projectList?.totalPages ? projectList?.totalPages : 10}
            perPage={projectList?.perPage ? projectList?.perPage : 10}
          />
        </div>
      )}
    </>
  );
};

export default SoulWriting;
