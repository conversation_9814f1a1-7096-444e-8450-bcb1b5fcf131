import React, { useEffect, useState, useRef } from "react";
import SoulWritingHeaderBar from "../../../components/SoulWriting/SoulWritingHeaderBar";
import SoulWritingTab from "../../../components/SoulWriting/SoulWritingTab";
import SoulWritingStep from "../../../components/SoulWriting/SoulWrittingStep";
import { useForm } from "react-hook-form";
import ProtoganistModal from "../../../components/Common/Modals/ProtoganistModal";
import ProtoganistNewModal from "../../../components/Common/Modals/ProtoganistNewModal";
import ProtoganistMoreInfoModal from "../../../components/Common/Modals/ProtoganistMoreInfoModal";
import ChooseCompanionModal from "../../../components/Common/Modals/ChooseCompanionModal";
//import ProtoganistListModal from "../../../components/Common/Modals/ProtoganistListModal";
import ProtagonistListModal from "../../../components/Common/Modals/ProtagonistListModal";
import ProtagonistEditFormModal from "../../../components/Common/Modals/ProtagonistEditFormModal";

import {
  createProject,
  getProjectById,
  getSoulWritingCharacter,
  soulWritingContent,
  getsoulWritingContent,
  SOUL_WRITING_CONTENT,
  getAllProjectList,
  soulWritingWordEstimation,
  isSoulwritingProductPurchased,
} from "../../../redux/action/soul-writing";
import SendToCompanionModal from "../../../components/Common/Modals/SendToCompanionModal";
import {
  getAllCompanions,
  getCompanionById,
} from "../../../redux/action/kuby-companion";
import { useRouter } from "next/router";
import { getCookie } from "cookies-next";
import { useCookie } from "next-cookie";
import SoleWritingSubmitButton from "../../../components/SoulWriting/SoleWritingSubmitButton";
import ChooseCharacterPopup from "../../../components/SoulWriting/SoulWrittingStep/ChooseCharacterPopup";
import { toast } from "react-hot-toast";
import _, { result } from "lodash";
import LeaveActionPoppup from "../../../components/SoulWriting/LeaveActionModalPopup";
import { removeHtmlTags, getLanguages, checklanguage } from "../../../constant";
import AddProjectModal from "../../../components/SoulWriting/SoulWrittingStep/AddProjectModal";
import PdfComponent from "../../../components/SoulWriting/SoulWrittingStep/PdfComponent";
import SoulWritingBillingModal from "../../../components/Common/Modals/SoulWritingBillingModal";
import ChangeCompanionAlertModal from "../../../components/Common/Modals/ChangeCompanionAlertModal";
import { redirect } from "next/dist/server/api-utils";
import CreateSoulWritingModal from "../../../components/Common/Modals/CreateSoulWritingModal";
import NextStepButton from "../../../components/SoulWriting/NextStepButton";
import SoulWritingModal from "../../../components/Common/Modals/SoulWritingModal";
import SoulwritingPurchaseModal from "../../../components/Common/Modals/SoulwritingPurchaseModal";

const SoulWritingCreate = ({ CategoryList }) => {
  const cookies = useCookie();
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    setError,
    clearErrors,
  } = useForm();
  watch(["reason", "title", "consent"]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [protagonistSurveyData, setProtagonistSurveyData] = useState(null);
  const [billingData, setBillingData] = useState(null);
  const [categoryListForm, setcategoryListForm] = useState(null);
  const [isModalShow, setIsModalShow] = useState(false); //show modal
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [showCharacterPopup, setShowCharacterPopup] = useState(false);
  const [lastOpenedStage, setLastOpenedStage] = useState(2);
  const [isSoulwritingEnabledData, setIsSoulwritingEnabledData] = useState(null);
  const router = useRouter();
  const values = getValues();
  const { query, asPath } = router;
  const { projectId, category_id } = query;
  const [isSubmited, setIsSubmited] = useState(query?.isSubmited);
  const [show, setshow] = useState(0);
  const [categoryList, setcategoryList] = useState(CategoryList);
  const [CompanionList, setCompanionList] = useState(null);
  const [isFetching, setisFetching] = useState(false);
  const [prefilledReason, setPrefilledReason] = useState("");
  const [companionId, setCompanionId] = useState(
    query?.companionId ? query?.companionId : null
  );
  const [protagonistObject, setProtagonistObject] = useState(null);
  const [componentWrapper, setcomponentWrapper] = useState([]);
  const [getCompanion, setcompanion] = useState({});
  const [projectInfo, setprojectInfo] = useState({});
  const [character, setCharacter] = useState(null);
  const [characterIndex, setcharacterIndex] = useState(0);
  const [categoryId, setcategoryId] = useState("");
  const [Step, setchooseStep] = useState(null);
  const [flag, setflag] = useState(false);
  const [queryData, setqueryData] = useState({
    redline: false,
    bridge: false,
    collapsetoggle: false,
  });
  const [submitting, setSubmiiting] = useState(false);
  const [bridgeArrayChar, setbridgeArrayChar] = useState([]);
  
  const [selectedTextWrraperCategory1, setselectedTextWrraperCategory1] =
    useState({
      reason: null,
      title: null,
      protogonistId: null,
      bridgeId: null,
      redlineId: null,
    });
  const [dateInfo, setDateInfo] = useState({
    trigger_date: null,
    projection_date: null,
  });
  const [saveCompanionStatus, setsaveCompanionStatus] = useState(null);
  const [memberstatusInfo, setmemberstatusInfo] = useState(null);
  const [SingleCharacter, setSingleCharacter] = useState({
    character: null,
    index: null,
  });

  const [projectList, setProjectList] = useState(null);
  const pdfRef = useRef(null);
  const pdfExportComponent = useRef(null);

  const [currentCategoryId, setCurrentCategoryId] = useState(null);

  const [nextClick, setNextClick] = useState(false);
  const [openPopup, setOpenPopup] = useState(true);
  const [isOpenedFromSoulWriteStep3, setIsOpenedFromSoulWriteStep3] =
    useState(false);

  // for step 3
  const [protagonistList, setProtagonistList] = useState([]);
  const [isAddProtagonistOpenFromS3, setIsOpenAddProtagonistOpenFromS3] =
    useState(false);
  const [fetchCharForS3, setFethcCharForS3] = useState(false);
  // for step 3
  const [submitClicked, setSubmitClicked] = useState(false);
  const [isStep3VideoOpen, setIsStep3VideoOpen] = useState(false);
  // for step 1 --- select Companion
  const [showCompanionCost, setShowCompanionCost] = useState(true);

  // for step 4
  const [step4Video, setStep4Video] = useState(false);

  //For enable/disable auto save when soul writing sumitted to the companion

  const [isAutoSaveEnabled, setIsAutoSaveEnabled] = useState(true);

  // useEffect(() => {
  //   if(!projectId){
  //     let reason = localStorage.getItem('soulwriting_reason');
  //     let title = localStorage.getItem('soulwriting_project');
  //   }
  // }, [])
  

  useEffect(() => {
    let intervalId = null;
    setTimeout(() => {
      let button = document.getElementById("auto_save");
      document.getElementById("auto_save");
      if (button) {
        intervalId = setInterval(() => {
          if(unsavedChanges){
            button.click();
          }
        }, 15000);
      }
    }, 15000);

    const warningText = getLanguages(checklanguage, "warningNote");
    const handleWindowClose = (e) => {
      if (!unsavedChanges) return;
      e.preventDefault();
      return (e.returnValue = warningText);
    };
    const handleBrowseAway = () => {
      if (!unsavedChanges) return;
      if (window.confirm(warningText)) return;
      router.events.emit("routeChangeError");
    };
    const handleRouteChangeError = () => {
      throw "Route has been aborted, ignore this message";
    };
    // window.addEventListener("beforeunload", handleWindowClose);
    // router.events.on("routeChangeStart", handleBrowseAway);
    // router.events.on("routeChangeError", handleRouteChangeError);

    return () => {
      // window.removeEventListener("beforeunload", handleWindowClose);
      // router.events.off("routeChangeStart", handleBrowseAway);
      // router.events.off("routeChangeError", handleRouteChangeError);

      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [unsavedChanges, isAutoSaveEnabled]);

  useEffect(() => {
    
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      
      if(!projectId){
        
        getIsSoulwritingPurchased();
        
      }else{
      }
      if(!projectId && !query?.reason){
        
        let reason = localStorage.getItem('soulwriting_reason');
        let title = localStorage.getItem('soulwriting_project');
        setValue('reason', reason || "");
        setValue('title', title || "");
        setPrefilledReason(reason);
      }else{
        if (query?.reason) {
          setPrefilledReason(query?.reason);
          setValue("reason", query?.reason);
          if (companionId != null) {
            getSingleCompanion(companionId, 1);
          }
        }
      }
      
      getProjectList();
      modifiedValue();
    } else {
      router.push(
        `/signin?redirectTourl=/dashboard/soulwriting/create-soulwriting`
      );
    }
  }, [flag]);

  useEffect(() => {}, [componentWrapper]);

  const modifiedValue = async () => {
    let componentWrrapper = [];
    let payloadResponse;
    let sliceArray = CategoryList?.slice(2, CategoryList.length);
    if (projectId != undefined) {
      try {
        let response = await getsoulWritingContent({ projectId });
        setmemberstatusInfo(response?.data?.responseData?.projectDetails);
        let characterData;
        if (projectId != undefined) {
          characterData = await getSoulWritingCharacter({
            projectId: projectId,
          });
          setCharacter(characterData?.data?.responseData?.characterList);
        }
        payloadResponse = [...response?.data?.responseData?.content];
        setLastOpenedStage(response?.data?.responseData?.projectDetails?.stage);
        var newList = {};
        for (let i = 0; i < sliceArray?.length; i++) {
          if (payloadResponse?.length > 0) {
            let { arrayData, UniqueArray } = matchData(
              sliceArray[i]?.id,
              payloadResponse
            );
            componentWrrapper = [...componentWrrapper, ...UniqueArray];
            if (arrayData.length > 0) {
              newList[`categoryForms${i + 1}`] = arrayData;
            } else {
              newList[`categoryForms${i + 1}`] = [
                {
                  content: "",
                  categoryId: sliceArray[i]?.id,
                  character:
                    characterData?.data?.responseData?.characterList[0],
                  lineNumber: 1,
                  errorMessage: "",
                  isRedLine: 0,
                  characterId:
                    characterData?.data?.responseData?.characterList?.[0]?.id,
                  painpictureCollapse: 0,
                },
              ];
            }
          } else {
            newList[`categoryForms${i + 1}`] = [
              {
                content: "",
                categoryId: sliceArray[i]?.id,
                character: characterData?.data?.responseData?.characterList[0],
                lineNumber: 1,
                errorMessage: "",
                characterId:
                  characterData?.data?.responseData?.characterList?.[0]?.id,
                isRedLine: 0,
                painpictureCollapse: 0,
              },
            ];
          }
        }
        setcategoryListForm(newList);
        if (
          newList != null &&
          _.find(newList[`categoryForms${4}`], ["isRedLine", 1]) != undefined
        ) {
          setqueryData({
            collapsetoggle: true,
          });
        }
      } catch (err) {}
    }

    chooseStep(componentWrrapper, newList, payloadResponse);
  };

  const matchData = (categoryId, payloadResponse) => {
    let arrayData = [];
    let componentWrapper = [];
    for (var j = 0; j < payloadResponse?.length; j++) {
      if (categoryId == parseInt(payloadResponse[j].categoryId)) {
        arrayData.push({
          content: payloadResponse[j].content?.replaceAll(
            /<span\s+style="[^"]*background-color:\s*rgb\s*\(\s*195\s*,\s*241\s*,\s*204\s*\)\s*"[^>]*>(.*?)<\/span>/g,
            "$1"
          ),
          categoryId: payloadResponse[j].categoryId,
          character: payloadResponse[j].character,
          lineNumber: payloadResponse[j].lineNumber,
          characterId: payloadResponse[j].characterId,
          uuid: payloadResponse[j].uuid,
          errorMessage: "",
          isRedLine: payloadResponse[j].isRedLine,
          comments:
            payloadResponse[j].comments != null
              ? payloadResponse[j].comments
              : [],
          painpictureCollapse: payloadResponse[j].painpictureCollapse,
        });
        if (payloadResponse[j].categoryId != 3) {
          componentWrapper.push({
            categoryId: payloadResponse[j].categoryId,
          });
        }
      }
    }
    const UniqueArray = [
      ...new Map(componentWrapper.map((v) => [v.categoryId, v])).values(),
    ];
    return { arrayData, UniqueArray };
  };

  const getSingleCompanion = async (id, type) => {
    if (id != null) {
      try {
        let response = await getCompanionById({ id });
        setcompanion(response?.data?.responseData);
        if (type === 1) {
        } else {
          setshow(0);
        }
      } catch (err) {}
    }
  };

  const getProjectList = async () => {
    try {
      let response = await getAllProjectList();
      setProjectList(response?.data?.responseData);
    } catch (err) {}
  };

  const getIsSoulwritingPurchased = async () => {
    try {
      let response = await isSoulwritingProductPurchased();
      let data = response?.data?.responseData;
      setIsSoulwritingEnabledData(data);
      
      
      
      //setProjectList(response?.data?.responseData);
    } catch (err) {}
  };

  const chooseStep = async (
    wrapper = [],
    newList = null,
    payloadResponse = null
  ) => {
    let array = [];
    if (projectId == undefined) {
      array.push({ categoryId: categoryList?.[0]?.id });
    } else {
      let data = await getSingleProject();
      if (data) {
        for (var i = 0; i < categoryList?.slice(0, 3).length; i++) {
          array.push({ categoryId: categoryList[i].id, ...data });
        }
      }

      if (
        data?.projectMeta != null &&
        Object.keys(data?.projectMeta)?.length > 0
      ) {
        for (var i = 0; i < categoryList?.length; i++) {
          for (var j = 0; j < payloadResponse?.length; j++) {
            if (payloadResponse[j] == categoryList[i].id) {
              array.push({ categoryId: categoryList[i].id });
            }
          }
          if (
            newList != null &&
            newList["categoryForms3"] != null &&
            _.find(newList["categoryForms3"], ["isRedLine", 1]) != undefined &&
            categoryList[i].name.includes("Redline")
          ) {
            array.push({ categoryId: categoryList[i].id });
          }
          if (
            data?.projectMeta?.bridgeCharacter?.[1]?.category_reason_title !=
            null
          ) {
            array.push(
              {
                categoryId:
                  data?.projectMeta?.bridgeCharacter?.[1]?.category_reason_title
                    ?.protogonistId,
              },
              {
                categoryId:
                  data?.projectMeta?.bridgeCharacter?.[1]?.category_reason_title
                    ?.bridgeId,
              },
              {
                categoryId:
                  data?.projectMeta?.bridgeCharacter?.[1]?.category_reason_title
                    ?.redlineId,
              }
            );
          }
        }

        array = [...new Map(array.map((v) => [v.categoryId, v])).values()];

        setbridgeArrayChar(
          data?.projectMeta?.bridgeCharacter?.[0]?.data?.selectedText || []
        );
        setselectedTextWrraperCategory1({
          ...data?.projectMeta?.bridgeCharacter?.[1]?.category_reason_title,
        });
        setDateInfo({
          ...data?.projectMeta?.bridgeCharacter?.[2],
          ...data?.projectMeta?.bridgeCharacter?.[3],
        });
      }
      saveFormdata(data);

      setCompanionId(data?.companionId);
    }

    let cWrapper = [...array, ...wrapper];
    let rr = _.remove(cWrapper, { categoryId: null });
    setcomponentWrapper(cWrapper);

    // if (array?.length > 0) {
    //   setTimeout(() => {
    //     document.querySelector(`#${CategoryList[array?.length]?.name?.toLowerCase()?.split(" ")?.join('')}`)?.scrollIntoView({ behavior: "smooth" })
    //   }, 1500)

    // }
  };
  const saveFormdata = (data) => {
    setValue("reason", data?.reason);
    setValue("title", data?.projectListId);
  };

  const getSingleProject = async () => {
    let payload = {};
    payload.id = projectId;
    payload.email = "<EMAIL>";
    try {
      let response = await getProjectById(payload);
      if (response?.data?.responseData?.companionId != null) {
        getSingleCompanion(response?.data?.responseData?.companionId);
      }
      /* No need for this now (payment link) as we charging as soon as the customer sends soulwriting to the companion */
      // if (response?.data?.responseData?.paymentLink != null) {
      //   window.location = response?.data?.responseData?.paymentLink
      // }
      setprojectInfo(response?.data?.responseData);
      return response?.data?.responseData;
    } catch (err) {}
  };

  const checkValidation = (contents) => {
    let checkError = { ...contents };
    let checkArrayStep = componentWrapper;
    let formvalid = true;
    for (let i in checkError) {
      for (let j = 0; j < checkError[i].length; j++) {
        for (let k = 0; k < checkArrayStep.length; k++) {
          if (checkArrayStep[k].categoryId == checkError[i][j].categoryId) {
            // if (checkError[i][j].content === "") {
            //   checkError[i][j]["errorMessage"] = "Field is required";
            //   formvalid = false;
            // } else
            if (removeHtmlTags(checkError[i][j]?.content)?.length > 1000) {
              checkError[i][j]["errorMessage"] = getLanguages(checklanguage, "soulWritingTextLengthMessage");
                //"character cannot be greater than 1000";
              formvalid = false;
            }
          }
        }
      }
    }

    setcategoryListForm(checkError);
    return formvalid;
  };

  const replaceTag = (str) => {
    str = str.replace(
      /<span\sstyle=\"background-color:rgb\(195,241,204\);\">/g,
      '<span class="edited">'
    );
    var tmp = document.createElement("div");
    tmp.innerHTML = str;
    let span = tmp.querySelectorAll(".edited");
    span.forEach((spn) => {
      let newtext = document.createTextNode(spn.innerText);
      spn.replaceWith(newtext);
    });
    return tmp.innerHTML;
  };

  const removeBridgeHighlightTag = (str) => {
    var tmp = document.createElement("div");
    tmp.innerHTML = str;
    let span = tmp.querySelectorAll(".bridgeHighlight");
    span.forEach((spn) => {
      let newtext = document.createTextNode(spn.innerText);
      spn.replaceWith(newtext);
    });
    return tmp.innerHTML;
  };

  const onsubmit = async (formvalues, type) => {
    console.log(formvalues, 'formvaluesformvaluesformvaluesformvalues')
    let button_type = formvalues?.button_type;
   
    if (isSubmited) {
      return;
    }
    // alert(isSubmited);
    //return;
    let returnBack = false;
    let autoSave = false;
    if (formvalues?.button_type == "save_leave") {
      returnBack = true;
      type = 0;
    } else if (formvalues?.button_type == "auto_save" && isAutoSaveEnabled) {
      autoSave = true;
      type = 0;
      if(!unsavedChanges){
        return false;
      }
    }
    delete formvalues.button_type;
    let updatedForms = {};
    if (categoryListForm != null) {
      if (!checkValidation(categoryListForm)) return;
      let dataFormObject = JSON.parse(JSON.stringify(categoryListForm));

      //remove lines which are deleted from each step
      for (i = 1; i <= 10; i++) {
        updatedForms = Object.assign(updatedForms, {
          [`categoryForms${i}`]: [],
        });
        if (
          dataFormObject[`categoryForms${i}`] &&
          dataFormObject[`categoryForms${i}`].length > 0
        ) {
          for (let k = 0; k < dataFormObject[`categoryForms${i}`].length; k++) {
            if (dataFormObject[`categoryForms${i}`][k]?.isDeleted == "temp")
              return;
            if (
              !dataFormObject[`categoryForms${i}`][k].isDeleted ||
              dataFormObject[`categoryForms${i}`][k]?.isDeleted != "permanent"
            ) {
              delete dataFormObject[`categoryForms${i}`][k].isDeleted;
              delete dataFormObject[`categoryForms${i}`][k].timeoutId;
              updatedForms[`categoryForms${i}`].push(
                dataFormObject[`categoryForms${i}`][k]
              );
            }
          }
        }
      }
      dataFormObject = { ...updatedForms };
      //return false;
      let checkRedLineIndex = _.findIndex(
        categoryListForm[`categoryForms${4}`],
        ["isRedLine", 1]
      );
      let arrayIndex = checkRedLineIndex + 1;
      if (arrayIndex != 0) {
        for (var i = 0; i < dataFormObject[`categoryForms${4}`].length; i++) {
          if (arrayIndex > i) {
            if (dataFormObject[`categoryForms${4}`][arrayIndex] != undefined) {
              dataFormObject[`categoryForms${4}`][arrayIndex].isRedLine = 0;
              dataFormObject[`categoryForms${4}`][
                arrayIndex
              ].painpictureCollapse = 1;
              arrayIndex++;
            }
          }
        }
      }
      formvalues.contents = JSON.parse(JSON.stringify(dataFormObject));
      let dataFormat = saveasDraft(formvalues);
      let contents = [];
      for (var i = 0; i < dataFormat.length; i++) {
        if (
          // dataFormat[i].content != "" &&
          dataFormat[i].characterId != "" &&
          dataFormat[i].categoryId != "" &&
          dataFormat[i].lineNumber != ""
        ) {
          if (type == 1) {
            // const regextTest = /<span\s+style="[^"]*background-color:\s*rgb\s*\(\s*195\s*,\s*241\s*,\s*204\s*\)\s*"[^>]*>(.*?)<\/span>/g
            dataFormat[i].content = replaceTag(dataFormat[i].content); //dataFormat[i].content?.replaceAll(regextTest , `$1`)
            dataFormat[i].content = removeBridgeHighlightTag(
              dataFormat[i].content
            );
            // return;
            contents.push(dataFormat[i]);
          } else {
            dataFormat[i].content = removeBridgeHighlightTag(
              dataFormat[i].content
            );
            contents.push(dataFormat[i]);
          }
        }
      }
      contents.projectId = projectId;
      let checkActiveStageArray = [];
      let checkArrayStep = componentWrapper;
      for (let i = 0; i < contents.length; i++) {
        for (let j = 0; j < checkArrayStep?.length; j++) {
          if (contents[i].categoryId == checkArrayStep[j].categoryId) {
            checkActiveStageArray.push(contents[i]);
          }
        }
      }
      let dataMatchArray = componentWrapper;
      for (var i = 0; i < dataMatchArray.length; i++) {
        if (
          selectedTextWrraperCategory1?.protogonistId != null &&
          selectedTextWrraperCategory1?.protogonistId ==
            dataMatchArray[i]?.categoryId
        ) {
          checkActiveStageArray.push(dataMatchArray[i]);
        }
        if (
          selectedTextWrraperCategory1?.redlineId != null &&
          selectedTextWrraperCategory1?.redlineId ==
            dataMatchArray[i]?.categoryId
        ) {
          checkActiveStageArray.push(dataMatchArray[i]);
        }
        if (
          selectedTextWrraperCategory1?.bridgeId != null &&
          selectedTextWrraperCategory1?.bridgeId ==
            dataMatchArray[i]?.categoryId
        ) {
          checkActiveStageArray.push(dataMatchArray[i]);
        }
      }
      checkActiveStageArray = [
        ...new Map(
          checkActiveStageArray.map((v) => [v.categoryId, v])
        ).values(),
      ];

      checkActiveStageArray = _.orderBy(
        checkActiveStageArray,
        ["categoryId"],
        ["asc"]
      );
      let dataPaylaod = {
        contents: contents,
        projectId,
        //activeStage: checkActiveStageArray?.length + 2,
        // activeStage:
        //   checkActiveStageArray?.[checkActiveStageArray?.length - 1]
        //     ?.categoryId,
        // activeStage: parseInt(currentCategoryId),
        //activeStage: 5,
        activeStage: lastOpenedStage,
      };
      if (type === 1) {
        dataPaylaod.isSaveAsDraft = 0;
      } else {
        dataPaylaod.isSaveAsDraft = 1;
        setSubmiiting(true);
      }

      dataPaylaod["projectMeta"] = {
        bridgeCharacter: [
          {
            data: {
              characterflag: true,
              selectedText: bridgeArrayChar,
            },
          },
          {
            category_reason_title: selectedTextWrraperCategory1,
          },
          {
            trigger_date: dateInfo.trigger_date,
          },
          {
            projection_date: dateInfo.projection_date,
          },
        ],
      };

      if (companionId != null) {
        dataPaylaod.companionId = companionId;
      }

      try {
        let response = null;
        setUnsavedChanges(false);
        // if(returnBack){
        //   response = await soulWritingContent(dataPaylaod)
        // }else{
        console.log(button_type, 'formvalues?.button_type')
          if (button_type == "save_leave") {
            setIsSubmitting(true);
            console.log('hereeeee')
          }
        response = await soulWritingWordEstimation(dataPaylaod);
        setIsSubmitting(false);
        
        //}
        //let response = await soulWritingContent(dataPaylaod)
        //let response = await soulWritingWordEstimation(dataPaylaod)

        if (autoSave) {

        } else {
          if (response?.data?.responseData && !returnBack) {
            setBillingData(response?.data?.responseData);
            if (!response?.data?.responseData?.paymentLink) {
              toast.success("Soulwriting sent to the companion");
              router.push("/dashboard/soulwriting");
            } else {
              setshow(10); //open billing information popup
            }
          } else {
            router.push("/dashboard/soulwriting");
          }
        }
        // setshow(0)
        // setmemberstatusInfo(response?.data?.responseData?.projectDetails)
        // if (type == 1) {
        //   setflag(true)
        //   toast.success('Send to companion  successfully')
        //   router.push('/dashboard/soulwriting')
        // }
        // else {
        //   setSubmiiting(false)
        // }
        // setshow(5);
      } catch (err) {
        setIsSubmitting(false);
        setSubmiiting(false);
      }
    }
  };

  const saveasDraft = (formvalues) => {
    let dataPayload = [];
    const { contents } = formvalues;
    for (const iterator in contents) {
      dataPayload = [...dataPayload, ...contents[iterator]];
    }
    for (var i = 0; i < dataPayload.length; i++) {
      delete dataPayload[i]?.character;
      //delete dataPayload[i]?.uuid
      delete dataPayload[i]?.versionId;
      delete dataPayload[i]?.status;
      delete dataPayload[i]?.isReviewed;
      delete dataPayload[i]?.diffContent;
      delete dataPayload[i]?.createdById;
      delete dataPayload[i]?.id;
      delete dataPayload[i]?.comments;
      delete dataPayload[i]?.projectId;
      delete dataPayload[i]?.errorMessage;
    }
    return dataPayload;
  };

  const createsoulWritingProject = async () => {
    let payload = getValues();

    if (companionId != null) {
      payload.companionId = companionId;
    }
    setisFetching(true);
    //payload.projectListId = payload.title;
    delete payload.contents;
    //delete payload.title;
    delete payload.consent;
    // return;
    try {
      setNextClick(true);
      let response = await createProject(payload);
      if (response?.data?.responseData?.stage) {
        setLastOpenedStage(response?.data?.responseData?.stage);
      }

      setisFetching(false);
      setshow(0);
      if (componentWrapper?.length == 2) {
        setcomponentWrapper([
          ...componentWrapper,
          { categoryId: categoryList[2]?.id },
        ]);
      }
      if (projectId == undefined) {
        router.push(
          {
            pathname: router?.pathname,
            query: {
              projectId: response?.data?.responseData?.id,
              showVideo: true,
            },
          },
          undefined,
          { shallow: true }
        );
      }
      // toast.success(getLanguages(checklanguage, "projectSuccessMessage"));
    } catch (err) {
      setisFetching(false);
    }
  };
  const getComPanionList = async (type, hideModalByDefault, step1) => {
    try {
      setShowCompanionCost(step1);
      let response = await getAllCompanions({
        language: localStorage.getItem("language") ?? "de",
        soulwritingProductId: 1,
        limit: 100,
      });
      if (!hideModalByDefault) {
        setshow(2);
      }

      setCompanionList(response?.data?.responseData);
      if (type === 1) {
        setsaveCompanionStatus(1);
      }
    } catch (err) {}
  };
  const moveToNextStep = (e) => {
    e.preventDefault();

    const values = getValues();
    if (componentWrapper?.length >= 1) {
      createsoulWritingProject();
      return false;
    }
    if (values.reason.length > 0 && values.consent) {
      if (projectList?.length == 0 || projectList == null) {
        setshow(6);
      } else {
        setcomponentWrapper((prev) => {
          let findId = _.find(prev, ["categoryId", categoryList[1]?.id]);
          if (findId != undefined) {
            return prev;
          } else {
            return [...prev, { categoryId: categoryList[1]?.id }];
          }
        });
        createsoulWritingProject();
      }
    }
  };

  //show modal dialog create soul writing
  useEffect(() => {
    if (router?.query?.projectId == undefined) {
      setIsModalShow(true);
    }
  }, [router?.query]);

  const videoLink =
    categoryList?.find((category) => {
      return category.id == currentCategoryId - 1;
    })?.videoLink || null;

  const descriptions =
    categoryList?.find((category) => {
      return category.id == currentCategoryId - 1;
    })?.description || null;

  useEffect(() => {
    const hash = asPath.split("#")[1];
    if (query && query.showVideo && !hash && !hash?.[1]) {
      setNextClick(true);
    }
  }, [asPath, query]);
  return (
    <>
      <form onSubmit={handleSubmit(onsubmit)}>
        <div className="d-flex soulwriting-main-wrpr">
          <SoulWritingTab
            setOpenPopup={setOpenPopup}
            setNextClick={setNextClick}
            nextClick={nextClick}
            categoryList={categoryList}
            setcategoryList={setcategoryList}
            componentWrapper={componentWrapper}
            setcomponentWrapper={setcomponentWrapper}
            setcategoryId={setcategoryId}
            setqueryData={setqueryData}
            setselectedTextWrraperCategory1={setselectedTextWrraperCategory1}
            setcategoryListForm={setcategoryListForm}
            bridgeArrayChar={bridgeArrayChar}
            categoryListForm={categoryListForm}
            memberstatusInfo={memberstatusInfo}
            lastOpenedStage={lastOpenedStage}
            setStep4Video={setStep4Video}
          />
          <div className="soulwriting-forms-wrpr">
            <SoulWritingHeaderBar
              character={character}
              componentWrapper={componentWrapper}
              pdfRef={pdfRef}
              projectInfo={projectInfo}
              bridgeArrayChar={bridgeArrayChar}
              setshow={setshow}
              categoryListForm={categoryListForm}
            />
            <SoulWritingStep
              setStep4Video={setStep4Video}
              step4Video={step4Video}
              setShowCompanionCost={setShowCompanionCost}
              submit={() => onsubmit(values, 1)}
              checkValidation={checkValidation}
              setIsStep3VideoOpen={setIsStep3VideoOpen}
              setSubmitClicked={setSubmitClicked}
              lastOpenedStage={lastOpenedStage}
              setLastOpenedStage={setLastOpenedStage}
              setIsOpenAddProtagonistOpenFromS3={
                setIsOpenAddProtagonistOpenFromS3
              }
              setFethcCharForS3={setFethcCharForS3}
              fetchCharForS3={fetchCharForS3}
              isModalShow={isModalShow}
              setIsOpenedFromSoulWriteStep3={setIsOpenedFromSoulWriteStep3}
              setProtagonistObject={setProtagonistObject}
              protagonistObject={protagonistObject}
              openPopup={openPopup}
              nextClick={nextClick}
              setNextClick={setNextClick}
              currentCategoryId={currentCategoryId}
              videoLink={videoLink}
              descriptions={descriptions}
              setUnsavedChanges={setUnsavedChanges}
              show={show}
              showTermsPopup={query?.reason}
              setValue={setValue}
              projectInfo={projectInfo}
              control={control}
              setshow={setshow}
              getComPanionList={getComPanionList}
              componentWrapper={componentWrapper}
              companionId={companionId}
              getCompanion={getCompanion}
              getValues={getValues}
              projectId={projectId}
              character={character}
              setCharacter={setCharacter}
              setcharacterIndex={setcharacterIndex}
              categoryListForm={categoryListForm}
              setcategoryListForm={setcategoryListForm}
              setchooseStep={setchooseStep}
              queryData={queryData}
              setbridgeArrayChar={setbridgeArrayChar}
              bridgeArrayChar={bridgeArrayChar}
              setselectedTextWrraperCategory1={setselectedTextWrraperCategory1}
              selectedTextWrraperCategory1={selectedTextWrraperCategory1}
              setqueryData={setqueryData}
              setDateInfo={setDateInfo}
              dateInfo={dateInfo}
              memberstatusInfo={memberstatusInfo}
              setmemberstatusInfo={setmemberstatusInfo}
              setSingleCharacter={setSingleCharacter}
              SingleCharacter={SingleCharacter}
              setError={setError}
              clearErrors={clearErrors}
              projectList={projectList}
              setProjectList={setProjectList}
              categoryList={categoryList}
              protagonistList={protagonistList}
              setProtagonistList={setProtagonistList}
            />
          </div>
        </div>
        <div className="d-flex align-center soulwriting-footer">
          <div className="soulwriting-footer-left">
            {componentWrapper?.length > 2 ? (
              // <button
              //   type="submit"
              //   onClick={() => setValue("button_type", "save_leave")}
              //   className="d-flex align-center footer-btn leave-btn"
              // >
              //   <span className="icon leave-icon"></span>{getLanguages(checklanguage, "saveAndLeave")}
              // </button>
              <>
                {memberstatusInfo?.customerStatus == 2 ? (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      router.push("/dashboard/soulwriting");
                    }}
                    className="d-flex align-center footer-btn leave-btn"
                  >
                    <span className="icon leave-icon"></span>
                    {getLanguages(checklanguage, "leave")}
                  </button>
                ) : (
                  <>
                    <button
                      type="submit"
                      onClick={() => setValue("button_type", "save_leave")}
                      className={`${isSubmitting ? "btn-loader-green" : ""} ${"btn-accent footer-btn"} ${
                        memberstatusInfo?.customerStatus == 2 ? "disabled" : ""
                      } saveLeave`}
                    >
                      {getLanguages(checklanguage, "saveAndLeave")}
                    </button>
                    {isAutoSaveEnabled && (
                      <button
                        id="auto_save"
                        style={{ display: "none" }}
                        type="submit"
                        onClick={() => setValue("button_type", "auto_save")}
                        className={`${"btn-accent footer-btn"} ${
                          memberstatusInfo?.customerStatus == 2
                            ? "disabled"
                            : ""
                        } saveLeave`}
                      >
                        {getLanguages(checklanguage, "saveAndLeave")}
                      </button>
                    )}
                  </>
                )}
              </>
            ) : (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  setshow(5);
                }}
                className="d-flex align-center footer-btn leave-btn"
              >
                <span className="icon leave-icon"></span>
                {getLanguages(checklanguage, "leave")}
              </button>
            )}

            {/* Next step button */}
            <NextStepButton
              setLastOpenedStage={setLastOpenedStage}
              lastOpenedStage={lastOpenedStage}
              currentCategoryId={currentCategoryId}
              setCurrentCategoryId={setCurrentCategoryId}
              componentWrapper={componentWrapper}
              setcomponentWrapper={setcomponentWrapper}
              categoryList={categoryList}
              moveToNextStep={moveToNextStep}
              getValues={getValues}
              values={values}
              setNextClick={setNextClick}
            />
          </div>
          <div className="soulwriting-footer-right">
            <SoleWritingSubmitButton
              isSoulwritingEnabledData={isSoulwritingEnabledData}
              setIsAutoSaveEnabled={setIsAutoSaveEnabled}
              lastOpenedStage={lastOpenedStage}
              bridgeArrayChar={bridgeArrayChar}
              getValues={getValues}
              memberstatusInfo={memberstatusInfo}
              submitting={submitting}
              values={values}
              componentWrapper={componentWrapper}
              moveToNextStep={moveToNextStep}
              getComPanionList={getComPanionList}
              setshow={setshow}
              companionId={companionId}
              categoryListForm={categoryListForm}
              checkValidation={checkValidation}
              onsubmit={() => onsubmit(values, 1)}
            />
          </div>
        </div>
      </form>
      <PdfComponent
        pdfRef={pdfRef}
        pdfExportComponent={pdfExportComponent}
        projectInfo={projectInfo}
        categoryListForm={categoryListForm}
        getCompanion={getCompanion}
        type={1}
      />
      {show == 1 && (
        <ProtoganistModal
          show={show}
          setSingleCharacter={setSingleCharacter}
          projectId={projectId}
          SingleCharacter={SingleCharacter}
          setCharacter={setCharacter}
          setcategoryListForm={setcategoryListForm}
          onHide={() => {
            setshow(0);
          }}
        />
      )}
      {show == 2 && (
        <ChooseCompanionModal
          show={show}
          onHide={() => {
            setIsAutoSaveEnabled(true);
            setshow(0);
          }}
          CompanionList={CompanionList}
          isFetching={isFetching}
          setCompanionId={setCompanionId}
          companionId={companionId}
          setshow={setshow}
          getSingleCompanion={() => getSingleCompanion(companionId)}
          saveCompanionStatus={saveCompanionStatus}
          componentWrapper={componentWrapper}
          //createsoulWritingProject={createsoulWritingProject}
          onsubmit={() => onsubmit(values, 1)}
          hideExtraInfo={true}
          showCompanionCost={showCompanionCost}
        />
      )}
      {show == 3 && (
        <SendToCompanionModal
          show={show}
          isFetching={isFetching}
          onHide={() => {
            setIsAutoSaveEnabled(true);
            setshow(0);
            if (projectInfo?.companionId == null) {
              setCompanionId(null);
            }
          }}
          onsubmit={() => onsubmit(values, 1)}
        />
      )}
      {show == 4 && (
        <ChooseCharacterPopup
          setProtagonistList={setProtagonistList}
          protagonistList={protagonistList}
          projectId={projectId}
          show={show}
          setshow={setshow}
          character={character}
          step={Step}
          setcategoryListForm={setcategoryListForm}
          categoryId={categoryId}
          characterIndex={characterIndex}
          onHide={() => {
            setshow(0);
          }}
          showCharacterPopup={showCharacterPopup}
          setShowCharacterPopup={setShowCharacterPopup}
        />
      )}
      {show == 5 && (
        <LeaveActionPoppup
          show={show}
          onHide={() => {
            setshow(0);
          }}
          router={router}
        />
      )}
      {show == 6 && (
        <AddProjectModal
          setcomponentWrapper={setcomponentWrapper}
          categoryList={CategoryList}
          show={show}
          projectList={projectList}
          onHide={() => {
            setshow(0);
          }}
          setProjectList={setProjectList}
          
        />
      )}
      {show == 10 && (
        <SoulWritingBillingModal
          show={show}
          data={billingData}
          onHide={() => {
            setIsAutoSaveEnabled(true);
            setshow(0);
          }}
          setshow={setshow}
        />
      )}
      {
        //To show alert popup for changing the companion
        show == 11 && (
          <ChangeCompanionAlertModal
            show={show}
            onHide={() => {
              setshow(0);
            }}
            setshow={setshow}
            CompanionList={CompanionList}
            getComPanionList={getComPanionList}
            showCompanionCost={showCompanionCost}
          />
        )
      }
      {
        //To show alert popup for accept policies for soulwriting
        
        isModalShow && !prefilledReason && (
          // <CreateSoulWritingModal
          //   show={show}
          //   onHide={() => {
          //     setshow(0);
          //   }}
          //   isModalShow={isModalShow}
          //   setIsModalShow={setIsModalShow}
          // />
          <SoulWritingModal
            prefilledReason={prefilledReason}
            setPrefilledReason={setPrefilledReason}
            show={isModalShow}
            onHide={() => {
              router.push(`/dashboard/soulwriting`);
            }}
            isModalShow={isModalShow}
            setIsModalShow={setIsModalShow}
          />
        )
      }
      {show == 12 && (
        <ProtoganistNewModal
          isAddProtagonistOpenFromS3={isAddProtagonistOpenFromS3}
          setProtagonistSurveyData={setProtagonistSurveyData}
          protagonistSurveyData={protagonistSurveyData}
          show={show}
          setSingleCharacter={setSingleCharacter}
          projectId={projectId}
          SingleCharacter={SingleCharacter}
          setCharacter={setCharacter}
          setcategoryListForm={setcategoryListForm}
          onHide={() => {
            setshow(13);
          }}
          setshow={setshow}
          showCharacterPopup={showCharacterPopup}
          setShowCharacterPopup={setShowCharacterPopup}
        />
      )}
      {show == 13 && (
        <ProtoganistMoreInfoModal
          isAddProtagonistOpenFromS3={isAddProtagonistOpenFromS3}
          setFethcCharForS3={setFethcCharForS3}
          fetchCharForS3={fetchCharForS3}
          showCharacterPopup={showCharacterPopup}
          setShowCharacterPopup={setShowCharacterPopup}
          protagonistSurveyData={protagonistSurveyData}
          setProtagonistSurveyData={setProtagonistSurveyData}
          show={show}
          setSingleCharacter={setSingleCharacter}
          projectId={projectId}
          SingleCharacter={SingleCharacter}
          setCharacter={setCharacter}
          setcategoryListForm={setcategoryListForm}
          onHide={() => {
            if (!isAddProtagonistOpenFromS3) {
              setshow(14);
            } else {
              setshow(0);
              setFethcCharForS3(!fetchCharForS3);
            }
          }}
          setshow={setshow}
        />
      )}
      {show == 14 && (
        <ProtagonistListModal
          setProtagonistList={setProtagonistList}
          protagonistList={protagonistList}
          setIsOpenedFromSoulWriteStep3={setIsOpenedFromSoulWriteStep3}
          setIsOpenAddProtagonistOpenFromS3={setIsOpenAddProtagonistOpenFromS3}
          categoryListForm={categoryListForm}
          setProtagonistObject={setProtagonistObject}
          setshow={setshow}
          protagonistSurveyData={protagonistSurveyData}
          setProtagonistSurveyData={setProtagonistSurveyData}
          show={show}
          setSingleCharacter={setSingleCharacter}
          projectId={projectId}
          SingleCharacter={SingleCharacter}
          setCharacter={setCharacter}
          setcategoryListForm={setcategoryListForm}
          onHide={() => {
            setshow(0);
          }}
        />
      )}
      {show == 15 && (
        <ProtagonistEditFormModal
          setIsOpenedFromSoulWriteStep3={setIsOpenedFromSoulWriteStep3}
          setProtagonistList={setProtagonistList}
          protagonistObject={protagonistObject}
          setshow={setshow}
          protagonistSurveyData={protagonistSurveyData}
          setProtagonistSurveyData={setProtagonistSurveyData}
          show={show}
          setSingleCharacter={setSingleCharacter}
          projectId={projectId}
          SingleCharacter={SingleCharacter}
          setCharacter={setCharacter}
          setcategoryListForm={setcategoryListForm}
          onHide={() => {
            if (!isOpenedFromSoulWriteStep3) {
              setshow(14);
            } else {
              setshow(0);
              setFethcCharForS3(!fetchCharForS3);
            }
          }}
        />
      )}
      {show == 17 && (
        <SoulwritingPurchaseModal
          isSoulwritingEnabledData={isSoulwritingEnabledData}
          show={show}
          values={values}
          getValues={getValues}
         
          
          onHide={() => {
            setshow(0);
          }}
        />
      )}
    </>
  );
};
export async function getServerSideProps(ctx) {
  let jwtToken = getCookie("jwtToken", { req: ctx.req, res: ctx.res });

  let headers = {
    "Content-Type": "application/json",
    accept: "application/json",
    language: ctx?.req?.cookies?.language ?? "de",
  };
  if (jwtToken) {
    headers = Object.assign(headers, { Authorization: `Bearer ${jwtToken}` });
  }
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/soul-writing/category`,
      {
        method: "GET",
        headers,
      }
    );
    const json = await response.json();
    return {
      props: {
        CategoryList: json?.responseData?.categoryList
          ? json?.responseData?.categoryList
          : null,
      },
    };
  } catch (err) {
    return { props: {} };
  }
}

export default SoulWritingCreate;
