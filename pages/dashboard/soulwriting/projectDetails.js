import React, { useState, useEffect, Fragment } from "react";
import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import { getLanguages, checklanguage } from "../../../constant";
import { getOldSoulWriting } from "../../../redux/action/soul-writing";

const ProjectDetails = () => {
  const router = useRouter();
  const { projectId } = router.query;
  const [oldProjectsLines, setOldProjectsLines] = useState([]);
  const [oldProjectsSubject, setOldProjectsSubject] = useState([]);
  const [oldProjectsComent, setOldProjectComments] = useState();
  const cookies = useCookie();

  useEffect(() => {
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push(
        "/signin?redirectTourl=/dashboard/soulwriting/projectDetails"
      );
    } else if (projectId) {
      fetchOldProjects();
    }
  }, [projectId]);

  const fetchOldProjects = async () => {
    try {
      if (projectId) {
        const response = await getOldSoulWriting({ id: projectId });
        const index =
          response?.data?.responseData?.records[0]?.contentDetails.length - 1;

        setOldProjectsLines(
          response?.data?.responseData?.records[0]?.contentDetails[index]?.lines
        );
        setOldProjectsSubject(
          response?.data?.responseData?.records[0]?.contentDetails[index]
            ?.subject
        );
        setOldProjectComments(
          response?.data?.responseData?.records[0]?.projectDetails?.comment
        );
      }
    } catch (error) {
      console.error("Error fetching old projects:", error);
    }
  };
  console.log(oldProjectsSubject, "oldProjectsSubject");
  return (
    <div className="h-100 prof-details-wrpr">
      <ul className="d-flex align-center cd-breadcrumb">
        <li className="breadcumb-item">
          <Link href={"/"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item">
          <Link href={"/dashboard/soulwriting/old-soulwriting"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "oldSoulWritingProjects")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item current">
          <em>{getLanguages(checklanguage, "projectDetails")}</em>
        </li>
      </ul>
      <div className="d-flex soul-step-outer soul-step3" id="occasion">
        <div className="soul-step-inner inner-header">
          <h4 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "projectDetails")}{" "}
          </h4>

          <div className="fourth-col generalComment ckEditorOldProject">
            <div
              className="companion_message"
              style={{ backgroundColor: "#ecf9ee" }}
            >
              <label className="commentLabel">
                {getLanguages(checklanguage, "generalComments")}:{" "}
              </label>

              <div className="commentContent">
                <div
                  className="text-cellContent"
                  dangerouslySetInnerHTML={{
                    __html: oldProjectsComent,
                  }}
                ></div>
              </div>
            </div>
          </div>
          <div className="d-flex w-100 scene-row title">
            <p className="p text-grey-6 fw500 d-flex align-center step-subtitle first-col">
              {getLanguages(checklanguage, "line")}
            </p>
            <p className="p text-grey-6 fw500 d-flex align-center step-subtitle second-col">
              {getLanguages(checklanguage, "actor")}
            </p>
            <p className="p text-grey-6 fw500 d-flex align-center step-subtitle third-col">
              {getLanguages(checklanguage, "scene")}
            </p>
          </div>

          <div className="d-flex w-100 scene-row">
            <p className="p text-grey-6 fw500 first-col">0</p>
            <div className="second-col">
              <div className="d-flex align-center person-wrpr personProject">
                <div className="person-cards">
                  <Fragment>
                    <h6
                      className="h6 fw500 person-card"
                      style={{ background: "", color: "" }}
                    >
                      {getLanguages(checklanguage, "occasion")}
                    </h6>
                  </Fragment>
                </div>
              </div>
            </div>
            <div className="scene-text-wrpr third-col">
              <div className="ckeditor-component ckEditorOldProject">
                <div
                  className="text-cell"
                  dangerouslySetInnerHTML={{
                    __html: oldProjectsSubject?.text,
                  }}
                ></div>
              </div>
            </div>

            <div className="fourth-col ckEditorOldProject">
              <div
                className="companion_message"
                style={{ backgroundColor: "#ecf9ee" }}
              >
                <label className="commentLabel">
                  {getLanguages(checklanguage, "comments")}:{" "}
                </label>

                <div className="commentContent">
                  <div
                    className="text-cellContent"
                    dangerouslySetInnerHTML={{
                      __html: oldProjectsSubject?.comment,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {oldProjectsLines?.map((line, index) => (
            <div className="d-flex w-100 scene-row" key={line._id}>
              <p className="p text-grey-6 fw500 first-col">{index + 1}</p>
              <div className="second-col">
                <div className="d-flex align-center person-wrpr personProject">
                  <div className="person-cards">
                    <Fragment>
                      <h6
                        className="h6 fw500 person-card"
                        style={{ background: "", color: "" }}
                      >
                        {line.actor}
                      </h6>
                    </Fragment>
                  </div>
                </div>
              </div>
              <div className="scene-text-wrpr third-col">
                <div className="ckeditor-component ckEditorOldProject">
                  <div
                    className="text-cell"
                    dangerouslySetInnerHTML={{ __html: line.text }}
                  ></div>
                </div>
              </div>

              <div className="fourth-col ckEditorOldProject">
                <div
                  className="companion_message"
                  style={{ backgroundColor: "#ecf9ee" }}
                >
                  <label className="commentLabel">
                    {getLanguages(checklanguage, "comments")}:{" "}
                  </label>

                  <div className="commentContent">
                    <div
                      className="text-cellContent"
                      dangerouslySetInnerHTML={{ __html: line.comment }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectDetails;
