import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import lottieJson from "../../../animation.json";
import { payMentSoulWriting } from "../../../redux/action/soul-writing";
import dynamic from "next/dynamic";
import { checklanguage, getLanguages } from "../../../constant";

const SoulWritingThankYouPage = () => {
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });

  const router = useRouter();
  const { query } = router;
  const cookies = useCookie();
  const {
    userInfo: { User },
  } = useSelector((state) => state.user);
  useEffect(() => {
    if (
      Object.keys(query)?.length > 0 &&
      query?.order_id &&
      query?.order_item_id
    )
      createPaymentForSoulWriting();
  }, [query]);

  const createPaymentForSoulWriting = async () => {
    let id;
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      id = User?.id;
    }
    try {
      let response = await payMentSoulWriting({
        data: router?.asPath?.split("?")[1],
      });
      // setTimeout(() => {
      //   router.push(
      //     {
      //       pathname: `/dashboard/soulwriting`,
      //       // query: {
      //       //   projectId: response?.data?.responseData?.projectId,
      //       //   isSubmited: true,
      //       // },
      //     },
      //     1000
      //   );
      // });
    } catch (err) {}
  };

  return (
    <div className="h-100 d-flex align-center justify-center thanku-wrpr">
      <div className="text-center thanku-inner">
        <div className="thanku-gif">
          <Lottie
            loop
            animationData={lottieJson}
            play
            style={{
              width: 120,
              height: 120,
              marginLeft: "auto",
              marginRight: "auto",
            }}
          />
        </div>
        <h3 className="h3 text-dark-grey fw500">
          {getLanguages(checklanguage, "paymentCompleted")}
        </h3>
        <p className="p text-grey-5">
          {getLanguages(checklanguage, "soulwritingDigi")}
        </p>
        <p className="p text-grey-5">
          {getLanguages(checklanguage, "soulwritingThankYou")}
        </p>
      </div>
      <img
        src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 1"
      />
      <img
        src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 2"
      />
    </div>
  );
};

export default SoulWritingThankYouPage;
