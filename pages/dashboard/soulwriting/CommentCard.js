import React, { useState } from "react";
import dynamic from "next/dynamic";
import { useSelector } from "react-redux";
import { checklanguage, getLanguages } from "../../../constant";
// import CKEditorComponent from "../../../components/FormFields/CKEditor";

let CKEditor;
if (typeof window !== "undefined") {
  CKEditor = dynamic(() => import("../../../components/FormFields/CKEditor"), {
    ssr: false,
  });
}

const CommentCard = ({ fieldData, index, isReadOnly }) => {
  const [commentText, setcommentText] = useState(
    fieldData?.comments?.[0]?.comment
  );
  const { userInfo } = useSelector((state) => state.user);

  const handleChangeFormData = (index, data) => {
    setcommentText(data);
  };

  return (
    <div className="comment-card-block">
      <div className="comment-card">
        <div className={`comment-card-inner companion_message`}>
          <p className="p fw500 text-grey-5 comment-name">
            {fieldData?.comments?.[0]?.author?.firstName}{" "}
            {fieldData?.comments?.[0]?.author?.lastName}{" "}
            {!fieldData?.comments?.[0]?.author?.firstName
              ? getLanguages(checklanguage, "comment")
              : ""}{" "}
            :
          </p>
          <CKEditor
            isDisabled={true}
            ckeditorValue={commentText}
            disabled={true}
            index={index}
            handleChangeFormData={handleChangeFormData}
            hideControlsOnBlur={true}
          />
        </div>
      </div>
      {fieldData?.comments?.[1]?.comment && (
        <div className="comment-card">
          <div className={`comment-card-inner member_message`}>
            <p className="p fw500 text-grey-5 comment-name">
              {fieldData?.comments?.[1]?.author?.firstName}{" "}
              {fieldData?.comments?.[1]?.author?.lastName} :
            </p>
            <p
              className="p text-dark-grey bg-white comment-text"
              dangerouslySetInnerHTML={{
                __html: fieldData?.comments?.[1]?.comment,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentCard;
