import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import lottieJson from "../../../animation.json";
import { payMentSoulWriting, buySoulwriting } from "../../../redux/action/soul-writing";
import dynamic from "next/dynamic";
import { checklanguage, getLanguages } from "../../../constant";

const SoulwritingProductThankyouPage = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });

  const router = useRouter();
  const { query } = router;
  const cookies = useCookie();
  const {
    userInfo: { User },
  } = useSelector((state) => state.user);

  useEffect(() => {
    const jwtToken = cookies.get("jwtToken");
    if (typeof jwtToken !== "undefined" && jwtToken !== null) {
      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
    }
  }, []);

  useEffect(() => {
    if (
      Object.keys(query)?.length > 0 &&
      query?.order_id &&
      query?.order_item_id
    )
      purchaseSoulWriting();
  }, [query]);

  const purchaseSoulWriting = async () => {
    let id;
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      id = User?.id;
    }
    try {
      let response = await buySoulwriting({
        data: router?.asPath?.split("?")[1],
      });
      router.push(router.pathname, "", { scroll: false });
    } catch (err) { }
  };

  

  return (
    <div className="thanku-wrpr" style={{padding: '1rem'}}>
      <div className="text-center thanku-inner" style={{maxWidth: '600px', margin: '0 auto'}}>
        <div className="thanku-gif">
          <Lottie
            loop
            animationData={lottieJson}
            play
            style={{
              width: 120,
              height: 120,
              marginLeft: "auto",
              marginRight: "auto",
            }}
          />
        </div>
        <h3 className="h3 text-dark-grey fw500">
          {getLanguages(checklanguage, "paymentCompleted")}
        </h3>
        <p className="p text-grey-5" style={{marginTop: '0.5rem'}}>
          {getLanguages(checklanguage, "soulwritingDigi")}
        </p>
        <p className="p text-grey-5" style={{marginTop: '-1.5rem', marginBottom: '0.5rem'}}>
          {getLanguages(checklanguage, isLoggedIn ? "soulwritingPurchaseThankYou" : "soulwritingPurchaseThankYouNonLoggedIn", [], [], true)}
        </p>
        <div style={{maxWidth: '500px', margin: '1rem auto', borderRadius: '12px', overflow: 'hidden', background: '#000'}}>
          <div style={{padding:'56.25% 0 0 0', position:'relative'}}>
            <iframe 
              src="https://player.vimeo.com/video/1041738169?badge=0&autopause=0&player_id=0&app_id=58479" 
              frameBorder="0" 
              allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
              style={{position:'absolute', top:0, left:0, width:'100%', height:'100%'}} 
              title="Danke-Video-Seelenschreiben">
            </iframe>
          </div>
        </div>
        {/* <script src="https://player.vimeo.com/api/player.js"></script> */}
        <button className="btn-accent sm w-100" style={{maxWidth: '300px', margin: '1rem auto 0', display: 'block'}} onClick={() => {
          router.push("/dashboard/soulwriting/create-soulwriting");
        }}>
          {getLanguages(checklanguage, isLoggedIn ? "continueSoulwriting" : "continueSoulwritingNonLoggedIn")}
        </button>
        {/* <img
          src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
          style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
          alt="invisible tracker 1"
        />
        <img
          src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
          style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
          alt="invisible tracker 2"
        /> */}
      </div>
    </div>
  );
};

export default SoulwritingProductThankyouPage;