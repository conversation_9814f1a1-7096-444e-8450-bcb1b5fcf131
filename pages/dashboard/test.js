import React, { useRef, useState } from "react";

const RedLineComponent = () => {
  const containerRef = useRef(null);
  const [redLinePosition, setRedLinePosition] = useState(null);
  const isDragging = useRef(false);

  const handleDragStart = (e) => {
    e.preventDefault();
    isDragging.current = true;
  };

  const handleDragMove = (e) => {
    if (!isDragging.current) return;

    const clientY = e.touches ? e.touches[0].clientY : e.clientY;

    const containerRect = containerRef.current.getBoundingClientRect();

    const newPosition = Math.max(
      0,
      Math.min(clientY - containerRect.top, containerRect.height)
    );

    setRedLinePosition(newPosition);
  };

  const handleDragEnd = () => {
    isDragging.current = false;

    // Snap red line to closest row
    const rows = containerRef.current.querySelectorAll(".scene-row");
    let closestRowIndex = -1;
    let closestDistance = Infinity;

    rows.forEach((row, index) => {
      const rowRect = row.getBoundingClientRect();
      const rowCenter = rowRect.top + rowRect.height / 2;
      const distance = Math.abs(rowCenter - redLinePosition);

      if (distance < closestDistance) {
        closestDistance = distance;
        closestRowIndex = index;
      }
    });

    const selectedRow = rows[closestRowIndex];
    const rowRect = selectedRow.getBoundingClientRect();
    const containerTop = containerRef.current.getBoundingClientRect().top;
    setRedLinePosition(rowRect.top + rowRect.height - containerTop);
  };

  return (
    <div
      ref={containerRef}
      className="container_test"
      onMouseMove={handleDragMove}
      onMouseUp={handleDragEnd}
      onTouchMove={handleDragMove}
      onTouchEnd={handleDragEnd}
    >
      {/* Rows */}
      {[...Array(3)].map((_, index) => (
        <div key={index} className="d-flex w-100 scene-row">
          <p className="p text-grey-6 fw500 first-col">{index + 1}</p>
          <div className="second-col">
            <h6
              className="h6 fw500 person-card"
              style={{ cursor: "pointer", visibility: "visible" }}
            >
              I
            </h6>
          </div>
          <div className="scene-text-wrpr third-col">
            <div className="f-in"></div>
            <div className="d-flex align-center justify-between">
              <span className="text-grey-7 font-inter char-count">
                344/1000
              </span>
              <div className="d-flex align-center action-btns">
                <button type="button" className="btn add-btn">
                  <span className="icon plus-icon"></span> Add row
                </button>
              </div>
            </div>
          </div>
          <div className="comment-card-block"></div>
        </div>
      ))}

      {/* Draggable Red Line */}
      <div
        className="d-flex soul-step-outer soul-step8"
        id="redline"
        draggable="true"
        onMouseDown={handleDragStart}
        onTouchStart={handleDragStart}
        style={{
          position: "absolute",
          top: redLinePosition ? `${redLinePosition}px` : "50%",
          left: 0,
          right: 0,
          height: "4px",
          backgroundColor: "red",
          cursor: "grab",
        }}
      >
        <div className="step-left" id="red-lineStep">
          <div className="d-flex align-center redline-wrpr">
            <span className="icon drag-icon"></span>
            <div className="red-line"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RedLineComponent;
