import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import lottieJson from "../../animation.json";
import { USER_CART } from "../../redux/action/cart";
import { postOrder } from "../../redux/action/order";
import { checklanguage, getLanguages } from "../../constant";
import dynamic from "next/dynamic";

const ThankYouPage = () => {
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });
  const router = useRouter();
  const dispatch = useDispatch();
  const { query } = router;
  const cookies = useCookie();
  const {
    userInfo: { User },
  } = useSelector((state) => state.user);
  useEffect(() => {
    if (
      Object.keys(query)?.length > 0 &&
      query?.order_id &&
      query?.order_item_id
    )
      createOrder();
  }, [query]);

  const createOrder = async () => {
    let id;
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      id = User?.id;
    }
    try {
      let response = await postOrder({
        userId: id,
        data: router?.asPath?.split("?")[1],
      });
      dispatch({
        type: USER_CART,
        payload: null,
      });
      router.push(router.pathname, "", { scroll: false });
    } catch (err) {}
  };

  return (
    <div className="h-100 d-flex align-center justify-center thanku-wrpr">
      <div className="text-center thanku-inner">
        <div className="thanku-gif">
          <Lottie
            loop
            animationData={lottieJson}
            play
            style={{
              width: 120,
              height: 120,
              marginLeft: "auto",
              marginRight: "auto",
            }}
          />
        </div>
        <h3 className="h3 text-dark-grey fw500">
          {getLanguages(checklanguage, "orderThankyouPageMessage")}
        </h3>
        <p className="p text-grey-5">
          {getLanguages(checklanguage, "orderThankyouPageDetailMessage", [], [], true)}
        </p>
        <button onClick={() => router.push("/")} className="btn-accent">
          {getLanguages(checklanguage, "backToReception")}
        </button>
      </div>
      <img
        src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 1"
      />
      <img
        src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 2"
      />
    </div>
  );
};

export default ThankYouPage;
