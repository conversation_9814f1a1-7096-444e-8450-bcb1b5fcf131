import { useRouter } from "next/router";
import React, { useEffect } from "react";
import lottieJson from "../animation.json";
import { checklanguage, getLanguages } from "../constant";
import dynamic from "next/dynamic";

const ThankYouPage = () => {
  const Lottie = dynamic(() => import("react-lottie-player"), { ssr: false });

  const router = useRouter();
  const { query } = router;

  useEffect(() => {
    setTimeout(() => {
      router.push("/");
    }, 5000);
  }, []);

  return (
    <div className="feedback">
      <div className="container mx-auto d-flex align-center justify-center flex-column h-100 over-auto">
        <div className="text-center thanku-inner">
          <div className="thanku-gif">
            <Lottie
              loop
              animationData={lottieJson}
              play
              style={{
                width: 120,
                height: 120,
                marginLeft: "auto",
                marginRight: "auto",
              }}
            />
          </div>
          <h4 className="h4 mb-0 text-dark-grey-5 fw500 img-card-title">
            {getLanguages(checklanguage, "thankYouMessageFeedback")}
          </h4>
          <p className="text-grey-5 feedback-content">
            {getLanguages(checklanguage, "thankYouMessageFeedbackDesc")}
          </p>
        </div>
      </div>
      <img
        src="https://event.webinarjam.com/t/sale/m1zpqu7mu8?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 1"
      />
      <img
        src="https://event.webinarjam.com/t/sale/67r68s5zu3?price=297.00&currency=EUR"
        style={{ visibility: "hidden", height: 0, width: 0, border: "none" }}
        alt="invisible tracker 2"
      />
    </div>
  );
};

export default ThankYouPage;
