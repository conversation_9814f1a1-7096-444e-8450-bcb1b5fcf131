import React, { useEffect, useState } from "react";
import { getCookie } from "cookies-next";
import ScriptViewComponent from "../components/SoulWriting/SoulWrittingStep/ScriptViewComponent";

const SoulwritingPdf = (props) => {
  let companionData = null;
  let dateInfo = {
    trigger_date: null,
    projection_date: null,
  };
  let projectInfo = null;
  let categoryListForm = null;
  if (props.projectDetails?.companion?.id) {
    companionData = props.projectDetails?.companion;
  }

  dateInfo = Object.assign(dateInfo, {
    ...props.projectDetails?.projectMeta?.bridgeCharacter?.[2],
    ...props.projectDetails?.projectMeta?.bridgeCharacter?.[3],
  });
  projectInfo = props.projectDetails;

  console.log(companionData, dateInfo, projectInfo);

  const matchData = (categoryId, payloadResponse) => {
    let arrayData = [];
    for (var j = 0; j < payloadResponse?.length; j++) {
      if (categoryId == parseInt(payloadResponse[j].categoryId)) {
        arrayData.push({
          content: payloadResponse[j].content,
          categoryId: payloadResponse[j].categoryId,
          character: payloadResponse[j].character,
          lineNumber: payloadResponse[j].lineNumber,
          characterId: payloadResponse[j].characterId,
          uuid: payloadResponse[j].uuid,
          errorMessage: "",
          isRedLine: payloadResponse[j].isRedLine,
          comments:
            payloadResponse[j].comments != null
              ? payloadResponse[j].comments
              : [],
          painpictureCollapse: payloadResponse[j].painpictureCollapse,
        });
      }
    }
    return arrayData;
  };

  const modifiedValue = (props) => {
    let payloadResponse;
    let categoryList = props.categories.categoryList;
    let sliceArray = categoryList?.slice(2, categoryList.length);

    payloadResponse = [...props.soulwritingContent.content];
    var newList = {};
    for (let i = 0; i < sliceArray?.length; i++) {
      if (payloadResponse?.length > 0) {
        let arrayData = matchData(sliceArray[i]?.id, payloadResponse);
        if (arrayData.length > 0) {
          newList[`categoryForms${i + 1}`] = arrayData;
        }
      }
    }
    categoryListForm = newList;
  };

  modifiedValue(props);

  

  return (
    <div className="prod-details-wrpr">
      <ScriptViewComponent
        view={"pdf"}
        dateInfo={dateInfo}
        categoryListForm={categoryListForm}
        projectInfo={projectInfo}
        getCompanion={companionData}
        language={props.requestedLang}
      />
    </div>
  );
};
export async function getServerSideProps(ctx) {
  let projectId = ctx.query?.projectId;
  let version = ctx.query?.version;
  let language = ctx.query?.language;

  let headers = {
    language: language || "de",
  };

  try {
    const [projectDetails, content, categories] = await Promise.all([
      fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/soul-writing/project-pdf/${projectId}?version=${version}`, {
          method: "GET",
          headers,
        }
      )
        .then((response) => {
          return response.json();
        })
        .catch((err) => {
          return err;
        }),
      fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/soul-writing/content-pdf/${projectId}?version=${version}`, {
          method: "GET",
          headers,
        }
      )
        .then((response) => {
          return response.json();
        })
        .catch((err) => {
          return err;
        }),
      fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/soul-writing/category`, {
        method: "GET",
        headers,
      })
        .then((response) => {
          return response.json();
        })
        .catch((err) => {
          return err;
        }),
    ]);
    const [projectDetailsResp, contentResp, categoriesResp] = await Promise.all(
      [projectDetails, content, categories]
    );
    return {
      props: {
        projectDetails: projectDetailsResp?.responseData,
        soulwritingContent: contentResp?.responseData,
        categories: categoriesResp?.responseData,
        requestedLang: language
      },
    };
  } catch (err) {
    console.log(err);
    return { props: {} };
  }
}
export default SoulwritingPdf;
