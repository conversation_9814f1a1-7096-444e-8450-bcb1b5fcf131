import Head from "next/head";
import Layout from "../components/Layout";

import "@fullcalendar/common/main.css";
import "@fullcalendar/daygrid/main.css";
import "@fullcalendar/timegrid/main.css";
import "react-loading-skeleton/dist/skeleton.css";

import { Provider } from "react-redux";
import { store, persistor } from "../redux/store";
import { PersistGate } from "redux-persist/integration/react";
import { useEffect, useState } from "react";
import Loader from "../components/loader";
//import { IntercomProvider } from "react-use-intercom";
import moment from "moment";
import "moment/locale/cs";
import "moment/locale/de";
import Layouts from "../facebookPixel";
import * as gtag from "../lib/ga/gtag";
import { useRouter } from "next/router";
import Script from "next/script";
import Hotjar from "../Hotjar";

const fbPixel = process.env.NEXT_PUBLIC_FB_PIXEL;

function MyApp({ Component, pageProps }) {
  const [loading, setloading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    let lang = localStorage.getItem("language");
    if (lang == "de" || !lang) {
      moment.locale("de");
    } else {
      if (lang == "en") {
        moment.locale("en");
      } else if (lang == "cz") {
        moment.locale("cs");
      }
    }
    setTimeout(() => {
      setloading(false);
    }, 1000);
  }, []);
  useEffect(() => {
    const handleRouteChange = (url) => {
      gtag.pageview(url);
    };
    router.events.on("routeChangeComplete", handleRouteChange);
    return () => {
      router.events.off("routeChangeComplete", handleRouteChange);
    };
  }, [router.events]);
  return (
    <>
      <Head>
        <meta charSet="utf-8" />
        <title>Kuby</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, shrink-to-fit=no"
        />
        <meta property="og:image" content="/images/banner.png" />
        <link
          rel="icon"
          href="/images/KUBY_Favicon.png"
          type="image/png"
          sizes="16x16"
        />
        <script
          src="https://www.digistore24.com/service/digistore.js"
          async
        ></script>
        <script async>
          {/* digistoreCart("on_cart_change=digiStoreCartUpdate"); */}
        </script>
        {/* <script src="/js/digistoreCallback.js" async ></script> */}

      </Head>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          {/* <IntercomProvider appId={process.env.NEXT_PUBLIC_INTERCOM_APP_ID}> */}
            {loading ? (
              <Loader />
            ) : fbPixel == "show" ? (
              <Layouts>
                <Hotjar>
                  <Layout Component={Component} pageProps={pageProps}>
                    <Component {...pageProps} />
                  </Layout>
                </Hotjar>
              </Layouts>
            ) : (
              <Layout Component={Component} pageProps={pageProps}>
                <Component {...pageProps} />
              </Layout>
            )}
          {/* </IntercomProvider> */}
        </PersistGate>
      </Provider>
    </>
  );
}

export default MyApp;
