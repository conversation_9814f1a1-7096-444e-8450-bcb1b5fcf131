import { Html, Head, Main, NextScript } from "next/document";
import Script from "next/script";
// import {GA_TRACKING_ID} from "../lib/ga/index"
export default function Document() {
  //   console.log(gtag.GA_TRACKING_ID, "gtttt");
  return (
    <Html>
      <Head>
        {/* <!-- CSS files  --> */}
        <link rel="stylesheet" href="/scss/style.css" />
        <link rel="stylesheet" href="/scss/developer.css" />
        {/* Hotjar Tracking Code */}
        
        <script
          async
          src={`https://www.googletagmanager.com/gtag/js?id=G-ED1KMHY4MP`}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'G-ED1KMHY4MP', {
                  page_path: window.location.pathname,
                });
              `,
          }}
        />
        
      </Head>
      <body>
        <Main />
        <NextScript />
        {/* <Script src="js/custom.js" type="text/javascript" strategy='beforeInteractive'></Script> */}
        <Script
          src="https://player.vimeo.com/api/player.js"
          strategy="beforeInteractive"
        ></Script>
        <Script
          src="https://www.digistore24.com/service/digistore.js"
          strategy="beforeInteractive"
        ></Script>
        <Script src="/js/Hilitor.js" strategy="beforeInteractive"></Script>
      </body>
    </Html>
  );
}
