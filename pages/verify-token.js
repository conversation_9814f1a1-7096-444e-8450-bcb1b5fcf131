import { useRouter } from "next/router";
import React, { useState, useEffect } from "react";
import { useCookie } from "next-cookie";
import toast from "react-hot-toast";
import {
  resendVerificationCode,
  verifyEmailsignup,
  SAVE_PROFILE_INFO,
} from "../redux/action/user/user";
import { useDispatch } from "react-redux";
import { checklanguage, getLanguages } from "../constant";
const VerifyEmailSignUp = () => {
  const router = useRouter();
  const cockies = useCookie();
  const [loading, setLoading] = useState(0);
  const dispatch = useDispatch();
  useEffect(() => {
    if (!router.isReady) return;
    const slug = router.query;
    verifySignUp(slug);
  }, [router.isReady]);
  const verifySignUp = async (slug) => {
    setLoading(1);
    var token = slug?.token;
    verifyEmailsignup({ token: token })
      .then((response) => {
        if (response.status === 200) {
          router.push("/");
          cockies.set("jwtToken", response?.data?.responseData?.token, {
            path: "/",
            maxAge: 1000000000000,
          });
          dispatch({
            type: SAVE_PROFILE_INFO,
            payload: response?.data?.responseData,
          });
          if(typeof window !=undefined){
            localStorage.setItem("jwtToken", response?.data?.responseData?.token)
          }
        }
      })
      .catch((err) => {
        setLoading(3);
      });
  };
  const resendVerificationlink = () => {
    resendVerificationCode({ email: router?.asPath?.split("=")[1]?.replace('&token',"") })
      .then((res) => {
        if (res.status == 200) {
          toast.success(getLanguages(checklanguage, "emailSentSuccessfully"));
        }
      })
      .catch((err) => {});
  };

  return (
    <section className="verified-sec">
      <div className="container">
        <div className="row">
          <div className="col-12">
            {loading === 1 ? (
              <>
                <div className="verify-process">
                  <div className="verify-img-wrpr">
                    <img
                      src="/images/loading-bars.gif"
                      alt=""
                      className="img-fluid"
                    />
                  </div>
                  <h3 className="h3 text-dark text-center">
                    Verifying Please wait{" "}
                  </h3>
                  <p className="p text-text text-center"></p>
                </div>
              </>
            ) : loading === 3 ? (
              <>
                <div className="verify-process">
                  <div className="verify-img-wrpr">
                    <img src="/images/error.gif" alt="" className="img-fluid" />
                  </div>
                  <h3 className="h3 text-dark text-center">Token Expired </h3>
                  <p className="p text-text text-center"></p> <br />
                  <a
                    href="javascript:void(0)"
                    className="link-blue text-center"
                    onClick={resendVerificationlink}
                  >
                    Resend your verification link
                  </a>
                </div>
              </>
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default VerifyEmailSignUp;
