import moment from "moment";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ChangePassword from "../../components/Authenticated/ChangePassword";
import EditProfile from "../../components/Authenticated/EditProfile";
import {
  GENDER_TYPES,
  PROFILE_NAV_LABELS,
  getLanguages,
  checklanguage,
} from "../../constant";
import CommonNavigation from "../../helpers/CommonNavigation";
import { getAllLanguages, SAVE_LANGUAGES } from "../../redux/action/auth";
import {
  getProfileData,
  SAVE_PROFILE_INFO,
  connectGoogle,
  removeGoogle,
  getProfileDetails,
} from "../../redux/action/user/user";
import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import ReactFlagComponent from "../../components/Common/ReactFlagComponent";

function Profile() {
  // local variables
  const [isView, setIsView] = useState(0);

  // use hooks
  const cookies = useCookie();
  const router = useRouter();
  const { query } = router;

  // redux values
  const { userInfo } = useSelector((state) => state.user);

  const { languages } = useSelector((state) => state.languages);

  // use hooks
  const dispatch = useDispatch();

  const getLanguagesData = async () => {
    try {
      const resp = await getAllLanguages();
      dispatch({
        type: SAVE_LANGUAGES,
        payload: resp?.data?.responseData?.languages,
      });
    } catch (e) {}
  };

  const getUserProfileData = async () => {
    try {
      const resp = await getProfileData();
      dispatch({
        type: SAVE_PROFILE_INFO,
        payload: resp?.data?.responseData,
      });
    } catch (e) {}
  };

  useEffect(() => {
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push("/");
    }
    getLanguagesData();
    if (query?.code) {
      let payload = {
        email: userInfo?.User?.email,
        code: query?.code,
        userId: userInfo?.User?.id,
        role: "customer",
        // clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
      };
      googleConnectRequest(payload);
    }

    fetchProfileData();
  }, [query]);

  const fetchProfileData = async () => {
    try {
      const res = await getProfileDetails();
      let userDetail = userInfo;
      let updatedData = res?.data?.responseData;
      updatedData = Object.assign(updatedData, { token: userDetail.token });

      if (res?.data?.responseData?.UserProfile) {
        dispatch({
          type: SAVE_PROFILE_INFO,
          payload: updatedData,
        });
      }
    } catch (e) {}
  };

  const googleConnectRequest = async (payload) => {
    try {
      const resp = await connectGoogle(payload);
      window.location.href = "/user/profile";
      // dispatch({
      //   type: SAVE_PROFILE_INFO,
      //   payload: resp?.data?.responseData,
      // });
    } catch (e) {}
  };

  const removeGoogleCalendar = async () => {
    let payload = {
      email: userInfo?.User?.email,
      role: "customer",
    };
    try {
      const resp = await removeGoogle(payload);
      try {
        const res = await getProfileDetails();
        let userDetail = userInfo;
        let updatedData = res?.data?.responseData;
        updatedData = Object.assign(updatedData, { token: userDetail.token });

        if (resp?.data?.responseData?.profile) {
          dispatch({
            type: SAVE_PROFILE_INFO,
            payload: updatedData,
          });
        }
        window.location.href = "/user/profile";
      } catch (e) {}

      // let userDetail = userInfo;
      // if(resp?.data?.responseData?.responseData?.profile){
      //   userDetail = Object.assign(userDetail, {UserProfile: resp.data.responseData.responseData.profile});
      //   dispatch({
      //     type: SAVE_PROFILE_INFO,
      //     payload: resp?.data?.responseData,
      //   });
      // }
      // window.location.href = "/user/profile"
    } catch (e) {}
  };

  //https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id=************-gfgk6l2sq26078o1uinfa87h4kdfbol9.apps.googleusercontent.com&redirect_uri=https://eabb-2409-40d1-a-f12c-c0b3-9709-5eaa-19d.ngrok-free.app&scope=openid&state=offline
  return (
    <>
      {/* expand */}
      <div className="h-100 prof-details-wrpr">
        <ul className="d-flex align-center cd-breadcrumb">
          <li className="breadcumb-item">
            <Link href={"/"} legacyBehavior>
              <a href="javascript:;" className="breadcumb-link">
                {getLanguages(checklanguage, "reception")}
              </a>
            </Link>
          </li>
          <li className="breadcumb-item current">
            <em>{getLanguages(checklanguage, "profiledetail")}</em>
          </li>
        </ul>
        <div className="prof-tabs-wrpr">
          <CommonNavigation data={PROFILE_NAV_LABELS} />
          <div
            className="tab-profiledetails tab-contents active"
            id="profiledetails"
          >
            <div
              className={`bg-white profile-tab-wrpr ${
                isView != 0 ? "edit-profile" : ""
              }`}
            >
              {query?.tab === "edit-profile" ? (
                <>
                  <div className="d-flex justify-between align-center profile-header">
                    <h4 className="h4 title">
                      {getLanguages(checklanguage, "editprofile")}
                    </h4>
                  </div>
                  <EditProfile setIsView={() => router.back() } />
                </>
              ) : query?.tab === "change-password" ? (
                <ChangePassword setIsView={() => router.back()} />
              ) : (
                <>
                  <div className="d-flex justify-between align-center profile-header">
                    <h4 className="h4 title">
                      {getLanguages(checklanguage, "profile")}
                    </h4>
                    <div className="d-flex align-center profile-btns">
                      {isView == 0 && (
                        <Link href="/user/profile?tab=change-password">
                          <a
                            //onClick={() => router.push('/user/profile?tab=change-password')}
                            className="btn sm"
                          >
                            <span className="icon change-pass-green"></span>
                            {getLanguages(checklanguage, "changepassword")}
                          </a>
                        </Link>
                      )}
                      <Link href="/user/profile?tab=edit-profile">
                        <a className="btn sm">
                          <span className="icon edit-icon-green"></span>
                          {getLanguages(checklanguage, "editdetail")}
                        </a>
                      </Link>

                      {process.env.NEXT_PUBLIC_GOOGLE_CALENDAR_OPTION ==
                      "show" ? (
                        userInfo?.UserProfile?.googleAccount ? (
                          <button
                            className="btn sm google-calendar-btn"
                            onClick={() => removeGoogleCalendar()}
                          >
                            <img src="/images/g_calendar.png" />
                            {getLanguages(checklanguage, "removeGoogle")}
                          </button>
                        ) : (
                          <a
                            className="btn sm google-calendar-btn"
                            href={process.env.NEXT_PUBLIC_GOOGLE_CONNECT_URL}
                          >
                            <img src="/images/g_calendar.png" />
                            {getLanguages(checklanguage, "connectGoogle")}
                          </a>
                        )
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                  <div className="d-flex align-center justify-between profile-info-wrpr">
                    <div className="d-flex align-center profile-card">
                      <div className="comp-img">
                        <img
                          src={
                            userInfo?.UserProfile?.attachment?.path
                              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${userInfo?.UserProfile?.attachment?.path}`
                              : "/images/userimg.svg"
                          }
                          alt=""
                          className="cover-img"
                        />
                      </div>
                      <div className="text-left profile-details">
                        <div className="d-flex align-center justify-center comp-name-wrpr">
                          <h6 className="h6 fw600 text-black-1">
                            {userInfo?.UserProfile?.firstName}{" "}
                            {userInfo?.UserProfile?.lastName}
                          </h6>
                          {userInfo?.UserProfile?.languages?.length > 0 &&
                            userInfo?.UserProfile?.languages?.map(
                              (_lang, index) => (
                                <ReactFlagComponent
                                  code={_lang?.mobileCode}
                                  height="16"
                                  key={index}
                                />
                              )
                            )}
                        </div>
                        {userInfo?.UserProfile?.gender && (
                          <p className="text-black-1 comp-desc">
                            {userInfo?.UserProfile?.gender == 1
                              ? GENDER_TYPES[checklanguage][0]?.name
                              : GENDER_TYPES[checklanguage][1]?.name}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="d-flex justify-between align-center join-date-wrpr">
                      <p className="fw500 text-grey-5 join-date-label">
                        {getLanguages(checklanguage, "joinedDate")}:
                      </p>
                      <p className="fw500 text-black-1 join-date">
                        {moment(userInfo?.UserProfile?.createdAt).format("LL")}
                      </p>
                    </div>
                  </div>
                  <div className="d-flex align-center profile-others">
                    <div className="other-details" style={{ width: "100%" }}>
                      <h6 className="text-grey-7 prof-label">
                        {getLanguages(checklanguage, "email")}:
                      </h6>
                      <p className="font-inter text-black-1">
                        {userInfo?.User?.email}
                      </p>
                    </div>
                  </div>
                  <div className="d-flex align-center profile-others">
                    <div className="other-details" style={{ width: "100%" }}>
                      <h6 className="text-grey-7 prof-label">
                        {getLanguages(checklanguage, "phone")}:
                      </h6>
                      <p className="font-inter text-black-1">
                        {userInfo?.User?.countryCode}{" "}
                        {userInfo?.User?.phoneNumber}
                      </p>
                    </div>

                    {userInfo?.UserProfile?.address ? (
                      <div className="other-details">
                        <h6 className="text-grey-7 prof-label">Address</h6>
                        <p className="font-inter text-black-1">
                          Rodelheimer Bahnweg 21, Hamburg, Germany, 20095
                        </p>
                      </div>
                    ) : null}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Profile;
