import React, { useEffect, useState } from "react";
import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import OldMeetingListTable from "../../components/ProfileTabWrapper/OldMeetingListTable";
import OldMeetingDetailModal from "../../components/ProfileTabWrapper/OldMeetingDetailModal";
import { getLanguages, checklanguage, formatStatus } from "../../constant";
import { getOldMeetings } from "../../redux/action/kuby-companion";

const OldMeetings = () => {
  const [oldMeetings, setOldMeetings] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState(null);
  const cookies = useCookie();
  const router = useRouter();

  useEffect(() => {
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push("/signin?redirectTourl=/user/old-meetings");
    } else {
      fetchOldMeetings();
    }
  }, []);

  const fetchOldMeetings = async () => {
    try {
      const response = await getOldMeetings();
      setOldMeetings(response?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching old meetings:", error);
    }
  };

  const handleShowModal = (meeting) => {
    setSelectedMeeting(meeting);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedMeeting(null);
  };

  return (
    <div className="h-100 prof-details-wrpr">
      <ul className="d-flex align-center cd-breadcrumb">
        <li className="breadcumb-item">
          <Link href={"/"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item">
          <Link href={"//user/meeting-history"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "meetinghistory")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item current">
          <em>{getLanguages(checklanguage, "oldMeetings")}</em>
        </li>
      </ul>
      <div className="inner-header">
        <h4 className="fw500 page-title">
          {getLanguages(checklanguage, "oldMeetings")}
        </h4>
        <div className="w-100 order-history bg-white">
          <div className="cart_wrpr">
            {oldMeetings ? (
              <table className="table">
                <thead>
                  <tr>
                    <th className="desktop-only">
                      {getLanguages(checklanguage, "status")}
                    </th>
                    <th>{getLanguages(checklanguage, "date")}</th>
                    <th>{getLanguages(checklanguage, "companion")}</th>
                    <th>{getLanguages(checklanguage, "duration")}</th>
                    <th>{getLanguages(checklanguage, "details")}</th>
                  </tr>
                </thead>
                <tbody>
                  {oldMeetings.map((meeting, i) => (
                    <OldMeetingListTable
                      key={i}
                      meeting={meeting}
                      handleShowModal={handleShowModal}
                    />
                  ))}
                </tbody>
              </table>
            ) : (
              <></>
            )}
          </div>
        </div>
        {selectedMeeting && (
          <OldMeetingDetailModal
            formatStatus={formatStatus}
            show={showModal}
            onHide={handleCloseModal}
            setShowModal={setShowModal}
            meeting={selectedMeeting}
          />
        )}
      </div>
    </div>
  );
};

export default OldMeetings;
