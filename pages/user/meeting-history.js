import { use<PERSON><PERSON>ie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import ReactPagination from "../../components/Common/ReactPagination";
import MeetingList from "../../components/ProfileTabWrapper/MeetingList";
import {
  PROFILE_NAV_LABELS,
  getLanguages,
  checklanguage,
  formatStatus,
} from "../../constant";
import CommonNavigation from "../../helpers/CommonNavigation";
import {
  completedMeetings,
  getEventsDetailById,
} from "../../redux/action/kuby-companion";
import MeetingDetailModal from "../../components/Common/Modals/MeetingDetailModal";
function MeetingHistory() {
  // use hooks
  const cookies = useCookie();
  const router = useRouter();
  const [currentPage, setcurrentPage] = useState(1);
  const [meetingEnd, setmeetingEnd] = useState(null);
  const [dateSelected, setdateSelected] = useState([null, null]);
  const [showModal, setShowModal] = useState(false);
  const [recordObject, setRecordObject] = useState(null);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    if (searchParams.get("meetingId")) {
      getMeetingDetails(searchParams.get("meetingId"));
    }
  }, [router.asPath]);
  // use hook
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push("/signin?redirectTourl=/user/meeting-history");
    } else {
      CompleledMeetings();
    }
  }, [currentPage, dateSelected]);

  const CompleledMeetings = async () => {
    let dateObject = {};
    if (dateSelected?.[0] != null && dateSelected?.[1] != null) {
      dateObject.startDate = dateSelected[0];
      dateObject.endDate = dateSelected[1];
    }
    try {
      let response = await completedMeetings({
        pageNumber: currentPage,
        limit: 10,
        ...dateObject,
        orderByValue: "ASC",
      });
      setmeetingEnd(response?.data?.responseData);
    } catch (err) {}
  };

  const getMeetingDetails = async (id) => {
    let dateObject = {};

    try {
      let response = await getEventsDetailById({
        id,
      });
      if (response?.data?.responseData?.id) {
        setRecordObject(response?.data?.responseData);
        setShowModal(true);
      }
    } catch (err) {}
  };
  return (
    <>
      {/* expand */}
      <div className="h-100 prof-details-wrpr">
        <ul className="d-flex align-center cd-breadcrumb">
          <li className="breadcumb-item">
            <Link href={"/"} legacyBehavior>
              <a href="javascript:;" className="breadcumb-link">
                {getLanguages(checklanguage, "reception")}
              </a>
            </Link>
          </li>
          <li className="breadcumb-item current">
            <em>{getLanguages(checklanguage, "meetinghistory")}</em>
          </li>
        </ul>

        {/* <div className="prof-tabs-wrpr">
          <CommonNavigation data={PROFILE_NAV_LABELS} />
          <div className="tab-meeting tab-contents" id="meeting"></div>
        </div> */}
        <MeetingList
          formatStatus={formatStatus}
          setShowModal={setShowModal}
          recordObject={recordObject}
          setRecordObject={setRecordObject}
          maxDate={new Date()}
          Meetinglist={meetingEnd}
          setdateSelected={setdateSelected}
          dateSelected={dateSelected}
        />
        {meetingEnd?.totalPages > 1 && (
          <div className="paginatate">
            <ReactPagination
              currentPage={currentPage}
              setcurrentPage={setcurrentPage}
              totalItemsCount={
                meetingEnd?.totalRecords ? meetingEnd?.totalRecords : 10
              }
              pageRangeDisplayed={10}
              totalPages={meetingEnd?.totalPages ? meetingEnd?.totalPages : 10}
              perPage={meetingEnd?.perPage ? meetingEnd?.perPage : 10}
            />
          </div>
        )}
      </div>
      {recordObject && showModal ? (
        <MeetingDetailModal
          formatStatus={formatStatus}
          show={showModal}
          setShowModal={setShowModal}
          recordObject={recordObject}
          setRecordObject={setRecordObject}
        />
      ) : (
        <></>
      )}
    </>
  );
}

export default MeetingHistory;
