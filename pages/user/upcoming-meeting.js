import moment from "moment";
import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import ReactPagination from "../../components/Common/ReactPagination";
import MeetingList from "../../components/ProfileTabWrapper/MeetingList";
import {
  checklanguage,
  getLanguages,
  PROFILE_NAV_LABELS,
  formatStatus,
} from "../../constant";
import CommonNavigation from "../../helpers/CommonNavigation";
import { upcomingMeetings } from "../../redux/action/kuby-companion";
const UpcommingMeeting = () => {
  const cookies = useCookie();
  const router = useRouter();
  const [curremtPage, setCurrentPage] = useState(1);
  const [UpcomingMeeting, setUpcomingMeeting] = useState(null);
  const [dateSelected, setdateSelected] = useState([null, null]);
  useEffect(() => {
    if (
      cookies.get("jwtToken") == undefined ||
      cookies.get("jwtToken") == null ||
      cookies.get("jwtToken") == ""
    ) {
      router.push("/signin?redirectTourl=/user/upcoming-meeting");
    } else {
      UpCommingMeeting();
    }
  }, [curremtPage, dateSelected]);

  const UpCommingMeeting = async () => {
    let dateObject = {};
    if (dateSelected?.[0] != null && dateSelected?.[1] != null) {
      dateObject.startDate = moment(dateSelected[0]).utc().toDate();
      dateObject.endDate = moment(dateSelected[1]).utc().toDate();
    }
    try {
      let response = await upcomingMeetings({
        pageNumber: curremtPage,
        limit: 10,
        ...dateObject,
      });
      setUpcomingMeeting(response?.data?.responseData);
    } catch (err) {}
  };
  return (
    <div className="h-100 prof-details-wrpr">
      <ul className="d-flex align-center cd-breadcrumb">
        <li className="breadcumb-item">
          <Link href={"/"} legacyBehavior>
            <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
            </a>
          </Link>
        </li>
        <li className="breadcumb-item current">
          <em>{getLanguages(checklanguage, "upcomingMeeting")}</em>
        </li>
      </ul>
      {/* <div className="prof-tabs-wrpr">
                <CommonNavigation data={PROFILE_NAV_LABELS} />
                <div className="tab-meeting tab-contents" id="meeting"></div>
            </div> */}
      <MeetingList
        listType="upcoming"
        formatStatus={formatStatus}
        minDDate={new Date()}
        meetingType={1}
        Meetinglist={UpcomingMeeting}
        setdateSelected={setdateSelected}
        dateSelected={dateSelected}
      />
      {UpcomingMeeting?.totalPages > 1 && (
        <div className="paginatate">
          <ReactPagination
            currentPage={curremtPage}
            setcurrentPage={setCurrentPage}
            totalItemsCount={
              UpcomingMeeting?.totalRecord ? UpcomingMeeting?.totalRecord : 10
            }
            totalPages={
              UpcomingMeeting?.totalPages ? UpcomingMeeting?.totalPages : 10
            }
            perPage={UpcomingMeeting?.perPage ? UpcomingMeeting?.perPage : 10}
          />
        </div>
      )}
    </div>
  );
};

export default UpcommingMeeting;
