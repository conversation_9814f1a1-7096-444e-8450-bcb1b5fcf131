import { useCookie } from "next-cookie";
import Link from "next/link";
import DateTimePicker from "react-datetime-picker/dist/entry.nostyle";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { PROFILE_NAV_LABELS, getLanguages, checklanguage } from "../../constant";
import CommonNavigation from "../../helpers/CommonNavigation";
import OrderHistoryComponent from "../../components/ProfileTabWrapper/OrderHistory";
import { getOrder } from "../../redux/action/order"
import moment from "moment";
import { CartScreenLoader } from "../../components/FieldLoader";
import ReactPagination from "../../components/Common/ReactPagination";
import { DateRangePickerComponent } from "../../components/Common/DateRangePickerComponent";
import { getCategoryLists } from "../../redux/action/product";
function OrderHistory({ CategoryList }) {

  const cookies = useCookie();
  const router = useRouter();
  const [currentPage, setcurrentPage] = useState(1);
  const [orderHistory, setOrderHistory] = useState(null);
  const [startDate, setstartDate] = useState(new Date());
  const [dateSelected, setdateSelected] = useState([null, null]);
  const [categpryArray, setcategpryArray] = useState([])

  useEffect(() => {
    if (cookies.get("jwtToken") == undefined || cookies.get("jwtToken") == null || cookies.get("jwtToken") == "") {
      router.push("/");
    }
    else {
      orderHistoryDetail();
    }
  }, [currentPage, dateSelected , categpryArray]);

  const orderHistoryDetail = async () => {
    let dateObject = {}
    if (dateSelected?.[0] != null && dateSelected?.[1] != null) {
      dateObject.startDate = dateSelected[0]
      dateObject.endDate = dateSelected[1]
    }
    try {
      let response = await getOrder({ pageNumber: currentPage, limit: 10, ...dateObject , categoryId : categpryArray?.length ?  categpryArray?.toString() :null  });
      setOrderHistory(response?.data?.responseData)
    }
    catch (err) {

    }
  }
  const handleChangeFilter = (e, item ,index) => {
    const {checked} = e.target
    if(checked) {
      setcategpryArray((prev)=>[...prev , item?.id])
    }
    else {
      setcategpryArray((prev)=>{
        let data = [...prev]
        return data.filter((id)=>id != item?.id)
      })
    }

  }
  return (
    <>
      <div className="h-100 prof-details-wrpr">
        <ul className="d-flex align-center cd-breadcrumb">
          <li className="breadcumb-item">
            <Link href={'/'} legacyBehavior>
              <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
              </a>
            </Link>
          </li>
          <li className="breadcumb-item current">
            <em>{getLanguages(checklanguage, "orderhistory")}</em>
          </li>
        </ul>
        {/* <div className="prof-tabs-wrpr">
          <CommonNavigation data={PROFILE_NAV_LABELS} />
          <div
            className="tab-orderhistory tab-contents"
            id="orderhistory"
          >
          </div>
        </div> */}
        {

          orderHistory == null ? <CartScreenLoader /> :
            <>
              <div className="order_header_bar">
                <p className="total_order">{orderHistory?.totalRecords == 1 ? getLanguages(checklanguage, 'totalOrdersSingular', ['noOfOrders'], [orderHistory?.totalRecords]) : getLanguages(checklanguage, 'totalOrders', ['noOfOrders'], [orderHistory?.totalRecords])}</p>
                <div
                  className={`table_form_in `}
                >

                  <div className="f-in w-100 right-icon">
                    <DateRangePickerComponent setdateSelected={setdateSelected} dateSelected={dateSelected} />
                  </div>

                </div>
              </div>
              <div className="check-list-blk d-flex">
                  {CategoryList?.map((category, i) => {
                    return (
                      <div key={i} className="form-check">
                        <input value={category?.id} onChange={(e) => handleChangeFilter(e, category ,i)} checked={categpryArray?.includes(category?.id)} name='category_type' type="checkbox" className="form-check-input" />
                        <p className='d-flex align-center checked_highligh'><span className='cat-icon'><img src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${category?.attachment?.path}`} /> &nbsp;&nbsp; </span> {category.CategoryContents?.name}</p>
                      </div>
                    )
                  })}
                </div>
                <div className={`w-100  ${orderHistory?.records?.length > 0 && 'order-history bg-white'}`}>
                {
                  orderHistory?.records?.length === 0 ?
                    <h4 style={{ margin: "auto", marginTop: "100px" }} className='h3 text-dark-grey text-center'>  {getLanguages(checklanguage, 'noResultFound')} </h4>
                    :
                    <div className='cart_wrpr'>
                      <table className="table">
                        <thead>
                          <tr>
                            <th>{getLanguages(checklanguage, 'orderId')}</th>
                            <th>{getLanguages(checklanguage, 'productName')}</th>
                            <th>{getLanguages(checklanguage, 'category')}</th>
                            <th>{getLanguages(checklanguage, 'purchasedOn')}</th>
                            <th>{getLanguages(checklanguage, 'quantity')}</th>
                            <th>{getLanguages(checklanguage, 'totalPrice')}</th>
                            <th></th>
                          </tr>
                        </thead>
                        <tbody>
                          {
                            orderHistory?.records?.map((item, i) => (
                              <OrderHistoryComponent
                                key={i}
                                order={item}
                                index={i}
                              />
                            ))
                          }
                        </tbody>
                      </table>
                    </div>
                }
              </div>
              {
                orderHistory?.totalPages > 1 &&
                <div className="paginatate">
                  <ReactPagination
                    currentPage={currentPage}
                    setcurrentPage={setcurrentPage}
                    totalItemsCount={orderHistory?.totalRecords ? orderHistory?.totalRecords : 10}
                    totalPages={orderHistory?.totalPages ? orderHistory?.totalPages : 10}
                    perPage={orderHistory?.perPage ? orderHistory?.perPage : 10}
                  />
                </div>
              }

            </>

        }

      </div>
    </>
  );
}

export async function getServerSideProps(ctx) {
  try {
    let response = await getCategoryLists();
    return {
      props: {
        CategoryList: response?.data?.responseData?.category,
      },
    }
  }
  catch (err) {
    return {
      props: {

      }
    }

  }
}
export default OrderHistory;
