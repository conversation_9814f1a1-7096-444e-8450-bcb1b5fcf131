import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { PROFILE_NAV_LABELS ,getLanguages ,checklanguage} from "../../constant";
import CommonNavigation from "../../helpers/CommonNavigation";

function OrderHistory() {

    // use hooks
    const cookies = useCookie();
    const router = useRouter();
  
    // use hook
    useEffect(()=> {
      if (cookies.get("jwtToken") == undefined || cookies.get("jwtToken") == null || cookies.get("jwtToken") == "") {
        router.push("/");
      }
    },[]);


  return (
    <>
     {/* expand */}
      <div className="over-auto right-section">
        <div className="h-100 prof-details-wrpr">
          <ul className="d-flex align-center cd-breadcrumb">
            <li className="breadcumb-item">
            <Link href={'/'} legacyBehavior>
              <a href="javascript:;" className="breadcumb-link">
              {getLanguages(checklanguage, "reception")}
              </a>
              </Link>
            </li>
            <li className="breadcumb-item current">
              <em>{getLanguages(checklanguage, "paymenthistory")}</em>
            </li>
          </ul>
          {/* <div className="prof-tabs-wrpr">
            <CommonNavigation data={PROFILE_NAV_LABELS} />
            <div className="tab-payment tab-contents" id="payment"></div>
          </div> */}
        </div>
      </div>
    </>
  );
}

export default OrderHistory;
