import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Conversation from "../../components/Feedback/Conversation";
import { validateToken } from "../../redux/action/kuby-companion";
import { checklanguage, getLanguages } from "../../constant";

const Feedback = ({ tokenDetail, queryQuestions, params }) => {
  // use redux
  const [userInfo, setUserInfo] = useState("");
  const [companionInfo, setCompanionInfo] = useState("");
  //local variables
  const [openConversation, setOpenConversation] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(true);

  let responseQuestions = queryQuestions?.responseData;

  useEffect(() => {
    setLoading(true);
    validateToken(params?.id)
      .then((response) => {
        // console.log("nsee", response?.data?.responseData);
        setUserInfo(response?.data?.responseData?.userInfo);
        setCompanionInfo(response?.data?.responseData?.companionInfo);

        setLoading(false);
        setError(false);
      })
      .catch((err) => {
        setLoading(false);
        setError(true);
      });
  }, []);

  return (
    <div className="feedback">
      <div className="container mx-auto d-flex align-center justify-center flex-column h-100 over-auto">
        {error && !loading && (
          <>
            <div className="text-center thanku-inner">
              <img src="/images/feedback_1.svg" alt="" />
              <h4 className="h4 mb-0 text-dark-grey-5 fw500 img-card-title">
                {getLanguages(checklanguage, "linkExpire")}
                <br />
                {getLanguages(checklanguage, "alreadyFeedbackGiven")}
              </h4>
            </div>
          </>
        )}

        {loading ? (
          "loading"
        ) : (
          <>
            {!loading && !error && !openConversation && (
              <div className="text-center thanku-inner">
                <img src="/images/feedback_1.svg" alt="" />
                <h4 className="h4 mb-0 text-dark-grey-5 fw500 img-card-title">
                  {getLanguages(checklanguage, "onemoment")}
                  <br />
                  {getLanguages(checklanguage, "weOfferYou")}
                </h4>
                <h5 className="fw500  text-center us-name">
                  {getLanguages(checklanguage, "dear")}{" "}
                  {`${userInfo?.firstName} ${userInfo?.lastName}`}
                </h5>

                <p className="text-grey-5 feedback-content">
                  {getLanguages(checklanguage, "conversationWith")}{" "}
                  <b className="fw600 text-grey-3">
                    {`${companionInfo?.firstName} ${companionInfo?.lastName}`}.
                  </b>{" "}
                  {getLanguages(checklanguage, "pleaseClick")}{" "}
                  <span className="text-accent">
                    {getLanguages(checklanguage, "start")}
                  </span>
                  ”! {getLanguages(checklanguage, "conversationFeedback")}
                </p>

                <div onClick={() => setOpenConversation(true)}>
                  <a href="javascript:;" className="sm btn-accent">
                    {getLanguages(checklanguage, "start")}
                  </a>
                </div>
              </div>
            )}
          </>
        )}

        {openConversation && (
          <Conversation questions={responseQuestions} token={params} />
        )}
      </div>
    </div>
  );
};

export default Feedback;

export async function getServerSideProps(ctx) {
  let headers = {
    "Content-Type": "application/json",
    accept: "application/json",
    language: ctx?.req?.cookies?.language ?? "de",
  };
  let params = ctx?.params;
  const [tokenDetailRes, queryQuestionRes] = await Promise.all([
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/feedback/${params?.id}`, {
      method: "GET",
      headers,
    })
      .then((res) => {
        return res;
      })
      .catch(() => {}),

    fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/feedback-questions?token=${params?.id}`,
      {
        method: "GET",
        headers,
      }
    )
      .then((res) => {
        return res;
      })
      .catch(() => {}),
  ]);
  const [tokenDetail, queryQuestions] = await Promise.all([
    tokenDetailRes ? tokenDetailRes.json() : null,
    queryQuestionRes ? queryQuestionRes.json() : null,
  ]);
  return { props: { tokenDetail, queryQuestions, params } };
}
