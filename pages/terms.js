import React from 'react'
import StaticPage from '../components/StaticPage'
import { getTermPolicy } from "../redux/action/staticPage"
import { TEMPORARY_REDIRECT_STATUS } from 'next/dist/shared/lib/constants'
const TermPolicy = ({ terms }) => {
    return (
        <StaticPage data={terms} />
    )
}
export async function getServerSideProps() {
    try {
        const res = await getTermPolicy("terms")
        return { props: { terms: res?.data?.responseData } }
    }
    catch (err) {
        return { props: {} }
    }
}
export default TermPolicy