import React from 'react'
import StaticPage from '../components/StaticPage'
import { getTermPolicy } from "../redux/action/staticPage"
import { TEMPORARY_REDIRECT_STATUS } from 'next/dist/shared/lib/constants'
const Support = ({ data }) => {
    return (
        <StaticPage data={data} />
    )
}
export async function getServerSideProps() {
    try {
        const res = await getTermPolicy("zoom-support")
        return { props: { data: res?.data?.responseData } }
    }
    catch (err) {
        return { props: {} }
    }
}
export default Support