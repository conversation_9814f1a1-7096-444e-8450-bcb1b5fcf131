import React from 'react'
import StaticPage from '../components/StaticPage'
import { getTermPolicy } from "../redux/action/staticPage"
const PrivacyPolicy = ({ privacy }) => {
    return (
        <StaticPage data={privacy} />
    )
}

export async function getServerSideProps() {
    try {
        const res = await getTermPolicy("privacy-policy")
        return { props: { privacy: res?.data?.responseData } }
    }
    catch (err) {
        return { props: {} }
    }
}
export default PrivacyPolicy