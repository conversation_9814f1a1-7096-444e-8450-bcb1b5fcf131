import React, { useEffect, useState } from 'react'
import queryString from 'query-string';
import { forcedLogin, SAVE_PROFILE_INFO } from "../redux/action/user/user"
import { useCookie } from "next-cookie";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import Loader from '../components/loader';
const ForcedLogin = () => {
    const cookies = useCookie();
    const router = useRouter();
    
    const { query } = router
    const [isFetching, setisFetching] = useState(false);
    const searchParams = new URLSearchParams(window.location.search);
    const key = searchParams.get('key');
    const dispatch = useDispatch();
    useEffect(() => {
        login(key);
    }, []);

    const login = async (key) => {
        try {
            setisFetching(true);
            const response = await forcedLogin({ key });
            console.log(response?.data?.responseData);
            
            let responseData = response?.data?.responseData;
            
            cookies.set("jwtToken", responseData?.token, {
                path: "/",
                maxAge: 1000000000000,
            });
            
            if (typeof window != undefined) {
                localStorage.setItem("jwtToken", responseData?.token)
            }
            
            dispatch({
                type: SAVE_PROFILE_INFO,
                payload: responseData,
            });
            setisFetching(false);
            if (query?.redirectTourl) {
                if (query?.companionId) {
                    router.push(`${query?.redirectTourl}&companionId=${parseInt(query.companionId)}&startDate=${query.startDate}&endDate=${query?.endDate}&type=${query?.type}`)
                }
                else {
                    router.push(query?.redirectTourl)
                }
            } else {
                router.push("/");
            }
        } catch (err) {

        }

    }
    return (
        <div><Loader /></div>
    )
}

export default ForcedLogin