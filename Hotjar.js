import { useEffect } from "react";
import { useRouter } from "next/router";

const hotjarId = process.env.NEXT_PUBLIC_HOTJAR_ID;
const hotjarSv = process.env.NEXT_PUBLIC_HOTJAR_SV;

const Hotjar = ({ children }) => {
  const router = useRouter();

  useEffect(() => {
    if (typeof window !== "undefined" && !window.hj) {
      (function (h, o, t, j, a, r) {
        
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: hotjarId, hjsv: hotjarSv };
        a = o.getElementsByTagName("head")[0];
        r = o.createElement("script");
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, "https://static.hotjar.com/c/hotjar-", ".js?sv=");
    }

    // Track route changes
    const handleRouteChange = () => {
        
      if (window.hj) {
        
        window.hj("stateChange", router.asPath);
      }
    };

    router.events.on("routeChangeComplete", handleRouteChange);

    return () => {
      router.events.off("routeChangeComplete", handleRouteChange);
    };
  }, [router.events]);

  return <>{children}</>;
};

export default Hotjar;
