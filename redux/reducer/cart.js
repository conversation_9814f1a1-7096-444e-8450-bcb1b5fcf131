import { USER_CART_DELETE, USER_CART } from "../action/cart";

const initialState = {
    userCart: null,
    total: 0
};

const userCart = (state = initialState, action) => {
    switch (action.type) {
        case USER_CART:
            return {
                ...state,
                userCart: action.payload,
                total: action.payload!=null ? action.payload.cartValue :0
            };
        case USER_CART_DELETE:
            return initialState;
        default:
            return state;

    }
}
export default userCart;