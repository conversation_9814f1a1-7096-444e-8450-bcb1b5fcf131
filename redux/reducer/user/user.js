import {SAVE_PROFILE_INFO ,CHECK_SIGNUP_ATATUS ,RESET_PASSWORD_ATATUS} from "../../action/user/user"
const initialState = {
    userInfo:{},
    isSignUpstatus : false,
    resetpasswordstatus : false
  };
  const user = (state = initialState, action) => {
    switch (action.type) {
      case SAVE_PROFILE_INFO:
        return {   
          ...state,
          userInfo: action.payload
        };   
        case CHECK_SIGNUP_ATATUS:
        return {   
          ...state,
          isSignUpstatus: action.payload
        };
        case RESET_PASSWORD_ATATUS:
          return {   
            ...state,
            resetpasswordstatus: action.payload
          };          
      default:
        return state;
    }
  }
  export default user;