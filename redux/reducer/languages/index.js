import { SAVE_LANGUAGES, DELETE_LANGUAGES } from "../../action/auth";
const initialState = {
  languages: [],
};
const languages = (state = initialState, action) => {
  switch (action.type) {
    case SAVE_LANGUAGES:
      return {
        ...state,
        languages: action.payload,
      };
    case DELETE_LANGUAGES:
      return {
        ...state,
        languages: [],
      };

    default:
      return state;
  }
};
export default languages;
