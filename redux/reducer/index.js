import { combineReducers } from 'redux';
import user from './user/user';
import languages from './languages/index';
import courses from './kuby-courses';
import cart from './cart'
import soulwriting from './soulwriting'
const appReducer = combineReducers({
    user,
    languages,
    courses,
    cart,
    soulwriting
});

const rootReducer = (state, action) => {
    return appReducer(state, action)
}

export default rootReducer;
