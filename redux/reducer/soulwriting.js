
import {SOUL_WRITING_FORM_VALUES  ,SOUL_WRITING_CONTENT } from "../action/soul-writing"
const initialState = {
     formstate : null,
     contents : null
  };
  const soulwriting = (state = initialState, action) => {
    switch (action.type) {
      case SOUL_WRITING_FORM_VALUES:
        return {   
          ...state,
          formstate: action.payload
        };   

        case SOUL_WRITING_CONTENT:
          return {   
            ...state,
            contents: action.payload
          };   

      default:
        return state;
    }
  }
  export default soulwriting;