import {GET_REVIEW_lIST , SINGLE_REVIEW } from "../action/kuby-courses"
const initialState = {
    reviewRatingList: null,
    singleReview : {}
   
  };
  const courses = (state = initialState, action) => {
    switch (action.type) {
      case GET_REVIEW_lIST:
        return {   
          ...state,
          reviewRatingList: action.payload
        };   
        case SINGLE_REVIEW:
        return {   
          ...state,
          singleReview: action.payload
        };;          
      default:
        return state;
    }
  }
  export default courses;