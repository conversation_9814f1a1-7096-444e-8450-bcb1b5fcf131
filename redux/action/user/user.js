import { apiService } from "../../store"
export const SAVE_PROFILE_INFO ="SAVE_PROFILE_INFO"
export const CHECK_SIGNUP_ATATUS = 'CHECK_SIGNUP_ATATUS'
export const RESET_PASSWORD_ATATUS = 'RESET_PASSWORD_ATATUS'

// export function siginIn(formValues) {
//     return apiService({
//         method: 'POST',
//         data: formValues,
//         url: `/user/login`,
//     });
// }

export function siginIn(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/login-customer`,
    });
}
export function siginUp(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/signup`,
    });
}

export function resendVerificationCode(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/resendCode`,
    });
}

export function resendSignupVerificationEmail(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/resend-verify-email`,
    });
}

export function forgotPassword(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/forget-password`,
    });
}

export function verifyEmailsignup(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/user/verify-token`,
    });
}
export function resetPassword(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/reset-password`,
    });
}
export function editUserProfile(formValues) {
    return apiService({
        method: 'PATCH',
        data: formValues,
        url: `/user/profile`,
    });
}

export function changeUserPassword(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/change-password`,
    });
}

export function getProfileData(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/users`,
    });
}

export function logout(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/user/logout`,
    });
}

export function getNotificaList(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/notification`,
    });
}

export function connectGoogle(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/calendar/authorize`,
    });
}

export function removeGoogle(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/calendar/de-authorize`,
    });
}

export function getProfileDetails(formValues) {
    return apiService({
        method: 'GET',
        data: formValues,
        url: `/user-profile`,
    });
}

export const forcedLogin = (params) => {
    return apiService({
        method: 'GET',
        params: params,
        url: `/admin/grant-access`,
    });
}