import { apiService } from "../store";

export function getProductList(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/product-list`,
    });
}

export function getUserProducts(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/course/getPurchasedTopic`,
    });
}
export function getProductDetailById(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/product-details`,
    });
}

export function addToWishLists(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/wish-list`,
    });
}

export function getWisLists(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/wish-list`,
    });
}


export function getCategoryLists(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/category-list`,
    });
}

export function getSimilarProduct(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/similar-products`,
    });
}