import { apiService } from "../store";

export function getAllCompanions(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/active-companion`,
  });
}

export function getMyCompanions(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/my-companion`,
  });
}

export function getCompanionById(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/companion-id`,
  });
}

export function getTimeSlots(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/events-slots-copy-test`,
  });
}

export function createMeeting(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/events`,
  });
}

export function getEventsCalendar(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/events-calander`,
  });
}

export function getEventsDetailById(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/events/id`,
  });
}

export function rescheduleMeeting(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/events/reschedule`,
  });
}
export function cancelMeeting(formValues) {
  return apiService({
    method: "DELETE",
    params: formValues,
    url: `/events-cancel`,
  });
}

export function meetingPayment(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/meeting-payment`,
  });
}
export function meetingPaymentEnd(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/complete-meeting-payment`,
  });
}

export function completedMeetings(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/completed-meetings`,
  });
}

export function upcomingMeetings(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/upcoming-meetings`,
  });
}
export function getOldMeetings(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/event/legacy`,
  });
}
export function getOldMeetingAudioWithId(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/attachment/meeting/download`,
  });
}

export function feedbackMessageSend(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/feedback`,
  });
}

export function validateToken(token) {
  return apiService({
    method: "GET",
    url: `/validate-token/${token}`,
  });
}

export function addToWaitingList(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/event/slot-reminder`,
  });
}

export function checkWaitingList(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/event/slot-reminder-check`,
  });
}

export function getCompanionsWithFreeSlots(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/events-all-companion-slots`,
  });
}
