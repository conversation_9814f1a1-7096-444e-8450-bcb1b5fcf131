import { apiService } from "../store";
export const USER_CART = "USER_CART"
export const USER_CART_DELETE = "USER_CART_DELETE"

export function addProductToCart(props) {
    return apiService({
        method: 'POST',
        data: props,
        url: `/cart-product`,
    });
}

export function getCartData(props) {
    return apiService({
        method: 'GET',
        params: props,
        url: `/cart-product`,
    });
}

export function removeProductFromCart(props) {
    return apiService({
        method: 'DELETE',
        params: props,
        url: `/cart-product`,
    });
}