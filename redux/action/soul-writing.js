import { apiService } from "../store";

export const SOUL_WRITING_FORM_VALUES = "SOUL_WRITING_FORM_VALUES";
export const SOUL_WRITING_CONTENT = "SOUL_WRITING_CONTENT";

export function getSoulWritingCategory(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/soul-writing/category`,
  });
}

export function addCharacters(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/soul-writing/character`,
  });
}

export function updateCharacters(formValues) {
  return apiService({
    method: "PATCH",
    data: formValues,
    url: `/soul-writing/character`,
  });
}

export function deleteCharacter(formValues) {
  return apiService({
    method: "DELETE",
    params: formValues,
    url: `/soul-writing/character`,
  });
}
export function createProject(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/soul-writing/project`,
  });
}

export function updateProject(formValues) {
  return apiService({
    method: "PATCH",
    data: formValues,
    url: `/soul-writing/project`,
  });
}

export function getProjectById(params) {
  return apiService({
    method: "GET",
    url: `/soul-writing/project/${params?.id}`,
  });
}

export function getsoulProjectVersion(formValues) {
  return apiService({
    method: "GET",
    url: `/soul-writing/project/${formValues?.projectId}`,
    params:
      formValues?.version != undefined ? { version: formValues?.version } : {},
  });
}

export function soulWritingContent(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/soul-writing/content`,
  });
}

export function getsoulWritingContent(formValues) {
  // console.log(`fffff`, formValues);
  return apiService({
    method: "GET",
    url: `/soul-writing/content/${formValues?.projectId}`,
    params:
      formValues?.version != undefined ? { version: formValues?.version } : {},
  });
}

export function getSoulWritingCharacter(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/soul-writing/character`,
  });
}

export function getSoulWritingProject(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/soul-writing/project`,
  });
}
export function getSetting() {
  return apiService({
    method: "GET",
    url: `/settings`,
  });
}

export function payMentSoulWriting(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/order-soul-writing`,
  });
}

export function addProjectList(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/soul-writing/project-list`,
  });
}

export function getAllProjectList(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/soul-writing/project-list`,
  });
}


//Comment section

export function postsoulwritingComment(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/soul-writing/comment`,
  });
}

//Get information for payment for the soulwriting while submitting the soulwriting for review
export function soulWritingWordEstimation(data) {
  return apiService({
    method: "POST",
    data: data,
    url: `/soul-writing/word-estimation`,
  });
}

export function getProtagonistQuestions(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/soul-writing/character/questions`,
  });
}

export function getOldSoulWriting(formValues) {
  return apiService({
    method: "GET",
    params: formValues,
    url: `/project/legacy`,
  });
}

export function exportSoulwritingPDF(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/generate-pdf`,
  });
}

export function isSoulwritingProductPurchased(params) {
  return apiService({
    method: "GET",
    params: params,
    url: `/user/soul-writing-status`,
  });
}

export function buySoulwriting(formValues) {
  return apiService({
    method: "POST",
    data: formValues,
    url: `/user/enable-soul-writing`,
  });
}
