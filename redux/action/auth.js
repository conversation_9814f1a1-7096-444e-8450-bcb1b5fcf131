import { apiService } from "../store";

export const SAVE_LANGUAGES = 'SAVE_LANGUAGES'
export const DELETE_LANGUAGES = 'DELETE_LANGUAGES'

export function uploadFile(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/attachment/upload`,
    });
}

export function removeFile(formValues) {
    return apiService({
        method: 'DELETE',
        params: formValues,
        url: `/attachment/delete`,
    });
}

export function getAllLanguages(formValues) {
    return apiService({
        method: 'GET',
       params: formValues,
        url: `/language`,
    });
}