import { apiService } from "../store";
export const GET_REVIEW_lIST ="GET_REVIEW_lIST"
export const SINGLE_REVIEW = 'CHECK_SIGNUP_ATATUS'
export function getTopic(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/course/getTopic`,
    });
}
export function getChapters(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/course/getChapters`,
    });
}

export function getFaqs(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/faq`,
    });
}

export function getSalesPage(formValues) {
    return apiService({
        method: 'GET',
        params: formValues,
        url: `/salesPage/getById`,
    });
}

export function updateTimeStamps(formValues) {
    return apiService({
        method: 'PATCH',
        data: formValues,
        url: `/course/timestamp`,
    });
}

export function postReviews(formValues) {
    return apiService({
        method: 'POST',
        data: formValues,
        url: `/course/giveReview`,
    });
}

export function getReviews(params) {
    return apiService({
        method: 'GET',
        params: params,
        url: `/course/getReviews`,
    });
}
export function getSingleReviews(params) {
    return apiService({
        method: 'GET',
        params: params,
        url: `/course/getReviewForUser`,
    });
}

export function payMentSelfPractice(params){
    return apiService({
        method: 'POST',
        data: params,
        url: `/confirmPayment`,
    });
}