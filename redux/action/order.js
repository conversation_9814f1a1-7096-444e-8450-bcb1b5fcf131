import { apiService } from "../store";

export function postOrder(props) {
    return apiService({
        method: 'POST',
        data: props,
        url: `/order`,
    });
}

export function getOrder(props) {
    return apiService({
        method: 'GET',
        params: props,
        url: `/order`,
    });
}

export function getOrderById(props) {
    return apiService({
        method: 'GET',
        params: props,
        url: `/order-id`,
    });
}