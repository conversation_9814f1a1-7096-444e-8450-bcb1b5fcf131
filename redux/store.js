import { createStore } from "redux";
import storage from "redux-persist/lib/storage";
import { useCookie } from "next-cookie";
import { persistStore, persistReducer } from "redux-persist";
import rootReducer from "./reducer";
import axios from "axios";
import { clearSession } from "../constant";
import toast from "react-hot-toast";
import moment from "moment";
import momentTimezone from "moment-timezone";
const persistConfig = {
  key: "root",
  storage,
};
const persistedReducer = persistReducer(persistConfig, rootReducer);
export const store = createStore(persistedReducer);
export const persistor = persistStore(store);
//  ===================================================

/* Create an Instance of Axios */
export const apiService = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
});

export const externalApiService = axios.create({
  baseURL: "",
});
// ------------------------ Axios interceptor request -------------------------
apiService.interceptors.request.use(
  function (config) {
    let UTCOffset = moment().utcOffset();
    let timezone = momentTimezone.tz.guess();
    let timezoneAbbr = moment.tz().zoneAbbr();
    // console.log(moment.tz(timezone).zoneAbbr(), 'timezone')
    let accessToken = null;
    const cookies = useCookie();
    // Fetching JWT Token from the local storage.
    if (cookies.get("jwtToken") != undefined)
      accessToken = cookies.get("jwtToken");
    config.headers = {
      Accept: "application/json",
      "Content-Type": config.url.includes("/attachment/upload")
        ? "multipart/form-data"
        : "application/json",
      utcoffset: UTCOffset,
      timezone: timezone + "|" + timezoneAbbr,
      language:
        typeof window !== "undefined" && localStorage.getItem("language")
          ? localStorage.getItem("language")
          : "de",
    };

    // Send JWT token in every request if access token value found.
    if (accessToken) config.headers["Authorization"] = `Bearer ${accessToken}`;

    config.params = {
      ...config.params, // Here, we send params in URL as a Query Params.
    };
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

// ------------------------ Axios interceptor response -------------------------
apiService.interceptors.response.use(
  function (response) {
    return response;
  },
  function (error) {
    if (
      typeof error?.response !== "undefined" &&
      typeof error?.response?.status !== "undefined"
    ) {
      switch (error.response.status) {
        // Unauthorized User - HTTP STATUS 401 - Error Handling
        case 401:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          setTimeout(() => {
            clearSession();
          }, 3000);
          break;
        case 440:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          clearSession();
          break;
        case 404:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          break;
        case 409:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          break;
        case 400:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          break;
        // Server Side Issues Handling
        case 500:
          error?.response?.data?.message
            ? toast.error(error?.response?.data?.message)
            : toast.error(
                "Sorry, something went wrong. Please try again later."
              );
          break;
        default:
          toast.error("Sorry, something went wrong. Please try again later.");
      }
    }

    return Promise.reject(error);
  }
);
// ============================ THE END ==================================
