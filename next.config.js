// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   reactStrictMode: false,
//   env: {
//     NEXT_PUBLIC_API_BASE_URL: "http://**************:3090",
//   }
// }

// module.exports = nextConfig


/** @type {import('next').NextConfig} */

try {
  const withTM = require("next-transpile-modules")([
    "@fullcalendar/common",
    "@babel/preset-react",
    "@fullcalendar/common",
    "@fullcalendar/daygrid",
    "@fullcalendar/interaction",
    "@fullcalendar/react",
    "@fullcalendar/timegrid"
  ]);

  module.exports = withTM({
    generateEtags: false,
    reactStrictMode: false,
    swcMinify: true,
    eslint: {
      ignoreDuringBuilds: true,
    },
    env: {
      //  NEXT_PUBLIC_API_BASE_URL: "https://api.kuby.info",
      //  NEXT_PUBLIC_API_BASE_URL: "http://**************:3090",
      DIGI_STORE_URL: 'https://www.digistore24.com'
    },
    // future: {
    //   webpack5: true,
    // },
    webpack: (config) => {
      config.resolve.fallback = { fs: false };
      return config;
    },
    images: {
      domains: ['api.kuby.info', 'apidev.kuby.info'], // Add your hostname here
    },
  });
} catch (e) { }
