{"email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "title": "Title", "passwordPlaceholder": "Enter password", "emailRequired": "Email is required", "validEmail": "Please enter valid email address", "psRequired": "Password is required", "passwordAtleast8": "Password must be more than 8 characters", "passwordAtleast20": "Password cannot be more than 20 characters", "fsNamePlaceholder": "Enter first name", "fsNamelabel": "First name", "fsNameValid": "First name is required", "lsNamePlaceholder": "Enter last name", "lsNamelabel": "Last name", "lsNameValid": "Last name is required", "NewasswordPlaceholder": "Create password", "NewPasswordLabel": "Create New Password", "OldPasswordLabel": "Enter Old Password", "OldPasswordPlaceholder": "Old Password", "OldPasswordRequired": "Old Password is required", "NewpsRequired": "New Password is required", "NewpasswordAtleast8": "New Password must be more than 8 characters", "NewpasswordAtleast20": "New Password cannot be more than 20 characters", "confirmPasswordlabel": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter your password", "ConfirmpsRequired": "Confirmed Password is required", "ConfirmpasswordAtleast8": "Confirmed Password must be more than 8 characters", "ConfirmpasswordAtleast20": "Confirm Password cannot be more than 20 characters", "successfullPasswordReset": "Successfully password reset", "successfullPasswordResetDesc": "Your password has been successfully reset. You can now use your new password to login to your account", "continuetoLogin": "Continue to login", "checkMailtitle": "Check your email inbox", "checkMaildesc": "we have sent a reset password link to", "open_mail": "Open your emails", "didnt_recieve_mail": "Didn’t recieve the email? Did you check the spam folder?", "clickresend": "Click to resend", "reception": "Home", "selfpractice": "Learning area", "calendar": "Calendar", "kubycompanion": "KUBYcompanions", "soulwriting": "12 Stages Soulwriting", "kubyshop": "KUBYshop", "profiledetail": "Profile", "history": "History", "meetinghistory": "Meeting history", "upcomingMeeting": "Upcoming meetings", "soulwritinghistory": "Soulwriting history", "orderhistory": "Order history", "payments": "Payments", "addresses": "Addresses", "changepassword": "Change password", "logout": "Logout", "manageaddress": "Manage Addresses", "paymenthistory": "Payment history", "profile": "Profile", "editprofile": "Edit Profile", "editdetail": "Edit details", "login": "<PERSON><PERSON>", "forgotpasswrd": "Forgot password?", "logincontinue": "Login to continue", "becomemember": "Register", "registertostart": "Register to get started", "Iagree": "I agree with the", "termcondition": "terms and conditions", "forgotpassrd": "Forgot Password", "forgotpassrdinstruction": "No worries. Enter the email address associated with your account, and we'll send the instructions to reset your password to that email.", "resetpassrd": "Reset password", "backlogin": "Back to login", "crNewpassword": "Create new password", "crNewpassrdInst": "In order to protect your account, your new password must be different from previous used passwords.", "newregistration": "New Registration", "professionals": "Companions", "students": "Students", "mycompanion": "My contacts", "seminars": "Seminars", "reasonRequired": "Reason is required", "reason": "Enter Reason", "overview": "Overview", "downloadfiles": "Download files", "comments&&rating": "Reviews & ratings", "review": "Add your review", "reviewRequired": "review is required", "ratingRequired": "rating is required", "ratingExp": " Rate your experience", "addComment": "Add comment", "commentRequired": "comment is required", "additionalInfo": "Additional information", "description": "Description", "soulwritingreason": "Reason", "soulWritingReasonPlaceholder": "What is the reason for your soul writing?", "projectName": "Enter project name", "fullNamelabel": "Full name", "fullNamereq": "Full name is required", "fullNameLabelPlaceholder": "Enter Full name", "kinship": "Degree of kinship", "selectdegreekinship": "Select degree of kinships", "degreekinshipreq": "Degree of kinship/acquaintance at the time of the scene ", "ageSelect": "Please enter age at the time of the scene ", "agemaxValid": "Age must not be greater than 125", "ageReq": "Age is required", "acronymlbl": "Acronym", "acronymplcaholder": "Enter acronym", "acronymreq": "Acronym is required", "projectlabel": "Project Name", "projectnamereq": "Project Name is required", "feedbacktitle": "One moment please! You are not finished yet.", "feedbacksubtitle": "Now we offer you to intensify the process of your consciousness for free.", "passedAwayDate": "Passed away in", "customKinship": "Enter kinnship", "customKinnshipMessage": "Enter custom kinnship", "placeHolderEnterPassedAwayYear": "Enter year", "scheduleAnAppointment": "Schedule an appointment", "confirmDateAndTime": "Confirm date & time", "confirmYourAppointment": "Confirm your appointment", "continue": "Continue", "cancel": "Cancel", "selectADateAndTime": "Select a Date & time", "myCompanion": "My Companion", "price": "Price", "dateOfOrigin": "Date of origin", "scheduleOneOnOneCall": "Schedule a 1:1 contact", "scheduleOneOnOneSoulwritingCall": "Soulwriting with feedback", "experience": "Contact hours", "introductoryVideo": "Introductory Video", "vita": "Vita", "male": "Male", "female": "Female", "selectLanguage": "Select language", "professional": "Companion", "student": "Student", "new_soul_writing_accept_terms_text": "I have read the terms and conditions and the data protection declaration and accept them.", "protagonistNameLabel": "Name, as you call the person", "degreeKinnshipPlaceholder": "daddy, grandma, friend, ...", "lifestatus": "Life status at the time of the scene", "agelabel": "Age at the time of the scene", "addProtogonistTitle": "Create new protagonist", "editProtogonistTitle": "Edit protagonist", "changeCompanion": "Change Companion", "projectNameRequiredMessage": "Project name is required", "addProtagonist": "Add protagonist", "next": "Next", "back": "Back", "moreInfoAboutProtagonist": "More information about protagonist", "save": "Save", "connection": "Relationship", "distance": "Distance", "sympathetic": "Sympathetic", "loginPageText": "Please enter your login details to continue.", "selectTitle": "Select title", "required": "Required", "acceptTermsAndConditions": "Please accept terms and conditions", "viewAll": "View all", "footerAllRightsReserved": "All rights reserved", "termsAndConditions": "Terms and conditions", "privacyPolicy": "Privacy policy", "selectCompanion": "Select companion", "years": "Years", "reset": "Reset", "gender": "Gender", "kubyStudy": "KUBYstudy", "totalAppoinments": "Total 1:1 meetings", "total": "Total", "orders": "orders", "totalProducts": "Total products", "totalAmount": "Total amount", "product": "Product", "category": "Category", "totalQuantity": "total quantity", "orderId": "Order ID", "productName": "Product name", "purchasedOn": "Purchased on", "quantity": "Quantity", "status": "Status", "project": "Project", "stage": "Stage", "version": "Version", "characters": "Characters", "recording": "Recording", "companion": "Companion", "paymentAmount": "Payment amount", "duration": "Duration", "date": "Date", "moreInformation": "More information", "orderDate": "Order date", "profilePictureRequired": "Profile picture is required", "removeGoogle": "Remove Google Calendar", "connectGoogle": "Connect Google Calendar", "joinedDate": "Joined date", "changePasswordInstructions": "Enter your old password and new password below to change your password.", "noResultFound": "No results found", "calendarPageInstructions": "In the calender you can see your upcoming and past appointments by day, week and month.", "createNewSoulWriting": "Create new soulwriting", "mySoulWriting": "My Soulwritings", "reasonLabel": "Reason", "reasonLengthMessage": "Reason cannot be greater than 500 characters", "createProject": "Create project", "projectSuccessMessage": "Project created successfully", "addNewProtagonist": "Add new Protagonist", "protagonist": "Protagonist", "occasion": "Occasion", "trigger": "<PERSON><PERSON>", "painPicture": "Pain Picture", "bridge": "Bridge", "rewrite": "Rewrite", "affirmation": "Affirmation", "projection": "Projection", "realization": "Realization", "viewCart": "View cart", "addToCart": "Add to cart", "buyNow": "Buy now", "addProject": "Add project", "add": "Add", "change": "Change", "addRow": "Add row", "clickToAddBridge": "Select bridge", "download": "Download", "scriptView": "Script view", "selectVersion": "Select version", "bookMeeting": "Book a 1:1 meeting", "sendToCompanion": "Send to companion", "notBooked": "Not booked", "booked": "Booked", "rescheduled": "Rescheduled", "cancelledWithRefund": "Cancelled with refund", "cancelledWithoutRefund": "Cancelled without refund", "notJoined": "Not joined", "completedWithCompletedPayment": "Finished with payment done", "completedWithPendingPayment": "Finished with payment pending", "mins": "mins", "Mins": "<PERSON>s", "time": "Time", "audioRecording": "Audio recording", "videoRecording": "Video recording", "videoPassword": "Video password", "saveAndLeave": "Save & leave", "cookie_consent_message": "This website uses cookies to enhance the user experience.", "cookie_consent_accept_button": "Accept", "protagonistMoreInfoHowOld": "2. How old is {nameOfProtagonist} in the scene?", "yearOfTheSceneQuestion": "Year of the scene", "isProtagonistStillAlive": "Is {nameOfProtagonist} still alive?", "yes": "Yes", "no": "No", "yearOfDeath": "Year of death", "howFarProtagonistLive": "2. How far does {nameOfProtagonist} live from you?", "liveInApartmentHouse": "In the apartment/house", "withInWalkingDistance": "Within walking distance", "distanceInMilesOrTime": "Miles away? travel time...", "howOftenYouMeet": "3. How often do you see {nameOfProtagonist}?", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "annually": "Annual", "onlyAt": "Only at", "soFarOnlyThisMuchTimes": "So far only ____ times", "sympatheticValueQuestion": "1. How sympathetic are you to {nameOfProtagonist}?", "veryTrustWorthy": "Very trustworthy", "neutral": "Neutral", "unsympathetic": "Unsympathetic", "repulsive": "Repulsive", "fearOccupied": "Fear occupied", "lessonList": "Lesson list", "mr": "Mr", "miss": "Miss", "cookiesConsentTitle": "Cookies for you", "cookieConsentDescription": "In order to guarantee you the best user experience here on Kuby, we use cookies. Some are necessary to ensure functionality, others we need for statistics and others help us to only show you the advertising that interests you. You can find out more in the data protection declaration.", "allowAll": "Allow all", "moreSettings": "More settings", "cookiesSettings": "Cookies settings", "sessionCookiesAccepted": "Session cookies accepted", "intercomCookiesAccepted": "Intercom cookies accepted", "allow": "Allow", "thankyouForSchedulingMeeting": "Thank you for scheduling meeting", "meetingScheduled": "Your meeting has been successfully scheduled", "backToCalendar": "Back to Calender", "thankyouForPayment": "Thank you! The full payment for your one to one meeting has been successfully processed. The payment will be charged through Digistore24.", "paymentPending": "Payment pending", "clickToPay": "Click to pay", "recordingAvailableMessage": "Meeting recordings will be available after the payment is done.", "inProgress": "In progress", "oneOnOneMeetingShortText": "Your one-on-one meeting will take place via the communication platform Zoom.us.", "oneOnOneMeetingText": "Enter the reason for your meeting, accept the terms and conditions and click on \"Schedule an appointment\". You will then be redirected to a payment form where you have to pay a small reservation fee. Once you have done this, your appointment has been successfully booked and you will be notified by email.", "wishlist": "Wishlist", "remove": "Remove", "chosenDateTime": "Your Chosen date & Time", "recordingConsent": "I consent to my conversation being recorded.", "termsAndConditionsConsent": "I have read the <a href = '{terms}' target='_blank'>terms and conditions</a> and the <a href = '{privacy-policy}' target='_blank'>data protection declaration</a>  and accept them.", "vimeoCookiesAccepted": "Vimeo cookies accepted", "trainings": "Trainings", "day": "Day", "week": "Week", "month": "Month", "today": "Today", "timeSlots": "Time slots", "reScheduleAnAppointment": "Reschedule appointment", "reschedule": "Reschedule / cancel", "totalMeetings": "Total {noOfMeetings} meetings", "totalOrders": "Total {noOfOrders} orders", "totalMeetingsSingular": "Total {noOfMeetings} meeting", "totalOrdersSingular": "Total {noOfOrders} order", "linkExpire": "This link has been expired", "alreadyFeedbackGiven": "Feedback is already provided.", "onemoment": "One moment please! You are not finished yet.", "weOfferYou": "Now we offer you to intensify the process of your consciousness for free.", "conversationFeedback": "With this we help you to reflect on this conversation for free, something which is essential for the process of your consciousness and thus for your healing.", "feedbackTitle": "Conversation Feedback", "feedbackInfo": "Please click at least on 4 aspects from the following catalogue describing the benefits of having your soul writing® commented. To do so please read all the aspects! You may change your clicks, eliminate them or click more than 4 aspects.", "sendMessage": "Send Message", "GiveFeedback": "<PERSON>", "thankYouMessageFeedback": "Thank you for your feedback", "thankYouMessageFeedbackDesc": "Your feedback has been submitted successfully.", "countryCodePlaceholder": "+49", "countryCodelabel": "Country code", "countryCodeValid": "Country code is required", "phoneNumberPlaceholder": "Enter phone number", "phoneNumberlabel": "Phone number", "phoneNumberValid": "Phone number is required", "somethingWentWrong": "Something went wrong", "errorMessageLimitReached": "You have reached the limit of 3 contacts with a student", "bookProffesional": "Book a professional", "hello": "Hello", "cartTotal": "Cart total", "subTotal": "Subtotal", "proceedToCheckout": "Proceed to checkout", "emptyCartMessage": "Your cart is empty", "emptyCartMessageDetail": "Looks like you have not added anything to you cart. Before proceed to checkoutyou must add some products to your shopping cart", "returnToKubyShop": "Return to KUBYshop", "orderThankyouPageMessage": "Thank you for your order!", "orderThankyouPageDetailMessage": "Your order has been placed. Please check your email for further instructions. <br/>The payment will be processed by Digistore24.com.", "selfPracticeThankyouPageDetailMessage": "The payment will be processed by Digistore24.com.", "backToReception": "Back to Reception", "readMore": "Read more", "movies": "Movies", "bookAProfessional": "Book a professional", "entryDate": "KUBYcompanion since", "whyKubytorium": "Vita", "kubyProducts": "Learning area", "myProducts": "My products", "phone": "Phone", "phoneNumberInstructions": "Provide your phone number, so your companion can directly reach you if necessary.", "contentForRecordingConsent": "We record your conversation with the companion for the following reasons:\n\n1. To make it available to you free of charge so that you can download it to your own device.\n\n2. To be able to prove what was said in a critical case.\n\n3. To further improve our call quality. We guarantee that your call will be kept securely encrypted and\n\n that no unauthorized person will ever have access to it. If we can help other people with your case,\n\n  then only in a completely anonymized legal form.", "soulWritingVideoDescription": "Here as a text,if you prefer to read:", "nativeLanguage": "What is your native language?", "enterNativeLanguage": "Enter your native language", "currentAge": "What is your current age?", "enterAge": "Enter your age", "genderQues": "What is your gender?", "information": "Your Information", "createYourSoulwriting": "Create Your Soulwriting", "leave": "Leave", "protagonists": "Protagonists", "line": "Line", "person": "Person", "scene": "Scene", "startSoulwriting": "Start soulwriting", "startYourSoulwriting": "Start your soulwriting", "termsAndConditionsRead": "I have read the terms and conditions and the data protection declaration and accept them.", "requestCantEmpty": "The request cannot be empty. Please fill out first!", "SoulWritingModalDesc": "The text for your consciousness process, the so-called Soul Writing® is automatically calculated according to the number of your keystrokes/characters (without spaces and punctuation marks). € 1 per 100 characters", "buySuggestion": "People who bought this, also bought...", "footerCopyright": "by Kuby Ltd.", "age": "Age", "nativeLang": "Native Language", "conxn": "Connection", "feeling": "Feeling", "i": "I", "addProtogonistDesc": "This is the list of all the people and beings that appear in the course of your soulwriting. We refer to them as protagonists, like the actors in a script or theater play.", "contact": "Contact", "lifeStatus": "Life status", "continueWriting": "Continue writing", "soulContentIntro": "Soul writing®, is automatically calculate your keystrokes/characters(without spaces and punctuation marks). 100 characters cost €1.00 including statutory VAT. If you improve your text after having it commented upon and send it again to your companion, you will only be charged for the newly added characters (100 Z = 1€).", "soulIntro": "Soul writing is an online story writing editor in a specific form (like a story-script for movies), where clients write their stories according to the 12 steps of KUBYmethod line by line.", "dateOfProjectEnteringInLife": "When did the project enter your life?", "selectACompanion": "Select a companion", "pastFromPainPic": "From painpicture and rewrite", "presentFromProjectOccasion": "From Project, Occasion and Trigger", "cancelMeeting": "Are You Sure Want to cancel this Meeting", "wantToChangeCompanion": "Are you sure want to change companion?", "payAgain": "This would mean that you have to pay again for all the soulwriting as a new companion will be reviewing all the writing again.", "chooseCompanion": "<PERSON>ose <PERSON>", "audio": "Audio", "notAvailable": "Not available", "video": "Video", "wantToSendCompanion": "Are you sure want to send to companion?", "modalContent": "This is your modal content", "browserNotSupport": "Your browser does not support the video tag.", "sendSoulwriting": "Send soulwriting", "priceCalculation": "<p class=priceCalculation>You can send your soulwriting directly to a certified KUBY companion who will comment on it and provide you with valuable feedback. Only when you submit it will you be charged 2 cents per character for KUBYcompanions or 0.1 cents per character for students, including VAT, excluding spaces and punctuation marks.</p><p class=priceCalculation>Below, you can see the characters you have entered and the price calculated from them .</p> <p class=priceCalculation>If you revise your soulwriting after receiving feedback from the companion and send it to them again, only the newly added characters will be charged .</p><p class=priceCalculation>Once the companion has commented on your soulwriting, you will be notified by email . </p><p class=priceCalculation>We wish you an inspiring soul contact. <p>", "contiueClick": "To continue click on", "sendText": "Send to companion", "or": "or", "toContinueEditing": " to continue editing your soul writing.", "numOfCharacters": "Number of characters entered:", "lowToHigh": "Low to High", "highToLow": "High to Low", "min": "Min", "noRecordAvailable": "No record available", "rateYourExperience": "Rate your experience :", "stars": "Stars", "yourComment": "Your comment :", "profilePicture": "profile picture", "kubyShop": "KUBYshop", "whoAmI": "Who am, was and will I?", "noData": "No Data", "passAway": "Pass Away", "alive": "Alive", "viewYourExistingStory": "View your existing story", "collapse": "Collapse", "myComment": "My Comment:", "submit": "Submit", "sureYouWantToLeave": "Sure, you want to leave ?", "notifications": "Notifications", "orderDetail": "Order Detail", "invoice": "Invoice", "subtotal": "Subtotal", "vat": "VAT", "calender": "<PERSON><PERSON>", "hideInfo": "Hide info", "moreInfo": "More info", "undo": "Undo", "dateOfProjectEnteringReq": "Date of project entering is required", "watchVideoToCont": "Watch the video to continue", "nextStage": "Next Stage", "targetDate": "Target Date", "present": "Present", "past": "Past", "areYouReady": "Are you ready? Great! ", "soulWritingIntroText": "Soulwriting is the essence of the KUBYmethod - it is the practice. Through soul writing, you achieve the change that makes your life more enjoyable. <br>You follow the 12 stages that give your soulwriting the necessary structure. The more precisely you follow the instructions at each stage, the greater your success. <br>It is beneficial if you revise your soulwriting several times. You will become more and more aware each time. Problem solving is a question of expanding your awareness. These 'aha' effects are the real purpose of soul writing. We let you do it, because you need a success to feel how valuable soulwriting is. <br>Start by stating the reason why you want to start soulwriting in the first place. The rest will be explained to you stage by stage by <PERSON><PERSON><PERSON> via video. <br>It is helpful to listen to the videos several times because, as mentioned, the more closely you follow the instructions, the more successful you will be. <br>If you are unsure or get stuck, book a one to one meeting with a companion. Only then will additional costs be incurred.<br>He/she will help you to have the courage to bring to light what is still repressed.<br> <a href='/dashboard/kuby-companion?tabstatus=professionals'>You can book a KUBYcompanion here.</a> <br>All the best!<br>Your KUBYteam", "very_trustworthy": "Very trustworthy", "fear_occupied": "Fear occupied", "err": "This field is required", "select": "Select", "selected": "Selected", "loading": "Loading...", "ex22": "e.g. 22", "showLess": "Show less", "warningNote": "You have unsaved changes - are you sure you wish to leave this page?", "action": "Action", "soulWriting": "Soulwriting", "draft": "In progress", "submitted": "Submitted", "received": "Received", "completed": "Completed", "recordingAndDetails": "Recording & Details", "cancelAMeeting": "cancel", "soulwritingVita": "Soulwriting", "checkEmailInbox": "Check your e-mail inbox", "resetPasswordLinkSentTo": "We have sent you a link to create a new password to ", "didNotReceiveEmail": "Didn't receive the email? Did you check the spam folder?", "openEmails": " Open your emails", "one_to_one_meeting": "One-to-One Meeting", "successfullyBooked": "Successfully booked", "meetingBooked": "Your one to one meeting has been successfully booked. The payment will be charged through Digistore24.", "metingTakePlace": "Your meeting will take place on {date}  with {companion} via Zoom.", "metingEmail": "One hour before the meeting starts, you will receive an e-mail with a Zoom link that you can use to join the meeting.", "viewMyMeting": "View my meeting", "dear": "Dear", "conversationWith": "You just had a conversation with", "pleaseClick": "  Please click now on the button “", "start": "Start", "oldSoulWritingProjects": "Old Soul Writing Projects", "oldMeetings": "Old Meetings", "projectDetails": "Project Details", "actor": "Actor", "imprint": "Imprint", "successfullyPaid": "Meeting successfully paid", "downloadRecording": "Download recording", "viewOldMeetings": " View Old Meetings", "empatyRequest": "The request cannot be empty. Please fill out first!", "acceptTerm": "Please accept the term to proceed", "companionLimit": "Here you can book a one-to-one meeting and/or text accompaniment with a KUBYstudent 3 times, the meeting costs 19 cents per minute and 0.1 cents per character. If you want to continue to be accompanied, this is only possible with a trained professional companion.", "forgotpassrdImported": "New Password Required", "forgotpassrdinstructionImported": "Due to the transition to our new portal, all users are required to create a new password for security reasons. Please enter your existing email address here!", "resetpassrdImported": "Create New Password", "generalComments": "General comment from the companion", "noNewMeeting": "No current entries, click  <a href = '/user/old-meetings' target='_blank'>“View Old Meetings”</a> to see old meetings", "paymentCompleted": "Payment completed successfully!", "soulwritingDigi": "The debit will be performed by Digistore24.com", "soulwritingThankYou": "You have successfully sent your soulwriting. As soon as your companion has commented on it, you will be notified by e-mail.", "goToHome": "Go to the homepage", "thankyouSelf": "Log in with the credentials received via email to access your purchased product.", "loginNow": "Login now", "downloadAudio": "Download audio", "downloading": "Downloading...", "downloaded": "Downloaded", "cancelled": "Cancelled", "pending": "Pending", "details": "Details", "reviewed": "Reviewed", "view": "View", "viewOldSoulwriting": "View Old SoulWriting", "noNewSoulWriting": "No current soulwritings, click on  <a href = '/dashboard/soulwriting/old-soulwriting' target='_blank'>“View Old SoulWriting</a> to view your soulwritings from our previous portal.", "dateOfProjectEntering": "Date of project entering", "redline": "Red line", "noSlotsAvailable": "No appointments available", "noSlotsAvailableMessage": "<strong>{nameOfCompanion}</strong> currently has no available dates. We will be happy to inform you by e-mail as soon as new dates become available.", "waitingListButton": "Notify me at {emailOfCustomer}", "addedToWaitingListSuccessMessageTitle": "You're All Set!", "addedToWaitingListSuccessMessageDescription": "We'll send you an email as soon as a time slot becomes available for <strong>{nameOfCompanion}</strong>. Thank you for your patience!", "addedToWaitingListSuccessMessageButton": "Got it!", "emailChangeMessage": "Your email update is almost complete! Please check your inbox at <strong>{newEmail}</strong> and click the confirmation link to activate your new email address. Until then, your current email address remains unchanged.", "soulWritingTextLengthMessage": "Characters cannot be greater than 1000", "meetingLink": "Meeting link", "waitingListNonLoggedInButton": "Log in to be notified", "soulwritingPurchaseThankYou": "You have successfully purchased soulwriting. Click below button to continue your soulwriting.", "continueSoulwriting": "Continue writing", "soulwritingPurchaseThankYouNonLoggedIn": "You have successfully purchased Soulwriting. <strong>Your login details have just been sent to your email.</strong>", "continueSoulwritingNonLoggedIn": "Log in to continue", "soulwritingPurchaseModalTitle": "Start Soulwriting", "soulwritingPurchaseModalDescription": "For a one-time payment of &#8364;{price}, you'll get:", "soulwritingPurchaseModalContentPoint1Title": "Guidance from <PERSON><PERSON><PERSON>", "soulwritingPurchaseModalContentPoint1Detail": "Videos walk you through each step of the process", "soulwritingPurchaseModalContentPoint2Title": "Simple and well-structured", "soulwritingPurchaseModalContentPoint2Detail": "Write and organize your soulwriting effortlessly", "soulwritingPurchaseModalContentPoint3Title": "Guaranteed data security", "soulwritingPurchaseModalContentPoint3Detail": "Your content is private and fully protected", "soulwritingPurchaseModalCancelButton": "Maybe later", "soulwritingPurchaseModalSubmitButton": "Unlock for &#8364;{price}", "soulwriting_title": "The 12 Stages Of Soulwriting", "clock": "Clock", "bookAnAppointment": "Book an appointment", "currentAppointments": "Current meetings", "tomorrow": "Tomorrow", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "certified": "Licenced", "inTraining": "In training", "oclock": "o'clock", "bookAppointment": "Book meeting", "showAllDates": "Show all dates ({remaining_slots} more)", "loadAnotherDates": "Load another {no_of_dates} dates", "showLessDates": "Show less dates", "companionSelectionDescriptionText1": "Book a meeting now with a licenced KUBYcompanion or a companion in training. They will show you how to solve your current problem using the KUBYmethod.", "companionSelectionDescriptionText2": "If you don’t find the right companion for you here, click on “All Companions” or “All Students.”", "allCompanions": "All companions", "allStudents": "All students", "companionLimitText1": "Here you can book a one-to-one meeting and/or text accompaniment with a KUBYstudent 3 times, the meeting costs 19 cents per minute and 0.1 cents per character.", "companionLimitText2": "If you want to continue to be accompanied, this is only possible with a trained professional companion.", "number": "No.", "literalSpeechDirection": "Literal speech/direction", "me": "I", "comment": "Comment", "downloadedFrom": "Downloaded from {portal}", "downloadWord": "Download as Word", "checkSignupMaildesc": "We have sent you a verification email link to complete your<br/>registration on KUBYtorium to <br/><span class='h6 fw700'>{email}</span>", "checkSignupMailSubdesc": "Open the email and verify your account with just one click!", "emailSent": "Email sent", "emailSentSuccessfully": "<PERSON>ail sent successfully", "agelabelForI": "Your age"}