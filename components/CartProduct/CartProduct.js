import React, { useCallback, useState } from 'react';
import { Number, Currency } from "react-intl-number-format";
import { toast } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import { currencyFormat } from '../../constant';
import { checklanguage, getLanguages } from '../../constant';
import { addProductToCart, USER_CART, removeProductFromCart } from "../../redux/action/cart"
const CartProduct = ({ product, setLoading }) => {
    const dispatch = useDispatch();
    const changeQuantity = useCallback(

        async (product, action) => {
            //console.log('--cart' , window.ds24cart_add(482244))
            if (action == "add") {
                setLoading(true)
                if(product?.count > 4){
                    setLoading(false)
                    toast.error(`You can't add more than  5 products`)
                    return false
                }
                try {
                    let response = await addProductToCart({
                        productId: product?.productId,
                        count: 1
                    })
                    setLoading(false)
                    dispatch({
                        type: USER_CART,
                        payload: response?.data?.responseData
                    })
                }
                catch (err) {

                }
            }
            else {
                removeOneProductFromCart(product, 1)
            }
        },
        [dispatch, product]
    );

    const removeOneProductFromCart = async (product, count) => {
        setLoading(true)
        try {
            let deleteCartResponse = await removeProductFromCart({ productId: product?.productId, count: count })
            setLoading(false)
            dispatch({
                type: USER_CART,
                payload: deleteCartResponse?.data?.responseData
            })
            //window.ds24cart_remove(product?.Product?.dgStoreId, 0);
        }
        catch (err) {

        }
    }
    return (
        <>
            <tr>
                <td>
                    <div className='d-flex align-center'>
                        <figure className='prod-img'>
                            <img className='img-fluid  cover-img' src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${product?.Product?.attachment?.path}`} />
                        </figure>
                        <div className="desc-wrpr cart_list mb-0">
                            <p className="prod-desc mb-0">{product?.Product?.content?.name}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <p className='d-flex align-center'>
                        {/* <span className='cat-icon'>
                            <img src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${product?.Product?.category?.attachment?.path}`} /> 
                        </span> */}
                        {product?.Product?.category?.content?.name}</p>
                </td>
                <td>
                    <div className="d-flex align-center price-btn-quantity">
                        <div className="d-flex align-center quantity-wrpr">
                            <button className="quantity-btn quantity-dec" onClick={() => {
                                changeQuantity(product, "subtract");
                            }} >-</button>
                            <p className="quantity-value">{product?.count}</p>
                            <button className="quantity-btn quantity-inc" onClick={() => {
                                changeQuantity(product, "add");
                            }}>+</button>
                        </div>
                    </div>
                </td>
                <td>
                    <p className='h6 fw500 text-grey-1'>
                        <Currency locale={checklanguage} currency={product?.Product?.price?.[0]?.Currency?.currencyCode}>{product?.Product?.totalValue.toFixed(2)}</Currency>
                        {/* {product?.Product?.price?.[0]?.Currency?.symbol}{product?.Product?.totalValue.toFixed(2)} */}
                        {/* {`${product?.count} * ${product?.Product?.price?.[0]?.price}  = ${product?.Product?.totalValue}`} */}
                    </p>
                    {/* {currencyFormat(product?.Product?.price?.length > 0 ? product?.Product?.price?.[0]?.price : 0, product?.Product?.price?.[0]?.Currency?.currencyCode)} */}
                </td>
                <td className='cart_cross_icon' onClick={() => removeOneProductFromCart(product, product?.count)}>
                    <span className='icon close-icon'></span>
                </td>
            </tr>
        </>
    )
}

export default CartProduct