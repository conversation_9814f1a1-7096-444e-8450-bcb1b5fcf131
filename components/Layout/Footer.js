import moment from "moment";
import { getLanguages, checklanguage } from "../../constant";
import { getSetting } from "../../redux/action/soul-writing";
import { useEffect, useState } from "react";

function Footer() {
  const [settings, setSettings] = useState([]);

  const getSettings = async () => {
    try {
      const request = await getSetting();
      setSettings(request?.data?.responseData?.records);
      console.log(`dddddd`, request?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  useEffect(() => {
    getSettings();
  }, []);

  const getUrl = (key) => {
    const language = localStorage.getItem("language");
    if (!settings || settings.length === 0) return "#";

    const setting = settings.find(
      (s) => s.key === `${key}_${language.toUpperCase()}`
    );
    console.log("keee", key, setting);
    return setting ? setting.value : "#";
  };

  return (
    <>
      <div className="d-flex align-center justify-between ftr-row">
        <div className="ftr-left">
          <ul className="d-flex align-center ftr-links-list">
            <li className="ftr-link-item">
              <a
                href={getUrl("IMPRINT")}
                target="_blank"
                rel="noreferrer"
                className="ftr-link active"
              >
                {getLanguages(checklanguage, "imprint")}
              </a>
            </li>
            <li className="ftr-link-item">
              <a
                href={getUrl("TERMS")}
                target="_blank"
                rel="noreferrer"
                className="ftr-link active"
              >
                {getLanguages(checklanguage, "termsAndConditions")}
              </a>
            </li>
            <li className="ftr-link-item">
              <a
                href={getUrl("PRIVACY")}
                target="_blank"
                rel="noreferrer"
                className="ftr-link active"
              >
                {getLanguages(checklanguage, "privacyPolicy")}
              </a>
            </li>
          </ul>
        </div>
        <div className="ftr-right">
          <ul className="d-flex align-center social-media-list">
            <li className="sm-item">
              <a
                href="https://www.facebook.com/kubymethode"
                className="sm-link"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src="/images/social_media_icons/facebook.svg"
                  className="img-fluid sm-icon"
                />
              </a>
            </li>
            <li className="sm-item">
              <a
                href="https://www.instagram.com/clemenskuby/"
                className="sm-link"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src="/images/social_media_icons/insta.svg"
                  className="img-fluid sm-icon"
                />
              </a>
            </li>
            <li className="sm-item">
              <a
                href="https://www.youtube.com/@clemenskuby3139"
                className="sm-link"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src="/images/social_media_icons/youtube.svg"
                  className="img-fluid sm-icon"
                />
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div className="d-flex align-center justify-center ftr-bottom-row">
        <p className="text-dark-grey text-center copywrite-text">
          {`${"Copyright ©"} ${moment().format("Y")}`}{" "}
          {getLanguages(checklanguage, "footerCopyright")}
        </p>
      </div>
    </>
  );
}
export default Footer;
