import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import React from "react";
import { useCookie } from "next-cookie";
import { isMobile } from "react-device-detect";
import NavLink from "../../helpers/ActiveLink";
import {
  completedMeetings,
  getOldMeetings,
  upcomingMeetings,
} from "../../redux/action/kuby-companion";
import {
  getLanguages,
  checklanguage,
  SIDBAR_ROUTES,
  SIDE_BAR_ARR,
  showHideNavigationLink,
} from "../../constant";

const soulWriting = process.env.NEXT_PUBLIC_SOUL_WRITING;
const calendar = process.env.NEXT_PUBLIC_CALENDAR;
const LeftSideBar = ({ showNavigation }) => {
  const cookies = useCookie();
  const [toggleNavigation, setToggleNavigation] = useState(false);
  const [showMeetingHistoryLink, setShowMeetingHistoryLink] = useState(false);
  const [showUpcomingMeetingLink, setShowUpcomingMeetingLink] = useState(false);
  let router = useRouter();
  let { query } = router;
  const toggleSiderbar = () => {
    setToggleNavigation(!toggleNavigation);
    if (!toggleNavigation) {
      document.getElementById("right-section-wrapper").classList.add("expand");
    } else {
      document
        .getElementById("right-section-wrapper")
        .classList.remove("expand");
    }
  };

  useEffect(() => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      CompleledMeetings();
      UpcomingMeetings();
      fetchOldMeetings();
    }
  }, []);

  useEffect(() => {
    if (!SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)) {
      setToggleNavigation(false);
      document
        .getElementById("right-section-wrapper")
        .classList.remove("expand");
    } else {
      setToggleNavigation(true);
      document.getElementById("right-section-wrapper").classList.add("expand");
    }
  }, [router.pathname]);

  const CompleledMeetings = async () => {
    try {
      let response = await completedMeetings({
        pageNumber: 1,
        limit: 1,
      });
      if (response?.data?.responseData?.records?.length) {
        setShowMeetingHistoryLink(true);
      }
    } catch (err) {}
  };
  const fetchOldMeetings = async () => {
    try {
      const response = await getOldMeetings();
      if (response?.data?.responseData?.records.length) {
        setShowMeetingHistoryLink(true);
      }
    } catch (error) {
      console.error("Error fetching old meetings:", error);
    }
  };
  const UpcomingMeetings = async () => {
    try {
      let response = await upcomingMeetings({
        pageNumber: 1,
        limit: 1,
      });
      if (response?.data?.responseData?.records?.length) {
        setShowUpcomingMeetingLink(true);
      }
    } catch (err) {}
  };

  return (
    <div
      id="left_sidebar"
      className={`${"left-sidebar"} ${
        toggleNavigation == true && !isMobile ? "closed" : ""
      } ${!showNavigation ? "d-none" : ""}`}
    >
      <button
        className={`${"toggle-sidebar"} ${
          toggleNavigation == true ? "closed" : ""
        }`}
        onClick={() => toggleSiderbar()}
      >
        <span className="icon arrow-icon"></span>
      </button>
      <ul id="nav-list" className="d-flex fd-col nav-list">
        {showHideNavigationLink("RECEPTION") ? (
          <NavLink
            path={"/"}
            activeClassName="reception"
            name={getLanguages(checklanguage, "reception")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? getLanguages(checklanguage, "reception")
                : ""
            }
          />
        ) : (
          <></>
        )}

        {showHideNavigationLink("SELF_PRACTICE") ? (
          <NavLink
            path={
              router.pathname.includes("self-practice-details") ||
              router.pathname.includes("self-practice-sales")
                ? router.asPath
                : "/dashboard/self-practice"
            }
            activeClassName="practice"
            name={getLanguages(checklanguage, "selfpractice")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? getLanguages(checklanguage, "selfpractice")
                : ""
            }
          />
        ) : (
          <></>
        )}

        {calendar == "show" ? (
          <>
            {" "}
            {showHideNavigationLink("CALENDAR") ? (
              <NavLink
                path={"/dashboard/calender"}
                activeClassName="calender"
                name={getLanguages(checklanguage, "calendar")}
                sideBarstatus={1}
                title={
                  SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR) ? "Calendar" : ""
                }
              />
            ) : (
              <></>
            )}
          </>
        ) : (
          <></>
        )}

        {showHideNavigationLink("KUBY_COMPANIONS") ? (
          <NavLink
            path={
              !query?.tabstatus
                ? "/dashboard/kuby-companion?tabstatus=currentAppointments"
                : `/dashboard/kuby-companion?tabstatus=${query?.tabstatus}`
            }
            activeClassName="companions"
            name={getLanguages(checklanguage, "kubycompanion")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? getLanguages(checklanguage, "kubycompanion")
                : ""
            }
          />
        ) : (
          <></>
        )}

        {soulWriting == "show" ? (
          showHideNavigationLink("SOUL_WRITING") ? (
            <NavLink
              path={
                router.pathname.includes("create-soulwriting")
                  ? router.asPath
                  : "/dashboard/soulwriting"
              }
              activeClassName="soul"
              name={getLanguages(checklanguage, "soulwriting")}
              sideBarstatus={1}
              title={
                SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                  ? getLanguages(checklanguage, "soulwriting")
                  : ""
              }
            />
          ) : (
            <></>
          )
        ) : (
          <></>
        )}
        {showHideNavigationLink("KUBY_SHOP") ? (
          <NavLink
            path={
              router.pathname.includes("product-detail") ||
              router.pathname.includes("cart") ||
              (router.pathname.includes("shop") &&
                Object.keys(query)?.length > 0)
                ? router.asPath
                : "/dashboard/shop"
            }
            activeClassName="shop"
            name={getLanguages(checklanguage, "kubyshop")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? getLanguages(checklanguage, "kubyshop")
                : ""
            }
          />
        ) : (
          <></>
        )}
        {showHideNavigationLink("MEETING_HISTORY") && showMeetingHistoryLink ? (
          <NavLink
            path={"/user/meeting-history"}
            activeClassName="meeting"
            name={getLanguages(checklanguage, "meetinghistory")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? "Meeting History"
                : ""
            }
          />
        ) : (
          <></>
        )}
        {showHideNavigationLink("UPCOMING_MEETINGS") &&
        showUpcomingMeetingLink ? (
          <NavLink
            path={"/user/upcoming-meeting"}
            activeClassName="meeting"
            name={getLanguages(checklanguage, "upcomingMeeting")}
            sideBarstatus={1}
            title={
              SIDBAR_ROUTES(router.pathname, SIDE_BAR_ARR)
                ? "Upcoming Meeting"
                : ""
            }
          />
        ) : (
          <></>
        )}
      </ul>
    </div>
  );
};

export default LeftSideBar;
// the end
