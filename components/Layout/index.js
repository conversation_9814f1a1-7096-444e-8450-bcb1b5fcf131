import { useRouter } from "next/router";
import { Toaster } from "react-hot-toast";
import Header from "../Authenticated/Header";
import HeaderPdf from "../NonAuthenticated/HeaderPdf";
import Wrapper from "./Wrapper";
import LeftSideBar from "./LeftSideBar";
import LoginSignUpWrapper from "./LoginSignUpWrapper";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useCookie } from "next-cookie";
//import { useIntercom } from "react-use-intercom";
import { getLanguages, checklanguage, getLandingPageUrl } from "../../constant";
import CookieConsent, { Cookies } from "react-cookie-consent";
import CookieConsentCard from "../Common/CookieConsentCard";
import { getIpInfo } from "../../redux/action/common";
import queryString from "query-string";

function Layout({ children, Component, pageProps }) {
  // const { boot, update } = useIntercom();
  let router = useRouter();
  const searchParams = new URLSearchParams(window.location.search);
  console.log(`searchParams`, searchParams);
  let language = searchParams.get("lang");
  if(language && (language != 'en' || language != 'cz' || language != 'de')){
    language = 'de';
  }
  const cookies = useCookie();
  const [lang, setLang] = useState(language || null);
  const { userInfo } = useSelector((state) => state.user);
  const [pathname, setPathname] = useState(router.pathname);

  

  useEffect(async () => {
    //boot();

    // setTimeout(() => {
    //   update({
    //     name: userInfo?.UserProfile?.firstName +' '+userInfo?.UserProfile?.lastName,
    //     email: userInfo?.User?.email
    //   });
    // }, 200)

    if (lang) {
      let s = window.location.search;
      const parsed = queryString.parse(s);
      delete parsed.lang;
      localStorage.setItem("language", lang);
      cookies.set("language", lang, {
        path: "/",
        maxAge: 1000000000000,
      });
      let url = window.location.origin + window.location.pathname;
      if (queryString.stringify(parsed)) {
        url = url + "?" + queryString.stringify(parsed);
      }
      window.location.href = url;
    } else {
      if (!localStorage.getItem("language")) {
        await getLangInfo();
      }
    }

    if (
      typeof window != undefined &&
      !localStorage.getItem("jwtToken") &&
      userInfo &&
      Object?.keys(userInfo)?.length == 0
    ) {
      cookies.remove("jwtToken");
    }
  }, []);

  const getLangInfo = async () => {
    //setIsFetching(true)
    let lang = "de";
    try {
      //let response = await getIpInfo("************"); //Czech ip for testing
      let response = await getIpInfo();
      console.log(response?.data?.country, "helloFromIndia");
      //setFetchingIpInfo(false);
      if (response?.data?.country?.toLowerCase() == "de") {
        lang = "de";
      } else if (response?.data?.country?.toLowerCase() == "at") {
        lang = "de";
      } else if (response?.data?.country?.toLowerCase() == "ch") {
        lang = "de";
      } else if (response?.data?.country?.toLowerCase() == "cz") {
        lang = "cz";
      // } else if (response?.data?.country?.toLowerCase() == "fr") {
      //   lang = "fr";
      } else {
        lang = "de";
      }
      localStorage.setItem("language", lang);
      cookies.set("language", lang, {
        path: "/",
        maxAge: 1000000000000,
      });
      router.reload();
    } catch (err) {
      console.log(err, "eeeeeeeeeeeeeeee");
      localStorage.setItem("language", "de");
      cookies.set("language", "de", {
        path: "/",
        maxAge: 1000000000000,
      });

      router.reload();
      //setFetchingIpInfo(false);
    }
  };

  // useEffect(() => {
  //   if (userInfo?.UserProfile?.firstName) {
  //     update({
  //       name:
  //         userInfo?.UserProfile?.firstName +
  //         " " +
  //         userInfo?.UserProfile?.lastName,
  //       email: userInfo?.User?.email,
  //     });
  //   }
  // }, [userInfo]);

  


  useEffect(() => {
    if(router.pathname == "/_error"){
      router.push("/");
    }
    setPathname(router.pathname)
    if (getLandingPageUrl() != "/") {
      if (router.pathname == "/") {
        router.push(getLandingPageUrl());
      }
    }
  }, [router?.pathname])
  console.log(pathname, 'paathname')
  return (
    <>
      {
        pathname === "/forcedLogin"
          ?
          (
            <div>{children}</div>
          )
          : pathname === "/signup" || pathname === "/signin" || pathname === "/forget-password" || pathname.includes("reset-password") || pathname === "/successfull" || pathname === "/mail-sent" || pathname.includes("verify-token") || pathname === "/signup-email-sent" 

            ?
            (
              <div className="main-wraper">
                <LoginSignUpWrapper>
                  {children}
                  <CookieConsentCard />
                </LoginSignUpWrapper>
              </div>
            )
            : pathname == "/soulwriting-pdf"
              ? (
                <div>
                  <HeaderPdf />
                  {children}
                </div>
              ) : pathname == "/" || pathname.includes("dashboard") || pathname.includes("self-practice-thank-you-page") || pathname.includes("user") ? (
                <>
                  <Header />
                  <div
                    className={`d-flex main-wraper inner  ${pathname.includes("create-soulwriting") && "soulwriting"
                      } `}
                  >
                    <LeftSideBar
                      showNavigation={
                        pathname.includes("create-soulwriting") ? false : true
                      }
                    />
                    <Wrapper
                      showNavigation={
                        pathname.includes("create-soulwriting") ? false : true
                      }
                    >
                      {children}
                    </Wrapper>
                  </div>
                  <CookieConsentCard />
                </>
              ) : pathname.includes("term") || pathname.includes("privacy-policy") ? (
                <>
                  {/* <Header/> */}
                  {children}
                  <CookieConsentCard />
                </>
              ) : (
                <>
                  <Header />
                  {children}
                  {/* <Component {...pageProps}></Component> */}
                  <CookieConsentCard />
                </>
              )}

      <Toaster
        position="top-right"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{}}
        toastOptions={{
          className: "toastr-msg",
          duration: 3000,
          style: { background: "#fff", color: "#363636" },
        }}
      />

      {/* <CookieConsent
        location="bottom"
        buttonText={getLanguages(checklanguage, "cookie_consent_accept_button")}
        cookieName="cookie_consent"
        style={{ background: "#2B373B" }}
        buttonStyle={{ color: "#4e503b", fontSize: "13px" }}
        expires={150}
      >
        {getLanguages(checklanguage, "cookie_consent_message")}
        
      </CookieConsent> */}
    </>
  );
}
export default Layout;
