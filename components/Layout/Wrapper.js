import React from "react";
import Loader from "../loader";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Footer from "./Footer";
const Wrapper = ({ children, showNavigation }) => {
  let router = useRouter();
  const [loading, setLoading] = useState(false);
  const [pathname, setPathname] = useState(router?.pathname ?? "");

  useEffect(() => {
    setPathname(router?.pathname);
    const handleStart = (url, { shallow }) => {
      setLoading(true);
    };
    const handleError = () => {
      setLoading(false);
    };
    const handleStop = () => {
      setLoading(false);
    };
    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleStop);
    // router.events.on("routeChangeError", handleError);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleStop);
      // router.events.off("routeChangeError", handleError);
    };
  }, [router]);
  return (
    <div
      id="right-section-wrapper"
      className={`over-auto right-section ${
        !showNavigation ? "right-sidebar-fullwidth" : ""
      }  ${
        (router.pathname.includes("create-soulwriting") ||
          router.pathname.includes("soulwriting")) &&
        "right-section_soulwriting expand"
      } ${
        router.pathname.includes("/dashboard/self-practice-details")
          ? "right-sidebar-fullwidth"
          : ""
      }

      `}
    >
      {loading ? <Loader /> : children}
      {pathname.indexOf("/dashboard/self-practice-details") === -1 &&
      pathname.indexOf("/dashboard/soulwriting") === -1 &&
      pathname.indexOf("/dashboard/kuby-companion") === -1 &&
      pathname.indexOf("/dashboard/shop") === -1 ? (
        <Footer />
      ) : (
        <></>
      )}
    </div>
  );
};

export default Wrapper;
