import React from "react";
import Link from "next/link";
const LoginSignUpWrapper = ({ children }) => {
  return (
    <div className="d-flex align-center onboarding-row">
      <div className="w-50 h-full over-auto left-half">
        <div className="onboarding-img-wrpr h-full over-hidden">
          <img
            src="/images/onboading-img.jpg"
            alt=""
            className="img-fluid cover-img"
          />
        </div>
      </div>
      <div className="w-50 h-full over-auto d-flex align-center right-half">
        <div className="h-full fd-col d-flex align-center onboarding-form-wrpr">
          <div className="logo-wrpr">
            <Link href={"/"} legacyBehavior>
              <a href="javascript:;">
                <img src="/images/logo.png" alt="LOGO" className="img-fluid" />
              </a>
            </Link>
          </div>
          <div className="w-100 onboard-tab-wrpr">
            <section id="tab-contents-wrpr" className="w-100 tab-contents-wrpr">
              {children}
            </section>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginSignUpWrapper;
