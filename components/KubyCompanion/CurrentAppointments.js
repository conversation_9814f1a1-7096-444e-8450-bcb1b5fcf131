import Image from "next/image";
import { useEffect, useState } from "react";
import { getCompanionById, getCompanionsWithFreeSlots } from "../../redux/action/kuby-companion";
import moment from "moment-timezone";
import { checklanguage, currencyFormat, getDayDescription, getLanguages } from "../../constant";
import { Currency } from "react-intl-number-format";
import { useRouter } from "next/router";
import CompanionScheduleModalForDirectBooking from "../Common/Modals/CompanionScheduleModalForDirectBooking";
import CompanionVitaModal from "../Common/Modals/CompanionVitaModal";
import ReactVimeoModal from "../Common/Modals/ReactVimeoModal";
import { useSelector } from "react-redux";
import CurrentAppointmentsSkeleton from "../Common/SkeletonLoader/CurrentAppointmentsSkeleton";


function CompanionCurrentAppointments({
    setModalOpen
}) {
    let router = useRouter();
    const { userInfo } = useSelector((state) => state.user);
    const [fetchingRecords, setFetchingRecords] = useState(true);
    const [getVitaDetail, setgetVitaDetail] = useState({});
    const [meetingSlotData, setMeetingSlotData] = useState(null);
    const [openBookingModal, setOpenBookingModal] = useState(false);
    const [recordsPaging, setRecordsPaging] = useState(null);
    const [isUserTypeStudent, setIsUserTypeStudent] = useState(false);
    const [vitaModalOpen, setVitaModalOpen] = useState(false);
    const [vimeoSrc, setVimeoSrc] = useState(false);
    const [videoId, setVideoId] = useState(false);
    const [vimeoOpen, setvimeoOpen] = useState(false)
    const [records, setRecords] = useState(null);
    let recordsToShow = 4;
    let startDate = moment().startOf('d').toISOString();
    let endDate = moment().add(10, 'day').toISOString();
    //const [startDate, setStartDate] = useState();
    useEffect(() => {
        fetchFreeSlots(startDate, endDate);
    }, [])

    const fetchFreeSlots = async () => {
        const slots = await getCompanionsWithFreeSlots({
            startDate: startDate,
            endDate: endDate
        });
        setFetchingRecords(false);
        let data = slots?.data?.responseData;
        let updatedArray = {};
        if (data && Object.keys(data)?.length > 0) {
            for (const [index, obj] of Object.keys(data).entries()) {
                updatedArray = Object.assign(updatedArray, { [obj]: [] })
                let result = sortRecords(data[obj]);
                updatedArray[obj] = result
            }
        }

        let oo = {
            totalRecords: 0,
            showing: 4,
            recordsLeft: 0

        }
        let pagingObject = {};
        for (const [index, obj] of Object.keys(updatedArray).entries()) {
            pagingObject = Object.assign(pagingObject, {
                [obj]: {
                    totalRecords: updatedArray[obj].length,
                    showing: recordsToShow,
                    recordsLeft: updatedArray[obj].length - recordsToShow
                }
            });

        }
        setRecordsPaging(pagingObject);
        setRecords(updatedArray);



    };

    const sortRecords = (array) => {
        return array.sort((a, b) => {
            const timeA = new Date(a.s_startTime);
            const timeB = new Date(b.s_startTime);

            // Sort by ascending s_startTime
            if (timeA - timeB !== 0) {
                return timeA - timeB;
            }

            // For same s_startTime, prioritize role: "companion"
            if (a.role === "companion" && b.role !== "companion") {
                return -1;
            }
            if (b.role === "companion" && a.role !== "companion") {
                return 1;
            }
            return 0; // If both s_startTime and role are identical, keep the order as is
        });
    }

    const getVitaDetails = async (id, userType) => {
        try {
            let response = await getCompanionById({ id: id });
            setgetVitaDetail(response?.data?.responseData);
            setVitaModalOpen(true);
            //setIsModalShow(type);
            //setModalOpen(true);
            setModalOpen(true);
            if (userType == "student") {
                setIsUserTypeStudent(true);
            } else {
                setIsUserTypeStudent(false);
            }
        } catch (err) { }
    };


    return (
        <div className="as_card_list">
            <div className="sub-heading-description">
            <p className="large">{getLanguages(checklanguage, "companionSelectionDescriptionText1")}</p>
            <p className="small">{getLanguages(checklanguage, "companionSelectionDescriptionText2")}</p>
          </div>
            {
                fetchingRecords == true
                &&
                <CurrentAppointmentsSkeleton listsToRender={5} />

            }
            {
                fetchingRecords == false && records && Object.keys(records).length > 0 &&
                Object.keys(records).map((obj, index) => {
                    let dayText = getDayDescription(obj);
                    return (
                        <div key={obj}>
                            <h2 className="cr_title">{dayText}, {moment(obj).locale(checklanguage).format('D MMMM')}</h2>
                            {
                                records?.[obj] && records?.[obj].length > 0 && records?.[obj]?.map((obj1, index1) => {
                                    if (recordsPaging?.[obj]?.showing - 1 < index1) {
                                        return <></>
                                    }
                                    return (
                                        <div className={`${"hs_card active-star"} ${obj1?.role == 'student' ? 'student' : ""}`}>
                                            <div className="cr_flex flex-1" style={{ cursor: 'pointer' }} onClick={() => {
                                                getVitaDetails(obj1.companionId, obj1.role);
                                            }} >
                                                <div className="image-container" >

                                                    <Image src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${obj1.profileImage}`} alt={obj1.firstName} height={64} width={64} />
                                                    {
                                                        obj1?.role == 'companion' &&
                                                        <div className="badge">
                                                            <Image
                                                                src={"/images/badge-star.svg"}
                                                                alt="certified"
                                                                width={15}
                                                                height={15}
                                                            />
                                                        </div>
                                                    }

                                                </div>
                                                <div className="flex-1 title_price">
                                                    <div className="title flex">

                                                        <h3 className="truncate">{obj1.firstName} {obj1.lastName}</h3>

                                                        <div className={`${obj1.role == 'student' ? "student_status" : ""}`}>
                                                            <div className="st_wrap">
                                                                {
                                                                    obj1.role == 'student'
                                                                        ?
                                                                        <Image
                                                                            src={"/images/graduation-cap.svg"}
                                                                            alt="certified"
                                                                            width={15}
                                                                            height={15}
                                                                        />
                                                                        :
                                                                        <Image
                                                                            src={"/images/certified.svg"}
                                                                            alt="certified"
                                                                            width={15}
                                                                            height={15}
                                                                        />
                                                                }
                                                                <span className={`${obj1.role == 'companion' ? "is_certified" : ""}`}>{getLanguages(checklanguage, obj1.role == 'student' ? "inTraining" : "certified")}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className={`${"price price_per_min"} ${obj1.role}`}><Currency locale={checklanguage} currency={"EUR"}>{parseFloat(obj1?.meetingPrice).toFixed(2)}</Currency>/Min</div>
                                                </div>
                                                <div className="cr_time">
                                                    <div className="hours">{moment(obj1?.s_startTime).format("HH:mm")}</div>
                                                    <div className="label">{getLanguages(checklanguage, 'oclock')}</div>
                                                </div>
                                            </div>
                                            <button className="act_btn btn-accent" onClick={() => {
                                                if (userInfo?.User?.email) {
                                                    let ddObj = {
                                                        startDate: moment(obj1?.s_startTime).toISOString(),
                                                        endDate: moment(obj1?.s_endTime).toISOString(),
                                                        timings: [
                                                            {
                                                                startTime: obj1.startTime,
                                                                endTime: obj1.endTime
                                                            }
                                                        ],
                                                        participant: [
                                                            {
                                                                userId: obj1.companionId,
                                                                role: "companion"
                                                            }
                                                        ],
                                                        title: "One-to-One Meeting",
                                                        groupId: 2,
                                                        allDay: 0
                                                    }
                                                    setMeetingSlotData(ddObj);
                                                    setOpenBookingModal(true);
                                                    setModalOpen(true);
                                                } else {
                                                    let redirectToUpdatedUrl = btoa(`/dashboard/kuby-companion?tabstatus=currentAppointments`);
                                                    router.push(
                                                        `/signin?redirectToUpdatedUrl=${redirectToUpdatedUrl}`
                                                    );
                                                }

                                            }}>{getLanguages(checklanguage, 'bookAnAppointment')}</button>
                                        </div>
                                    )
                                })
                            }
                            {
                                recordsPaging?.[obj]?.recordsLeft <= 0 && recordsPaging[obj].showing > recordsToShow
                                    ?
                                    <button className="btn-load-more arrow-up" onClick={() => {
                                        let oo = JSON.parse(JSON.stringify(recordsPaging));
                                        oo = Object.assign(oo, {
                                            [obj]: {
                                                totalRecords: oo[obj].totalRecords,
                                                showing: recordsToShow,
                                                recordsLeft: oo[obj].totalRecords - recordsToShow
                                            }
                                        });
                                        setRecordsPaging(oo);
                                    }}>
                                        <Image

                                            src={"/images/arrow.svg"}
                                            alt="certified"
                                            width={20}
                                            height={20}
                                        />

                                        <span>{getLanguages(checklanguage, "showLessDates")}</span>


                                    </button>
                                    :

                                    (recordsPaging?.[obj]?.totalRecords > recordsPaging?.[obj]?.showing)


                                    &&
                                    <button className="btn-load-more" onClick={() => {
                                        let oo = JSON.parse(JSON.stringify(recordsPaging));
                                        let showing = oo[obj].showing + 10;
                                        if (showing >= oo.totalRecords) {
                                            showing = oo.totalRecords
                                        }
                                        let recordsLeft = oo[obj].recordsLeft - 10;
                                        if (recordsLeft <= 0) {
                                            recordsLeft = 0;
                                        }

                                        oo = Object.assign(oo, {
                                            [obj]: {
                                                totalRecords: oo[obj].totalRecords,
                                                showing: showing,
                                                recordsLeft: recordsLeft
                                            }
                                        });
                                        setRecordsPaging(oo);
                                    }}>
                                        <Image

                                            src={"/images/arrow.svg"}
                                            alt="certified"
                                            width={20}
                                            height={20}
                                        />
                                        {

                                            recordsPaging[obj].showing > recordsToShow
                                                ?
                                                <span>{getLanguages(checklanguage, "loadAnotherDates", ["no_of_dates"], [recordsPaging?.[obj]?.recordsLeft < 10 ? recordsPaging?.[obj]?.recordsLeft : 10])}</span>
                                                :
                                                <span>{getLanguages(checklanguage, "showAllDates", ["remaining_slots"], [recordsPaging?.[obj]?.recordsLeft])}</span>
                                        }

                                    </button>
                            }

                        </div>

                    )
                })
            }

            {
                openBookingModal
                    ?
                    <CompanionScheduleModalForDirectBooking
                        setModalOpen={setOpenBookingModal}
                        show={openBookingModal}
                        onHide={() => {
                            setOpenBookingModal(false);
                            setMeetingSlotData(null);
                            setModalOpen(false);
                        }}
                        setMeetingSlotData={setMeetingSlotData}
                        meetingSlotData={meetingSlotData}
                        CompanionId={meetingSlotData?.participant[0]?.userId}
                    />
                    :
                    <></>

            }

            {
                vitaModalOpen == true &&
                <CompanionVitaModal
                    setModalOpen={setVitaModalOpen}
                    show={vitaModalOpen}
                    onHide={() => {
                        setModalOpen(false);
                        setVitaModalOpen(false);
                    }}
                    //setIsModalShow={setIsModalShow}
                    onHideVimeo={() => setvimeoOpen(false)}
                    setvimeoOpen={setvimeoOpen}
                    getVitaDetail={getVitaDetail}
                    setgetVitaDetail={setgetVitaDetail}
                    setVimeoSrc={setVimeoSrc}
                    setVideoId={setVideoId}
                    isUserTypeStudent={isUserTypeStudent}
                    hideButtons={true}
                    
                />
            }

            {vimeoOpen == true && (
                <ReactVimeoModal
                    setModalOpen={setModalOpen}
                    vimeoOpen={vimeoOpen}
                    vimeoSrc={vimeoSrc}
                    onHideVimeo={() => {
                        if (videoId != "") {
                            setvimeoOpen(false);
                        } else {
                            setVitaModalOpen(true);
                            setvimeoOpen(0);
                        }
                    }}
                />
            )}

        </div>
    )
}
export default CompanionCurrentAppointments;
