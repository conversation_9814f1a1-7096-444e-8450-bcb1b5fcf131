import React, { useEffect, useRef, useState } from 'react'
import Player from '@vimeo/player';
import { updateTimeStamps } from "../redux/action/kuby-courses"
import _ from 'lodash'
import ReactPlayer from 'react-player';
const ImageLoader = () => {
    return <div className="loader_container"><img className='loader-wrpr' src="/images/tube-spinner.svg" /></div>
}
const ReactVideoPlayer = ({ SubVideosLesson, setPlay, Play, getChapterLists, setgetChapterLists }) => {
    const [Loading, setLoading] = useState(true);
    const [isSeekPositionSet, setIsSeekPositionSet] = useState(false);
    const playerRef = useRef(null);
    const [accumulatedTime, setAccumulatedTime] = useState(0);
    const [duration, setDuration] = useState(null);
    const onProgress = async (a, b) => {
        //setAccumulatedTime(prevTime => prevTime + a.playedSeconds);
        if (duration) {
            let interval;
            let payload = {
                amountWatched: a?.playedSeconds,
                totalLength: duration,
                videoId: SubVideosLesson?.id
            }
            //console.log(accumulatedTime + a.playedSeconds, 'ppppppp1')
            if (a.playedSeconds - accumulatedTime >= 4) {
                setAccumulatedTime(a.playedSeconds);
                await UpdateSubVideoCourseTimeStamps(payload);
                let calculatePercentage = percentCalculation(payload.amountWatched, duration);
                setgetChapterLists((prev) => {
                    let updateVideoTimeStamp = { ...getChapterLists }
                    for (var i = 0; i < updateVideoTimeStamp?.topicList?.length; i++) {
                        for (var j = 0; j < updateVideoTimeStamp?.topicList[i]?.subVideos?.length; j++) {
                            if (updateVideoTimeStamp?.topicList[i]?.subVideos[j]?.id == SubVideosLesson?.id)
                                updateVideoTimeStamp.topicList[i].subVideos[j].percentageWatched = calculatePercentage
                        }
                    }
                    return updateVideoTimeStamp
                })

            }

        }
    }

    const UpdateSubVideoCourseTimeStamps = async (playbackrate) => {

        try {
            let resonse = await updateTimeStamps(playbackrate);
        }
        catch (err) {

        }
    }

    function percentCalculation(a, b) {
        let c = (+a / +b) * 100;
        return c;
    }
    return (
        <div className="lesson-video-wrpr player-wrapper" style={{paddingTop: Loading? "0px": "56.25%"}}>
            {
                Loading && <ImageLoader />

            }
            {
                SubVideosLesson && SubVideosLesson?.videoLink
                    ?
                    <div style = {{visibility: Loading?"hidden":"visible"}}>
                    <ReactPlayer
                        ref={playerRef}
                        url={SubVideosLesson?.videoLink}
                        controls={true}
                        width='100%'
                        height='100%'
                        className='react-player'
                        onProgress={(a, b) => onProgress(a, b)}
                        onDuration={(d) => {
                            if (d) {
                                setDuration(d);
                            }
                        }}
                        onEnded={(d) => {
                        }}
                        onReady={() => {
                            setLoading(false)
                            if (playerRef.current) {
                                let amountWatched = SubVideosLesson?.amountWatched?.toFixed(0);
                                if(SubVideosLesson?.percentageWatched >= 95){
                                    amountWatched = 0;
                                }
                                if(!isSeekPositionSet && amountWatched > 0){
                                    playerRef.current.seekTo(amountWatched);
                                    setIsSeekPositionSet(true);
                                }
                                    

                            }
                        }}
                    />
                    </div>
                    :
                    <></>
            }
            {/* <iframe style = {{visibility: Loading ? 'hidden' : 'visible'}} id={`top_right_video`} src={SubVideosLesson?.videoLink} width="100%" height="auto" allowFullScreen="allowfullscreen" className="img-fluid"> </iframe> */}
        </div>

    )
}

export default ReactVideoPlayer