import React, { useState } from "react";
import { feedbackMessageSend } from "../../redux/action/kuby-companion";
import toast, { Toaster } from 'react-hot-toast';
import { useRouter } from 'next/router'
import { checklanguage, getLanguages } from "../../constant";


const Conversation = ({ questions, token }) => {
  const [checkedItems, setCheckedItems] = useState([]);
  const router = useRouter();

  let tokenId = token?.id;

  const handleFeebackSend = async (e) => {
    e.preventDefault();
    try {
      if (checkedItems?.length >= 4) {
        let response = await feedbackMessageSend({  token:tokenId ,feedback:checkedItems });

        // console.log(response, "feebackSend");
        router.push("/thank-you-page");


        // if(response.status ===200){
          

        //   router.push("/thank-you-page");
        // }

        // console.log(response, "response");
      } else {
        toast.error("Please Select min 4 questions");
      }
    } catch (err) {
      
       toast.error("Something went wrong");
       
    }
  };

  const handleCheckboxChange = (event) => {
    const itemId = parseInt(event.target.value);
    // console.log(itemId, "itemId");
    setCheckedItems((prevCheckedItems) => {
      if (prevCheckedItems.includes(itemId)) {
        return prevCheckedItems.filter((id) => id !== itemId);
      } else {
        console.log(typeof parseInt(itemId), "type");

        return [...prevCheckedItems, itemId];
      }
    });
  };


  return (
    <div className="ConversationFeedback">
      <h4 className="fw500 page-title title-feedback">{getLanguages(checklanguage, "feedbackTitle")}</h4>
      <p className="ConvrstionFedbak-info">
      {getLanguages(checklanguage, "feedbackInfo")}
      </p>

      <ul className="list-group feedback_questions">
        {questions?.map((item) => (
          <>
            {console.log(questions.includes(item.id), "hello")}
            <li className="list-group-item form-check w-100" key={item?.id}>
              <input
                className="form-check-input me-1"
                type="checkbox"
                aria-label="..."
                value={item?.id}
                checked={checkedItems.includes(item.id)}
                onChange={handleCheckboxChange}
              />
             <label className="fw500 form-check-label"> {item?.question}</label>
            </li>
          </>
        ))}
      </ul>

      <div className="actionLink">
        <a
          href="#"
          className="sm btn-accent"
          onClick={handleFeebackSend}
        >
         {getLanguages(checklanguage, "sendMessage")}
        </a>
      </div>
    </div>
  );
};

export default Conversation;
