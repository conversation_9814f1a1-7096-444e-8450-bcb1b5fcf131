import { useState } from "react";
import { useForm } from "react-hook-form";
import { checklanguage, getLanguages, TitleArray } from "../../constant";
import InputField from "../FormFields/InputField";
import ReactSelectField from "../FormFields/SelectField";

function AddAddressComponent() {
  // use hooks
  const { handleSubmit, control, setValue } = useForm();

  //   local variables
  const [isFetching, setIsFetching] = useState(false);
  return (
    <>
      <form>
        <div className="profile-form">
          <div className="w-100 form-wrpr">
            <div className="d-flex justify-between f-row">
              <ReactSelectField
                control={control}
                label="Title *"
                name="title"
                type="text"
                autoFocus={false}
                placeholder={"Select Title"}
                autoComplete="off"
                optionLabel={"label"}
                optionValue={"value"}
                formInClass={"w-50"}
                options={TitleArray?.map((title) => ({
                  label: title?.label,
                  value: title?.value,
                  id: title?.id,
                }))}
                rules={{
                  required: {
                    value: true,
                    message: "Title is required",
                  },
                }}
              />
              <InputField
                control={control}
                tabindex={1}
                autoComplete="off"
                label={getLanguages(checklanguage, "fsNamelabel")}
                name="firstName"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "fsNamePlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-50"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "fsNameValid"),
                  },
                }}
              />
            </div>
            <div className="d-flex justify-between f-row">
              <ReactSelectField
                control={control}
                label="Title *"
                name="title"
                type="text"
                autoFocus={false}
                placeholder={"Select Title"}
                autoComplete="off"
                optionLabel={"label"}
                optionValue={"value"}
                formInClass={"w-50"}
                options={TitleArray?.map((title) => ({
                  label: title?.label,
                  value: title?.value,
                  id: title?.id,
                }))}
                rules={{
                  required: {
                    value: true,
                    message: "Title is required",
                  },
                }}
              />
              <InputField
                control={control}
                tabindex={1}
                autoComplete="off"
                label={getLanguages(checklanguage, "fsNamelabel")}
                name="firstName"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "fsNamePlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-50"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "fsNameValid"),
                  },
                }}
              />
            </div>
            <div className="d-flex justify-between f-row">
              <ReactSelectField
                control={control}
                label="Title *"
                name="title"
                type="text"
                autoFocus={false}
                placeholder={"Select Title"}
                autoComplete="off"
                optionLabel={"label"}
                optionValue={"value"}
                formInClass={"w-50"}
                options={TitleArray?.map((title) => ({
                  label: title?.label,
                  value: title?.value,
                  id: title?.id,
                }))}
                rules={{
                  required: {
                    value: true,
                    message: "Title is required",
                  },
                }}
              />
              <InputField
                control={control}
                tabindex={1}
                autoComplete="off"
                label={getLanguages(checklanguage, "fsNamelabel")}
                name="firstName"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "fsNamePlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-50"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "fsNameValid"),
                  },
                }}
              />
            </div>
            <ReactSelectField
              control={control}
              label="Title *"
              name="title"
              type="text"
              autoFocus={false}
              placeholder={"Select Title"}
              autoComplete="off"
              optionLabel={"label"}
              optionValue={"value"}
              formInClass={"w-100"}
              options={TitleArray?.map((title) => ({
                label: title?.label,
                value: title?.value,
                id: title?.id,
              }))}
              rules={{
                required: {
                  value: true,
                  message: "Title is required",
                },
              }}
            />
          </div>
          <div className="form-btns-wrpr">
            <div className="d-flex align-center justify-end btn-wrpr">
              <button
                type="button"
                className="btn-secondary sm"
                onClick={() => setIsView()}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "cancel")}
                </span>
              </button>
              <button
                type="submit"
                className={`btn-accent sm ${isFetching && "btn-loader"}`}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "save")}
                </span>
              </button>
            </div>
          </div>
        </div>
      </form>
    </>
  );
}

export default AddAddressComponent;
