import { useState } from "react";
import { useForm } from "react-hook-form";
import { checklanguage, getLanguages } from "../../constant";
import { changeUserPassword } from "../../redux/action/user/user";
import InputField from "../FormFields/InputField";

function ChangePassword({ setIsView }) {
  // local variables
  const [isFetching, setisFetching] = useState(false);
  const [showOldPasswordShown, setshowOldPasswordShown] = useState(false);
  const [showPasswordShown, setshowPasswordShown] = useState(false);
  const [confirmshowPasswordShown, setconfirmshowPasswordShown] =
    useState(false);

  // use hooks
  const { handleSubmit, control, watch, getValues } = useForm();

  // to change user's password
  const onSubmit = async (formValues) => {
    if (formValues.confirmpassword) {
      delete formValues.confirmpassword;
    }
    setisFetching(true);
    try {
      await changeUserPassword(formValues);
      setisFetching(false);
      setIsView();
    } catch (err) {
      setisFetching(false);
    }
  };
  return (
    <>
      <div className="d-flex justify-between align-center profile-header">
        <h4 className="h4 title">
          {getLanguages(checklanguage, "changepassword")}
        </h4>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="profile-form">
          <div className="w-100 form-wrpr">
            <h6 className="h6 font-inter text-grey-6 ">
              {getLanguages(checklanguage, "changePasswordInstructions")}
            </h6>
            <div className="d-flex mt-10 change-pass-wrpr">
              <div className="form-in w-50">
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "OldPasswordLabel") + " *"}
                  name="oldPassword"
                  setshowPasswordShown={setshowOldPasswordShown}
                  labelClass="f-label"
                  showPasswordShown={showOldPasswordShown}
                  type={showOldPasswordShown ? "text" : "password"}
                  placeholder={getLanguages(
                    checklanguage,
                    "OldPasswordPlaceholder"
                  )}
                  hideandshow={true}
                  className="form-control"
                  inputClassName="f-in right-icon w-100"
                  autoFocus={true}
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(
                        checklanguage,
                        "OldPasswordRequired"
                      ),
                    },
                  }}
                />
              </div>
              <div className="new-pass-wrpr w-50">
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "NewPasswordLabel") + " *"}
                  name="newPassword"
                  labelClass="f-label"
                  className="form-control"
                  inputClassName="f-in right-icon w-100"
                  setshowPasswordShown={setshowPasswordShown}
                  showPasswordShown={showPasswordShown}
                  type={showPasswordShown ? "text" : "password"}
                  watch={watch}
                  placeholder={getLanguages(
                    checklanguage,
                    "NewasswordPlaceholder"
                  )}
                  hideandshow={true}
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "NewpsRequired"),
                    },
                    minLength: {
                      value: 8,
                      message: getLanguages(
                        checklanguage,
                        "NewpasswordAtleast8"
                      ),
                    },
                    maxLength: {
                      value: 20,
                      message: getLanguages(
                        checklanguage,
                        "NewpasswordAtleast20"
                      ),
                    },
                  }}
                />
                <InputField
                  control={control}
                  autoComplete="off"
                  labelClass="f-label"
                  className="form-control"
                  inputClassName="f-in right-icon w-100"
                  label={getLanguages(checklanguage, "confirmPasswordlabel")}
                  name="confirmpassword"
                  setshowPasswordShown={setconfirmshowPasswordShown}
                  showPasswordShown={confirmshowPasswordShown}
                  type={confirmshowPasswordShown ? "text" : "password"}
                  placeholder={getLanguages(
                    checklanguage,
                    "confirmPasswordPlaceholder"
                  )}
                  hideandshow={true}
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "ConfirmpsRequired"),
                    },
                    minLength: {
                      value: 8,
                      message: getLanguages(
                        checklanguage,
                        "ConfirmpasswordAtleast8"
                      ),
                    },
                    maxLength: {
                      value: 20,
                      message: getLanguages(
                        checklanguage,
                        "ConfirmpasswordAtleast20"
                      ),
                    },
                    validate: (value) =>
                      value === getValues("newPassword") ||
                      "Password and Confirm password do not match",
                  }}
                />
              </div>
            </div>
          </div>
          <div className="form-btns-wrpr">
            <div className="d-flex align-center justify-end btn-wrpr">
              <button
                type="button"
                className="btn-secondary sm"
                onClick={() => setIsView()}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "cancel")}
                </span>
              </button>
              <button
                type="submit"
                className={`btn-accent sm ${isFetching && "btn-loader"}`}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "save")}
                </span>
              </button>
            </div>
          </div>
        </div>
      </form>
    </>
  );
}
export default ChangePassword;
