import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";

import {
  checklanguage,
  GENDER_TYPES,
  getLanguages,
  TitleArray,
} from "../../constant";
import InputField from "../FormFields/InputField";

import MultiRadioBtnField from "../FormFields/RadioBtnField";
import UploadImageField from "../FormFields/UploadImageField";
import {
  editUserProfile,
  SAVE_PROFILE_INFO,
} from "../../redux/action/user/user";
import ReactSelectField from "../FormFields/SelectField";

function EditProfile({ setIsView }) {
  // local variables
  const [isFetching, setisFetching] = useState(false);

  const [emailMessage, setEmailMessage] = useState("");

  // use hooks
  const dispatch = useDispatch();

  // redux values
  const { userInfo } = useSelector((state) => state.user);
  const { languages } = useSelector((state) => state.languages);

  // use hooks
  const { handleSubmit, control, setValue } = useForm();

  // to edit user profile
  const onSubmit = async (formValues) => {
    if (formValues.attachment == null || formValues.attachment == "" || formValues.attachment == undefined) {
      delete formValues.attachment;
    }

    if (formValues.gender) {
      +formValues.gender;
    }
    setisFetching(true);
    try {
      
      if(!formValues.title){
        delete formValues.title;
      }
      if(!formValues.countryCode){
        delete formValues.countryCode;
      }

      if(!formValues.gender){
        delete formValues.gender;
      }

      if(!formValues.phoneNumber){
        delete formValues.phoneNumber;
      }

      if(!formValues.languages || !formValues.languages.length){
        delete formValues.languages;
      }
      console.log(formValues, 'formValues');
      let response = await editUserProfile(formValues);
      let userProfileData = { ...userInfo };
      userProfileData.UserProfile.title = formValues.title;
      userProfileData.UserProfile.firstName = formValues.firstName;
      userProfileData.UserProfile.lastName = formValues.lastName;
      userProfileData.User.email = formValues?.email;
      userProfileData.UserProfile.lastName = formValues.lastName;
      if (formValues.languages?.length > 0) {
        const filteredLanguages = languages.filter((_lang) =>
          formValues.languages.includes(_lang?.id)
        );
        userProfileData.UserProfile.languages = formValues.languages
          ? filteredLanguages
          : [];
      }

      userProfileData.UserProfile.gender = formValues.gender;
      userProfileData.UserProfile.attachment = formValues.attachment;
      dispatch({
        type: SAVE_PROFILE_INFO,
        payload: userProfileData,
      });
      setisFetching(false);
      if (response?.data?.isEmailChanged) {
        setEmailMessage(getLanguages(checklanguage, "emailChangeMessage", ["newEmail"], [formValues?.email], true));
      } else {
        setIsView();
      }
    } catch (err) {
      setisFetching(false);
    }
  };

  const setValuesInFields = () => {
    setValue("title", userInfo.UserProfile?.title);
    setValue("firstName", userInfo.UserProfile?.firstName);
    setValue("lastName", userInfo.UserProfile?.lastName);
    setValue("countryCode", userInfo.User?.countryCode);
    setValue("phoneNumber", userInfo.User?.phoneNumber);
    setValue("languages", userInfo.UserProfile?.languages.map(_lang => _lang?.id));
    setValue("gender", userInfo.UserProfile?.gender);
    setValue("attachment", userInfo.UserProfile?.attachment);
    setValue("email", userInfo.User.email);
  };

  useEffect(() => {
    setValuesInFields();
  }, []);
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="profile-form">
        <div className="d-flex w-100 form-wrpr">
          <div className="profile-form-left">
            <UploadImageField
              control={control}
              name="attachment"
              isUserProfilePic={false}
              extensionTypes={["jpeg", "jpg", "png"]}
              setValue={setValue}
              rules={{
                required: {
                  value: false,
                  message: getLanguages(checklanguage, "profilePictureRequired"),
                },
              }}
            />
          </div>

          <div className="profile-form-right">
            {
              emailMessage
                ?
                <p className="email_change_message">
                  {emailMessage}
                </p>
                :
                <></>
            }
            <div className="d-flex justify-between f-row">
              <ReactSelectField
                control={control}
                label={getLanguages(checklanguage, "title") + " *"}
                name="title"
                type="text"
                autoFocus={false}
                placeholder={"Select Title"}
                autoComplete="off"
                optionLabel={"label"}
                optionValue={"value"}
                formInClass={"name-title"}
                options={TitleArray[checklanguage]?.map((title) => ({
                  label: title?.label,
                  value: title?.value,

                }))}
                // rules={{
                //   required: {
                //     value: true,
                //     message: "Title is required",
                //   },
                // }}
              />
              <InputField
                control={control}
                tabindex={1}
                autoComplete="off"
                label={getLanguages(checklanguage, "fsNamelabel")}
                name="firstName"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "fsNamePlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-43"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "fsNameValid"),
                  },
                }}
              />
              <InputField
                control={control}
                autoComplete="off"
                label={getLanguages(checklanguage, "lsNamelabel")}
                name="lastName"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "lsNamePlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-43"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "lsNameValid"),
                  },
                }}
              />
            </div>
            <InputField
              control={control}
              autoComplete="off"
              label={getLanguages(checklanguage, "email")}
              name="email"
              labelClass="f-label"
              type="text"
              placeholder={getLanguages(checklanguage, "emailPlaceholder")}
              className="form-control"
              inputClassName="f-in w-100"
              rules={{
                required: {
                  value: true,
                  message: getLanguages(checklanguage, "emailRequired"),
                },
                pattern: {
                  value:
                    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                  message: getLanguages(checklanguage, "validEmail"),
                },
              }}
            />
            <div className="d-flex justify-between f-row">

              <InputField
                control={control}
                tabindex={1}
                autoComplete="off"
                label={getLanguages(checklanguage, "countryCodelabel")}
                name="countryCode"
                // defaultValue="+"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "countryCodePlaceholder")}
                className="form-control"
                inputClassName="f-in"
                formInClass="countryCode"
                // rules={{
                //   required: {
                //     value: true,
                //     message: getLanguages(checklanguage, "countryCodeValid"),
                //   },
                // }}
              />
              <InputField
                control={control}
                autoComplete="off"
                label={getLanguages(checklanguage, "phoneNumberlabel")}
                name="phoneNumber"
                labelClass="f-label"
                type="text"
                placeholder={getLanguages(checklanguage, "phoneNumberPlaceholder")}
                className="form-control"
                inputClassName="f-in w-100"
                formInClass="w-100"
                // rules={{
                //   required: {
                //     value: true,
                //     message: getLanguages(checklanguage, "phoneNumberValid"),
                //   },
                // }}
              />
            </div>
            <ReactSelectField
              control={control}
              label="Language *"
              name="languages"
              type="text"
              placeholder={"Select Language"}
              autoComplete="off"
              optionLabel={"label"}
              optionValue={"value"}
              formInClass={'w-100'}
              multi={true}
              options={languages?.map((title) => ({
                label: title?.name,
                value: title?.id,
                id: title?.id,
              }))}
              // rules={{
              //   required: {
              //     value: true,
              //     message: "Language is required",
              //   },
              // }}
            />

            <MultiRadioBtnField
              name="gender"
              control={control}
              formclass={'w-100'}
              label={getLanguages(checklanguage, 'gender') + "*"}
              optionValue={"id"}
              // rules={{
              //   required: {
              //     value: true,
              //     message: getLanguages(checklanguage, 'required'),
              //   },
              // }}
              options={GENDER_TYPES[checklanguage]}
            />
          </div>

        </div>
        <div className="form-btns-wrpr">
          <div className="d-flex align-center justify-end btn-wrpr">
            <button
              type="button"
              className="btn-secondary sm"
              onClick={() => setIsView()}
            >
              <span className="btn-text-wrpr">{getLanguages(checklanguage, 'cancel')}</span>
            </button>
            <button
              type="submit"
              className={`btn-accent sm ${isFetching && "btn-loader"}`}
            >
              <span className="btn-text-wrpr">{getLanguages(checklanguage, 'save')}</span>
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}

export default EditProfile;
