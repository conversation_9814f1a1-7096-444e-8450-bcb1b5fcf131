import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import BookMeetingModal from "../Common/Modals/BookMeetingModal";
import NotificationComponent from "../notificationComponent";
import {
  logout,
  SAVE_PROFILE_INFO,
  getNotificaList,
} from "../../redux/action/user/user";
import { getCartData, USER_CART } from "../../redux/action/cart";
import { getIpInfo } from "../../redux/action/common";
import Loader from "../loader";
import {
  checklanguage,
  getLanguages,
  showHideNavigationLink,
  getLandingPageUrl,
} from "../../constant";
import {
  GET_REVIEW_lIST,
  SINGLE_REVIEW,
} from "../../redux/action/kuby-courses";

// import { SAVE_PROFILE_INFO } from "../../redux/action/user/user";

const profileMenu = process.env.NEXT_PUBLIC_PROFILE_MENU;

const Header = () => {
  // use hooks
  const cookies = useCookie();
  const { userCart } = useSelector((state) => state.cart);
  const router = useRouter();
  const dispatch = useDispatch();
  const [fetchingInfo, setFetchingIpInfo] = useState(true);

  // local varibales
  const [isModalShow, setIsModalShow] = useState(0);
  const [isNotificationModal, setIsNotificationModal] = useState(false);
  const [notificationList, setnotificationList] = useState(null);
  const [hamburgerMenu, sethamburgerMenu] = useState(false);

  const [language, setlanguage] = useState(
    typeof window !== "undefined" && localStorage.getItem("language")
      ? localStorage.getItem("language")
      : "de"
  );

  //   redux variables
  const { userInfo } = useSelector((state) => state.user);

  //   menu click
  const onDropdownItemClick = (type) => {
    switch (type) {
      case "profile":
        router.push("/user/profile");
        break;
      case "meeting":
        router.push("/user/meeting-history");
        break;
      case "upcomingMeeting":
        router.push("/user/upcoming-meeting");
        break;
      case "soulWriting":
        router.push("/user/soulwriting-history");
        break;
      case "order":
        router.push("/user/order-history");
        break;
      case "payments":
        router.push("/user/payments");
        break;
      // case "addresses":
      //   router.push("/user/addresses");
      //   break;
      case "change-password":
        router.push("/user/profile?tab=change-password");
        break;
      case "logout":
        logoutUser();
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      getAllUserCartData();
    }
  }, [cookies.get("jwtToken")]);

  const getAllUserCartData = async () => {
    try {
      let response = await getCartData();
      dispatch({
        type: USER_CART,
        payload: response?.data?.responseData,
      });
    } catch (err) {}
  };
  const logoutUser = async () => {
    try {
      let response = await logout({ deviceType: 3 });
      dispatch({ type: SAVE_PROFILE_INFO, payload: {} });
      dispatch({
        type: USER_CART,
        payload: null,
      });
      dispatch({
        type: GET_REVIEW_lIST,
        payload: null,
      });
      dispatch({
        type: SINGLE_REVIEW,
        payload: {},
      });
      cookies.remove("jwtToken");
      window.location.href = "/";
      let lang = localStorage.getItem("language");
      let cookies_consent = localStorage.getItem("cookies_consent");
      localStorage.clear();
      localStorage.setItem("language", lang);
      cookies.set("language", lang, {
        path: "/",
        maxAge: 1000000000000,
      });
      localStorage.setItem("cookies_consent", cookies_consent);
    } catch (err) {}
  };
  const onProfileClick = (e) => {
    const element = document.getElementById("profile-dropdown");
    if (
      e?.target?.classList?.contains?.("profile-btn") ||
      e?.target?.parentElement?.classList?.contains?.("profile-btn") ||
      e?.target?.parentElement?.classList?.contains?.("user-name")
    ) {
      element?.classList?.toggle?.("active");
    } else {
      element?.classList?.remove?.("active");
    }
  };
  useEffect(() => {
    if (!localStorage.getItem("language")) {
      //getLangInfo();
    } else {
      setFetchingIpInfo(false);
    }

    document.onclick = function (e) {
      onProfileClick(e);
    };
  }, []);
  // const getLangInfo = async () => {
  //   //setIsFetching(true)
  //   let lang = "en";
  //   try {
  //     //let response = await getIpInfo("************"); //Czech ip for testing
  //     let response = await getIpInfo();
  //     console.log(`resssx`, response);
  //     setFetchingIpInfo(false);
  //     if (response?.data?.country?.toLowerCase() == "de") {
  //       lang = "de";
  //     } else if (response?.data?.country?.toLowerCase() == "at") {
  //       lang = "de";
  //     } else if (response?.data?.country?.toLowerCase() == "ch") {
  //       lang = "de";
  //     } else if (response?.data?.country?.toLowerCase() == "cz") {
  //       lang = "cz";
  //     } else {
  //       lang = "en";
  //     }
  //     localStorage.setItem("language", lang);
  //     console.log(response.data.country, "rrrrr");
  //     router.reload();
  //   } catch (err) {
  //     localStorage.setItem("language", "en");

  //     router.reload();
  //     setFetchingIpInfo(false);
  //   }
  // };
  const Bookmetting = (e) => {
    e.preventDefault();
    // router.push("/dashboard/kuby-companion?tabstatus=professionals");
    const url = "/dashboard/kuby-companion?tabstatus=professionals";
    window.open(url, "_blank");

    // if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
    //   setIsModalShow(1)
    // }
    // else {
    //   router.push('/signin')
    // }
  };

  const getAllNotification = async () => {
    try {
      let response = await getNotificaList();
      setIsNotificationModal(true);
      setnotificationList(response?.data?.responseData?.notifications);
    } catch (err) {}
  };
  const hamMenu = () => {
    const sidebar = document.getElementById("left_sidebar");
    if (sidebar.classList.contains("closed")) {
      sidebar.classList.remove("closed");
    }
    sethamburgerMenu(!hamburgerMenu);
    if (!hamburgerMenu) {
      document
        .getElementById("left_sidebar")
        .classList.add("active", "mobileVIew");
    } else {
      document
        .getElementById("left_sidebar")
        .classList.remove("active", "mobileVIew");
    }
  };
  return (
    <>
      {fetchingInfo && <Loader />}
      <div
        className={`d-flex justify-between align-center bg-white header ${
          router.pathname.includes("create-soulwriting") &&
          "soulwriting_header_bar"
        }`}
      >
        <div className="d-flex align-center left-wrpr">
          <div
            id="mobile_main_menu"
            className={`${"ham-icon-wrpr"} ${hamburgerMenu ? "open" : ""}`}
            onClick={() => hamMenu()}
          >
            <span className="ham-bar bar1"></span>
            <span className="ham-bar bar2"></span>
            <span className="ham-bar bar3"></span>
          </div>
          <div className="logo-wrpr">
            <Link href={"/"}>
              <a>
                <img src="/images/logo.png" alt="KUBY" className="img-fluid" />
              </a>
            </Link>
          </div>
        </div>
        <div className="d-flex align-center header-btn-wrpr">
          <select
            className="language-select"
            value={language}
            onChange={(e) => {
              
              if (typeof window != undefined) {
                localStorage.setItem("language", e.target.value);
                cookies.set("language", e.target.value, {
                  path: "/",
                  maxAge: 1000000000000,
                });
                //setTimeout(() => {
                  
                  window.location.href = getLandingPageUrl(e.target.value);
                  //router.push(getLandingPageUrl);
                //}, 500);
                //router.reload();
              }
            }}
          >
            <option value="en">English</option>
            <option value="de">Deutsch</option>
            <option value="cz">čeština</option>
            {/* <option value="fr">français</option> */}
          </select>
          {showHideNavigationLink("CALENDAR") ? (
            <button
              className="sm btn-accent book-meeting-btn desktop-only"
              onClick={Bookmetting}
            >
              <span className="meeting-text">
                {getLanguages(checklanguage, "bookMeeting")}
              </span>
              <span className="meeting-icon">
                <img src="/images/add.svg" className="img-fluid" />
              </span>
            </button>
          ) : (
            <></>
          )}

          {cookies.get("jwtToken") != undefined ? (
            <> </>
          ) : (
            <div className={` d-flex  align-center header-btn-inner`}>
              <Link legacyBehavior href={"/signin"}>
                <a className="sm btn">
                  <span className="icon login-icon"></span>
                  <span className="btn-text">
                    {getLanguages(checklanguage, "login")}
                  </span>
                </a>
              </Link>
              <Link legacyBehavior href={"/signup"}>
                <a className="sm btn">
                  <span className="icon register-icon"></span>
                  <span className="btn-text">
                    {getLanguages(checklanguage, "newregistration")}
                  </span>
                </a>
              </Link>
            </div>
          )}

          {cookies.get("jwtToken") != undefined ? (
            <ul className="d-flex align-center header-btn-inner header-btn-list">
              {/* {
                showHideNavigationLink('NOTIFICATIONSS')
                  ?
                  <li className="header-btn-item notification">
                    <button
                      className="header-btn"
                      onClick={() => getAllNotification()}
                    >
                      <span className="icon noti-icon"></span>
                    </button>
                  </li>
                  :
                  <></>
              } */}
              {showHideNavigationLink("KUBY_SHOP") ? (
                <>
                  {/* <li className="header-btn-item wishlist">
                      <button  onClick={() => router.push('/dashboard/wishlist')} className="header-btn">
                        <span className="icon wishlist-icon"></span>
                      </button>
                    </li> */}
                  <li className="header-btn-item cart">
                    <button
                      onClick={() => router.push("/dashboard/shop/cart")}
                      className="header-btn"
                    >
                      <span className="icon cart-icon"></span>
                      {userCart?.records?.length > 0 && (
                        <span className="cart-count">
                          {userCart?.records?.length}{" "}
                        </span>
                      )}
                    </button>
                  </li>
                </>
              ) : (
                <></>
              )}

              <li className="header-btn-item profile-wrpr" id="profile-wrpr">
                <a href="javascript:;" className="header-btn profile-btn">
                  <img
                    src={
                      userInfo?.UserProfile?.attachment?.path
                        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${userInfo?.UserProfile?.attachment?.path}`
                        : "/images/userimg.svg"
                    }
                    alt=""
                    className="cover-img profile-img"
                  />
                  <p className="fw500 text-dark-gray user-name">
                    <span className="text-grey-5 fw500">
                      {
                        checklanguage != 'cz'
                        ?
                        <>{getLanguages(checklanguage, "hello")},</>
                        :
                        <></>
                      }
                      
                    </span>{" "}
                    {userInfo?.UserProfile?.firstName}
                  </p>
                </a>
                <div className="prof-dropdown-menu" id="profile-dropdown">
                  <div className="mobile-inner-icons">
                    <ul className="d-flex align-center justify-center header-btn-inner header-btn-list">
                      {/* <li className="header-btn-item">
                        <a
                          href="javascript:;"
                          className="header-btn"
                          onClick={() => getAllNotification()}
                        >
                          <span className="icon noti-icon"></span>
                        </a>
                      </li> */}
                      {/* <li className="header-btn-item">
                        <button onClick={() => router.push('/dashboard/wishlist')} className="header-btn">
                          <span className="icon wishlist-icon"></span>
                        </button>
                      </li> */}
                      <li className="header-btn-item">
                        <button
                          onClick={() => router.push("/dashboard/shop/cart")}
                          className="header-btn"
                        >
                          <span className="icon cart-icon"></span>
                          {userCart?.records?.length > 0 && (
                            <span className="cart-count">
                              {userCart?.records?.length}{" "}
                            </span>
                          )}
                        </button>
                      </li>
                    </ul>
                  </div>
                  <div className="prof-menu-wrpr welcome-block">
                    {/* <p className="text-grey-1 fw500 dropdown-lbl">
                      Welcome Frank
                    </p> */}

                    <a
                      href="javascript:;"
                      className="dropdown-link"
                      onClick={() => onDropdownItemClick("profile")}
                    >
                      <span className="icon profile-icon"></span>{" "}
                      {getLanguages(checklanguage, "profiledetail")}
                    </a>
                  </div>

                  <div className="prof-menu-wrpr history-block border-none p-0">
                    {/* <p className="text-grey-7 fw500 dropdown-lbl">{getLanguages(checklanguage, "history")}</p> */}
                    {/*  */}

                    {profileMenu == "show" ? (
                      <>
                        {" "}
                        {showHideNavigationLink("UPCOMING_MEETINGS") ? (
                          <a
                            href="javascript:;"
                            className="dropdown-link"
                            onClick={() =>
                              onDropdownItemClick("upcomingMeeting")
                            }
                          >
                            <span className="icon meeting-icon"></span>
                            {getLanguages(checklanguage, "upcomingMeeting")}
                          </a>
                        ) : (
                          <></>
                        )}
                        {showHideNavigationLink("MEETING_HISTORY") ? (
                          <a
                            href="javascript:;"
                            className="dropdown-link"
                            onClick={() => onDropdownItemClick("meeting")}
                          >
                            <span className="icon meeting-icon"></span>
                            {getLanguages(checklanguage, "meetinghistory")}
                          </a>
                        ) : (
                          <></>
                        )}
                        {showHideNavigationLink("SOUL_WRITING") ? (
                          <a
                            href="javascript:;"
                            className="dropdown-link"
                            onClick={() => onDropdownItemClick("soulWriting")}
                          >
                            <span className="icon soulwriting-icon"></span>
                            {getLanguages(checklanguage, "soulwritinghistory")}
                          </a>
                        ) : (
                          <></>
                        )}
                      </>
                    ) : (
                      <></>
                    )}
                    {/* {showHideNavigationLink("KUBY_SHOP") ? (
                      <a
                        href="javascript:;"
                        className="dropdown-link"
                        onClick={() => onDropdownItemClick("order")}
                      >
                        <span className="icon order-icon"></span>
                        {getLanguages(checklanguage, "orderhistory")}
                      </a>
                    ) : (
                      <></>
                    )} */}

                    {/* <a
                      href="javascript:;"
                      className="dropdown-link"
                      onClick={() => onDropdownItemClick("payments")}
                    >
                      <span className="icon payment-icon"></span>{getLanguages(checklanguage, "payments")}
                    </a> */}

                    {/* <a
                      href="javascript:;"
                      className="dropdown-link"
                      onClick={() => onDropdownItemClick("addresses")}
                    >
                      <span className="icon address-icon"></span>{getLanguages(checklanguage, "addresses")}
                    </a> */}
                  </div>

                  <div className="prof-menu-wrpr logout-block">
                    <a
                      href="javascript:;"
                      className="dropdown-link"
                      onClick={() => onDropdownItemClick("change-password")}
                    >
                      <span className="icon change-pass-icon"></span>
                      {getLanguages(checklanguage, "changepassword")}
                    </a>
                    <a
                      href="javascript:;"
                      className="dropdown-link"
                      onClick={() => onDropdownItemClick("logout")}
                    >
                      <span className="icon logout-icon"></span>
                      {getLanguages(checklanguage, "logout")}
                    </a>
                  </div>
                </div>
              </li>
            </ul>
          ) : null}
        </div>
      </div>

      {isModalShow ? (
        <BookMeetingModal
          show={isModalShow == 1}
          onHide={() => {
            setIsModalShow(0);
          }}
        />
      ) : null}

      {isNotificationModal && (
        <NotificationComponent
          show={isNotificationModal}
          notificationList={notificationList}
          onHide={() => {
            setIsNotificationModal(false);
          }}
        />
      )}
    </>
  );
};

export default Header;
