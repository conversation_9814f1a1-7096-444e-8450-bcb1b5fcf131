import moment from 'moment';
import React, { useEffect } from 'react'
import { currencyFormat } from '../constant';
import { checklanguage, getLanguages } from '../constant';
import AudioPlayerComponent from '../components/Common/AudioPlayerComponent'

const DigitalProductViewModal = ({ show, onHide, details, index }) => {
    const { custom, orderDetails } = details
    useEffect(() => {
        document.addEventListener('click', hideModal)
        return () => document.removeEventListener('click', hideModal)
    }, []);

    const hideModal = (event) => {
        const modal = document.querySelector('#modalOverlay');
        if (event.target == modal) {
            onHide()
        }
    }
    return (
        <div className={`modal video-modal  ${show ? 'show' : ''}`}>
            <div className="overlay-div" id="modalOverlay"></div>
            <div className="modal-content order_content">
                
                <div className="modal-body order-details" title = {details?.UserProducts[index]?.ProductLinks?.[0]?.url}>
                    
                    {
                        details?.UserProducts[index]?.ProductLinks?.[0]?.type == "video"
                        ?
                        <iframe id={`top_right_video`} src={details?.UserProducts[index]?.ProductLinks?.[0]?.url} width="100%" height="auto" allowFullScreen="allowfullscreen" className="img-fluid"> </iframe>
                        :
                        details?.UserProducts[index]?.ProductLinks?.[0]?.type == "audio"
                        ?
                        <AudioPlayerComponent path = {details?.UserProducts[index]?.ProductLinks?.[0]?.url} />
                        :
                        <></>
                    }
                {/* <iframe id={`top_right_video`} src={SubVideosLesson?.videoLink} width="100%" height="auto" allowFullScreen="allowfullscreen" className="img-fluid"> </iframe> */}
                    
                </div>
            </div>
        </div>
    )
}

export default DigitalProductViewModal