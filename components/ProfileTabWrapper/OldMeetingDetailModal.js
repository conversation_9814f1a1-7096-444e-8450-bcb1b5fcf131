import React, { useEffect, useState } from "react";
import moment from "moment";
import { Currency } from "react-intl-number-format";
import { getLanguages, checklanguage } from "../../constant";
import { getOldMeetingAudioWithId } from "../../redux/action/kuby-companion";
console.log(process.env.NEXT_PUBLIC_API_BASE_URL, "hhh");

function OldMeetingDetailModal({
  formatStatus,
  show,
  onHide,
  setShowModal,
  meeting,
}) {
  const [downloadText, setDownloadText] = useState(
    getLanguages(checklanguage, "downloadAudio")
  );

  useEffect(() => {
    console.log("meeting", meeting);
  }, [meeting]);

  const hideModal = () => {
    setShowModal(0);
    onHide();
  };

  const openMeetingAudio = async (id) => {
    setDownloadText(getLanguages(checklanguage, "downloading"));
    const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/attachment/meeting/download?id=${id}`;
    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        },
      });
      if (!response.ok) {
        throw new Error("Network response was not ok " + response.statusText);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${id}.m4a`;
      document.body.appendChild(link);
      link.click();
      link.remove();
      setDownloadText(getLanguages(checklanguage, "downloaded"));
    } catch (error) {
      console.error("Error fetching the audio file:", error);
      setDownloadText("Error downloading file");
    }
  };

  return (
    <>
      <div
        className={`modal more-info-modal ${show ? "show" : ""}`}
        id="profileModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <h5>{getLanguages(checklanguage, "moreInformation")}</h5>
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={hideModal}
              >
                <span className="icon close-icon"></span>
              </a>
            </div>
            <div className="modal-body">
              <div className="more-info-wrpr">
                {meeting?.meetingDetails?.status === "payment_pending" && (
                  <div className="d-flex align-start info-row">
                    <div className="info-left" style={{ marginTop: "5px" }}>
                      {getLanguages(checklanguage, "paymentPending")}
                    </div>
                    <div className="info-right">
                      {`${meeting?.meetingDetails?.currency} ${meeting?.meetingDetails?.invoices?.[1]?.amount}`}{" "}
                      <a
                        style={{ background: "green", color: "#fff" }}
                        className="btn btn-sm primary-btn ml-10"
                        href={
                          meeting?.meetingDetails?.invoices?.[1]?.paymentLink
                        }
                      >
                        {getLanguages(checklanguage, "clickToPay")}
                      </a>
                    </div>
                  </div>
                )}
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "status")}
                  </div>
                  <div className="info-right">
                    {meeting?.meetingDetails?.status}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "date")}
                  </div>
                  <div className="info-right">
                    {localStorage.getItem("language") === "en"
                      ? moment(
                          meeting?.meetingDetails?.createTimeStamp?.$date
                        ).format("D MMM, Y")
                      : moment(
                          meeting?.meetingDetails?.createTimeStamp?.$date
                        ).format("D. MMM Y")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "time")}
                  </div>
                  <div className="info-right">
                    {localStorage.getItem("language") === "en"
                      ? moment(
                          meeting?.meetingDetails?.createTimeStamp?.$date
                        ).format("hh:mm A")
                      : moment(
                          meeting?.meetingDetails?.createTimeStamp?.$date
                        ).format("HH:mm")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "duration")}
                  </div>
                  <div className="info-right">
                    {Math.round(meeting?.meetingDetails?.zoomMeeting?.duration)}{" "}
                    {getLanguages(checklanguage, "mins")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "totalAmount")}
                  </div>
                  <div className="info-right">
                    <Currency locale={checklanguage} currency={"EUR"}>
                      {meeting?.meetingDetails?.totalAmount}
                    </Currency>
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "companion")}
                  </div>
                  <div className="info-right">
                    {meeting?.companion?.UserProfile?.firstName}{" "}
                    {meeting?.companion?.UserProfile?.lastName}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "reasonLabel")}
                  </div>
                  <div className="info-right">
                    {meeting?.meetingDetails?.subject}
                  </div>
                </div>
                {meeting?.meetingDetails?.status === "completed" ? (
                  <>
                    <div className="d-flex align-start info-row">
                      <div className="info-left">
                        {getLanguages(checklanguage, "audioRecording")}
                      </div>
                      <div className="info-right">
                        {meeting?.meetingDetails?.zoomMeeting
                          ?.recordingReceived ? (
                          <a
                            style={{ color: "#499557", cursor: "pointer" }}
                            onClick={() =>
                              openMeetingAudio(meeting?.meetingDetails?._id)
                            }
                          >
                            {downloadText}
                          </a>
                        ) : (
                          <>{getLanguages(checklanguage, "notAvailable")}</>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="d-flex align-start info-row">
                    <div
                      className="info-left"
                      style={{ maxWidth: "100%", flex: "none" }}
                    >
                      {getLanguages(checklanguage, "recordingAvailableMessage")}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default OldMeetingDetailModal;
