import React, { useEffect, useState } from "react";
import moment from "moment";
import { useRouter } from "next/router";
import { CartScreenLoader } from "../FieldLoader";
import MettingListTable from "./MettingListTable";
import { DateRangePickerComponent } from "../Common/DateRangePickerComponent";
import { getLanguages, checklanguage } from "../../constant";
import CalenderMeetingInfoModal from "../Common/CalenderMeetingInfoModal";
import { getOldMeetings } from "../../redux/action/kuby-companion";

const MeetingList = ({
  formatStatus,
  Meetinglist,
  setdateSelected,
  dateSelected,
  meetingType,
  minDDate,
  maxDate,
  setShowModal,
  setRecordObject,
  listType,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showEventInfo, setshowEventInfo] = useState(null);
  const [meetingPopupSuccess, setmeetingPopupSuccess] = useState(false);
  const [successMeetingType, setsuccessMeetingType] = useState(1);
  const [showOldMeetings, setShowOldMeetings] = useState(false);
  const router = useRouter();

  const fetchOldMeetings = async () => {
    try {
      const response = await getOldMeetings();
      if (response?.data?.responseData?.records.length > 0) {
        setShowOldMeetings(true);
      }
    } catch (error) {
      console.error("Error fetching old meetings:", error);
    }
  };
  useEffect(() => {
    fetchOldMeetings();
  }, []);
  return (
    <>
      {Meetinglist === null ? (
        <CartScreenLoader />
      ) : (
        <>
          <div className="order_header_bar">
            <p className="total_order">
              {Meetinglist?.totalRecords == 1
                ? getLanguages(
                    checklanguage,
                    "totalMeetingsSingular",
                    ["noOfMeetings"],
                    [Meetinglist?.totalRecords]
                  )
                : getLanguages(
                    checklanguage,
                    "totalMeetings",
                    ["noOfMeetings"],
                    [Meetinglist?.totalRecords]
                  )}
            </p>
            <div className="table_form_in">
              <div className="f-in w-100 right-icon">
                <DateRangePickerComponent
                  maxDate={maxDate}
                  minDDate={minDDate}
                  setdateSelected={setdateSelected}
                  dateSelected={dateSelected}
                />
              </div>
            </div>
            {showOldMeetings && (
              <button
                className="btn btn-primary ml-3"
                onClick={() => router.push("/user/old-meetings")}
              >
                {getLanguages(checklanguage, "viewOldMeetings")}
              </button>
            )}
          </div>
          <div
            className={`w-100 ${
              Meetinglist?.records?.length > 0 && "order-history bg-white"
            }`}
          >
            {Meetinglist?.records?.length === 0 ? (
              <h4
                style={{ margin: "auto", marginTop: "100px" }}
                className="h3 text-dark-grey text-center"
              >
                {showOldMeetings
                  ? getLanguages(
                      checklanguage,
                      "noNewMeeting",
                      null,
                      null,
                      true
                    )
                  : getLanguages(checklanguage, "noResultFound")}
              </h4>
            ) : (
              <div className="cart_wrpr">
                <table className="table">
                  <thead>
                    <tr>
                      <th className="desktop-only">
                        {getLanguages(checklanguage, "status")}
                      </th>
                      <th>{getLanguages(checklanguage, "date")}</th>
                      <th>{getLanguages(checklanguage, "companion")}</th>
                      {listType != "upcoming" && (
                        <th>{getLanguages(checklanguage, "duration")}</th>
                      )}
                      {meetingType == 1 ? <th>&nbsp;</th> : null}
                    </tr>
                  </thead>
                  <tbody>
                    {Meetinglist?.records?.map((item, i) => (
                      <MettingListTable
                        listType={listType}
                        setshowEventInfo={setshowEventInfo}
                        setIsModalOpen={setIsModalOpen}
                        listingType="upcoming_meetings"
                        formatStatus={formatStatus}
                        setShowModal={setShowModal}
                        setRecordObject={setRecordObject}
                        key={i}
                        meetingType={meetingType}
                        meeting={item}
                        index={i}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {isModalOpen && (
            <CalenderMeetingInfoModal
              show={isModalOpen}
              showEventInfo={showEventInfo}
              meetingPopupSuccess={meetingPopupSuccess}
              setmeetingPopupSuccess={setmeetingPopupSuccess}
              setsuccessMeetingType={setsuccessMeetingType}
              successMeetingType={successMeetingType}
              onHide={() => {
                setIsModalOpen(false);
              }}
              calledFrom="listing"
            />
          )}
        </>
      )}
    </>
  );
};

export default MeetingList;
