import moment from "moment";
import React from "react";
import _ from "lodash";
import ReactFlagComponent from "../Common/ReactFlagComponent";
import { currencyFormat, getLanguages, checklanguage } from "../../constant";
import { useRouter } from "next/router";

const MettingListTable = ({
  listType,
  listingType,
  formatStatus,
  meeting,
  meetingType,
  setShowModal,
  setRecordObject,
  setshowEventInfo,
  setIsModalOpen,
  index,
}) => {
  let companionName = _.find(meeting?.Participants, ["role", "companion"]);

  const router = useRouter();

  const routeHandler = (token) => {
    router.push(`/feedback/${token}`);
  };
  console.log(`listTypeee`, listType);
  return (
    <tr>
      <td className="desktop-only">{formatStatus(meeting?.eventStatus)}</td>
      <td>{moment(meeting?.startDate).format("lll")}</td>
      {/* <td>{moment(meeting?.startDate).format('dddd')}</td>
            <td>{moment(meeting?.startDate).format('HH:mm')}</td> */}
      {/* <td>{Math.round(meeting?.zoomDuration)}</td> */}
      {/* {
                meetingType == 1 ? null :
                    <>
                        <td>
                            {
                                meeting?.audioRecording
                                    ?
                                    <a style={{ color: "#D9512C" }} target="_blank" rel="noreferrer" href={meeting?.audioRecording ? process.env.NEXT_PUBLIC_API_BASE_URL + "/" + meeting?.audioRecording?.path : "#"}><u>Audio</u></a>
                                    :
                                    <>Not available</>
                            }
                        </td>
                        <td>
                            {
                                meeting?.videoLink
                                    ?
                                    <><a style={{ color: "#D9512C" }} target="_blank" rel="noreferrer" href={meeting?.videoLink}><u>Video</u></a> {meeting?.videoPassword ? <><br />[Pass&nbsp;code:&nbsp;{meeting?.videoPassword}]</> : <></>} </>
                                    :
                                    <>Not available</>
                            }


                        </td>
                    </>

            } */}
      <td>
        {companionName?.User?.firstName} {companionName?.User?.lastName}
      </td>
      {/* <td>
                {
                    companionName?.User?.languages?.length > 0 &&
                    companionName?.User?.languages?.map((_lang, index) => {
                        return (
                            <ReactFlagComponent code={_lang?.mobileCode} height="16" key={index} />
                        )
                    })
                }
            </td> */}
      {/* {
                meetingType == 1 ? null :
                    <td>{meeting?.Invoice?.isPaid === 0 ? <a style={{ color: "#D9512C" }} href={meeting?.Invoice?.paymentLink}><u>Pending</u></a> : <p style={{ color: "green" }} >Paid</p>}</td>
            }

            <td>{
                meetingType === 1 ? currencyFormat(parseInt(meeting?.Order?.data?.amount), meeting?.Order?.data?.currency)
                    :
                    currencyFormat(meeting?.totalAmount, meeting?.Invoice?.data?.currency)

            }</td> */}

      {listType != "upcoming" ? (
        <td>
          {Math.round(meeting?.zoomDuration)}{" "}
          {getLanguages(checklanguage, "mins")}
          {meeting?.feedback?.token ? (
            <span
              className="feedback_link"
              onClick={() => routeHandler(meeting?.feedback?.token)}
            >
              {"     "} {getLanguages(checklanguage, "GiveFeedback")}
            </span>
          ) : (
            <span> </span>
          )}
        </td>
      ) : (
        <></>
      )}

      {meetingType != 1 && (
        <td>
          <button
            className="btn btn-sm"
            onClick={() => {
              setRecordObject(meeting);
              setShowModal(true);
            }}
          >
            {getLanguages(checklanguage, "recordingAndDetails")}
          </button>
        </td>
      )}
      {meetingType == 1 && (
        <td>
          <button
            type="button"
            className="btn btn-sm"
            onClick={() => {
              setshowEventInfo(meeting);
              setIsModalOpen(true);
            }}
          >
            {getLanguages(checklanguage, "reschedule")}
          </button>
        </td>
      )}
    </tr>
  );
};

export default MettingListTable;
