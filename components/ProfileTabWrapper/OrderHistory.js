import moment from 'moment';
import React, { useState } from 'react'
import { checklanguage, currencyFormat, getLanguages } from "../../constant"
import { getOrderById } from "../../redux/action/order"
import OrderDetailModal from '../OrderDetailModal';
import DigitalProductViewModal from "../DigitalProductViewModal";
const OrderHistory = ({ order, index }) => {
    const { custom, data, orderDetails, UserProducts } = order;
    const [orderdetails, setorderdetails] = useState({})
    const [orderDetalPopup, setorderDetalPopup] = useState(false);
    const [digitalProductView, setDigitalProductView] = useState(false);
    const [selectedOrderData, setSelectedOrderData] = useState(null);
    // const getOrderDetail = async () => {
    //     try {

    //         let response = await getOrderById({ id: order?.id })
    //         setorderdetails(response?.data?.responseData);
    //         setorderDetalPopup(true)
    //     }
    //     catch (err) {

    //     }

    // }
    const setOrderDetail = (data) => {
        setSelectedOrderData(data);
        setorderDetalPopup(true)
    }
    return (
        <>
            <tr>
                <td>
                    <a onClick={() => setOrderDetail(order)} href="javascript:void(0)"> {data?.order_id}</a>
                </td>
                <td>
                    {orderDetails?.items[0]?.product_name}
                </td>
                <td>
                    <p className='d-flex align-center'><span className='cat-icon'><img src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${custom?.[0]?.product?.category?.attachment?.path}`} /> &nbsp;&nbsp; </span> {custom?.[0]?.product?.category?.content?.name}</p>
                </td>
                <td>
                    {moment(order?.updatedAt)?.format('lll')}
                </td>
                <td>
                    {orderDetails?.items?.[0]?.quantity}
                </td>
                <td>
                    {currencyFormat(orderDetails?.items?.[0]?.amounts?.first?.total_brutto_amount?.toFixed(2), data?.currency)}
                </td>
                <td>
                    {
                        UserProducts?.length && UserProducts[index]?.ProductLinks?.length
                        ?
                        <button className="sm btn-accent" onClick = {(e) => {
                            setSelectedOrderData(order);
                            setDigitalProductView(true)
                        }}>{getLanguages(checklanguage, 'view')}</button>
                        :
                        <></>
                    }
                </td>
            </tr>
            {
                orderDetalPopup &&
                <OrderDetailModal
                    show={orderDetalPopup}
                    details={selectedOrderData}
                    onHide={() => setorderDetalPopup(false)}
                />
            }
            {
                digitalProductView &&
                <DigitalProductViewModal
                    show={digitalProductView}
                    details={selectedOrderData}
                    onHide={() => setDigitalProductView(false)}
                    index={index}
                />
            }

        </>
    )
}

export default OrderHistory