import React from "react";
import moment from "moment";
import { getLanguages, checklanguage } from "../../constant";

const OldMeetingListTable = ({ meeting, handleShowModal }) => {
  const companionName = meeting?.companion?.UserProfile || {};

  return (
    <tr>
      <td className="desktop-only">
        {getLanguages(checklanguage, `${meeting?.meetingDetails?.status}`)}
      </td>
      <td>
        {moment(meeting?.meetingDetails?.createTimeStamp?.$date).format("lll")}
      </td>
      <td>
        {companionName?.firstName} {companionName?.lastName}
      </td>
      <td>
        {meeting?.meetingDetails?.zoomMeeting?.duration
          ? Math.round(meeting?.meetingDetails?.zoomMeeting?.duration)
          : 0}{" "}
        {getLanguages(checklanguage, "mins")}
      </td>
      <td>
        <button
          type="button"
          className="btn btn-sm"
          onClick={() => handleShowModal(meeting)}
        >
          {getLanguages(checklanguage, "recordingAndDetails")}
        </button>
      </td>
    </tr>
  );
};

export default OldMeetingListTable;
