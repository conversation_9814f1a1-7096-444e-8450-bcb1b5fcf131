import { Fragment } from "react";
import { Controller } from "react-hook-form";

// Component imports - -
import { errorType } from "../../constant";

function CheckboxField({
  control,
  defaultValue,
  rules,
  label,
  name,
  value,
  readOnly,
  int,
  onSelect,
  trueValue,
  falseValue,
  disabled,
  array,
  labelClass,
  tooltipLink,
}) {
  const handleChange = (val, onChange) => {
    if (trueValue !== undefined && falseValue !== undefined) {
      if (int !== undefined) {
        onChange(val ? parseInt(trueValue) : parseInt(falseValue));
      } else {
        onChange(val ? trueValue : falseValue);
      }
    } else {
      onChange(val);
    }

    if (onSelect) onSelect(val);
  };

  return (
    <>
      <Controller
        control={control}
        rules={rules}
        defaultValue={defaultValue}
        name={name}
        render={({ field, fieldState: { error } }) => {
          return (
            <div
              className={`f-in w-100 form-check ${
                error !== undefined ? "f-error" : ""
              }`}
            >
              <input
                {...field}
                type="checkbox"
                value={field.value}
                className="form-check-input"
                checked={field.value}
                onChange={(e) => handleChange(e.target.checked, field.onChange)}
              />
              <label className={labelClass}>{label}</label>
              {tooltipLink ? (
                <a href={tooltipLink} rel="noreferrer" target="_blank">
                  <span className="icon q-mark-icon"></span>
                </a>
              ) : (
                <></>
              )}
              {errorType?.map((type) => {
                return (
                  <Fragment key={type}>
                    {error?.type === type && error?.message !== "" ? (
                      <p className="error d-flex align-center">
                        <span className="icon info-icon"></span>
                        {error?.message}
                      </p>
                    ) : null}
                  </Fragment>
                );
              })}
            </div>
          );
        }}
      />
    </>
  );
}

CheckboxField.defaultProps = {
  defaultValue: "",
};

export default CheckboxField;
