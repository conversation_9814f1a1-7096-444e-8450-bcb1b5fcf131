import { use<PERSON><PERSON>roll<PERSON> } from "react-hook-form";
import { Rating } from 'react-simple-star-rating'

function RatingFormField({
    name,
    control,
    rules,
    defaultValue,
    onChange,
    errorClass,
    label,
}) {
    const {
        field,
        fieldState: { error },
    } = useController({ name, control, rules, defaultValue });

    //   on Change input
    const onInputChange = (_value) => {
        if (onChange) onChange(_value);
        field.onChange(_value);
    };
    return (
        <>

            <Rating
                count={5}
                onClick={onInputChange}
                size={24}
                initialValue={field?.value}
                classNames={'rating-star-wrpr'}
            />
        </>
    );
}

/**
 * @property defaults
 */
RatingFormField.defaultProps = {
    defaultValue: "",
    rules: {},
    errorClass: "error",
    onChange: (value) => value,
    optionLabel: "name",
    optionValue: "value",
};

export default RatingFormField;
