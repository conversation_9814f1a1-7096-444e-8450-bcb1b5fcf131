import { React, useEffect, useRef } from "react";
import Editor from "ckeditor5-custom-build/build/ckeditor";
import { CKEditor } from "@ckeditor/ckeditor5-react";
//  ===================================================

function CKEditorComponent({
  label,
  placeholder,
  labelClass,
  error,
  value,
  handleChangeFormData,
  index,
  ckeditorValue,
  memberstatusInfo,
  fieldData,
  hideControlsOnBlur,
  onBlurCallback,
}) {
  const editorRef = useRef("");
  return (
    <>
      <div
        ref={editorRef}
        className={`f-in  ${fieldData?.errorMessage !== "" ? "f-error" : ""}`}
      >
        <CKEditor
          config={{
            toolbar: [
              "fontBackgroundColor",
              "fontColor",
              "underline",
              "bold",
              "italic",
              "|",
              "undo",
              "redo",
              "strikethrough",
            ],
            fontBackgroundColor: {
              colors: [
                {
                  color: "#FACCCC",
                  label: "Red",
                },
                {
                  color: "#FFEBCC",
                  label: "Green",
                },
                {
                  color: "#FFFF00",
                  label: "Blue",
                },
                {
                  color: "#CCE8CC",
                  label: "Yellow",
                },
                {
                  color: "#CCE0F5",
                  label: "Orange",
                },
                {
                  color: "#EBD6FF",
                  label: "Purple",
                },
              ],
              columns: 5,
              documentColors: 10,
            },
            // extraPlugins: [uploadPlugin],
            htmlSupport: {
              allow: [
                {
                  name: /.*/,
                  attributes: true,
                  classes: true,
                  styles: true,
                },
              ],
            },
          }}
          editor={Editor}
          data={ckeditorValue}
          onReady={(editor) => {}}
          disabled={
            memberstatusInfo?.customerStatus == 1
              ? false
              : memberstatusInfo?.customerStatus == 2
              ? true
              : memberstatusInfo?.customerStatus == 3
              ? false
              : false
          }
          onChange={(event, editor) => {
            let data = editor.getData();
            handleChangeFormData(index, data);
          }}
          onBlur={(event, editor) => {
            if (onBlurCallback) {
              let data = editor.getData();
              handleChangeFormData(index, data);
              onBlurCallback(index, data);
            }
          }}
          onFocus={(event, editor) => {}}
        />
        {fieldData?.errorMessage ? (
          <p className="error d-flex align-center">
            <span className="icon info-icon"></span>
            {fieldData?.errorMessage}
          </p>
        ) : null}
      </div>
    </>
  );
}

export default CKEditorComponent;

//  ====================== THE END =============================
