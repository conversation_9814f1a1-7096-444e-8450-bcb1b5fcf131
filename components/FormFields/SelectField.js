import { use<PERSON><PERSON>roller } from "react-hook-form";
import { useEffect, useState } from "react";
import Select from "react-select";
import { errorType } from "../../constant";
function ReactSelectField({
    name,
    control,
    rules,
    defaultValue,
    label,
    multi,
    prefixClass,
    containerClass,
    optionValue,
    optionLabel,
    readOnly,
    options,
    onSelect,
    placeholder,
    normalize,
    subLabel,
    mainClass,
    autoFocus,
    isTime,
    formInClass,
    tabIndex,
    SelectMenuButton
}) {
    const [initialValue, setInitialValue] = useState(null);
    const {
        field,
        fieldState: { error },
    } = useController({ name, control, rules, defaultValue });
    // handle value on change
    const handleChange = (value) => {
        // send value to onChange function
        if (onSelect) {
            if (normalize) onSelect(normalize(value));
            else onSelect(value);
        }
        if (multi) {
            let multiData = value.map((_value) =>
                optionValue !== undefined ? _value[optionValue] : _value?.value
            );
            field.onChange(multiData);
        } else {
            let singleData =
                optionValue !== undefined ? value[optionValue] : value?.value;
            field.onChange(`${singleData}`);
            if (normalize) field.onChange(normalize(singleData));
            else field.onChange(singleData);
        }
    };
    // handling value according to selected one
    const handleValue = () => {
        if (field?.value !== "") {
            if (multi) {
                return options?.filter((c) =>
                    field?.value?.includes?.(
                        optionValue !== undefined ? c[optionValue] : c.value
                    )
                );
            } else {
                return options?.find((c) =>
                    optionValue
                        ? c[optionValue] === field?.value
                        : c.value === field?.value
                );
            }
        } else {
            return "";
        }
    };

    useEffect(() => {
        setInitialValue(defaultValue);
    }, [defaultValue])


    
    return (
        <>
            <div className={`form-in  ${formInClass ? formInClass : ''} ${mainClass ? mainClass : ''} ${error ? 'f-error' : ''}`}>
                {label && <label className={`f-label`}>{label}</label>}
                <div className="f-in w-100">
                    <Select
                        defaultValue={initialValue || null}
                        autoFocus={autoFocus ?? false}
                        isDisabled={readOnly}
                        name={name}
                        tabIndex={tabIndex}
                        value={handleValue()}
                       // components={SelectMenuButton ? { MenuList: SelectMenuButton  } : null}
                        onChange={(val) => handleChange(val)}
                        options={options}
                        placeholder={placeholder ? placeholder : label}
                        isMulti={multi}
                        // getOptionLabel={(opt) => opt[optionLabel]}
                        // getOptionValue={(opt) => opt[optionValue]}
                        className={`${"basic-single"} ${containerClass || ""}`}
                        classNamePrefix={prefixClass || "kuby-react-select"}
                    />
                </div>
                {errorType?.map((type) => {
                    if (error?.type === type && error?.message !== "")
                        return (
                            <p className="error d-flex align-center"><span className="icon info-icon"></span>{error?.message}</p>
                        );
                })}
            </div>
        </>
    );
}
export default ReactSelectField;