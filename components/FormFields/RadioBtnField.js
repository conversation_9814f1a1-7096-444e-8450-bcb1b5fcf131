import { use<PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { errorType } from "../../constant";


function MultiRadioBtnField({
  name,
  control,
  rules,
  defaultValue,
  options,
  optionValue,
  onChange,
  errorClass,
  formclass,
  label,
}) {
  const {
    field,
    fieldState: { error },
  } = useController({ name, control, rules, defaultValue });

  //   on Change input
  const onInputChange = (_value) => {
    if (onChange) onChange(_value);
    field.onChange(_value);
  };

  console.log('--',options[optionValue])
  return (
    <>
      <div className={`form-in ${formclass}  ${error !== undefined ? "f-error" : ""}`}>
        {label && <label className="f-label">{label}</label>}
        <div className="f-in w-100 radio-buttons">
          {options?.map((_option, index) => {
            return (
              <div className="form-group" key={index}>
                <input
                  id={name+index}
                 className={`form-control  ${error !== undefined ? "radio-btn-error" : ""}`}
                  type="radio"
                  name={name}
                  value={_option?.[optionValue]}
                  checked={
                    field.value !== "" && _option?.[optionValue] == field.value
                  }
                  onChange={(e) => onInputChange(e.target.value)}
                />
                <label className="r-label" htmlFor={name+index}>
                  {_option?.name}
                </label>
              </div>
            );
          })}
        </div>

        {errorType?.map((type) => {
          if (error?.type === type && error?.message !== "")
            return (
              <p key={type} className={errorClass || "error"}>
                <span className="icon info-icon"></span>{error?.message}
              </p>
            );
        })}
      </div>
    </>
  );
}

/**
 * @property defaults
 */
MultiRadioBtnField.defaultProps = {
  defaultValue: "",
  rules: {},
  errorClass: "error",
  onChange: (value) => value,
  optionLabel: "name",
  optionValue: "value",
};

export default MultiRadioBtnField;
