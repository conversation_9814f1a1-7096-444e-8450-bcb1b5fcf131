import { useController } from "react-hook-form";
import React, { useState } from "react";
import DateTimePicker from "react-datetime-picker/dist/entry.nostyle";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";
import { errorType } from "../../constant";

function ReactTimePickerField({
    name,
    control,
    rules,
    defaultValue,
    placeholder,
    minDate,
    maxDate,
    label,
    mainClass,
    errorClass,
    onSelect
}) {
    // react form hooks
    const {
        field,
        fieldState: { error },
    } = useController({ name, control, rules, defaultValue });

    //   local variables
    const [date, setDate] = useState(null);

    //  on date Change
    const onChangeDate = (date) => {
        setDate(date);
        // setting form value
        if (date) {
            if (onSelect) onSelect(date)
            // field.onChange(moment.utc(date));
            field.onChange(date);
        }
    };
    return (
        <>
            <div
                className={`form-in  ${mainClass ? mainClass : ""}  ${error ? "f-error" : ""
                    }`}
            >
                {label && <label className="f-label">{label}</label>}
                <div className="f-in w-100 right-icon">
                    <DateTimePicker
                        disableClock={false}
                        dayPlaceholder={'dd'}
                        monthPlaceholder={'mm'}
                        yearPlaceholder={'yyyy'}
                        calendarIcon={<img src="/images/calender-icon.svg"></img>}
                        format={'MM/dd/y'}
                        clearIcon={false}
                        clockClassName=""
                        onChange={onChangeDate}
                        placeholder={placeholder}
                        className ="form-control"
                        value={field?.value ? new Date(field?.value) : date}
                        maxDate={maxDate}
                        minDate={minDate}
                    />
                </div>
                {errorType?.map((type) => {
                    if (error?.type === type && error?.message !== "") {
                        return (
                            <p key={type} className={errorClass || "error"}>
                                {error?.message}
                            </p>
                        );
                    }
                })}
            </div>
        </>
    );
}

export default ReactTimePickerField;
