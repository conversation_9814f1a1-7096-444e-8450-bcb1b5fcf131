import { Fragment } from "react";
import { Controller } from "react-hook-form";
import { errorType } from "../../constant";

//  =================================================== 

function TextareaField({ control, rules, label, name, type, rows, optional, defaultValue, readOnly, style, placeholder, setTextAreaCount ,optionLabel ,labelClass,textAreaClass }) {
    const handleOnChange = (e, onChange) => {
        onChange(e.target.value)
        if (name == optionLabel) {
            setTextAreaCount(e.target.value.length);
        }
    };
    return (
        <>
            <Controller
                control={control}
                rules={rules}
                defaultValue={defaultValue !== undefined ? defaultValue : ''}
                name={name}
                render={({ field, fieldState: { error } }) => {
                    return (
                        <div className={`f-in w-100 ${error !== undefined ? 'f-error' : ''}`}>
                               {(label ? <label className={labelClass}>{label} {optional && <small>{(optional)}</small>}</label> : '')}
                            <textarea {...field}
                                style={style !== undefined ? style : {}}
                                placeholder={placeholder}
                                readOnly={readOnly}
                                type={type}
                                rows={rows}
                                className={`form-control textAreaClass ${textAreaClass && 'textAreaClass'}`}
                                onChange={(e) => handleOnChange(e, field.onChange)}
                            />
                            {errorType?.map(type => {
                                return <Fragment key={type}>{error?.type === type && error?.message !== "" ?  <p className="error d-flex align-center"><span className="icon info-icon"></span>{error?.message}</p>: null}</Fragment>
                            })}
                        </div>
                    );
                }}
            />
        </>
    )
};

TextareaField.defaultProps = {
    rows: '1',
};


export default TextareaField;

//  ====================== THE END ============================= 
