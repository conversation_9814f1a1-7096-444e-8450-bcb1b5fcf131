import { Fragment } from "react";
import { Controller } from "react-hook-form";
import { errorType } from "../../constant";
//  ===================================================

function InputField({
  minLength,
  control,
  formInClass,
  normalize,
  defaultValue,
  rules,
  disabled,
  label,
  name,
  type,
  optional,
  readOnly,
  placeholder,
  onInputChange,
  autoComplete,
  maxLength,
  autoFocus,
  onBlur,
  className,
  labelClass,
  inputClassName,
  hideandshow,
  setshowPasswordShown,
  showPasswordShown,
  onSelect,
  inputTypestatus,
  optionField,
}) {
  function normalizeField(value) {
    var v = value.replace(/[^0-9.\s]/i, "");
    if (v.match(/^[0-9]{0,2}$/)) {
      return v;
    }
  }

  const handleOnChange = (e, onChange) => {
    if (normalize !== undefined) {
      onChange(normalize(e.target.value));
      if (onInputChange !== undefined) {
        onInputChange(normalize(e.target.value));
      }
    } else {
      onChange(
        type === "number"
          ? name == optionField
            ? normalizeField(e.target.value)
            : e.target.value
          : e.target.value?.replace(/^\s*\s*$/, "")
      );
      if (onInputChange !== undefined) {
        onInputChange(
          type === "number"
            ? parseInt(e.target.value)
            : e.target.value?.replace(/^\s*\s*$/, "")
        );
      }
    }
    if (onSelect) {
      onSelect(e.target.value);
    }
  };
  //  ===================================================
  return (
    <>
      <Controller
        control={control}
        rules={rules}
        defaultValue={defaultValue !== undefined ? defaultValue : ""}
        name={name}
        render={({ field, fieldState: { error } }) => {
          return (
            <div
              className={`form-in ${formInClass ? formInClass : ""} ${
                error !== undefined ? "f-error" : ""
              }`}
            >
              {type !== "hidden" &&
                (label ? (
                  <label className={labelClass}>
                    {label} {optional && <small>{optional}</small>}
                  </label>
                ) : (
                  ""
                ))}
              <div className={inputClassName}>
                <input
                  {...field}
                  placeholder={placeholder ? placeholder : label}
                  readOnly={readOnly ? true : false}
                  type={type}
                  disabled={disabled}
                  autoComplete={autoComplete}
                  maxLength={maxLength}
                  minLength={minLength}
                  autoFocus={autoFocus}
                  className={className}
                  onKeyDown={(evt) =>
                    type == "number" &&
                    (evt.keyCode === 69 ||
                      evt.keyCode === 189 ||
                      evt.keyCode === 187) &&
                    evt.preventDefault()
                  }
                  onChange={(e) => handleOnChange(e, field.onChange)}
                  onBlur={(e) => {
                    onBlur ? onBlur(e.target.value) : null;
                  }}
                />
                {hideandshow && (
                  <span
                    className={`icon eye-icon ${showPasswordShown && "open"}`}
                    onClick={() => setshowPasswordShown(!showPasswordShown)}
                  ></span>
                )}
              </div>
              {errorType?.map((type) => {
                return (
                  <Fragment key={type}>
                    {error?.type === type && error?.message !== "" ? (
                      <p className="error d-flex align-center">
                        <span className="icon info-icon"></span>
                        {error?.message}
                      </p>
                    ) : null}
                  </Fragment>
                );
              })}
            </div>
          );
        }}
      />
    </>
  );
}

InputField.defaultProps = {
  disabled: false,
};

export default InputField;
