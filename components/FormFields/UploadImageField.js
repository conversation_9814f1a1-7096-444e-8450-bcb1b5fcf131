import { useState } from "react";
import { useController } from "react-hook-form";
import { errorType, getLanguages, checklanguage } from "../../constant";
import { removeFile, uploadFile } from "../../redux/action/auth";
import FieldLoader from "../FieldLoader";
import Loader from "../loader";

function UploadImageField({ control, name, rules, setValue, extensionTypes }) {
  // use hooks
  const {
    field,
    fieldState: { error },
  } = useController({ control, name, rules });

  // local variables
  const [exntensionError, setExtensionError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // on image change
  const onImageChange = async (e) => {
    setIsLoading(true);
    const formData = new FormData();
    let file = e?.target?.files[0];
    let currentFileExtension = file?.type.split("/")[1];
    formData.append("files", e?.target?.files[0]);
    if (
      extensionTypes?.length > 0 &&
      !extensionTypes.includes(currentFileExtension)
    ) {
      field.onChange("");
      setExtensionError(true);
      setIsLoading(false);
      return;
    }

    setExtensionError(false);

    try {
      const resp = await uploadFile(formData);

      field.onChange(
        resp?.data?.responseData?.length > 0 && resp?.data?.responseData[0]
      );
      e.target.value = "";
      setIsLoading(false);
    } catch (e) {
      setIsLoading(false);
    }
  };

  const onRemoveImage = async () => {
    setValue(`${name}`, "");
    try {
      const resp = await removeFile({ ids: field?.value?.id });
      setValue(`${name}`, "");
    } catch (e) {}
  };

  return (
    <>
      <div
        className={`form-in w-100 ${error || exntensionError ? "f-error" : ""}`}
      >
        <div className="d-flex align-center profile-pic-wrpr">
          <div
            className={`form-in w-100 text-center upload-img  ${
              error || exntensionError ? "f-error" : ""
            }`}
          >
            <span className="upload-dp">
              {isLoading ? (
                <FieldLoader />
              ) : (
                <img
                  src={
                    field?.value?.id
                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${field?.value?.path}`
                      : "/images/userimg.svg"
                  }
                  className="cover-img"
                />
              )}
            </span>

            <input
              type="file"
              placeholder=""
              title=""
              className={`form-control upload-dp-in ${
                isLoading ? "disabled" : ""
              }`}
              onChange={(e) => {
                onImageChange(e);
              }}
            />

            <p
              className={`upload-label text-dark-grey ${
                isLoading ? "disabled" : ""
              }`}
            >
              <span className="icon upload-grey"></span>
              {field?.value?.id
                ? getLanguages(checklanguage, "change")
                : getLanguages(checklanguage, "add")}
              {getLanguages(checklanguage, "profilePicture")}
            </p>
          </div>
          {field?.value?.id && (
            <a
              href="javascript:;"
              className={`text-grey-6 fw500 remove-img ${
                isLoading ? "disabled" : ""
              }`}
              onClick={() => onRemoveImage()}
            >
              {getLanguages(checklanguage, "remove")}
            </a>
          )}
        </div>

        {exntensionError ? (
          <p className="error">
            Allowed file extensions are{" "}
            {extensionTypes?.map(
              (_type, index) =>
                `${_type} ${
                  extensionTypes?.length - 1 == index
                    ? ""
                    : extensionTypes?.length - 2 == index
                    ? "and "
                    : ", "
                }`
            )}{" "}
          </p>
        ) : null}

        {errorType?.map((type) => {
          if (error?.type === type && error?.message !== "") {
            return (
              <p key={type} className="error">
                <span className="icon info-icon"></span>
                {error?.message}
              </p>
            );
          }
        })}
      </div>
    </>
  );
}

export default UploadImageField;
