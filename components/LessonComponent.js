import React, { useEffect, useState } from 'react'
import moment from 'moment'
import { checkCourseCommingSoon } from "../constant"
import { CircularProgressbarWithChildren, buildStyles } from 'react-circular-progressbar'
const LessonComponent = ({initialVideoToPlay, setLessonNavigation, getChapterLists, setSubVideosLesson, setPlay, setViewData, subLessonId, setsubLessonId, Play ,setUpdateIndex }) => {
    const [firstTimeLoad, setFirstTimeLoad] = useState(true);
    useEffect(() => {
        setTimeout(() => {
            if(initialVideoToPlay && initialVideoToPlay.length == 2){
                window.document.getElementById("lesson_" + initialVideoToPlay[0]).click();
                window.document.getElementById("sub_lesson_" + initialVideoToPlay[0] +"_"+initialVideoToPlay[1]).click();
                setTimeout(() => {
                    
                }, 1000)
                
            }else{
                window.document.getElementById("lesson_0").click();
            }
            
        }, 200)
        
    }, [])
    const handleHideShowLesson = (e, lesson , index) => {
        setFirstTimeLoad(false);
        if (lesson?.accessibleDate === null && lesson?.subVideos?.length < 1) {
            return false
        }
        if (lesson?.accessibleDate != null && checkCourseCommingSoon(lesson?.accessibleDate)) {
            return false
        }
        if (e.target.closest('.accordion-content')) {
            return false;
        }
        const elem = e.target.closest('.accordion-items').classList;
        const isActive = elem.contains('active');
        document.querySelectorAll(`.accordion-items`).forEach(elem => elem.classList.remove('active'));
        isActive ? elem.remove('active') : elem.add('active');
        setUpdateIndex((prev)=> ({...prev , parentIndex : index}))
    }

    const handleChangeLesson = (sublesson ,index) => {
        setLessonNavigation(false);
        setSubVideosLesson(null)
        setPlay(!Play);
        let dataobject = {}
        dataobject.description = sublesson?.description,
            dataobject.attachment = sublesson?.attachments,
            setViewData({ ...dataobject });
        setsubLessonId(sublesson?.id)
        setUpdateIndex((prev)=> ({...prev , childIndex : index}))
        document.querySelector(`#top_right_video`)?.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" })
        setTimeout(() => {
            setSubVideosLesson(sublesson)
        }, 100)
    }
    return (
        <div className="tab-listing-wrap">
            {
                getChapterLists?.topicList?.map((lesson, index) => (
                    <div id = {"lesson_"+index} className={`${"product-list-wrap accordion-items"} `} key={index} onClick={(e) => handleHideShowLesson(e, lesson, index)}>
                        <div className="wrap-heading accordion-heading" >
                            <h6 className="h6 fw500 text-dark-grey lesson-name">{lesson?.name}</h6>
                            {
                                lesson?.accessibleDate != null ?
                                    <>
                                        {!checkCourseCommingSoon(lesson?.accessibleDate) ?
                                            <>
                                                <div className="action-wrap">
                                                    {/* <p className="text-grey-5 fw500 lesson-date ">{moment(lesson?.accessibleDate).format('MMM D')}</p> */}
                                                    <span className="icon arrow-icon"></span>
                                                </div>
                                                <a id = {"lesson_a_"+index} href="javascript:void(0)" className="arrow-action"></a>
                                            </>
                                            :
                                            <div className="action-wrap">
                                                <p className="text-grey-5 fw500 lesson-date ">{moment(lesson?.accessibleDate).format('MMM D')}</p>
                                                <span className="icon lock-icon"></span>
                                                {/* <span className="icon arrow-icon"></span> */}
                                            </div>
                                        }
                                    </>
                                    :
                                    <>
                                        <div className="action-wrap">
                                            {/* <p className="text-grey-5 fw500 lesson-date ">{moment(lesson?.accessibleDate).format('MMM D')}</p> */}
                                            <span className="icon arrow-icon"></span>
                                        </div>
                                        <a id = {"lesson_a_"+index} href="javascript:void(0)" className="arrow-action"></a>
                                    </>

                            }
                        </div>
                        <div className="wrap-content accordion-content">
                            <div className="wrap-content-accordion">
                                <ul className="sub-lessson-list">
                                    {
                                        lesson?.subVideos.map((sublesson, i) => {
                                            return (
                                            <li id = {"sub_lesson_"+index+"_"+i} className={`d-flex align-center justify-between sub-lessson ${subLessonId == sublesson?.id &&"active"}`} key={i} onClick={() => handleChangeLesson(sublesson , i)}>
                                                <p className={`p text-dark-grey sub-lesson-name `}>{sublesson?.name}</p>
                                                {
                                                    sublesson?.percentageWatched >= 0 && sublesson?.percentageWatched < 97 ?
                                                        <span className="loaderView_sublesson sm">
                                                            <CircularProgressbarWithChildren styles={
                                                                buildStyles({
                                                                    pathColor: "#FC7900",
                                                                    strokeLinecap: "butt",
                                                                    textColor: "#404040"
                                                                })
                                                            } strokeWidth={10} value={sublesson?.percentageWatched}>
                                                            </CircularProgressbarWithChildren>
                                                        </span> :
                                                        <span className="icon check-icon-white active"></span>
                                                
                                                }


                                            </li>
                                        )}
                                        )
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>
                ))
            }

        </div>
    )
}

export default LessonComponent