import React, { useEffect, useRef, useState } from 'react'
import Player from '@vimeo/player';
import { updateTimeStamps } from "../redux/action/kuby-courses"
import _ from 'lodash'
const ImageLoader = () => {
    return <div className="loader_container"><img className='loader-wrpr' src="/images/tube-spinner.svg" /></div>
}
const VimeoPlayer = ({ SubVideosLesson, setPlay, Play, getChapterLists, setgetChapterLists }) => {
    
    const [Loading, setLoading] = useState(true);
    const callPlayVimeo = () => {
        var iframe = document.querySelector('iframe');
        var player = null;
        iframe.onload = () => {
            setLoading(false);
        }
        if (Loading) {
            return false
        }
        var options = {
            width: 640,
            loop: true,
            controls: true,
            url: SubVideosLesson?.videoLink,
        };
        player = new Player(iframe, options);

        
        
        let interval;
        const onPlay = function (data) {
            interval = setInterval(function () {
                let playbackrate = {}
                player.getCurrentTime().then(function (seconds) {
                    playbackrate.amountWatched = seconds

                });
                player.getDuration().then(async function (duration) {
                    if(SubVideosLesson?.amountWatched >= playbackrate.amountWatched ) {
                        return false
                    }
                    playbackrate.totalLength = duration
                    playbackrate.videoId = SubVideosLesson?.id
                    let calculatePercentage = percentCalculation(playbackrate.amountWatched,duration);
                    // duration = the duration of the video in seconds
                   await UpdateSubVideoCourseTimeStamps(playbackrate);
                    setgetChapterLists((prev) => {
                        let updateVideoTimeStamp = {...getChapterLists}
                         for( var i =0 ; i < updateVideoTimeStamp?.topicList?.length ; i++){
                            for(var j = 0 ; j < updateVideoTimeStamp?.topicList[i]?.subVideos?.length ; j++){
                                if(updateVideoTimeStamp?.topicList[i]?.subVideos[j]?.id == SubVideosLesson?.id)
                                updateVideoTimeStamp.topicList[i].subVideos[j].percentageWatched = calculatePercentage
                            }
                         }
                        return updateVideoTimeStamp
                    })
                }).catch(function (error) {
                    // an error occurred
                });
            }, 10000);
            player.on('pause', function () {
                clearInterval(interval)
            });
        };
       
        player.on('play', onPlay);
        player.on('ended', async function(data) {
            let playendTime = {}
            playendTime.amountWatched = data.seconds
            playendTime.totalLength = data.duration
            playendTime.videoId = SubVideosLesson?.id
             await UpdateSubVideoCourseTimeStamps(playendTime);
             setgetChapterLists((prev) => {
                let updateVideoTimeStamp = {...getChapterLists}
                 for( var i =0 ; i < updateVideoTimeStamp?.topicList?.length ; i++){
                    for(var j = 0 ; j < updateVideoTimeStamp?.topicList[i]?.subVideos?.length ; j++){
                        if(updateVideoTimeStamp?.topicList[i]?.subVideos[j]?.id == SubVideosLesson?.id)
                        updateVideoTimeStamp.topicList[i].subVideos[j].percentageWatched = percentCalculation(data.seconds,data.duration)
                    }
                 }
                return updateVideoTimeStamp
            })
            
           clearInterval(interval)
          });
            player.setCurrentTime(SubVideosLesson?.amountWatched?.toFixed(0)).then(function (seconds) {
                // seconds = the actual time that the player seeked to
            }).catch(function (error) {
                switch (error.name) {
                    case 'RangeError':
                        // the time was less than 0 or greater than the video’s duration
                        break;

                    default:
                        // some other error occurred
                        break;
                }
            });
        
    }
    function percentCalculation(a, b){
        let c = (+a/+b)*100;
        return c;
      }

    useEffect(() => {
        callPlayVimeo()
    }, [Play, Loading])

    const UpdateSubVideoCourseTimeStamps = async (playbackrate) => {
        try {
            let resonse = await updateTimeStamps(playbackrate);
        }
        catch (err) {

        }
    }
    return (
        <div className="lesson-video-wrpr player-wrapper" style={{paddingTop: "0px"}} >
            {
            Loading && <ImageLoader />
            }
            <iframe style = {{visibility: Loading ? 'hidden' : 'visible'}} id={`top_right_video`} src={SubVideosLesson?.videoLink} width="100%" height="auto" allowFullScreen="allowfullscreen" className="img-fluid"> </iframe>
        </div>

    )
}

export default VimeoPlayer