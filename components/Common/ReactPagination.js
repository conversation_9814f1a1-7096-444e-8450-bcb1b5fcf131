import React from 'react'
import Pagination from "react-js-pagination";

const ReactPagination = ({ currentPage, setcurrentPage, totalItemsCount, totalPages ,perPage,pageRangeDisplayed }) => {

  const handlePageChange = (pageNumber) => {
    setcurrentPage(pageNumber)
  }
  return (
    <Pagination
      activePage={currentPage}
      itemsCountPerPage={perPage}
      totalItemsCount={totalItemsCount}
      // pageRangeDisplayed={totalPages}
      pageRangeDisplayed={pageRangeDisplayed}
      onChange={handlePageChange}
    />
  )
}

export default ReactPagination