import { useRouter } from 'next/router'
import React from 'react'
import { ProductSkeleton } from "./SkeletonLoader/ProductSkeleton"
import InfiniteScroll from 'react-infinite-scroll-component';
import { InfinitySpin } from 'react-loader-spinner'
import { Number, Currency } from "react-intl-number-format";
import { getLanguages, checklanguage } from '../../constant';
import { useCookie } from 'next-cookie';
import { useDispatch, useSelector } from "react-redux";
const ProductShop = ({ ProductLists, checkProductInCart, addToCart, IsFetching, addToWishList, isWisList, setCurrentPage, hasNext, setHasNext }) => {
    const { userCart } = useSelector(state => state.cart);
    const router = useRouter()
    const cookies = useCookie();
    const fetchData = async () => {
        if (ProductLists?.totalPages > 1) {
            setTimeout(() => {
                setCurrentPage(prev => prev + 1);
                setHasNext(true)
            }, 500)
        }
    }
    const buyNowProduct = (id) => {
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            //window.location.href = `https://www.checkout-ds24.com/product/${parseInt(id)}`;
            window.location.href = `https://www.digistore24.com/product/${parseInt(id)}`;
        }
        else {
            router.push(`/signin?redirectTourl=/${router.asPath}`)
        }
    }
    const checkIfProductIsInCart = (productId) => {
        if (userCart?.records?.length) {
            let products = [...userCart?.records];
            for (let i = 0; i < products?.length; i++) {
                if (products[i]?.productId == productId) {
                    return true;
                }
            }

        }
        return false;

    }
    return (
        <div className="right-bottom-wrpr">
            {/* <InfiniteScroll
                    style={{ overflow: "none" }}
                    dataLength={ProductLists != null ? ProductLists?.products?.length : []} //This is important field to render the next data
                    next={fetchData}
                    hasMore={hasNext}
                    loader={<div className='loader-svg'>
                        <InfinitySpin
                            width='200'
                            color="#248BFF"
                        /></div>} 
                >*/}
            <div className="d-flex flex-wrap product-wrpr">
                {
                    IsFetching ?
                        <ProductSkeleton listsToRender={6} />
                        :
                        ProductLists?.products?.map((product, index) => {
                            return (
                                <div className="d-flex align-center product-card" key={index} >
                                    <div className="product-image-wrpr" onClick={() => router.push(`/dashboard/shop/product-detail?pid=${product?.id}`)}>
                                        <img src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${product?.attachment?.path}`} alt="" className="contain-img" />
                                    </div>
                                    <div className="product-details-wrpr">
                                        <div className="d-flex fd-col product-top-wrpr" onClick={() => router.push(`/dashboard/shop/product-detail?pid=${product?.id}`)}>
                                            {/* <p className="text-grey-6 product-cat">{getLanguages(checklanguage, 'category')}: <span className="cat-name">{product?.category?.content?.name}</span></p> */}
                                            <h6 className="text-grey-3 fw600 product-title">{product?.content?.name}</h6>
                                            <p className="text-grey-5 product-desc">{product?.content?.descriptionText?.substring(0, 160) + '....'} <span className="readmore">{getLanguages(checklanguage, 'readMore')}</span></p>
                                        </div>
                                        <div className="d-flex fd-col product-bottom-wrpr">
                                            <p className="text-grey-1 fw600 product-price"><Currency locale={checklanguage} currency={"EUR"}>{product?.price?.[0]?.price.toFixed(2)}</Currency></p>
                                            <div className="d-flex align-center prod-btn-wrpr">
                                                {/* <a href="javascript:void(0)" className="btn w-50 prod-btn wishlist-btn" onClick={() => addToWishList(product?.id, index, product?.WishLists)}><span className={`icon ${product?.WishLists?.length > 0 ? 'wishlist-icon-green-fill' : "wishlist-icon-green"} `}></span>{product?.WishLists?.length > 0 ? getLanguages(checklanguage, 'remove') : getLanguages(checklanguage, 'wishlist')}</a> */}
                                                {
                                                    // checkProductInCart(product?.id) || isWisList === 1 ?
                                                    //     <a href="javascript:void(0)" onClick={() => {
                                                    //         if (isWisList == 1) {
                                                    //             if (checkProductInCart(product?.id)) {

                                                    //             }
                                                    //             else {
                                                    //                 addToCart(product, index)
                                                    //             }
                                                    //             addToWishList(product?.id, index, product?.WishLists)
                                                    //         }
                                                    //         else {
                                                    //             router.push('/dashboard/shop/cart')
                                                    //         }
                                                    //     }} className="btn w-50 prod-btn cart-btn">
                                                    //         <span className="icon cart-icon-green"></span>
                                                    //         {isWisList === 1 ? 'Move to cart' : "View Cart"}
                                                    //     </a>
                                                    //     :
                                                    //     isWisList != 1 && 
                                                    <>
                                                        <a href="javascript:void(0)" onClick={() => {
                                                            if(checkIfProductIsInCart(product?.id)){
                                                                router.push('/dashboard/shop/cart')
                                                            }else{
                                                                addToCart(product, index)
                                                            }
                                                            
                                                        }} className="btn  prod-btn cart-btn"><span className="icon cart-icon-green"></span>{checkIfProductIsInCart(product?.id) ? getLanguages(checklanguage, 'viewCart') : getLanguages(checklanguage, 'addToCart')} </a>
                                                        <a onClick={(e) => {
                                                            e.preventDefault();
                                                            buyNowProduct(product?.dgStoreId)
                                                        }}
                                                            href="javascript:;" className="btn-accent prod-btn buy-now-btn">{getLanguages(checklanguage, 'buyNow')}</a>
                                                    </>
                                                }

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}

            </div>
            {/* </InfiniteScroll> */}
        </div>
    )
}

export default ProductShop