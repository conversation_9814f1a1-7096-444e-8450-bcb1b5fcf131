import { useRouter } from "next/router";
import { useState } from "react";
import { getLanguages, checklanguage } from "../../../constant";
function Filters({ languageArray }) {
  const router = useRouter();
  const { query } = router;
  const [filters, setFilters] = useState(false);
  const handleChangefFilter = (e) => {
    const { name, value } = e.target;
    let newQuery = { ...router.query };
    switch (name?.toLowerCase()) {
      case "language":
        newQuery.language = value;
        break;
      case "gender":
        newQuery.gender = value;
        break;
      case "rating":
        newQuery.rating = value;
        break;
      case "price":
        newQuery.price = value;
        break;
      case "experience":
        if (value == 0) {
          newQuery.lowerExperience = 0;
          newQuery.hignerExperience = 5;
        } else if (value == 5) {
          newQuery.lowerExperience = 5;
          newQuery.hignerExperience = 10;
        } else if (value == 10) {
          newQuery.lowerExperience = 10;
          newQuery.hignerExperience = 100;
        }

        break;

      default:
        null;
    }

    router.push(
      {
        pathname: `${router?.pathname}`,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };
  const filtersMenu = () => {
    setFilters(!filters);
  };
  return (
    <>
      <div className="inner-head-btns-wrpr">
        <button
          type="button"
          className="filter-btn"
          onClick={() => filtersMenu()}
        >
          <img src="/images/Filter.svg" className="filter-icon" />
        </button>
        <div
          className={`d-flex align-center text-right inner-header-btns ${
            filters ? "active" : ""
          }`}
        >
          <div
            onClick={() =>
              router.push(
                `/dashboard/kuby-companion?tabstatus=professionals`,
                "",
                { scroll: false }
              )
            }
            style={{ color: "#499557", cursor: "pointer" }}
            className="d-flex align-center"
          >
            {getLanguages(checklanguage, "reset")}
          </div>
          <div className="d-flex align-center header-filter">
            <select
              name="language"
              onChange={handleChangefFilter}
              value={query?.language}
              className="filter-select"
            >
              <option className="d-none">
                {getLanguages(checklanguage, "selectLanguage")}
              </option>

              {languageArray?.languages?.map((item, i) => (
                <option key={i} value={item?.id}>
                  {item?.name}
                </option>
              ))}
            </select>
          </div>
          <div className="d-flex align-center header-filter">
            <select
              name="gender"
              onChange={handleChangefFilter}
              value={query?.gender}
              className="filter-select"
            >
              <option className="d-none">
                {getLanguages(checklanguage, "gender")}
              </option>
              <option value={1}>{getLanguages(checklanguage, "male")}</option>
              <option value={2}>{getLanguages(checklanguage, "female")}</option>
            </select>
          </div>
          {/* <div className="d-flex align-center header-filter">
          <select name="rating" onChange={handleChangefFilter} value={query?.rating} className="filter-select">
            <option className="d-none" >Rating</option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
          </select>
        </div> */}
          <div className="d-flex align-center header-filter">
            <select
              name="price"
              onChange={handleChangefFilter}
              value={query?.price}
              className="filter-select"
            >
              <option className="d-none">
                {getLanguages(checklanguage, "price")}
              </option>
              <option value="2">
                {getLanguages(checklanguage, "lowToHigh")}
              </option>
              <option value="3">
                {getLanguages(checklanguage, "highToLow")}
              </option>
            </select>
          </div>
          {/* <div className="d-flex align-center header-filter">
          <select name="experience" onChange={handleChangefFilter} value={query?.lowerExperience} className="filter-select">
            <option className="d-none" >{getLanguages(checklanguage, "experience")}</option>
            <option value={0}>Less than 5</option>
            <option value={5}>More than 5 & less than  10</option>
            <option value={10}>More than 10</option>
          </select>
        </div> */}
        </div>
      </div>
    </>
  );
}

export default Filters;
