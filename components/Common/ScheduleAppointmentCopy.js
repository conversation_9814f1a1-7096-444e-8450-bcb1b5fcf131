import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import {
  APPOINTMENT_SCHEDULE_SETTINGS,
  checklanguage,
  convertUTCToLocalMinutes,
  getLanguages,
} from "../../constant";
import { useEffect, useState } from "react";
import moment from "moment";
import _ from "lodash";
import Calendar from "react-calendar";

function ScheduleAppointmentCopy({
  setTimeSlotArray,
  timeSlotData,
  //timeSlotArray,
  setbookTimeSlot,
  bookTimeSlot,
  scheduleTime,
}) {
  var timezone = moment.tz.guess();
  let currentDate = moment(new Date()).format("MMMM Y");
  const [currentMonth, setCurrentMonth] = useState(currentDate);
  const [value, onChange] = useState(new Date());
  let [timeSlotArray, settimeSlotArray] = useState(null);
  const [daySlots, setDaySlots] = useState(null);
  useEffect(() => {
    if(timeSlotData){
      createTimeSlotArray(timeSlotData);
    }
    
  }, [timeSlotData]);

  const createTimeSlotArray = (allAvailableSlots) => {
    let allEvents = JSON.parse(JSON.stringify([]));
    let aSlots = [];
    if (allAvailableSlots?.length) {
      let temp = {};
      let lastDate = null;
      allAvailableSlots.forEach((obj, index) => {
        let slots = [];
        Object.keys(obj).forEach((obj1, index1) => {
          // if(index > 1){
          //   return;
          // }
          obj[obj1]?.event?.forEach((obj2, index2) => {
            obj2?.Timings?.forEach((obj3, index3) => {
              let tt = 0;
              let timezone = moment.tz.guess();
              
              if(obj3.offset){
                
                while(obj3.startTime + tt * parseInt(obj2.duration) < obj3.endTime){
                  //let sDate = moment.utc(obj1).add(obj3.startTime + tt * parseInt(obj2.duration), "m").toISOString();
                  //let sDate=moment(obj2.startDate).startOf("d").add(obj3.startTime,"m").toISOString();
                  
                  const hostTimezone = moment(obj2.startDate).tz(timezone).format();

                  // Convert IST to Europe/Berlin

                  
                  let sTime = convertUTCToLocalMinutes(obj3.startTime, timezone)
                  let sDate = moment(obj2.startDate).startOf('d').add(sTime + tt * parseInt(obj2.duration), 'm').toISOString();
                  temp = {
                    start: sDate,
                    end: moment(sDate).add(parseInt(obj2.duration), "m").toISOString(),
                    allDay: 0,
                    extendedProps: {},
                  };
                  tt++;
                  allEvents.push(temp);
                  slots.push({
                    start: sDate,
                    end: moment(sDate).add(parseInt(obj2.duration), "m").toISOString(),
                    
                    isEnable: true
                    // startTime: startTime,
                    // endTime: endTime,
                    // isEnable,
                  });
                }

              }else{

                while (obj3.startTime + tt * parseInt(obj2.duration) < obj3.endTime) {
                  let sDate = moment(obj1).add(obj3.startTime + tt * parseInt(obj2.duration), "m").toISOString();
                    temp = {
                      start: sDate,
                      end: moment(sDate).add(parseInt(obj2.duration), "m").toISOString(),
                      allDay: 0,
                      extendedProps: {},
                    };
                    tt++;
                    allEvents.push(temp);
                    slots.push({
                      start: sDate,
                      end: moment(sDate).add(parseInt(obj2.duration), "m").toISOString(),
                      
                      isEnable: true
                      // startTime: startTime,
                      // endTime: endTime,
                      // isEnable,
                    });
                }
              }
            });
          });
          lastDate = obj1;
        });
        //aSlots.push({date: moment(Object.keys(obj)[0]).tz(timezone).format('YYYY-MM-DD HH:mm:ss z'), slots: slots?slots:[]});
        aSlots.push({intialDate: Object.keys(obj)[0], date: moment.utc(Object.keys(obj)[0]).startOf('d').format("YYYY-MM-DDTHH:mm:ss.SSS"), slots: slots?slots:[]});
      });
    }
    console.log(aSlots, allEvents, 'allEvents')
    settimeSlotArray(aSlots);
  }
  
  // const createTimeSlotArray = (tt) => {
  //   let timezone = moment.tz.guess();
    
  //   let aSlots = [];
  //   for(const [i, o] of tt.entries()){
  //     let slots = [];
  //     let d = Object.keys(o);
  //     let date = d[0];
      
  //     console.log(o[date], Object.keys(o), 'tttttttttttttt');
  //     //create slots
  //     if(o[date]?.event?.length > 0){
  //       console.log(date, 'popopo1')
  //       for(const [ii, oo] of o[date]?.event.entries()){
  //         console.log(oo, 'popopo3')
          
  //         for(const [iii, ooo] of oo.Timings.entries()){
  //           console.log(ooo, 'popopo33');
  //           let startTimeLimit = ooo.startTime;
  //           let endTimeLimit = ooo.endTime;
  //           let duration = parseInt(oo.duration)
            
            
  //           //console.log(start, end, duration, 'ddddddddfff');
  //           if(startTimeLimit < endTimeLimit){
  //             let startTime = moment(date).add(parseInt(ooo.startTime), "m").toDate();
  //             //console.log(date,startTime, start, end, duration, 'ddddddddfff');
  //             let current = date;
  //             let end = parseInt(ooo.startTime);
  //             while(true){
  //               let start = parseInt(end);
  //               end = parseInt(start) + parseInt(duration);
  //               //startTime = moment(current).add(parseInt(start), "m").toDate();
  //               let endTime = moment(startTime).add(duration, "m").toDate();
                
                
  //               slots.push({
  //                 start: startTime,
  //                 end: endTime,
  //                 startTime: ooo.startTime,
  //                 endTime: ooo.endTime,
  //                 isEnable: true
  //                 // startTime: startTime,
  //                 // endTime: endTime,
  //                 // isEnable,
  //               });
  //               console.log(date, startTime, endTime, end, ooo.endTime, 'xxxxxxxxxxxx')
  //               startTime = endTime;
                
  //               if(end >= parseInt(ooo.endTime)){
  //                 break;
  //               }
  //             }
  //           }
  //           // while(true){
  //           //   if(){

  //           //   }
  //           //   startTime = moment(date).add(ooo.startTime);
  //           //   endTime = ooo.startTime + parseInt(ooo.duration);
  //           //   if(){
  //           //     break;
  //           //   }
  //           // }
  //         }
  //       }
  //     }

  //     aSlots.push({date: date, slots: slots?slots:[]});

      

  //   }
  //   console.log(aSlots, 'tttttttttttttt111');
  //   console.log(aSlots, 'tttttttttttttt1112');
  //   settimeSlotArray(aSlots);
    
  // }




  const onSelectTimeSlot = (e, time) => {
    e.preventDefault();
    setbookTimeSlot((prevState) => ({
      ...prevState,
      startDate: time?.start,
      endDate: time?.end,
      timings: [
        {
          startTime: time?.startTime,
          endTime: time?.endTime,
        },
      ],
    }));
  };

  const fetchSlots = (date) => {
    let obj_to_locate = { date: moment.utc(date).startOf('d').format("YYYY-MM-DDTHH:mm:ss.SSS") };

    let result = _.find(timeSlotArray, obj_to_locate);
    if (result?.slots) {
      setDaySlots(result?.slots);
    }else{
      setDaySlots(null)
    }
    setTimeout(() => {
      document.getElementById("slots-container")?.scrollIntoView();
    }, 100);
  };



  return (
    <>
      <p className="label fw500 text-grey-5">
        {getLanguages(checklanguage, "selectADateAndTime")}:
      </p>
      <div className="appointment-calender-wrpr">
        <div className="d-flex align-center justify-between">
          <p className="text-dark-grey fw600 h6"> {currentMonth}</p>
        </div>
        
        <div>
          <Calendar
            className={"meeting-schedule-calendar"}
            tileClassName={"calender-day-tile"}
            onChange={onChange}
            value={value}
            minDate={new Date()}
            minDetail="month"
            showFixedNumberOfWeeks={false}
            showNeighboringMonth={false}
            showNavigation={true}
            onClickDay={fetchSlots}
            onActiveStartDateChange={(e) => {
              setCurrentMonth(moment(e.activeStartDate).format("MMMM, Y"));
            }}
            nextLabel={<span className="icon arrow-icon"></span>}
            prevLabel={<span className="icon arrow-icon"></span>}
            tileDisabled= {({ activeStartDate, date, view }) => {
              let result = _.find(timeSlotData, obj => _.has(obj, moment(date).toISOString()));
            }}
            // tileDisabled={({ activeStartDate, date, view }) => {
            //   console.log(moment(date).toISOString(), 'kkkkkkkkkk')
            //   let obj_to_locate = { date: date };
            //   console.log(timeSlotArray, obj_to_locate, 'obj_to_locate');
            //   //let result = _.find(timeSlotArray, {date: obj_to_locate});
            //   let result = _.find(timeSlotArray, item => moment(item.date).toISOString() == moment(date).toISOString());
              
            //   if (!result) {
            //     return date;
            //   } else if (result?.slots?.length > 0) {
            //     console.log(result, timeSlotArray, 'rrrrrrrrrrr', obj_to_locate)
            //     let dateDisabled = true;

            //     for (let i = 0; i < result.slots.length; i++) {
            //       let isDisable = result.slots[i].isEnable
            //         ? result.slots[i]?.start <
            //           moment().add(scheduleTime, "hours").utc().toDate()
            //         : true;
            //       if (!isDisable) {
            //         dateDisabled = false;
            //         break;
            //       }
            //     }
            //     if (dateDisabled) {
            //       return date;
            //     }
            //   } else {
            //     return date;
            //   }
            // }}
          />
        </div>
        <div className="slot-wrpr">
          {daySlots?.length > 0 ? (
            <>
              <p className="p fw600 text-dark-grey">
                {getLanguages(checklanguage, "timeSlots")}
              </p>
              <div className="d-flex align-center cal-row" id="slots-container">
                {daySlots?.map((time, i) => {
                  let isDisable = time.isEnable
                    ? time?.start <
                      moment().add(scheduleTime, "hours").utc().toDate()
                    : true;
                  return !isDisable ? (
                    <div
                      className={`time  ${
                        isDisable
                          ? "disabled"
                          : time?.start == bookTimeSlot?.startDate
                          ? "active"
                          : ""
                      }`}
                      key={i}
                    >
                      {moment.utc(time?.start).tz(timezone).format("HH:mm")}
                      <a
                        href="javascript:void(0)"
                        onClick={(e) => onSelectTimeSlot(e, time)}
                        className="time-link"
                      ></a>
                    </div>
                  ) : (
                    <></>
                  );
                })}
              </div>
              <div id="slots-container"></div>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
}

export default ScheduleAppointmentCopy;
