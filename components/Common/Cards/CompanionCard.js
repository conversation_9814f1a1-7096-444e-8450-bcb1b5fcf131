import { useEffect, useState } from "react";
import { Number, Currency } from "react-intl-number-format";

import CompanionScheduleModal from "../Modals/CompanionScheduleModal";
import CompanionVitaModal from "../Modals/CompanionVitaModal";
import SoulWritingModal from "../Modals/SoulWritingModal";
import moment from "moment";
import { useRouter } from "next/router";
import ReactFlagComponent from "../ReactFlagComponent";
import {
  getCompanionById,
  getTimeSlots,
} from "../../../redux/action/kuby-companion";
import {
  getYearExperience,
  getLanguages,
  checklanguage,
} from "../../../constant";
import ReactVimeoModal from "../Modals/ReactVimeoModal";
import MeetingSuccessModal from "../Modals/MeetingSuccessModal";
import { useCookie } from "next-cookie";
import _ from "lodash";
import StudentMeetingsExhaustedModal from "../Modals/StudentMeetingsExhaustedModal";

function CompanionCard({
  isModal,
  CompanionList,
  tab,
  soulWriting,
  postProject,
  alreadySelectedCompanion,
  showSelectButton,
  activeTab,
  hideExtraInfo,
  errorModal,
  setErrorModal,
  hideErrorModal,
  showCompanionCost,
  setModalOpen,
}) {
  const [isImageOpen, setIsImageOpen] = useState(false);
  const [profilePic, setProfilePic] = useState(null);
  // local varibales
  let cList = [];
  if (CompanionList?.users && CompanionList?.users?.length > 0) {
    CompanionList.users.forEach((obj, index) => {
      CompanionList.users[index] = Object.assign(CompanionList.users[index], {
        userType: obj?.Roles?.[0]?.name,
      });
    });
  }

  CompanionList.users = _.orderBy(CompanionList.users, ["userType"], ["asc"]);
  const [showMaxLimitModal, setShowMaxLimitModal] = useState(false);
  const [isModalShow, setIsModalShow] = useState(0); //1 for vita modal , 2 for schedule modal, 3 for soulwriting
  const router = useRouter();
  const cookies = useCookie();

  const [allowStudentMeetings, setAllowStudentMeetings] = useState(
    CompanionList?.studentMeetingMeta?.allowStudentMeetings ? true : false
  );
  const [getVitaDetail, setgetVitaDetail] = useState({});
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState("");
  const [videId, setVideoId] = useState("");
  const [getTimeSlotData, setgetTimeSlotData] = useState(null);
  const [MeetingSlot, setMeetingSlot] = useState(null);
  const [CompanionId, setCompanionId] = useState("");
  const [meetingPopupSuccess, setmeetingPopupSuccess] = useState(false);
  const [isUserTypeStudent, setIsUserTypeStudent] = useState(false);
  const [queryID, setQueryId] = useState(null);
  const { query } = router;
  useEffect(() => {
    setAllowStudentMeetings(
      CompanionList?.studentMeetingMeta?.allowStudentMeetings ? true : false
    );
  }, [CompanionList]);
  useEffect(() => {
    if (query?.companionId || query?.startDate) {
      getQueryCallFunction();
    }
  }, [query?.companionId]);
  const getQueryCallFunction = async () => {
    await getVitaDetails(parseInt(query?.companionId), parseInt(query?.type));
    await getTimeSlot(parseInt(query?.companionId));
  };
  const getVitaDetails = async (id, type, userType) => {
    try {
      let response = await getCompanionById({ id: id });
      setgetVitaDetail(response?.data?.responseData);
      
      setIsModalShow(type);
      setModalOpen(true);
      if (userType == "student") {
        setIsUserTypeStudent(true);
      }
    } catch (err) {}
  };

  const getTimeSlot = async (id, type) => {
    setgetTimeSlotData(null);
    setCompanionId(id);
    try {
      let response = await getTimeSlots({
        companionId: id,
        startDate: moment().startOf("day").utc().toDate(),
        endDate: moment().add(30, "days").endOf("day").utc().toDate(),
      });
      setgetTimeSlotData(response?.data?.responseData);
      //setMeetingSlot(response?.data?.responseData?.meetings);
      let data = {};
      data.id = id;
      data.type = type;
      setQueryId(data);
    } catch (err) {}
  };
  const handleIntroductoryVideo = (e, url, id) => {
    e.preventDefault();
    setVimeoSrc(url);
    setvimeoOpen(1);
    setVideoId(id);
  };

  const chooseCompanion = (id) => {
    setCompanionId(id);
    postProject(id);
  };

  return (
    <>
      {CompanionList?.users?.length > 0 ? (
        CompanionList?.users?.map((list, i) => {
          if (alreadySelectedCompanion == list?.id) {
            return <></>;
          }
          if (activeTab && activeTab == 1 && list.userType != "companion") {
            return <></>;
          }
          if (activeTab && activeTab == 2 && list.userType != "student") {
            return <></>;
          }
          return (
            <>
              <div
                onClick={(e) => {
                  soulWriting == 1
                    ? chooseCompanion(list?.id)
                    : e.preventDefault();
                }}
                className={`companion-card gg ${
                  soulWriting == 1 &&
                  CompanionId != "" &&
                  list?.id === CompanionId &&
                  "active"
                } `}
                key={i}
              >
                <div className="text-center companion-details">
                  <div className="comp-img">
                    <img
                      onClick = {() => {
                        setProfilePic(list?.UserProfile?.attachment?.path
                          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${list?.UserProfile?.attachment?.path}`
                          : "images/user-image.jpg");
                        setIsImageOpen(true)
                        setModalOpen(true);
                      }}
                      src={
                        list?.UserProfile?.attachment?.path
                          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${list?.UserProfile?.attachment?.path}`
                          : "images/user-image.jpg"
                      }
                      alt=""
                      className="cover-img"
                    />
                  </div>
                  <div className="d-flex align-center justify-center comp-name-wrpr">
                    <h6 className="h6 fw600 comp-name">
                      {list?.UserProfile?.firstName}{" "}
                      {list?.UserProfile?.lastName}{" "}
                    </h6>
                    <div className="name-flag-wrpr">
                      {list?.Languages?.length > 0 &&
                        list?.Languages?.map((_lang, index) => {
                          return (
                            <ReactFlagComponent
                              code={_lang?.mobileCode}
                              height="16"
                              key={index}
                            />
                          );
                        })}
                    </div>
                  </div>
                  <p className="text-grey-5 comp-desc">
                    {list?.UserProfile?.gender === 1
                      ? getLanguages(checklanguage, "male")
                      : getLanguages(checklanguage, "female")}
                    ·{" "}
                    <span className="text-grey-1">
                      {list.userType == "companion"
                        ? getLanguages(checklanguage, "professional")
                        : getLanguages(checklanguage, "student")}
                    </span>
                  </p>
                  {/* <div className="rating-stars">
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/empty-star.svg" className="empty" />
              </div> */}
                  {hideExtraInfo ? (
                    <></>
                  ) : (
                    <div className="d-flex align-center justify-center btn-tertiary-wrpr">
                      <button
                        className="btn-tertiary"
                        id="profile"
                        onClick={() =>
                          getVitaDetails(list?.id, 1, list?.userType)
                        }
                      >
                        <span className="icon user-icon"></span>
                        {getLanguages(checklanguage, "vita")}
                      </button>
                      {list?.UserProfile?.video != null &&
                        list.userType != "student" && (
                          <button
                            className="btn-tertiary"
                            onClick={(e) =>
                              handleIntroductoryVideo(
                                e,
                                list?.UserProfile?.video,
                                list?.id
                              )
                            }
                          >
                            <span className="icon video-icon"></span>
                            {getLanguages(checklanguage, "introductoryVideo")}
                          </button>
                        )}
                    </div>
                  )}
                </div>

                {isModal ? null : (
                  <div className="comp-card-button-wrpr">
                    {showCompanionCost == "step1" ? (
                      <></>
                    ) : (
                      <div className="d-flex align-center justify-between">
                        {!query?.tabstatus?.includes("students") &&
                          list?.UserProfile?.experience != null && (
                            <p className="text-grey-6">
                              {getLanguages(checklanguage, "entryDate")} :{" "}
                              <span className="fw600 text-grey-1">
                                {moment(list?.UserProfile?.experience).format(
                                  "ll"
                                )}
                              </span>
                            </p>
                          )}
                        <p className="h6 text-grey-6">
                          <span
                            className="fw600 text-grey-1"
                            style={{ display: "inline-block" }}
                          >
                            <Currency locale={checklanguage} currency={"EUR"}>
                              {list?.UserProfile?.meetingPrice != null
                                ? list?.UserProfile?.meetingPrice
                                : 0}
                            </Currency>
                          </span>
                          /{getLanguages(checklanguage, "min")}
                        </p>
                      </div>
                    )}

                    {showSelectButton ? (
                      <div className="d-flex align-center justify-between">
                        <p className="h6 text-grey-6">
                          <button className="btn-accent sm w-100">
                            {soulWriting == 1 &&
                            CompanionId != "" &&
                            list?.id === CompanionId
                              ? getLanguages(checklanguage, "selected")
                              : getLanguages(checklanguage, "select")}
                          </button>
                        </p>
                      </div>
                    ) : (
                      <></>
                    )}

                    {soulWriting == 1 ? null : (
                      <>
                        <button
                          className="btn-accent w-100"
                          id="schedule_btn"
                          onClick={() => {
                            if (tab == "students" && !allowStudentMeetings) {
                              setShowMaxLimitModal(true);
                            } else {
                              getTimeSlot(list?.id, 2);
                              getVitaDetails(list?.id, 2);
                            }
                          }}
                        >
                          {" "}
                          <span className="icon video-chat"></span>{" "}
                          {getLanguages(checklanguage, "scheduleOneOnOneCall")}
                        </button>
                        {list?.soulwritingProductId ? (
                          <button
                            className="btn-accent w-100"
                            onClick={() => {
                              if (tab == "students" && !allowStudentMeetings) {
                                setShowMaxLimitModal(true);
                              } else {
                                getVitaDetails(list?.id, 3);
                              }
                            }}
                          >
                            {" "}
                            <span className="icon soul-icon-white"></span>{" "}
                            {getLanguages(
                              checklanguage,
                              "scheduleOneOnOneSoulwritingCall"
                            )}
                          </button>
                        ) : (
                          <></>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
              {/* ) } */}
            </>
          );
        })
      ) : (
        <div
          style={{ margin: "auto", marginTop: "200px" }}
          className="text-center"
        >
          <h3 className="h3 text-dark-grey fw500">
            {" "}
            {getLanguages(checklanguage, "noRecordAvailable")}
          </h3>
        </div>
      )}
      {isModalShow === 1 ? (
        <CompanionVitaModal
          setModalOpen={setModalOpen}
          show={isModalShow == 1}
          onHide={() => {
            setIsModalShow(0);
            setModalOpen(false);
          }}
          setIsModalShow={setIsModalShow}
          onHideVimeo={() => setvimeoOpen(false)}
          setvimeoOpen={setvimeoOpen}
          getVitaDetail={getVitaDetail}
          setgetVitaDetail={setgetVitaDetail}
          setVimeoSrc={setVimeoSrc}
          setVideoId={setVideoId}
          isUserTypeStudent={isUserTypeStudent}
        />
      ) : isModalShow === 2 ? (
        <CompanionScheduleModal
          setModalOpen={setModalOpen}
          show={isModalShow == 2}
          errorModal={errorModal}
          hideErrorModal={hideErrorModal}
          getVitaDetail={getVitaDetail}
          getTimeSlotData={getTimeSlotData}
          queryID={queryID}
          MeetingSlot={MeetingSlot}
          CompanionId={CompanionId}
          setErrorModal={setErrorModal}
          setmeetingPopupSuccess={setmeetingPopupSuccess}
          onHide={() => {
            setIsModalShow(0);
            setModalOpen(false);
          }}
        />
      ) : isModalShow === 3 ? (
        <SoulWritingModal
          callingFrom="companion_card"
          setModalOpen={setModalOpen}
          show={isModalShow == 3}
          getVitaDetail={getVitaDetail}
          onHide={() => {
            setIsModalShow(0);
            setModalOpen(false);
          }}
        />
      ) : null}
      {vimeoOpen === 1 && (
        <ReactVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (videId != "") {
              setvimeoOpen(0);
            } else {
              setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
        />
      )}
      {showMaxLimitModal && (
        <StudentMeetingsExhaustedModal
          show={showMaxLimitModal}
          onHide={setShowMaxLimitModal}
        />
      )}
      {isImageOpen && (
        <div className="modal-overlay" onClick={() => {
          setIsImageOpen(false);
          setModalOpen(false);
          }}>
          <img
            src={profilePic}
            alt="Enlarged Profile"
            className="modal-image"
            onClick={(e) => e.stopPropagation()} // Prevent modal from closing when clicking the image
          />
        </div>
      )}
    </>
  );
}
export default CompanionCard;
