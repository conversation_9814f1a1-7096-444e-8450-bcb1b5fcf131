import moment from "moment";
import ReactFlagComponent from "../ReactFlagComponent";
import { checklanguage, getLanguages } from "../../../constant";
import { Currency } from "react-intl-number-format";
import Image from "next/image";
import { useState } from "react";

function CompanionDetailCard({
  outerDivClass,
  modalType,
  currentStep,
  totalStep,
  getVitaDetail,
  bookTimeSlot,
}) {
  const [isImageOpen, setIsImageOpen] = useState(false);
  const [profilePic, setProfilePic] = useState(null);
  return (
    <>
      <div
        className={`d-flex align-center profile-card ${
          outerDivClass ? outerDivClass : ""
        }`}
      >
        {getVitaDetail ? (
          <div className="comp-img">
            <Image
              style={{cursor: "pointer"}}
              src={
                getVitaDetail?.UserProfile?.attachment?.path
                  ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${getVitaDetail?.UserProfile?.attachment?.path}`
                  : "/images/userplace_holder.png"
              }
              height={80}
              width={80}
              alt=""
              className="cover-img"
              onClick = {() => {
                setProfilePic(getVitaDetail?.UserProfile?.attachment?.path
                  ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${getVitaDetail?.UserProfile?.attachment?.path}`
                  : "/images/userplace_holder.png");
                setIsImageOpen(true)
              }}
            />
          </div>
        ) : (
          <></>
        )}

        <div className="text-left companion-details">
          <div className="d-flex align-center justify-center comp-name-wrpr">
            <h6 className="h6 fw600 comp-name">
              {getVitaDetail?.UserProfile?.firstName}{" "}
              {getVitaDetail?.UserProfile?.lastName}
            </h6>
            <div className="name-flag-wrpr">
              {getVitaDetail?.Languages?.length > 0 &&
                getVitaDetail?.Languages?.map((_lang, index) => {
                  return (
                    <ReactFlagComponent
                      code={_lang?.mobileCode}
                      height="16"
                      key={index}
                    />
                  );
                })}
            </div>
          </div>
          <p className="text-grey-5 comp-desc">
            {getVitaDetail ? (
              <>
                {getVitaDetail?.UserProfile?.gender === 1
                  ? getLanguages(checklanguage, "male")
                  : getLanguages(checklanguage, "female")}{" "}
                ·{" "}
                <span className="text-grey-1">
                  {getVitaDetail?.Roles?.[0]?.name == "companion"
                    ? getLanguages(checklanguage, "professional")
                    : getLanguages(checklanguage, "student")}
                </span>
              </>
            ) : (
              <></>
            )}
          </p>
          {getVitaDetail ? (
            <div className="rating-stars">
              {/* <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/filled-star.svg" className="filled" />
                <img src="/images/empty-star.svg" className="empty" /> */}
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>

      {modalType == "schedule" || modalType == "soul" ? (
        <div className="d-flex align-center justify-end w-50 appt-date-price">
          {currentStep == totalStep && modalType === "schedule" ? (
            <div className="appt-date">
              <p className="fw500 text-grey-5 label">
                {getLanguages(checklanguage, "chosenDateTime")}:
              </p>
              <h6 className="h6 fw600 text-black">
                {localStorage.getItem("language") == "en"
                  ? moment(bookTimeSlot?.startDate)?.format(
                      "MMMM D, YYYY & HH:mm"
                    )
                  : moment(bookTimeSlot?.startDate)?.format(
                      "D. MMM YYYY [um] HH:mm"
                    )}
              </h6>
            </div>
          ) : null}

          <div className="appt-time">
            <p className="fw500 text-grey-5 label text-right">
              {modalType === "schedule"
                ? getLanguages(checklanguage, "price")
                : getLanguages(checklanguage, "dateOfOrigin")}
              :
            </p>

            {modalType === "schedule" ? (
              getVitaDetail?.UserProfile?.meetingPrice != null && (
                <h6 className="h6 fw600 text-black" style={{ display: "flex" }}>
                  <Currency locale={checklanguage} currency={"EUR"}>
                    {getVitaDetail?.UserProfile?.meetingPrice != null
                      ? getVitaDetail?.UserProfile?.meetingPrice
                      : 0}
                  </Currency>
                  {/* €{getVitaDetail?.UserProfile?.meetingPrice} */}
                  <span className="text-grey-6">
                    /{getLanguages(checklanguage, "min")}
                  </span>
                </h6>
              )
            ) : (
              <h6 className="h6 fw600 text-black">14 Oct, 2022 </h6>
            )}
          </div>
        </div>
      ) : null}
      {isImageOpen && (
        <div className="modal-overlay" onClick={() => setIsImageOpen(false)}>
          <img
            src={profilePic}
            alt="Enlarged Profile"
            className="modal-image"
            onClick={(e) => e.stopPropagation()} // Prevent modal from closing when clicking the image
          />
        </div>
      )}
    </>
  );
}

export default CompanionDetailCard;
