import React, { useEffect, useState } from "react";
import InputField from "../FormFields/InputField";
import { useForm } from "react-hook-form";
import { checklanguage, getLanguages } from "../../constant";
import RatingFormField from "../FormFields/RatingField";
import { postReviews, GET_REVIEW_lIST } from "../../redux/action/kuby-courses";
import { useSelector, useDispatch } from "react-redux";
import RatingStar from "./Rating";
const ReactStarCompnonent = ({
  ratingComment,
  SubVideosLesson,
  onHide,
  getAllVideoReviewss,
  setSubVideosLesson,
  setOverAllRating,
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
    getValues,
    clearErrors,
  } = useForm();
  const state = useSelector((state) => state.courses);
  const { reviewRatingList, singleReview } = state;
  const dispatch = useDispatch();
  const [IsFetching, setIsFetching] = useState(null);
  const [Toggle, setToggle] = useState(null);
  useEffect(() => {
    if (Object.keys(singleReview)?.length > 0) {
      setValue("rating", singleReview?.rating);
      setValue("comment", singleReview?.comment);
      setToggle(false);
      clearErrors();
    } else {
      clearErrors();
      setToggle(true);
      setValue("rating", "");
      setValue("comment", "");
    }
  }, [singleReview]);

  const onSubmit = async (formvalues) => {
    setIsFetching(true);
    formvalues.videoId = SubVideosLesson?.id;
    try {
      let response = await postReviews(formvalues);
      if (ratingComment == 2) {
        onHide();
      }
      let upDatePayload = { ...reviewRatingList };
      let Index = upDatePayload?.reviewList?.findIndex(
        (item) => item?.id == response?.data?.responseData?.id
      );
      if (Index > -1) {
        upDatePayload.reviewList[Index] = response?.data?.responseData;
      }
      dispatch({
        type: GET_REVIEW_lIST,
        payload: upDatePayload,
      });
      getAllVideoReviewss(SubVideosLesson?.id);
      setSubVideosLesson((prev) => {
        let updateObject = { ...SubVideosLesson };
        updateObject.userReview.rating = response?.data?.responseData?.rating;
        return updateObject;
      });
      setToggle(false);
      setIsFetching(false);
      setOverAllRating(2);
    } catch (err) {
      setIsFetching(false);
    }
  };
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="rate-comment-sec">
          {Object.keys(singleReview)?.length > 0 && (
            <div
              className="edit-comment-wrpr"
              onClick={() => setToggle(!Toggle)}
            >
              <span className="icon edit-icon-green"></span>
            </div>
          )}
          <div className="rating-section">
            <span className="f-label rate">
              {getLanguages(checklanguage, "rateYourExperience")}
            </span>
            <div className="rating-star-wrpr-outer">
              <div className="d-flex align-center">
                {Toggle ? (
                  <RatingFormField
                    control={control}
                    name="rating"
                    type="text"
                    rules={{
                      required: {
                        value: true,
                        message: getLanguages(checklanguage, "ratingRequired"),
                      },
                    }}
                  />
                ) : (
                  <RatingStar
                    count={singleReview?.rating}
                    iconsCount={5}
                    className={"rating-star-wrpr"}
                  />
                )}

                <p className="text-grey-6 fw500 star-count">
                  {getLanguages(checklanguage, "stars")}
                </p>
              </div>
              {errors?.rating?.type && errors?.rating?.message !== "" ? (
                <p className="star_error">
                  <span className="icon info-icon"></span>
                  {errors?.rating?.message}
                </p>
              ) : null}
            </div>
          </div>
          <div className="comment-section">
            {Toggle ? (
              <>
                <span className="f-label rate">
                  {getLanguages(checklanguage, "addComment")} :
                </span>
                <div
                  className={`${
                    ratingComment != 1 ? "" : "d-flex"
                  }  w-100 add-comment-wrpr`}
                >
                  <InputField
                    control={control}
                    autoComplete="off"
                    name="comment"
                    type="text"
                    className="form-control"
                    inputClassName="f-in w-100 right-icon"
                    rules={{
                      required: {
                        value: true,
                        message:
                          ratingComment === 1
                            ? getLanguages(checklanguage, "commentRequired")
                            : getLanguages(checklanguage, "reviewRequired"),
                      },
                    }}
                  />
                  <button
                    type="submit"
                    className={` btn-accent  ${
                      ratingComment != 1 ? "btn-margin" : ""
                    } ${IsFetching && "btn-loader disabled"}`}
                  >
                    {getLanguages(checklanguage, "addComment")}
                  </button>
                </div>
              </>
            ) : (
              <>
                <span className="f-label rate">
                  {getLanguages(checklanguage, "yourComment")}
                </span>
                <p className="text-dark-grey font-inter">
                  {singleReview?.comment}
                </p>
              </>
            )}
          </div>
        </div>
      </form>
    </>
  );
};

export default ReactStarCompnonent;
