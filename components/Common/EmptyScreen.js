import { useRouter } from 'next/router'
import React from 'react'
import { getLanguages, checklanguage } from '../../constant'

const EmptyScreen = ({ productType }) => {
    const router = useRouter()
    return (
        <div className='d-flex align-center empty-screen justify-center thanku-wrpr'>
            <div className='text-center thanku-inner'>
                {productType === 2 ? 
                <div className='wishlist-img'>
                    <img src="/images/wishlist.png" />
                </div> :productType === 1 ?
                    <div className='wishlist-img'>
                    
                    </div> 
                    :
                    productType === 3 ?
                    <div className='cart_icon-img'>
                       <img src="/images/cart_icon.png" />
                    </div>  : <></>
                    }
                <h3 className='h3 text-dark-grey fw500'>{productType === 1 ? 'No item available' :productType === 2 ?  "Your wishlist is empty" : productType === 3 ? getLanguages(checklanguage, 'emptyCartMessage'): null}</h3>
                <p className='p text-grey-5'>{productType === 3 ? getLanguages(checklanguage, 'emptyCartMessageDetail')
                    : productType === 2 ?  "Looks like you have not added anything to you wishlist. Explore more and shortlist some items" : null }
                </p>
                {(productType === 2 ||  productType === 3 ) && <button onClick={() => router.push('/dashboard/shop')} className='btn-accent'>{getLanguages(checklanguage, 'returnToKubyShop')}</button>}
            </div>
        </div>
    )
}

export default EmptyScreen