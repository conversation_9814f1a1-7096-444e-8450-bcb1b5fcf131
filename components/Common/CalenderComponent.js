import FullCalendar from "@fullcalendar/react";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import dayGridPlugin from "@fullcalendar/daygrid";

// import { Tooltip } from "bootstrap";

import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import moment from "moment";
import CalenderMeetingInfoModal from "./CalenderMeetingInfoModal";
import _, { initial } from "lodash";
import { checklanguage, getLanguages } from "../../constant";

const CalendarComponent = ({
  initialDate,
  appointments,
  setEndDate,
  setStartDate,
  meetingPopupSuccess,
  setmeetingPopupSuccess,
  setsuccessMeetingType,
  successMeetingType,
}) => {
  // local variables
  const [events, setAllEvents] = useState([]);
  const [currentEvent, setCurrentEvent] = useState(null);
  const [isAppointmentModalShow, setIsAppointmentModalShow] = useState(false);
  const [currentGrid, setCurrentGrid] = useState("timeGridDay");
  const [eventClicked, setEventClicked] = useState(false);
  const [clickedEventInfo, setClickedEventInfo] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showEventInfo, setshowEventInfo] = useState({});

  // use hooks
  const router = useRouter();

  useEffect(() => {
    let day = document.querySelector(".fc-timeGridDay-button");
    let week = document.querySelector(".fc-timeGridWeek-button");
    let month = document.querySelector(".fc-dayGridMonth-button");
    let today = document.querySelector(".fc-today-button");
    day.innerText = getLanguages(checklanguage, "day");
    week.innerText = getLanguages(checklanguage, "week");
    month.innerText = getLanguages(checklanguage, "month");
    today.innerText = getLanguages(checklanguage, "today");
  }, []);

  const renderEventContent = (eventInfo) => {
    const { title, start, end } = eventInfo?.event;

    return (
      <div className={`d-flex align-center justify-between meeting-card`}>
        <p className="mb-0 p-name text-dark">
          <span className="meeting-icon"></span>
          <span className="text-dark-grey fw500 meeting-time">
            {" "}
            {localStorage.getItem("language") == "en"
              ? moment(start)?.format("hh:mm a")
              : moment(start)?.locale("de").format("HH:mm [Uhr]")}
          </span>
          <span className="text-grey-5 fw500 meeting-name">
            {getLanguages(checklanguage, `${title}`)}
          </span>
        </p>
      </div>
    );
  };

  // const handleMouseEnter = (info) => {
  //   const rect = info.el.getBoundingClientRect();
  //   info.el.parentNode.classList.add("active-date");
  //   let tooltip = document.querySelector("#appointment-toolip");
  //   if (!tooltip) {
  //     tooltip = document.createElement("div");
  //     document.body.appendChild(tooltip);
  //   }
  //   tooltip.className = `calender-tootip`;
  //   tooltip.style.top = `${rect.top + 78}px`;
  //   tooltip.style.left = `${rect.left + 10}px`;
  //   tooltip.style.position = "absolute";
  //   tooltip.id = "appointment-toolip";
  //   tooltip.innerHTML = `
  //   <div class="calender-tooltip" id="appointment-tooltip">
  //   <div class="d-flex align-center justify-between">
  //     <p class="text-gray-800 time-wrpr">
  //       <span class="time-icon"></span>
  //       ${moment(info?.event?.extendedProps?.startDate).format("hh:mm a")}
  //     </p>
  //     <p class="text-primary fw600 tooltip-date">${
  //       moment(info?.event?.extendedProps?.startDate).isSame(moment(), "day")
  //         ? "Today"
  //         : ""
  //     }</p>
  //   </div>
  //   <div class="patient-details-wrpr d-flex align-center">
  //     <div class="patient-img-blk">
  //       <figure class="mb-0 p-img-wrpr sm">
  //         <img
  //           src=${info?.event?.extendedProps?.petImg}
  //           alt="patient-image"
  //           class="img-fluid p-img"
  //         />
  //       </figure>
  //     </div>
  //     <div class="patient-details">
  //       <p class="fw700 p-name text-white">${
  //         info?.event?.extendedProps?.name
  //       }</p>
  //       <p class="p-desc text-gray-800">
  //       ${info?.event?.extendedProps?.species}
  //       ${info?.event?.extendedProps?.breed && ` · `}
  //       ${info?.event?.extendedProps?.breed}
  //       ${info?.event?.extendedProps?.gender && ` · `}
  //       ${info?.event?.extendedProps?.gender}
  //       ${info?.event?.extendedProps?.age && ` · `}
  //      ${moment(info?.event?.extendedProps?.age).format("DD/MM/YYYY")}
  //       </p>
  //       <p class="disease-type">${info?.event?.extendedProps?.diseases}</p>
  //     </div>
  //   </div>
  //   </div>
  //   </div>`;
  // };

  // const handleMouseLeave = (info) => {
  //   info.el.parentNode.classList.remove("active-date");
  //   document.querySelector("#appointment-toolip")?.remove();
  // };

  // useEffect(() => {
  //   if (eventClicked) {
  //     handleMouseLeave(clickedEventInfo);
  //     router.push(
  //       `/dashboard/appointments/detail/${clickedEventInfo?.event?.extendedProps?.id}`
  //     );
  //   }
  // }, [eventClicked]);

  // use hooks
  useEffect(() => {
    let allEvents = [];
    if (appointments?.length > 0) {
      for (let i = 0; i < appointments?.length; i++) {
        allEvents.push({
          title: appointments[i].title,
          start: appointments[i].startDate,
          end: appointments[i].endDate,
          allDay: appointments[i].allDay,
          extendedProps: {
            Participants: appointments[i].Participants,
            eventId: appointments[i]?.id,
            description: appointments[i].description,
            zoomLink: appointments[i].zoomLink,
          },
        });
      }
    }
    setAllEvents(allEvents);
  }, [appointments]);
  var datee = new Date();
  var newDate = datee.setDate(datee.getDate() + 5);
  // Add five days to current date

  return (
    <div>
      <FullCalendar
        locale={checklanguage}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView={"timeGridDay"}
        // to show only 2 events in month grid and rest will be show in popup
        dayMaxEvents={2}
        // to prevent overlap in events of same time inside day and week grid
        eventMaxStack={1}
        // to prevent events from overlapping
        eventMinHeight={20}
        height={"auto"}
        slotEventOverlap={false}
        headerToolbar={{
          left: "today,title,prev,next",
          right: "timeGridDay,timeGridWeek,dayGridMonth",
        }}
        eventContent={renderEventContent}
        // nowIndicator={true}
        // scrollTime= {new Date()}
        eventClick={(info) => {
          setshowEventInfo(info?.event);
          setIsModalOpen(true);
          //  setEventClicked(true);
        }}
        // mouse hover or leave events
        // eventMouseEnter={handleMouseEnter}
        // eventMouseLeave={handleMouseLeave}
        datesSet={(arg) => {
          // setCurrentGrid(a e);
          setStartDate(arg?.start);
          setEndDate(arg?.end);
        }}
        initialDate={initialDate || null}
        events={events}
      />

      {isModalOpen && (
        <CalenderMeetingInfoModal
          show={isModalOpen}
          showEventInfo={showEventInfo}
          meetingPopupSuccess={meetingPopupSuccess}
          setmeetingPopupSuccess={setmeetingPopupSuccess}
          setsuccessMeetingType={setsuccessMeetingType}
          successMeetingType={successMeetingType}
          onHide={() => {
            setIsModalOpen(false);
          }}
        />
      )}
    </div>
  );
};

export default CalendarComponent;
