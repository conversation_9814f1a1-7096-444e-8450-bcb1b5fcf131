import { useRouter } from "next/router";
import React from "react";
import { checklanguage, getLanguages } from "../../constant";

const SimilarProduct = ({ similarProduct }) => {
  const router = useRouter();
  return (
    <div className="similar-prod-wrpr">
      <h3 className="text-black-1 fw600 title">
        {getLanguages(checklanguage, "buySuggestion")}
      </h3>
      <div className="d-flex similar-prod-row relatedProduct">
        {similarProduct?.map((product, i) => (
          <div
            className="similar-prod"
            key={i}
            onClick={() =>
              router.push(`/dashboard/shop/product-detail?pid=${product?.id}`)
            }
          >
            <div className="prod-img">
              <img
                src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${product?.attachment?.path}`}
                alt=""
                className="cover-img"
              />
            </div>
            <h6 className="h6 text-grey-3 fw500 prod-title">
              {product?.content?.name}
            </h6>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimilarProduct;
