import moment from "moment";
import { useEffect, useState } from "react";
import _ from "lodash";
import CompanionScheduleModal from "./Modals/CompanionScheduleModal";
import {
  getEventsDetailById,
  getTimeSlots,
} from "../../redux/action/kuby-companion";
import {
  checkRescheduleTime,
  checkCancelTime,
  checklanguage,
  getLanguages,
} from "../../constant";
import CancelMeetingModal from "./Modals/CancelMeetingModal";
function CalenderMeetingInfoModal({
  show,
  onHide,
  showEventInfo,
  meetingPopupSuccess,
  setmeetingPopupSuccess,
  setsuccessMeetingType,
  successMeetingType,
  calledFrom,
}) {
  let companionName = "";
  if (calledFrom == "listing") {
    companionName = _.find(showEventInfo?.Participants, ["role", "companion"]);
  } else {
    companionName = _.find(showEventInfo?.extendedProps?.Participants, [
      "role",
      "companion",
    ]);
  }

  const [isModalShow, setIsModalShow] = useState(0);
  const [getTimeSlotData, setgetTimeSlotData] = useState(null);
  const [MeetingSlot, setMeetingSlot] = useState(null);
  const [getVitaDetail, setgetVitaDetail] = useState({});
  const [isSubmitting, setisSubmitting] = useState(false);
  const [isCancelModalOpen, setisCancelModalOpen] = useState(false);
  const [canecelMeetingId, setCancelMeetingId] = useState("");
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };
  const getVitaDetails = async (id, type) => {
    setisSubmitting(true);
    try {
      let response = await getEventsDetailById({ id: id });
      let findCompanionId = "";
      if (calledFrom == "listing") {
        findCompanionId = _.find(showEventInfo?.Participants, [
          "role",
          "companion",
        ]);
      } else {
        findCompanionId = _.find(showEventInfo?.extendedProps?.Participants, [
          "role",
          "companion",
        ]);
      }
      
      let data = {
        UserProfile: {
          attachment: {
            path: "",
          },
          gender: "",
          firstName: " ",
          lastName: "",
          meetingPrice: "",
        },
      };
      
      data.UserProfile.attachment.path = findCompanionId?.User?.profilePhotoUrl;
      data.UserProfile.firstName = findCompanionId?.User?.firstName;
      data.UserProfile.lastName = findCompanionId?.User?.lastName;
      data.description = response?.data?.responseData?.description;
      data.UserProfile.userId = findCompanionId?.userId;
      (data.userId = findCompanionId?.userId),
        (data.Languages = findCompanionId?.User?.languages);
      data.UserProfile.gender = findCompanionId?.User?.gender;
      data.UserProfile.meetingPrice = findCompanionId?.User?.meetingPrice;
      data.UserProfile.scheduleTime = findCompanionId?.User?.scheduleTime;
      data.eventId = id;
      setgetVitaDetail(data);
      await getTimeSlot(
        findCompanionId?.userId,
        response?.data?.responseData?.startDate,
        response?.data?.responseData?.endDate
      );
      setIsModalShow(type);
      setisSubmitting(false);
      setsuccessMeetingType(1);
    } catch (err) {}
  };
  // const getTimeSlot = async (id) => {
  //   setgetTimeSlotData(null);
  //   try {
  //     let response = await getTimeSlots({
  //       companionId: id,
  //       startDate: moment().startOf("day").utc().toDate(),
  //       endDate: moment().add(30, "days").endOf("day").utc().toDate(),
  //     });
  //     setgetTimeSlotData(response?.data?.responseData?.workingHours);
  //     setMeetingSlot(response?.data?.responseData?.meetings);
  //   } catch (err) {}
  // };
  const getTimeSlot = async (id, type) => {
      setgetTimeSlotData(null);
      //setCompanionId(id);
      
      try {
        let response = await getTimeSlots({
          companionId: id,
          startDate: moment().startOf("day").utc().toDate(),
          endDate: moment().add(30, "days").endOf("day").utc().toDate(),
        });
        setgetTimeSlotData(response?.data?.responseData);
        //setMeetingSlot(response?.data?.responseData?.meetings);
        let data = {};
        data.id = id;
        data.type = type;
        setQueryId(data);
      } catch (err) {}
    };
  return (
    <>
      <div className={`modal meeting-info-modal ${show ? "show" : ""}`}>
        <div className="calendar-overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-header">
            <h4 className="h4 fw500 text-dark-grey">{getLanguages(checklanguage, "one_to_one_meeting")}</h4>
            <div className="d-flex calender_meeting_schedule">
              <img src="/images/calendar.svg" />
              <p className="fw500 h6 text-grey-6">
                {/* {moment(
                  calledFrom == "listing"
                    ? showEventInfo?.startDate
                    : showEventInfo?.start
                ).format("MMMM D, YYYY & h:mm a")} */}
                {localStorage.getItem("language") == "en"
                  ? moment(
                      calledFrom == "listing"
                        ? showEventInfo?.startDate
                        : showEventInfo?.start
                    )?.format("MMMM D, YYYY & HH:mm")
                  : moment(
                      calledFrom == "listing"
                        ? showEventInfo?.startDate
                        : showEventInfo?.start
                    )?.format("D. MMM YYYY [um] HH:mm")}
              </p>
            </div>
            <a
              href="javascript:;"
              className="close-btn schedule_close_btn"
              id="closeModal"
              onClick={() => onHide()}
            >
              <span className="icon close-icon"></span>
            </a>
          </div>
          <div className="modal-body">
            <p className="calendar_status">
              {getLanguages(checklanguage, "status")} :{" "}
              <span className="meeting-status">
                {getLanguages(checklanguage, "booked")}
              </span>
            </p>
            <div></div>
            <p className="cal-dsc">
              {getLanguages(checklanguage, "oneOnOneMeetingShortText")}
            </p>
            <div className="schedule-appointment-wrpr">
              <p className="label fw500 text-grey-5">
                {getLanguages(checklanguage, "myCompanion")}:
              </p>
              <div className="d-flex align-center justify-between modal-prof-card">
                <div className={`d-flex align-center profile-card`}>
                  <div className="comp-img">
                    <img
                      src={
                        companionName?.User?.profilePhotoUrl != null
                          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${companionName?.User?.profilePhotoUrl}`
                          : "/images/userplace_holder.png"
                      }
                      alt=""
                      className="cover-img"
                    />
                  </div>
                  <div className="text-left companion-details">
                    <div className="d-flex align-center justify-center comp-name-wrpr">
                      <h6 className="h6 fw600 comp-name">
                        {companionName?.User?.firstName}{" "}
                        {companionName?.User?.lastName}
                      </h6>
                    </div>
                  </div>
                </div>
              </div>
              <p className="label fw500 text-grey-5">
                {getLanguages(checklanguage, "reasonLabel")}:
              </p>
              <p>
                {calledFrom == "listing"
                  ? showEventInfo?.description
                  : showEventInfo?.extendedProps?.description}
              </p>
              {calledFrom == "listing" ? (
                <>
                  {showEventInfo?.zoomLink != null && (
                    <p className="label fw500 text-grey-5">
                      {getLanguages(checklanguage, "meetingLink")}:{" "}
                      <a
                        rel="noreferrer"
                        style={{ marginLeft: "10px" }}
                        href={showEventInfo?.zoomLink}
                        target="_blank"
                      >
                        {showEventInfo?.zoomLink}
                      </a>
                    </p>
                  )}
                </>
              ) : (
                <>
                  {showEventInfo?.extendedProps?.zoomLink != null && (
                    <p className="label fw500 text-grey-5">
                      <span style={{wordWrap: "normal", whiteSpace: "nowrap"}}>{getLanguages(checklanguage, "meetingLink")}</span>:{" "}
                      <a
                        rel="noreferrer"
                        style={{ marginLeft: "10px" }}
                        href={showEventInfo?.extendedProps?.zoomLink}
                        target="_blank"
                      >
                        {showEventInfo?.extendedProps?.zoomLink}
                      </a>
                    </p>
                  )}
                </>
              )}
            </div>
          </div>
          <div className="d-flex align-center modal-btn-wrpr">
            <button
              className={`btn-secondary sm w-25 ${
                checkCancelTime(
                  calledFrom == "listing"
                    ? showEventInfo?.startDate
                    : showEventInfo?.start,
                  companionName
                ) && "disabled"
              }`}
              onClick={() => {
                setisCancelModalOpen(true);
                setCancelMeetingId(
                  calledFrom == "listing"
                    ? showEventInfo?.id
                    : showEventInfo?.extendedProps?.eventId
                );
              }}
            >
              {getLanguages(checklanguage, "cancelAMeeting")}
            </button>
            <button
              className={`${isSubmitting && "btn-loader"} btn-accent sm w-25 ${
                checkRescheduleTime(
                  calledFrom == "listing"
                    ? showEventInfo?.startDate
                    : showEventInfo?.start,
                  companionName
                ) && "disabled"
              }`}
              onClick={() =>
                getVitaDetails(
                  calledFrom == "listing"
                    ? showEventInfo?.id
                    : showEventInfo?.extendedProps?.eventId,
                  2
                )
              }
            >
              {getLanguages(checklanguage, "reschedule")}
            </button>
          </div>
        </div>
      </div>
      {isModalShow === 2 && (
        <CompanionScheduleModal
          show={isModalShow == 2}
          getVitaDetail={getVitaDetail}
          getTimeSlotData={getTimeSlotData}
          setmeetingPopupSuccess={setmeetingPopupSuccess}
          MeetingSlot={MeetingSlot}
          resccheduleMeeting={successMeetingType}
          CompanionId={showEventInfo?.extendedProps?.eventId}
          onHide={() => {
            setIsModalShow(0);
            onHide();
          }}
        />
      )}
      {isCancelModalOpen && (
        <CancelMeetingModal
          show={isCancelModalOpen}
          onHide={() => {
            setisCancelModalOpen(false);
            onHide();
          }}
          id={canecelMeetingId}
          setmeetingPopupSuccess={setmeetingPopupSuccess}
          setsuccessMeetingType={setsuccessMeetingType}
          setisCancelModalOpen={setisCancelModalOpen}
        />
      )}
    </>
  );
}

export default CalenderMeetingInfoModal;
