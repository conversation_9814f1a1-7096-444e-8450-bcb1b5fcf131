import { useCookie } from "next-cookie";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { getTopic } from "../../redux/action/kuby-courses";
import SeminarSkeleton from "./SkeletonLoader/SeminarSkeleton";
import { getLanguages, checklanguage } from "../../constant";
const Seminar = () => {
  const cookies = useCookie();
  const router = useRouter();
  const [ListCourses, setListCourses] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  useEffect(() => {
    getTopicCourses();
  }, []);
  const getTopicCourses = async () => {
    setIsFetching(true);
    try {
      let response = await getTopic();
      setIsFetching(false);
      setListCourses(response?.data?.responseData);
    } catch (err) {}
  };

  const checkRedirection = (seminar) => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      if (!seminar?.isPurchased) {
        if (seminar.salesPageLink) {
          //   router.push(seminar.salesPageLink);
          window.location.href = seminar.salesPageLink;
        } else {
          router.push(
            `/dashboard/self-practice-sales?id=${seminar?.id}&paymentlink=${seminar?.purchaseLink}`
          );
        }
        // window.location = seminar?.purchaseLink;
      } else if (seminar?.isPurchased == 1) {
        router.push(`/dashboard/self-practice-details/${seminar?.id}`);
      } else {
        router.push(`/dashboard/self-practice-sales`);
      }
      router.push(`/dashboard/self-practice-details/${seminar?.id}`);
    } else {
      router.push(
        `/signin?redirectTourl=/dashboard/self-practice-details/${seminar?.id}`
      );
    }
  };
  return (
    <div className="d-flex w-100 comp-reception-row">
      <div className="w-100 companions-wrpr com-row-left companions-wrpr-full-wdith">
        <div className="h-100 w-100 d-flex fd-col align-center justify-center h-100 center-panel-inner">
          <div className="w-100 d-flex align-center justify-between heading-row">
            <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
              {getLanguages(checklanguage, "kubyProducts")}
            </h4>
          </div>
          <div
            id="seminar-products-container"
            className="d-flex w-100 seminar-wrpr reception-card-block  comp_slider"
          >
            {isFetching ? (
              <SeminarSkeleton listsToRender={2} type={1} />
            ) : (
              ListCourses?.Seminars?.topicList?.map((seminar, index) => (
                <>
                  <div
                    className="img-name-card"
                    key={index}
                    onClick={() => checkRedirection(seminar)}
                  >
                    <div className="relative img-card">
                      <img
                        src={
                          seminar?.posterImage?.attachmentUrl != null
                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${seminar?.posterImage?.attachmentUrl}`
                            : "/images/test.jpeg"
                        }
                        alt=""
                        className="img-fluid cover-img"
                      />
                      {seminar?.isPurchased ? (
                        <></>
                      ) : (
                        <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                          <span className="lock"></span>
                        </div>
                      )}
                    </div>
                    <h6 className="h6 text-grey-3">
                      <span className="fw600">{seminar?.name} –</span>{" "}
                      {seminar?.subTitle}
                    </h6>
                  </div>
                </>
              ))
            )}

            {isFetching
              ? null
              : // <SeminarSkeleton listsToRender={3} type={2} />
                ListCourses?.KUBYstudy?.topicList?.map((kubystudy, i) => (
                  <div
                    className="img-name-card"
                    key={i}
                    onClick={() => checkRedirection(kubystudy)}
                  >
                    <div className="relative img-card">
                      <img
                        src={
                          kubystudy?.posterImage?.attachmentUrl != null
                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${kubystudy?.posterImage?.attachmentUrl}`
                            : "/images/test.jpeg"
                        }
                        alt=""
                        className="img-fluid cover-img"
                      />

                      <>
                        {kubystudy?.isPurchased ? (
                          <></>
                        ) : (
                          <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                            <span className="lock"></span>
                          </div>
                        )}
                      </>
                    </div>
                    <h6 className="h6 text-grey-3">
                      <span className="fw600">{kubystudy?.name} –</span>
                      {kubystudy?.subTitle}
                    </h6>
                  </div>
                ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Seminar;
