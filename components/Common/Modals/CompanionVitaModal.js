import CompanionDetailCard from "../Cards/CompanionDetailCard";
import { useEffect, useState } from "react";
import { Number, Currency } from "react-intl-number-format";
import moment from "moment";
import {
  getYearExperience,
  getLanguages,
  checklanguage,
} from "../../../constant";
function CompanionVitaModal({
  hideButtons,
  show,
  onHide,
  setIsModalShow,
  getVitaDetail,
  setvimeoOpen,
  setVimeoSrc,
  setgetVitaDetail,
  setVideoId,
  isUserTypeStudent,
  setModalOpen,
}) {
  
  useEffect(() => {
    //return () => document.removeEventListener("click", hideModal);
    
  }, []);

  const hideModal = (event) => {
    setModalOpen(false);
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };


  return (
    <>
      <div
        className={`modal profile-modal ${show ? "show" : ""}`}
        id="profileModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => {
                  onHide();
                  setgetVitaDetail({});
                }}
              >
                <span className="icon close-icon"></span>
              </a>
              <div className="profile-top">
                <div className="d-flex align-center justify-between profile-info-wrpr">
                  <CompanionDetailCard
                    modalType="vital"
                    getVitaDetail={getVitaDetail}
                  />
                  {getVitaDetail?.UserProfile?.video != null &&
                    !isUserTypeStudent && (
                      <div>
                        <button
                          className="btn-tertiary"
                          onClick={() => {
                            setvimeoOpen(true);
                            onHide();
                            setVimeoSrc(getVitaDetail?.UserProfile?.video);
                            setVideoId("");
                          }}
                        >
                          <span className="icon video-icon"></span>
                          {getLanguages(checklanguage, "introductoryVideo")}
                        </button>
                      </div>
                    )}
                </div>
                <div className="d-flex justify-between profile-details-wrpr">
                  {!isUserTypeStudent && (
                    <p className="d-flex align-center text-grey-6 comp-date">
                      <span className="icon icon-calender"></span>
                      {getLanguages(checklanguage, "entryDate")}:
                      <span className="text-dark-grey fw600">
                        &nbsp;&nbsp;
                        {moment(getVitaDetail?.UserProfile?.experience).format(
                          "ll"
                        )}
                      </span>
                    </p>
                  )}

                  {/*
                    To be used for only spacing
                    */}
                  <p className="d-flex align-center text-grey-6 comp-app">
                    <span
                      className="text-dark-grey fw600"
                      style={{ minWidth: "200px" }}
                    ></span>
                  </p>
                  <p className="d-flex align-center text-grey-6 comp-app">
                    <span
                      className="text-dark-grey fw600"
                      style={{ minWidth: "200px" }}
                    ></span>
                  </p>
                  {/* {getVitaDetail?.UserProfile?.experience != null && <p className="d-flex align-center text-grey-6 comp-experience">
                    <span className="icon icon-exp"></span>
                    {getLanguages(checklanguage, "experience")}:
                    <span className="text-dark-grey fw600">&nbsp;&nbsp;{getYearExperience(getVitaDetail?.UserProfile?.experience)} {getLanguages(checklanguage, "years")}</span>
                  </p>}
                  <p className="d-flex align-center text-grey-6 comp-app">
                    <span className="icon video-chat-green"></span>
                    {getLanguages(checklanguage, 'totalAppoinments')}:
                    <span className="text-dark-grey fw600"> &nbsp;&nbsp;2026</span>
                  </p> */}
                </div>
              </div>
            </div>
            <div className="modal-body">
              <div className="comp-about-wrpr">
                <div className="comp-about">
                  <h6 className="h6 fw500 comp-about-title">
                    {getLanguages(checklanguage, "whyKubytorium")}:
                  </h6>
                  <p
                    className="text-grey-3 font-inter comp-about-desc"
                    dangerouslySetInnerHTML={{
                      __html: getVitaDetail?.UserProfile?.vita,
                    }}
                  ></p>
                </div>
              </div>
            </div>
            <div className="d-flex align-center justify-between modal-footer">
              <div className="price-wrpr">
                <p className="text-grey-5 ">
                  {getLanguages(checklanguage, "price")}:
                </p>
                <p className="fw600 text-black-1" style={{ display: "flex" }}>
                  <Currency locale={checklanguage} currency={"EUR"}>
                    {getVitaDetail?.UserProfile?.meetingPrice != null
                      ? getVitaDetail?.UserProfile?.meetingPrice
                      : 0}
                  </Currency>
                  &nbsp;
                  <span className="text-grey-6">
                    /{getLanguages(checklanguage, "min")}
                  </span>
                </p>

                {/* <p className="fw600 text-black-1">
                  €{" "}
                  {getVitaDetail?.UserProfile?.meetingPrice != null
                    ? getVitaDetail?.UserProfile?.meetingPrice
                    : 0}{" "}
                  <span className="text-grey-6">/Min</span>
                </p> */}
              </div>
              &nbsp;&nbsp;
              {
                hideButtons == false || typeof hideButtons == 'undefined' &&
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-accent sm w-100"
                    onClick={() => {
                      setIsModalShow(2);
                    }}
                  >
                    {" "}
                    <span className="icon video-chat"></span>
                    {getLanguages(checklanguage, "scheduleOneOnOneCall")}
                  </button>
                  <button
                    className="btn-accent sm w-100"
                    onClick={() => {
                      setIsModalShow(3);
                    }}
                  >
                    {" "}
                    <span className="icon soul-icon-white"></span>
                    {getLanguages(checklanguage, "soulwritingVita")}
                  </button>
                </div>
              }

            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default CompanionVitaModal;
