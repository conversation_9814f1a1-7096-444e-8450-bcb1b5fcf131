import React from "react";
import {
  checklanguage,
  getLanguages,
  capitalizeAndReplace,
} from "../../../constant";

const ProtagonistCard = ({
  obj,
  index,
  toggleAccordian,
  activeIndex,
  openEditF<PERSON>,
  deleteProtagonist,
  show<PERSON><PERSON><PERSON><PERSON><PERSON>,
  deletedProtagonist,
}) => {
  let colorStyle = {};
  if (obj?.color?.backgroundColor) {
    colorStyle = {
      background: obj.color.backgroundColor,
      color: obj.color.fontColor,
    };
  }

  return (
    <div className="accordion-item" key={index}>
      <div className="accordion-item-header protagonist-list">
        <div className="d-flex justify-between w-100 protagonist-card">
          <div
            className="d-flex align-center protagonist-details"
            style={{ cursor: "pointer" }}
            onClick={() => {
              toggleAccordian(index);
            }}
          >
            <div className="protagonist-img">
              <h6 className="h6 fw500 person-card" style={colorStyle}>
                {obj?.Acronym === "I"
                  ? getLanguages(checklanguage, "i")
                  : obj?.Acronym}
              </h6>
            </div>
            <div className="">
              <p className="p fw500 text-grey-1">
                {obj?.name === "I"
                  ? getLanguages(checklanguage, "i")
                  : obj?.name}
                {index > 0 && (
                  <>
                    (
                    {obj?.title === 1
                      ? getLanguages(checklanguage, "male")
                      : getLanguages(checklanguage, "female")}
                    )
                  </>
                )}
              </p>
            </div>
          </div>
          <div className="d-flex align-center edit-status-wrpr">
            <button
              className={`${"status-badge"} ${
                activeIndex === index ? "gray" : "green"
              }`}
              onClick={() => {
                toggleAccordian(index);
              }}
            >
              {activeIndex === index
                ? getLanguages(checklanguage, "hideInfo")
                : getLanguages(checklanguage, "moreInfo")}
            </button>
            <div className="d-flex align-center protogonist-action-btns">
              <button
                type="button"
                className="btn edit-btn sm"
                onClick={() => {
                  openEditForm(obj);
                }}
              >
                <span className="icon edit-icon-grey"></span>
              </button>
              {!obj?.isDefault && (
                <button
                  className="btn delete-btn sm"
                  onClick={() => {
                    deleteProtagonist(obj.id);
                  }}
                >
                  <span className="icon delete-icon"></span>
                </button>
              )}
              {showUndoButton && deletedProtagonist.id === obj.id && (
                <button className="btn undo-btn sm" onClick={cancelDelete}>
                  Undo
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="accordion-item-body protagonist-body">
        <div
          className={`${"protogonist-content"} ${
            activeIndex === index ? "active" : ""
          }`}
        >
          <p className="text-grey-1 protogonist-text">
            {getLanguages(checklanguage, "gender")}:{" "}
            <span className="text-grey-6">
              {obj?.title === 1
                ? getLanguages(checklanguage, "male")
                : getLanguages(checklanguage, "female")}
            </span>{" "}
          </p>
          <p className="text-grey-1 protogonist-text">
            {getLanguages(checklanguage, "age")}:{" "}
            <span className="text-grey-6">
              {capitalizeAndReplace(obj?.age)}
            </span>{" "}
          </p>
          {obj?.protogonistObject?.nativeLanguage && (
            <p className="text-grey-1 protogonist-text">
              {getLanguages(checklanguage, "nativeLang")}:{" "}
              <span className="text-grey-6">
                {capitalizeAndReplace(obj?.protogonistObject?.nativeLanguage)}
              </span>{" "}
            </p>
          )}
          {obj?.degreeOfKinship && (
            <p className="text-grey-1 protogonist-text">
              {getLanguages(checklanguage, "conxn")}:{" "}
              <span className="text-grey-6">
                {capitalizeAndReplace(obj?.degreeOfKinship)}
              </span>{" "}
            </p>
          )}
          {obj?.sympethetic && (
            <p className="text-grey-1 protogonist-text">
              {getLanguages(checklanguage, "feeling")}:{" "}
              <span className="text-grey-6">
                {capitalizeAndReplace(obj?.sympethetic)}
              </span>{" "}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProtagonistCard;
