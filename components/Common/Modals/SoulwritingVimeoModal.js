import React, { useEffect, useState, useRef } from "react";
import Vimeo from "@u-wave/react-vimeo";
import { checklanguage, getLanguages } from "../../../constant";
import ProtagonistQuesModal from "./ProtagonistQuesModal";
import VideoSkeleton from "./VideoSkeletonModal";
import ReactPlayer from "react-player";

const SoulwritingVimeoModal = ({
  vimeoSrc,
  vimeoOpen,
  onHideVimeo,
  onHide,
  description,
  openModel,
  setShowProtagonistQues,
  setvimeoOpen,
  category,
}) => {
  const playerRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [vSrc, setVSrc] = useState(vimeoSrc);
  const [showVideo, setShowVideo] = useState(false)
  // const [showProtagonistQues, setShowProtagonistQues] = useState(false);

  // useEffect(() => {
  //   window?.document.addEventListener("click", hideModal);

  //   return () => window?.document.removeEventListener("click", hideModal);
  // }, []);

  // const hideModal = (event) => {
  //   const modal = window?.document.querySelector("#modalOverlay");
  //   if (event.target === modal) {
  //     onHideVimeo();
  //   }
  // };
  const modalRef = useRef(null);
  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      event.stopPropagation();
    }
  };
  useEffect(() => {
    //setTimeout(() => {
      setShowVideo(true);
    //}, 2000);
  }, [])
  useEffect(() => {
    setVSrc(vimeoSrc);
  }, [vimeoSrc])
  useEffect(() => {
    window?.document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window?.document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleProtoganistContClick = () => {
    setvimeoOpen(0);
    setShowProtagonistQues(true);
    setTimeout(() => {
      document.querySelector(`#protagonist-3`)?.scrollIntoView({
        behavior: "instant",
        block: "center",
        inline: "nearest",
      });
    }, 500);
  };
  //alert(vimeoSrc);

  return (
    <>
      <div
        className={`modal video-modal ${vimeoOpen ? "show" : ""}`}
        id="scheduleModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div
          className="modal-content"
          style={{ minHeight: "600px" }}
          ref={modalRef}
        >
          <div className="modal-dialog">
            <div className="video-title">
              <h3>{getLanguages(checklanguage, "watchVideoToCont")}</h3>
            </div>
            <div className="view_video">
              {loading && <VideoSkeleton />}
              {
                showVideo && <Vimeo
                  video={vSrc}
                  width={800}
                  height={"auto"}
                  //autoplay
                  onReady={() => setLoading(false)}
                />
              }

              {/* <ReactPlayer
                    ref={playerRef}
                    url={vimeoSrc}
                    controls={true}
                    width={800}
                    height={"auto"}
                    className='react-player'
                    
                    onReady={() => {
                        setLoading(false)
                        
                    }}
                /> */}
            </div>

            {!loading && (
              <>
                {openModel ? (
                  <div className="continue-wrap">
                    <a
                      href="javascript:;"
                      className="continue-btn btn-accent"
                      id="continue"
                      onClick={handleProtoganistContClick}
                    >
                      <span className="btn-continue">
                        {getLanguages(checklanguage, "continue")}
                      </span>
                    </a>
                  </div>
                ) : (
                  <div className="continue-wrap">
                    <a
                      href="javascript:;"
                      className="continue-btn btn-accent"
                      id="continue"
                      onClick={() => {
                        onHideVimeo();
                        setTimeout(() => {
                          document.querySelector(category)?.scrollIntoView({
                            behavior: "instant",
                            block: "center",
                            inline: "nearest",
                          });
                        }, 500);
                      }}
                    >
                      <span className="btn-continue">
                        {getLanguages(checklanguage, "continue")}
                      </span>
                    </a>
                  </div>
                )}
              </>
            )}
            <div className="video-description">
              {/* <h5>
                {getLanguages(checklanguage, "soulWritingVideoDescription")}
              </h5> */}
              <div
                className="desc"
                dangerouslySetInnerHTML={{ __html: description }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SoulwritingVimeoModal;
