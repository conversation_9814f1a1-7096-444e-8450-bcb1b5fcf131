import { useState, useEffect } from "react";
import {
  BOOK_MEETING_STEPS_DATA,
  COMPANION_NAV_LABELS,
  STEPS_DATA,
  checklanguage,
  getLanguages,
} from "../../../constant";
import CommonNavigation from "../../../helpers/CommonNavigation";
import CompanionCard from "../Cards/CompanionCard";
import CompanionDetailCard from "../Cards/CompanionDetailCard";
import Filters from "../Filters";
import ScheduleAppointment from "../ScheduleAppointment";
import StepsNavBar from "../Steps/stepsNavBar";

function BookMeetingModal({ show, onHide }) {
  // local variables
  const [step, setStep] = useState(1);
  const [MeetingTab, setMeetingTab] = useState(1);

  useEffect(() => {
    document.addEventListener("click", hideModal);
    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };

  return (
    <>
      <div
        className={`modal schedule-modal ${show ? "show" : ""}`}
        id="scheduleModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => onHide()}
              >
                <span className="icon close-icon"></span>
              </a>
              <div className="schedule-top">
                <h4 className="h4 text-dark-grey fw500 text-center">
                  {getLanguages(checklanguage, "scheduleAnAppointment")}
                </h4>
                <StepsNavBar
                  currentStep={step}
                  array={BOOK_MEETING_STEPS_DATA}
                />
              </div>
            </div>
            <div className="modal-body">
              <div className="schedule-appointment-wrpr">
                {step == 1 ? (
                  <>
                    <p className="label fw500 text-grey-5 appt-desc">
                      {getLanguages(checklanguage, "oneOnOneMeetingText")}
                    </p>
                    <ScheduleAppointment />
                  </>
                ) : step == 2 ? (
                  <>
                    <div className="d-flex align-center justify-end w-50 appt-date-price">
                      <div className="appt-date">
                        <p className="fw500 text-grey-5 label">
                          {getLanguages(checklanguage, "chosenDateTime")}:
                        </p>
                        <h6 className="h6 fw600 text-black">
                          14 Oct, 2022 & 03:30 PM
                        </h6>
                      </div>
                      <div className="appt-time">
                        {/* <p className="fw500 text-grey-5 label text-right">Price:</p>
                        <h6 className="h6 fw600 text-black">€ 1.99 <span className="text-grey-6">/Min</span></h6> */}
                      </div>
                    </div>
                    <div className="over-auto right-section">
                      <div className="h-100 companion-wrpr">
                        <div className="d-flex align-center justify-between b-0 inner-header">
                          <div className="d-flex align-center title-wrpr">
                            <p className="label fw500 text-grey-5 appt-desc">
                              {" "}
                              {getLanguages(checklanguage, "selectACompanion")}
                            </p>
                          </div>
                          <Filters />
                        </div>
                        <div className="companion-tab-wrpr">
                          <CommonNavigation
                            data={COMPANION_NAV_LABELS}
                            kubynavbarStatus={2}
                            MeetingTab={MeetingTab}
                            setMeetingTab={setMeetingTab}
                          />
                          <div className="tab-companion tab-contents companion-card-row active">
                            <CompanionCard isModal={true} />
                            <CompanionCard isModal={true} />
                            <CompanionCard isModal={true} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <p className="label fw500 text-grey-5 appt-desc">
                      {getLanguages(checklanguage, "oneOnOneMeetingText")}
                    </p>
                    <div className="appt-form-wrpr">
                      <div className="form-in w-100 appt-textarea-wrpr">
                        <p className="d-flex align-center fw500 text-grey-5">
                          {getLanguages(checklanguage, "reasonLabel")}:{" "}
                          <span className="icon q-mark-icon"></span>
                        </p>
                        <div className="f-in w-100">
                          <textarea className="form-control textAreaClass"></textarea>
                        </div>
                        <div className="d-flex align-center justify-between textare-desc">
                          <p className="font-inter text-grey-6">
                            {getLanguages(checklanguage, "requestCantEmpty")}
                          </p>
                          <p className="font-inter text-grey-6">
                            0/500 {getLanguages(checklanguage, "characters")}
                          </p>
                        </div>
                      </div>
                      {/* <div className="f-in w-100 form-check">
                        <input type="checkbox" className="form-check-input" />
                        <label className="fw400 form-check-label">
                          I consent to my conversation being recorded{" "}
                          <span className="icon q-mark-icon"></span>
                        </label>
                      </div> */}
                      <div className="f-in w-100 form-check">
                        <input type="checkbox" className="form-check-input" />
                        <label className="fw400 form-check-label">
                          {getLanguages(
                            checklanguage,
                            "termsAndConditionsRead"
                          )}
                          <span className="icon q-mark-icon"></span>
                        </label>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="d-flex align-center justify-between modal-footer">
              <div className="price-wrpr">
                {step == 1 && (
                  <>
                    <p className="text-grey-5 fw500">
                      {getLanguages(checklanguage, "chosenDateTime")}:
                    </p>
                    <h6 className="h6 fw600 text-black-1">
                      10 Oct, 2022 & 13:00 PM
                    </h6>
                  </>
                )}
              </div>
              {step == 1 || step == 2 ? (
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => {
                      step == 2 ? setStep(1) : onHide();
                    }}
                  >
                    {step === 1
                      ? getLanguages(checklanguage, "cancel")
                      : "Back"}
                  </button>
                  <button
                    className="btn-accent sm w-100"
                    onClick={() => {
                      setStep(`${step == 1 ? 2 : 3}`);
                    }}
                  >
                    {getLanguages(checklanguage, "continue")}
                  </button>
                </div>
              ) : (
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => {
                      setStep(2);
                    }}
                  >
                    {getLanguages(checklanguage, "back")}
                  </button>
                  <button className="btn-accent sm w-100">
                    {getLanguages(checklanguage, "scheduleAnAppointment")}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default BookMeetingModal;
