import React, { useEffect } from "react";
import { checklanguage, getLanguages } from "../../../constant";

const ChangeCompanionAlertModal = ({
  show,
  onHide,
  isFetching,
  onsubmit,
  setshow,
  getComPanionList,
  showCompanionCost,
}) => {
  useEffect(() => {
    if (showCompanionCost == "step1") {
      getComPanionList(1, true, "step1");
    } else {
      getComPanionList(1, true);
    }
  }, []);
  return (
    <div
      className={`modal soulwriting-bill change-companion-confirmation ${
        show == 11 ? "show" : ""
      } `}
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "wantToChangeCompanion")}
            </h4>
          </div>
          <div className="modal-body">
            <div className="bill-modal-test">
              <div className="d-flex companion-card-row">
                <p>{getLanguages(checklanguage, "payAgain")}</p>
              </div>
            </div>
            <div className="d-flex align-center justify-end modal-footer">
              <div className="d-flex align-center modal-btn-wrpr">
                <button className="btn sm w-100" onClick={() => onHide()}>
                  {getLanguages(checklanguage, "no")}
                </button>
                <button
                  className={`btn-accent sm w-100  ${
                    isFetching && "btn-loader"
                  }`}
                  onClick={() => {
                    //getComPanionList(1);
                    onHide();
                    setshow(2);
                  }}
                >
                  {getLanguages(checklanguage, "yes")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangeCompanionAlertModal;
