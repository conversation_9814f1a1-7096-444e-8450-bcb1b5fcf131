import React, { useEffect, useState } from "react";
import CompanionCard from "../Cards/CompanionCard";
import { checklanguage, getLanguages } from "../../../constant";

const ChooseCompanionModal = ({
  show,
  onHide,
  CompanionList,
  isFetching,
  setCompanionId,
  companionId,
  setshow,
  componentWrapper,
  saveCompanionStatus,
  getSingleCompanion,
  onsubmit,
  hideExtraInfo,
  showCompanionCost,
}) => {
  const [alreadySelectedCompanion, setAlreadySelectedCompanion] =
    useState(companionId);
  const postProject = (id) => {
    setCompanionId(id);
  };
  const [activeTab, setActiveTab] = useState(1);
  return (
    <div
      className={`modal companion-modal ${show == 2 ? "show" : ""} `}
      id="companion"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog companion-choose-modal companion-choose-modal-soulwriting">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "chooseCompanion")}
            </h4>
            <ul className="d-flex tab-list-wrpr soulwriting-choose-companion">
              <li className="tab-item ff  ">
                <a
                  href="javascript:void(0);"
                  onClick={() => setActiveTab(1)}
                  className={`${"tab-link"} ${activeTab == 1 ? "active" : ""}`}
                >
                  {getLanguages(checklanguage, "professionals")}
                </a>
              </li>
              <li className="tab-item ff  ">
                <a
                  href="javascript:void(0);"
                  onClick={() => setActiveTab(2)}
                  className={`${"tab-link"} ${activeTab == 2 ? "active" : ""}`}
                >
                  {getLanguages(checklanguage, "students")}
                </a>
              </li>
            </ul>
          </div>
          {/* <p>
            {" "}
            imply dummy text of the printing and typesetting industry. Lorem
            Ipsum has been the industry's standard dummy text ever since the
            1500s, when an unknown printer took a galley of type and scrambled
            it to make a type specimen book. It has survived not only five
            centuries,{" "}
          </p> */}
          <div className="modal-body">
            <div className="choose-comp">
              <div className="d-flex companion-card-row">
                <CompanionCard
                  showCompanionCost={showCompanionCost}
                  CompanionList={CompanionList}
                  soulWriting={1}
                  postProject={postProject}
                  alreadySelectedCompanion={alreadySelectedCompanion}
                  showSelectButton={true}
                  activeTab={activeTab} //1 for companions & 2 for students
                  hideExtraInfo={hideExtraInfo}
                />
              </div>
            </div>
            <div className="d-flex align-center justify-end modal-footer">
              <div className="d-flex align-center modal-btn-wrpr">
                <button
                  onClick={() => {
                    setCompanionId(null), onHide();
                  }}
                  className="btn sm w-100"
                >
                  {getLanguages(checklanguage, "cancel")}
                </button>
                <button
                  onClick={() => {
                    if (saveCompanionStatus == 1) {
                      getSingleCompanion();
                    } else {
                      onsubmit();
                      //setshow(3)
                    }
                  }}
                  className={`btn-accent sm w-100 ${
                    companionId == null && "disabled"
                  } ${isFetching && "btn-loader"}`}
                >
                  {getLanguages(checklanguage, "save")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChooseCompanionModal;
