import React, { useEffect, useRef, useState } from "react";
import { getLanguages, checklanguage } from "../../../constant";
import {
  getSoulWritingCharacter,
  updateCharacters,
} from "../../../redux/action/soul-writing";
import { useRouter } from "next/router";
import { toast } from "react-hot-toast";

const ProtagonistQuesModal = ({
  show,
  onHide,
  setvimeoOpen,
  projectId,
  character,
  setCharacter,
  setLastOpenedStage,
  lastOpenedStage,
  setSubmitClicked,
  setFethcCharForS3,
  fetchCharForS3,
  setProtagonistList,
  setNextClick,
}) => {
  const modalRef = useRef(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [formData, setFormData] = useState({
    gender: "",
    age: "",
    nativeLanguage: "",
  });
  const router = useRouter();

  const questions = [
    {
      label: getLanguages(checklanguage, "genderQues"),
      name: "gender",
      type: "radio",
      options: [
        getLanguages(checklanguage, "male"),
        getLanguages(checklanguage, "female"),
      ],
    },
    {
      label: getLanguages(checklanguage, "currentAge"),
      name: "age",
      type: "number",
      placeholder: getLanguages(checklanguage, "enterAge"),
    },
    {
      label: getLanguages(checklanguage, "nativeLanguage"),
      name: "nativeLanguage",
      type: "text",
      placeholder: getLanguages(checklanguage, "enterNativeLanguage"),
    },
  ];

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex >= 0) {
      const currentQuestion = questions[currentQuestionIndex];
      const currentValue = formData[currentQuestion.name];

      if (!currentValue) {
        toast.error(getLanguages(checklanguage, "err"));
        return;
      }
    }
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  const handleRadioInputChange = (event) => {
    const { name, value } = event.target;
    let formD = Object.assign(formData, { [name]: value });
    setFormData(formD);

    setTimeout(() => {
      handleNext();
    }, 500);
  };

  const fetchSoulWritingCharacter = async () => {
    if (projectId != undefined) {
      let characterData = await getSoulWritingCharacter({
        projectId: projectId,
      });
      setCharacter(characterData?.data?.responseData?.characterList);
    }
  };
  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      event.stopPropagation();
    }
  };
  useEffect(() => {
    fetchSoulWritingCharacter();
    window?.document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window?.document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSubmit = async (event) => {
    setSubmitClicked(true);
    event.preventDefault();
    if (currentQuestionIndex === questions.length - 1) {
      const currentQuestion = questions[currentQuestionIndex];
      const currentValue = formData[currentQuestion.name];

      if (!currentValue) {
        toast.error(getLanguages(checklanguage, "err"));
        return;
      }
      let finalPayload = {
        projectId: projectId,
        id: character[0]?.id,
        age: formData?.age,
        title:
          formData?.gender == "Male" || formData?.gender == "Männlich"
            ? "1"
            : "2",
        Acronym:
          character[0]?.Acronym == "I"
            ? getLanguages(checklanguage, "i")
            : character[0]?.Acronym,
        name:
          character[0]?.name == "I"
            ? getLanguages(checklanguage, "i")
            : character[0]?.name,
        protogonistObject: {
          defaultCharacter: true,
          nativeLanguage: formData?.nativeLanguage,
        },
      };
      const response = await updateCharacters(finalPayload);
      if (response) {
        setFethcCharForS3(!fetchCharForS3);
        // const result = await getSoulWritingCharacter({
        //   projectId: projectId,
        // });
        // setProtagonistList(result?.data?.responseData?.characterList);
        setTimeout(() => {
          setLastOpenedStage(4);
        }, 200);
        let tag = window?.document?.getElementById("soulwriting-tab-id-4");
        tag?.click();
        
        setTimeout(() => {
          setvimeoOpen(1);
        }, 400);
      }

      onHide();
    } else {
      handleNext();
    }
  };
  return (
    <div className={`modal protagonist-modal new ${show ? "show" : ""}`}>
      <div className="overlay-div"></div>
      <div className="modal-content" ref={modalRef}>
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {" "}
              {getLanguages(checklanguage, "information")}
            </h4>
          </div>

          <div className="modal-body">
            <div className="protagonist-form-wrpr">
              <div className="text-center form-inner protogonist-form-inner">
                <div className="protogonist-opt-list">
                  <div className="text-center form-inner">
                    <div className="question-wrpr">
                      <h4 className="h4 text-black-1">
                        {questions[currentQuestionIndex].label}
                      </h4>
                    </div>

                    {questions[currentQuestionIndex].type === "radio" &&
                      questions[currentQuestionIndex].options.map(
                        (option, index) => (
                          <div key={index} className="form-in w-100">
                            <label className="protogonist-opt" htmlFor={option}>
                              <input
                                type="radio"
                                id={option}
                                name={questions[currentQuestionIndex].name}
                                value={option}
                                onChange={handleRadioInputChange}
                                checked={
                                  formData[
                                  questions[currentQuestionIndex].name
                                  ] === option
                                }
                              />
                              <div className="protogonist-content">
                                <span>{option}</span>
                              </div>
                            </label>
                          </div>
                        )
                      )}
                    {questions[currentQuestionIndex].type === "number" && (
                      <div className="form-in w-100">
                        <label className="protogonist-opt">
                          <input
                            className="form-control"
                            type="text"
                            name={questions[currentQuestionIndex].name}
                            placeholder={
                              questions[currentQuestionIndex].placeholder
                            }
                            onChange={handleInputChange}
                            value={
                              formData[questions[currentQuestionIndex].name]
                            }
                          />
                        </label>
                      </div>
                    )}
                    {questions[currentQuestionIndex].type === "text" && (
                      <div className="form-in w-100">
                        <input
                          className="form-control"
                          type="text"
                          name={questions[currentQuestionIndex].name}
                          placeholder={
                            questions[currentQuestionIndex].placeholder
                          }
                          onChange={handleInputChange}
                          value={formData[questions[currentQuestionIndex].name]}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="d-flex align-center justify-end bt-0 modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  {currentQuestionIndex > 0 && (
                    <button
                      type="button"
                      className="btn-tertiary sm w-100"
                      onClick={handleBack}
                    >
                      {getLanguages(checklanguage, "back")}
                    </button>
                  )}

                  {currentQuestionIndex === questions.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleSubmit}
                      className="btn-accent sm w-100"
                    >
                      {getLanguages(checklanguage, "save")}
                    </button>
                  ) : (
                    <div className="btn-accent sm w-100" onClick={handleNext}>
                      {getLanguages(checklanguage, "next")}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtagonistQuesModal;
