import React, { useEffect } from "react";
import { useState } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import { Number, Currency } from "react-intl-number-format";

const SoulWritingBillingModal = ({ show, onHide, data }) => {
  const [isFetching, setIsFetching] = useState(false);
  const [billingData, setBillingData] = useState(data);
  useEffect(() => {
    setBillingData(data);
  }, [data]);

  console.log(billingData, 'bbbbbb')

  return (
    <div className={`modal soulwriting-bill ${show == 10 ? "show" : ""} `}>
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "sendSoulwriting")}
            </h4>
          </div>
          <div className="modal-body">
            <div className="bill-modal-test">
              <div className="d-flex companion-card-row">
                <p className="billing_intro">
                  {getLanguages(
                    checklanguage,
                    "priceCalculation",
                    null,
                    null,
                    true
                  )}
                  <br />
                  {getLanguages(checklanguage, "contiueClick")} &quot;
                  {getLanguages(checklanguage, "sendText")}&quot;{" "}
                  {getLanguages(checklanguage, "or")} &quot;
                  {getLanguages(checklanguage, "cancel")}&quot;
                  {getLanguages(checklanguage, "toContinueEditing")}
                </p>
              </div>
              <table className="table border-0">
                <tr>
                  <td>
                    <p className="text-grey-1 fw500">
                      {getLanguages(checklanguage, "numOfCharacters")}
                    </p>
                  </td>
                  <td>
                    <span className="fw400">
                      {billingData?.changeCharacterCount}
                    </span>
                  </td>
                </tr>

                <tr>
                  <td>
                    <p className="text-grey-1 fw500">
                      {getLanguages(checklanguage, "price")}:
                    </p>
                  </td>
                  <td>
                    {" "}
                    {/* <span className="fw400">
                      EUR {billingData?.amount.toFixed(2)}
                    </span> */}
                    <Currency locale={checklanguage} currency={"EUR"}>
                      {billingData?.amount.toFixed(2)}
                    </Currency>
                  </td>
                </tr>
              </table>
            </div>
            <div className="d-flex align-center justify-end modal-footer">
              <div className="d-flex align-center modal-btn-wrpr">
                <button className="btn sm w-100" onClick={() => onHide()}>
                  {getLanguages(checklanguage, "cancel")}
                </button>
                <a
                  href={billingData?.paymentLink}
                  className={`btn-accent sm w-100  ${
                    isFetching && "btn-loader"
                  }`}
                >
                  {getLanguages(checklanguage, "sendToCompanion")}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SoulWritingBillingModal;
