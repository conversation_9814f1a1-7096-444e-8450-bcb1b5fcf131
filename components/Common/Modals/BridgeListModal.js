import React from "react";
import BridgeList from "./BridgeList";
import { checklanguage, getLanguages } from "../../../constant";

const BridgeListModal = ({
    setshow,
    show,
    setqueryData,
    bridgeArrayChar,
    removeBridge,
    memberstatusInfo,
    categoryListForm,
    setcategoryListForm,
    setmemberstatusInfo
}) => {

    // let countPast = 0;
    // let countPresent = 0;
    // const bridgeHighlights = () => {
    //     if (memberstatusInfo?.customerStatus == 2) return;
    //     let dataArrayBridge = [...bridgeArrayChar];
    //     let categoryArray = { ...categoryListForm };
    //     let memberPrevData = JSON.parse(JSON.stringify(memberstatusInfo));
    //     for (let i = 0; i < bridgeArrayChar.length; i++) {
    //         if (bridgeArrayChar[i].category == 2) {
    //             let title = memberPrevData.title?.replace(
    //                 dataArrayBridge[i]?.selectionText, `<span class="bridgeHighlight">${dataArrayBridge[i]?.selectionText}</span>`
    //             )
    //             memberPrevData = Object.assign(memberPrevData, { title: title })
    //             console.log('memberPrevData', dataArrayBridge[i]?.selectionText)



    //         } else {
    //             for (let iterator in categoryArray) {
    //                 for (let j = 0; j < categoryArray[iterator]?.length; j++) {
    //                     if (
    //                         dataArrayBridge[i]?.lineNumber ==
    //                         categoryArray[iterator][j].lineNumber
    //                     ) {
    //                         categoryArray[iterator][j].content = categoryArray[iterator][
    //                             j
    //                         ].content?.replace(
    //                             dataArrayBridge[i]?.selectionText,
    //                             `<span class="bridgeHighlight">${dataArrayBridge[i]?.selectionText}</span>`
    //                         );
    //                     }
    //                 }
    //             }

    //         }
    //     }
    //     setmemberstatusInfo(memberPrevData);
    //     setcategoryListForm(categoryArray);

    // };
    return (

        <div
            className={`modal protagonist-list-modal bridge  ${show ? "show" : ""}`}
            id="protagonist"
        >
            <div className="overlay-div" id="modalOverlay"></div>
            <div className="modal-content">
                <div className="modal-dialog">
                    <div className="modal-header">
                        <a
                            href="javascript:;"
                            className="close-btn"
                            id="closeModal"
                            onClick={(e) => {
                                e.preventDefault();
                                setshow(0);
                            }}
                        >
                            <span className="icon close-icon"></span>
                        </a>
                        <h5 className="h5 text-dark-grey fw500 d-flex align-center">
                            {getLanguages(checklanguage, "bridge")}
                        </h5>
                    </div>
                    <div className="modal-body">

                        <BridgeList
                            setqueryData={setqueryData}
                            bridgeArrayChar={bridgeArrayChar}
                            removeBridge={removeBridge}
                            memberstatusInfo={memberstatusInfo}
                            categoryListForm={categoryListForm}
                            setcategoryListForm={setcategoryListForm}
                            setmemberstatusInfo={setmemberstatusInfo}
                        />
                    </div>
                </div>
            </div>
        </div>


    );
};

export default BridgeListModal;
