import { useRouter } from 'next/router'
import React from 'react'

const MeetingSuccessModal = ({ show, onHide  ,resccheduleMeeting}) => {
    const  router = useRouter();
    const { query } = router;
    const onHideCallBack = () =>{
        onHide();
        if (query?.companionId && query?.type) {
              router.push(`${router.pathname}?tabstatus=${query.tabstatus}`)
           }
    }
    return (
        <div
            className={`modal meeting_success_modal ${show ? "show" : ""}`}
            id="scheduleModal"
        >
            <div className="overlay-div" id="modalOverlay"></div>
            <div className="modal-content">
                <div className="modal-dialog">
                    <div className="modal-header">
                        <a
                            href="javascript:;"
                            className="close-btn"
                            id="closeModal"
                            onClick={() => onHideCallBack()}
                        >
                            <span className="icon close-icon"></span>
                        </a>
                    </div>
                    <div className="modal-body">
                        <div className="success-content">
                            <div className='successs-gif-wrpr'>
                                <img src="/images/Successfull.gif" width={'100%'} height={"100%"}/>
                            </div>
                            <h3 className="h3 text-dark-grey fw500 text-center">
                                {resccheduleMeeting === 1 ? 'Appointment Rescheduled Successfully' : resccheduleMeeting === 0 ? 'Appointment Scheduled Successfully' :"Appointment cancelled successfully"}
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default MeetingSuccessModal