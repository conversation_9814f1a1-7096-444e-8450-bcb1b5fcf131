import React, { useEffect, useState } from "react";
import {
  <PERSON>Picker,
  BlockPicker,
  ChromePicker,
  CirclePicker,
  CompactPicker,
  GithubPicker,
  HuePicker,
  MaterialPicker,
  PhotoshopPicker,
  SketchPicker,
  SliderPicker,
  SwatchesPicker,
  TwitterPicker,
} from "react-color";
import { useForm } from "react-hook-form";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
  capitalizeAndReplace,
  BACKGROUND_COLORS,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  getSoulWriting<PERSON>hara<PERSON>,
  update<PERSON><PERSON>cters,
} from "../../../redux/action/soul-writing";
import _ from "lodash";
const ProtagonistEditFormModal = ({
  setProtagonistList,
  protagonistObject,
  show,
  onHide,
  projectId,
  setCharacter,
  SingleCharacter,
  setcategoryListForm,
  setSingleCharacter,
  setIsOpenedFromSoulWriteStep3,
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
  } = useForm({ shouldValidate: true });

  const [isFetching, setIsFetching] = useState(false);
  const [customKinnshipField, setCustomKinnshipField] = useState(false);
  const [acronym, setAcronym] = useState(protagonistObject?.Acronym);
  const [colors, setColors] = useState(
    protagonistObject?.color || {
      backgroundColor: "#CBECEA",
      fontColor: "#262626",
    }
  );
  const [showBackgroundColorPicker, setShowBackgroundColorPicker] =
    useState(false);
  const [showFontColorPicker, setShowFontColorPicker] = useState(false);
  const popover = {
    position: "absolute",
    zIndex: "2",
  };
  const cover = {
    position: "fixed",
    top: "0px",
    right: "0px",
    bottom: "0px",
    left: "0px",
  };
  watch("lifeStatus");
  watch("Acronym");
  useEffect(() => {
    setValue("projectId", protagonistObject?.projectId);
    setValue("id", protagonistObject?.id);
    setValue("title", protagonistObject?.title);
    setValue("name", protagonistObject?.name);
    setValue("age", protagonistObject?.age);
    setValue("Acronym", protagonistObject?.Acronym);
    setValue("color", protagonistObject?.color);

    if (!protagonistObject?.isDefault) {
      setValue("degreeOfKinship", protagonistObject?.degreeOfKinship);
      setValue("lifeStatus", protagonistObject?.lifeStatus);
      setValue("passAwayDate", protagonistObject?.passAwayDate);
      // setValue("distance", capitalizeAndReplace(protagonistObject?.distance));
      setValue(
        "sympethetic",
        capitalizeAndReplace(protagonistObject?.sympethetic)
      );
    }
  }, []);

  const onSubmit = async (formvalues) => {
    //setIsFetching(true);
    //return;
    if(!formvalues?.color){
      formvalues = Object.assign(formvalues, {
        color: {
          backgroundColor: "#CBECEA",
          fontColor: "#262626"
        }
      })
    }
    try {
      let response = await updateCharacters(formvalues);
      if (response) {
        const result = await getSoulWritingCharacter({
          projectId: projectId,
        });
        setProtagonistList(result?.data?.responseData?.characterList);
        onHide();
      }
    } catch (err) {
      setIsFetching(false);
    }
  };

  const updateAcronym = (value) => {
    if (value?.length) {
      setValue("Acronym", value.substr(0, 3));
    }
  };

  const backgroundColor = (color) => {
    let c = JSON.parse(JSON.stringify(colors));
    c = Object.assign(c, { backgroundColor: color.hex });
    setValue("color", c);
    setColors(c);
  };

  const fontColor = (color) => {
    let c = JSON.parse(JSON.stringify(colors));
    c = Object.assign(c, { fontColor: color.hex });
    setValue("color", c);
    setColors(c);
  };

  return (
    <div
      className={`modal protagonist-modal ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "editProtogonistTitle")}
            </h4>
          </div>
          <div className="modal-body">
            <form
              className="protagonist-form-wrpr"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="form-inner">
                <div
                  className="d-flex justify-between f-row"
                  style={{ marginBottom: "20px" }}
                >
                  <div className="d-flex align-center protagonist-details">
                    <div className="protagonist-img">
                      <h6
                        className="h6 fw500 person-card"
                        style={{
                          background: colors.backgroundColor,
                          color: colors.fontColor,
                        }}
                      >
                        {getValues("Acronym")?.toUpperCase()}
                      </h6>
                    </div>
                    <div>
                      <div
                        style={{
                          height: "34px",
                          width: "100px",
                          marginLeft: "20px",
                          background: colors.backgroundColor,
                          border: "2px solid #ccc",
                        }}
                        onClick={() => {
                          setShowFontColorPicker(false);
                          setShowBackgroundColorPicker(true);
                        }}
                      ></div>
                      {showBackgroundColorPicker ? (
                        <div style={popover}>
                          <div
                            style={cover}
                            onClick={() => {
                              setShowBackgroundColorPicker(false);
                            }}
                          />
                          <TwitterPicker
                            width={"250px"}
                            color={colors.backgroundColor}
                            colors={BACKGROUND_COLORS}
                            onChange={backgroundColor}
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </div>
                    <div>
                      <div
                        style={{
                          height: "34px",
                          width: "100px",
                          marginLeft: "20px",
                          background: colors.fontColor,
                          border: "2px solid #ccc",
                        }}
                        onClick={() => {
                          setShowBackgroundColorPicker(false);
                          setShowFontColorPicker(true);
                        }}
                      />
                      {showFontColorPicker ? (
                        <div style={popover}>
                          <div
                            style={cover}
                            onClick={() => {
                              setShowFontColorPicker(false);
                            }}
                          />
                          <TwitterPicker
                            color={colors.fontColor}
                            colors={["#000000", "#ffffff"]}
                            onChange={fontColor}
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                </div>

                <div className="d-flex justify-between f-row">
                  <div className="w-50">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={getLanguages(
                        checklanguage,
                        "protagonistNameLabel"
                      )}
                      name="name"
                      onBlur={updateAcronym}
                      labelClass="f-label"
                      formInClass="w-100"
                      type="text"
                      placeholder={getLanguages(checklanguage, "name")}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "fullNamereq"),
                        },
                      }}
                    />
                  </div>
                  <div className="w-50">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={getLanguages(checklanguage, "acronymlbl")}
                      name="Acronym"
                      labelClass="f-label"
                      formInClass="w-100"
                      type="text"
                      maxLength={3}
                      placeholder={getLanguages(checklanguage, " ")}
                      className="form-control upper-case"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "acronymreq"),
                        },
                        minLength: {
                          value: protagonistObject?.isDefault ? 1 : 2,
                          message: protagonistObject?.isDefault
                            ? "Use at least one characters"
                            : "Use at least two characters",
                        },
                      }}
                    />
                  </div>
                </div>
                <div className="d-flex justify-between f-row">
                  <div className="w-50">
                    <MultiRadioBtnField
                      name="title"
                      control={control}
                      formclass={"w-100"}
                      label={getLanguages(checklanguage, "gender") + "*"}
                      optionValue={"id"}
                      rules={{
                        required: {
                          value: true,
                          message: "Please select atleast one gender",
                        },
                      }}
                      options={GENDER_TYPES[checklanguage]}
                    />
                  </div>
                </div>
                {!protagonistObject?.isDefault ? (
                  <div className="d-flex justify-between f-row">
                    <div className="w-100">
                      <InputField
                        control={control}
                        autoComplete="off"
                        label={getLanguages(checklanguage, "connection")}
                        name="degreeOfKinship"
                        labelClass="f-label"
                        formInClass="w-100"
                        type="text"
                        placeholder={getLanguages(checklanguage, "connection")}
                        className="form-control"
                        inputClassName="f-in w-100"
                        rules={{
                          required: {
                            value: true,
                            message: getLanguages(checklanguage, "fullNamereq"),
                          },
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <></>
                )}

                <div className="d-flex justify-between f-row">
                  <div className="w-50">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={(getValues("Acronym")?.toUpperCase() == "I" || getValues("Acronym")?.toUpperCase() == "ICH") ? getLanguages(checklanguage, "agelabelForI") : getLanguages(checklanguage, "agelabel")}
                      name="age"
                      labelClass="f-label"
                      formInClass="w-100"
                      optionField={"age"}
                      type="number"
                      placeholder={(getValues("Acronym")?.toUpperCase() == "I" || getValues("Acronym")?.toUpperCase() == "ICH") ? getLanguages(checklanguage, "agelabelForI") : getLanguages(checklanguage, "agelabel")}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "ageReq"),
                        },
                      }}
                    />
                  </div>
                  {/* {!protagonistObject?.isDefault ? (
                    <div className="w-50">
                      <InputField
                        control={control}
                        autoComplete="off"
                        label={getLanguages(checklanguage, "distance")}
                        name="distance"
                        labelClass="f-label"
                        formInClass="w-100"
                        optionField={"distance"}
                        type="text"
                        placeholder={getLanguages(checklanguage, "distance")}
                        className="form-control"
                        inputClassName="f-in w-100"
                        rules={{
                          required: {
                            value: true,
                            message: getLanguages(checklanguage, "required"),
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <></>
                  )} */}
                </div>
                {!protagonistObject?.isDefault ? (
                  <div className="d-flex justify-between f-row">
                    <div className="w-50">
                      <InputField
                        control={control}
                        autoComplete="off"
                        label={getLanguages(checklanguage, "sympathetic")}
                        name="sympethetic"
                        labelClass="f-label"
                        formInClass="w-100"
                        type="text"
                        placeholder={getLanguages(checklanguage, "sympathetic")}
                        className="form-control"
                        inputClassName="f-in w-100"
                        rules={{
                          required: {
                            value: true,
                            message: getLanguages(checklanguage, "required"),
                          },
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <></>
                )}

                {/* {!protagonistObject?.isDefault ? (
                  <div className="d-flex justify-between f-row">
                    <MultiRadioBtnField
                      name="lifeStatus"
                      control={control}
                      formclass={"w-50"}
                      label={getLanguages(checklanguage, "lifestatus")}
                      optionValue={"value"}
                      rules={{
                        required: {
                          value: true,
                          message: "Please select atleast lifestatus ",
                        },
                      }}
                      options={LIFE_STATUS[checklanguage]}
                    /> */}
                {/* {
                                        getValues('lifeStatus') == 0 &&
                                        <ReactTimePickerField
                                            name='passAwayDate'
                                            label={getLanguages(checklanguage, "passedAwayDate")}
                                            control={control}
                                            mainClass={'w-50'}
                                            placeholder={getLanguages(checklanguage, "ageSelect")}
                                            maxDate={new Date()}
                                            rules={{
                                                required: {
                                                    value: true,
                                                    message: getLanguages(checklanguage, "ageReq")
                                                },
                                            }}
                                        />
                                    } */}

                {/* {getValues("lifeStatus") == 0 && (
                      <InputField
                        control={control}
                        autoComplete="off"
                        name="passAwayDate"
                        label={getLanguages(checklanguage, "passedAwayDate")}
                        labelClass="f-label"
                        formInClass="w-50"
                        optionField={"age"}
                        type="number"
                        placeholder={getLanguages(
                          checklanguage,
                          "placeHolderEnterPassedAwayYear"
                        )}
                        className="form-control"
                        inputClassName="f-in w-100"
                        rules={
                          {
                           
                          }
                        }
                      />
                    )} */}
                {/* </div>
                ) : (
                  <></>
                )} */}
              </div>
              <div className="d-flex align-center justify-end modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      onHide();
                    }}
                    className="btn sm w-100"
                  >
                    {getLanguages(checklanguage, "cancel")}
                  </button>
                  <button
                    type="submit"
                    className={`btn-accent sm w-100 ${
                      isFetching && "btn-loader"
                    }`}
                  >
                    {SingleCharacter != null ? "Update" : "Save"}{" "}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtagonistEditFormModal;
