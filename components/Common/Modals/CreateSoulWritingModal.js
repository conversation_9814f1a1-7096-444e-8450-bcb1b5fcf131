import CompanionDetailCard from "../Cards/CompanionDetailCard";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import TextareaField from "../../FormFields/TextareaField";
import CheckBoxSelect from "../../FormFields/SingleCheckbox";
import { getLanguages, checklanguage } from "../../../constant";
import { useRouter } from "next/router";
import Link from "next/link";
function CreateSoulWritingModal({
  isModalShow,
  setIsModalShow,
  show,
  onHide,
  getVitaDetail,
}) {
  const router = useRouter();
  const { handleSubmit, control, setValue, getValues, watch } = useForm();
  const [TextAreaCount, setTextAreaCount] = useState(0);
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };
  const onsubmit = async (formvalues) => {
    if (formvalues?.remeber_me) {
      setIsModalShow(false);
    }
  };
  return (
    <>
      <div
        id="profileModal"
        className={`modal soulwriting-modal ${isModalShow ? "show" : ""} `}
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => {
                  onHide();
                }}
              >
                <span
                  className="icon close-icon"
                  onClick={() => router.push(`/dashboard/soulwriting`)}
                ></span>
              </a>
              <div className="schedule-top">
                <h4 className="h4 text-dark-grey fw500 text-center">
                  {getLanguages(checklanguage, "startYourSoulwriting")}
                </h4>
              </div>
            </div>
            <form onSubmit={handleSubmit(onsubmit)}>
              <div className="modal-body">
                <div className="schedule-appointment-wrpr">
                  <p className="label fw500 text-grey-5">
                    {getLanguages(checklanguage, "Terms & Conditions")}:
                  </p>

                  <div className="appt-form-wrpr" id="writesoul">
                    <CheckBoxSelect
                      control={control}
                      className="f-in w-100 form-check"
                      fieldClassName="form-check-input"
                      name="remeber_me"
                      labelClass="fw400 form-check-label"
                      trueValue={true}
                      label={
                        <>
                          {getLanguages(
                            checklanguage,
                            "termsAndConditionsReadd"
                          )}
                        </>
                      }
                      rules={{
                        required: {
                          value: true,
                          message: "Please accept the term to proceed",
                        },
                      }}
                    />{" "}
                    <Link href="/privacy-policy">
                      <a>
                        <span className="icon q-mark-icon"></span>
                      </a>
                    </Link>
                  </div>
                </div>
              </div>
              <div className="d-flex align-center justify-end modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => router.push(`/dashboard/soulwriting`)}
                  >
                    {getLanguages(checklanguage, "cancel")}
                  </button>
                  <button className="btn-accent sm w-100">
                    {getLanguages(checklanguage, "startSoulwriting")}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}

export default CreateSoulWritingModal;
