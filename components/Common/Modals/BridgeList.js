import React from "react";
import { getLanguages, checklanguage } from "../../../constant";
//import { Hilitor } from "../../../constant";

const BridgeList = ({
  setqueryData,
  bridgeArrayChar,
  removeBridge,
  memberstatusInfo,
  categoryListForm,
  setcategoryListForm,
  setmemberstatusInfo,
  setUnsavedChanges
}) => {
  let countPast = 0;
  let countPresent = 0;

  const bridgeHighlights = () => {
    if (memberstatusInfo?.customerStatus == 2) return;
    let dataArrayBridge = [...bridgeArrayChar];

    let categoryArray = { ...categoryListForm };
    let memberPrevData = JSON.parse(JSON.stringify(memberstatusInfo));
    for (let i = 0; i < bridgeArrayChar.length; i++) {
      if (bridgeArrayChar[i].category == 0) {
        var myHilitor = new window.Hilitor("texthtml_project", "MARK");
        if (myHilitor?.setMatchType) {
          myHilitor.setMatchType("open");
          myHilitor.apply(dataArrayBridge[i]?.selectionText);
        }
      } else {
        for (let iterator in categoryArray) {
          for (let j = 0; j < categoryArray[iterator]?.length; j++) {
            if (
              dataArrayBridge[i]?.lineNumber ==
              categoryArray[iterator][j].lineNumber
            ) {
              var myHilitor = new window.Hilitor(
                "texthtml" + dataArrayBridge[i].lineNumber,
                "MARK"
              );
              if (myHilitor?.setMatchType) {
                myHilitor.setMatchType("open");
                myHilitor.apply(dataArrayBridge[i]?.selectionText);
              }
            }
          }
        }
      }
    }
    setmemberstatusInfo(memberPrevData);
    //setcategoryListForm(categoryArray);
  };

  return (
    <div className="d-flex bridge-table-wrpr">
      <div className="bridge-block bridge-past-block">
        <div className="bridgeTime">
          <h5 className="fw500 bridgePast ">
            {getLanguages(checklanguage, "past")}
          </h5>
          <h6 className="text-grey-6 bridge-subtitle ">
            {getLanguages(checklanguage, "pastFromPainPic")}
          </h6>
        </div>
        <ul className="bridge-list">
          {bridgeArrayChar?.length > 0 &&
            bridgeArrayChar.map((obj, index) => {
              if (obj.type == "present") {
                return <></>;
              }
              countPast++;
              return (
                <li
                  key={index}
                  className="d-flex align-center bridge-list-item past"
                >
                  <span className="bridge-count">{countPast}</span>
                  <div
                    className={`${
                      obj.selectionText
                        ? "d-flex align-center bridge-card"
                        : "new_bridge"
                    } `}
                  >
                    {obj.selectionText && (
                      <button
                        id={"bridge_" + index}
                        className="btn-text"
                        type="button"
                        onClick={() => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "past",
                              redline: false,
                              date: new Date(),
                              case: obj.selectionText ? null : "update_bridge",
                              bridgeIndex: obj.selectionText ? null : index,
                              isReadOnly: true,
                            };
                          });

                          setTimeout(() => {
                            bridgeHighlights();
                            if (obj.selectionText) {
                              document
                                .querySelector(`#texthtml${obj?.lineNumber}`)
                                ?.scrollIntoView({
                                  behavior: "smooth",
                                  block: "center",
                                  inline: "nearest",
                                });
                            } else {
                              document
                                .querySelector(`#painpicture-6`)
                                ?.scrollIntoView({
                                  behavior: "smooth",
                                  block: "center",
                                  inline: "nearest",
                                });
                            }
                          }, 200);
                        }}
                      >
                        <span className="icon eye-icon open"></span>
                      </button>
                    )}

                    {obj.selectionText ? (
                      <p
                        title={obj.selectionText}
                        className="p fw500 text-grey-5 bridge-content"
                        onClick={(e) => {
                          document.getElementById("bridge_" + index).click();
                        }}
                      >
                        {obj.selectionText}
                      </p>
                    ) : (
                      <button
                        id={"bridge_" + index}
                        className="d-flex align-center add-bridge-card"
                        type="button"
                        onClick={() => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "past",
                              redline: false,
                              date: new Date(),
                              case: "update_bridge",
                              bridgeIndex: index,
                              isReadOnly: false,
                            };
                          });

                          setTimeout(() => {
                            bridgeHighlights();
                            document
                              .querySelector(`#painpicture-6`)
                              ?.scrollIntoView({
                                behavior: "smooth",
                                block: "center",
                                inline: "nearest",
                              });
                          }, 200);
                        }}
                      >
                        <span className="cursor-had-icon">
                          <img
                            src="/images/cursor-hand.svg"
                            className="img-fluid"
                          />
                        </span>
                        <p
                          className="p text-accent fw500"
                          id="clickToAddBridge_past"
                        >
                          {getLanguages(checklanguage, "clickToAddBridge")}
                        </p>
                      </button>
                    )}

                    {obj.selectionText ? (
                      <button
                        className="btn-text"
                        type="button"
                        onClick={(e) => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "past",
                              redline: false,
                              date: new Date(),
                            };
                          });
                          setTimeout(() => {
                            bridgeHighlights();
                          }, 100);

                          setTimeout(() => {
                            removeBridge(e, obj, index);
                          }, 300);
                        }}
                      >
                        <span className="icon delete-icon"></span>
                      </button>
                    ) : (
                      <></>
                    )}
                  </div>
                </li>
              );
            })}
          <li className="d-flex align-center bridge-list-item">
            <button
              className="d-flex align-center add-bridge-card"
              type="button"
              onClick={() => {
                setqueryData((prev) => {
                  return {
                    ...prev,
                    bridge: true,
                    type: "past",
                    redline: false,
                    date: new Date(),
                    case: null,
                    bridgeIndex: null,
                  };
                });

                setTimeout(() => {
                  bridgeHighlights();
                  document.querySelector(`#painpicture-6`)?.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                  });
                }, 200);
              }}
            >
              <span className="cursor-had-icon">
                <img src="/images/cursor-hand.svg" className="img-fluid" />
              </span>
              <p className="p text-accent fw500" id="clickToAddBridge_past">
                {getLanguages(checklanguage, "clickToAddBridge")}
              </p>
            </button>
          </li>
        </ul>
      </div>

      <div className="bridge-block bridge-present-block">
        <div className="bridgeTime">
          <h5 className="bridgePersent fw500">
            {getLanguages(checklanguage, "present")}
          </h5>
          <h6 className="text-grey-6 bridge-subtitle ">
            {getLanguages(checklanguage, "presentFromProjectOccasion")}
          </h6>
        </div>

        <ul className="bridge-list">
          {bridgeArrayChar?.length > 0 &&
            bridgeArrayChar.map((obj, index) => {
              if (obj.type == "past") {
                return <></>;
              }
              countPresent++;
              return (
                <li
                  key={index}
                  className="d-flex align-center bridge-list-item present"
                >
                  {/* <span className="bridge-count">{countPresent}</span> */}

                  <div
                    className={`${
                      obj.selectionText
                        ? "d-flex align-center bridge-card"
                        : "new_bridge"
                    } `}
                  >
                    {obj.selectionText && (
                      <button
                        id={"bridge_" + index}
                        className="btn-text"
                        type="button"
                        onClick={() => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "present",
                              redline: false,
                              date: new Date(),
                              case: obj.selectionText ? null : "update_bridge",
                              bridgeIndex: obj.selectionText ? null : index,
                              isReadOnly: true,
                            };
                          });

                          setTimeout(() => {
                            bridgeHighlights();
                            if (obj.category == 0) {
                              document
                                .querySelector(`#texthtml_project`)
                                ?.scrollIntoView({
                                  behavior: "smooth",
                                  block: "center",
                                  inline: "nearest",
                                });
                            } else {
                              if (obj.selectionText) {
                                document
                                  .querySelector(`#texthtml${obj?.lineNumber}`)
                                  ?.scrollIntoView({
                                    behavior: "smooth",
                                    block: "center",
                                    inline: "nearest",
                                  });
                              } else {
                                document
                                  .querySelector(`#occasion-4`)
                                  ?.scrollIntoView({
                                    behavior: "smooth",
                                    block: "center",
                                    inline: "nearest",
                                  });
                              }
                            }
                          }, 200);
                        }}
                      >
                        <span className="icon eye-icon open"></span>
                      </button>
                    )}
                    {obj.selectionText ? (
                      <p
                        title={obj.selectionText}
                        className="p fw500 text-grey-5 bridge-content"
                        onClick={(e) => {
                          document.getElementById("bridge_" + index).click();
                        }}
                      >
                        {obj.selectionText}
                      </p>
                    ) : (
                      <button
                        id={"bridge_" + index}
                        className="d-flex align-center add-bridge-card"
                        type="button"
                        onClick={() => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "present",
                              redline: false,
                              date: new Date(),
                              case: "update_bridge",
                              bridgeIndex: index,
                              isReadOnly: false,
                            };
                          });
                          setTimeout(() => {
                            bridgeHighlights();
                            document
                              .querySelector(`#occasion-4`)
                              ?.scrollIntoView({
                                behavior: "smooth",
                                block: "center",
                                inline: "nearest",
                              });
                          }, 200);
                        }}
                      >
                        <span className="cursor-had-icon">
                          <img
                            src="/images/cursor-hand.svg"
                            className="img-fluid"
                          />
                        </span>
                        <p className="p text-accent fw500">
                          {getLanguages(checklanguage, "clickToAddBridge")}
                        </p>
                      </button>
                    )}

                    {obj.selectionText ? (
                      <button
                        className="btn-text"
                        type="button"
                        onClick={(e) => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: true,
                              type: "present",
                              redline: false,
                              date: new Date(),
                            };
                          });
                          setTimeout(() => {
                            bridgeHighlights();
                          }, 100);
                          setTimeout(() => {
                            removeBridge(e, obj, index);
                          }, 300);
                        }}
                      >
                        <span className="icon delete-icon"></span>
                      </button>
                    ) : (
                      <></>
                    )}
                  </div>
                  {!bridgeArrayChar[index].selectionText &&
                  !bridgeArrayChar[index - 1].selectionText ? (
                    <button
                      className="btn-text full-delete-bridge"
                      type="button"
                      onClick={(e) => {
                        setqueryData((prev) => {
                          return {
                            ...prev,
                            bridge: false,
                            type: "",
                            redline: false,
                            date: new Date(),
                          };
                        });
                        // setTimeout(() => {
                        //     bridgeHighlights();
                        // }, 100)
                        setTimeout(() => {
                          removeBridge(e, obj, index, "delete_complete_bridge");
                        }, 200);
                      }}
                    >
                      <span className="icon delete-icon"></span>
                    </button>
                  ) : (
                    <></>
                  )}
                </li>
              );
            })}
          <li className="d-flex align-center bridge-list-item">
            {/* <span className="bridge-count"></span> */}
            <button
              className="d-flex align-center add-bridge-card"
              type="button"
              onClick={() => {
                setqueryData((prev) => {
                  return {
                    ...prev,
                    bridge: true,
                    type: "present",
                    redline: false,
                    date: new Date(),
                    case: null,
                    bridgeIndex: null,
                  };
                });
                setTimeout(() => {
                  bridgeHighlights();
                  document.querySelector(`#occasion-4`)?.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                  });
                }, 200);
              }}
            >
              <span className="cursor-had-icon">
                <img src="/images/cursor-hand.svg" className="img-fluid" />
              </span>
              <p className="p text-accent fw500" id="clickToAddBridge_present">
                {getLanguages(checklanguage, "clickToAddBridge")}
              </p>
            </button>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default BridgeList;
