import React from "react";
import { checklanguage, getLanguages } from "../../../constant";

const SoulStepsVideos = ({ onClose, videoLink }) => {
  console.log(`vidddddd`, videoLink);
  return (
    // <div
    //   className="modal"
    //   onClick={onClose}
    //   style={{
    //     display: "block",
    //     position: "fixed",
    //     zIndex: 1000,
    //     left: 0,
    //     top: 0,
    //     width: "100%",
    //     height: "100%",
    //     overflow: "auto",
    //     backgroundColor: "rgba(0, 0, 0, 0.4)",
    //   }}
    // >
    //   <div
    //     className="modal-content"
    //     style={{
    //       backgroundColor: "white",
    //       margin: "15 % auto",
    //       padding: "20px",
    //       border: "1px solid #888",
    //       width: "80%",
    //       maxWidth: "600px",
    //     }}
    //     onClick={(e) => e.stopPropagation()}
    //   >
    //     {/* Close button */}
    //     <span
    //       className="close"
    //       style={{
    //         color: "#aaa",
    //         float: "right",
    //         fontSize: "28px",
    //         fontWeight: "bold",
    //       }}
    //       onClick={onClose}
    //     >
    //       <p>This is your modal content</p>
    //     </span>

    //     <video controls autoPlay>
    //       <source src={videoLink} type="video/mp4" />
    //       Your browser does not support the video tag.
    //     </video>
    //   </div>
    // </div>
    <>
      <div className="modal" onClick={onClose}>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
          <span className="close" onClick={onClose}>
            <p>{getLanguages(checklanguage, "modalContent")}</p>
          </span>

          <video controls autoPlay>
            <source src={videoLink} type="video/mp4" />
            {getLanguages(checklanguage, "browserNotSupport")}
          </video>
        </div>
      </div>
    </>
  );
};

export default SoulStepsVideos;
