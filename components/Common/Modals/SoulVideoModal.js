import React, { useEffect, useState, useRef } from "react";
import Vimeo from "@u-wave/react-vimeo";
import { checklanguage, getLanguages } from "../../../constant";
import ProtagonistQuesModal from "./ProtagonistQuesModal";
import VideoSkeleton from "./VideoSkeletonModal";

const SoulVideoModal = ({
  vimeoSrc,
  vimeoOpen,
  onHideVimeo,
  onHide,
  description,
  openModel,
  setShowProtagonistQues,
  setvimeoOpen,
  category,
  json,
}) => {
  console.log(`vimeoSrc`, vimeoSrc);
  const [loading, setLoading] = useState(true);

  const modalRef = useRef(null);
  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      event.stopPropagation();
    }
  };
  useEffect(() => {
    window?.document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window?.document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  //   const handleProtoganistContClick = () => {
  //     setvimeoOpen(0);
  //     setShowProtagonistQues(true);
  //     setTimeout(() => {
  //       document.querySelector(`#protagonist-3`)?.scrollIntoView({
  //         behavior: "smooth",
  //         block: "center",
  //         inline: "nearest",
  //       });
  //     }, 500);
  //   };

  return (
    <>
      <div
        className={`modal video-modal soulIntro ${vimeoOpen ? "show" : ""}`}
        id="scheduleModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div
          className="modal-content"
          style={{ minHeight: "600px" }}
          ref={modalRef}
        >
          <div className="modal-dialog">
            <div className="video-title">
              <h3>{getLanguages(checklanguage, "watchVideoToCont")}</h3>
            </div>
            <div className="soulIntro_video" style={{ width: "100%" }}>
              {loading && <VideoSkeleton />}

              <Vimeo
                video={vimeoSrc}
                width={800}
                height={"auto"}
                autoplay
                onReady={() => setLoading(false)}
              />
            </div>
            <div className="continue-wrap">
              <a
                href="javascript:;"
                className="continue-btn btn-accent"
                id="continue"
                onClick={() => {
                  onHideVimeo();
                }}
              >
                <span className="btn-continue">
                  {getLanguages(checklanguage, "continue")}
                </span>
              </a>
            </div>
            <div className="video-description">
              {/* <h5>
                {getLanguages(checklanguage, "soulWritingVideoDescription")}
              </h5> */}
              <div
                className="desc"
                dangerouslySetInnerHTML={{ __html: description }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SoulVideoModal;
