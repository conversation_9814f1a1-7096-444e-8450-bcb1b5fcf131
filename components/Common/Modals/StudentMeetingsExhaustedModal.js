import React, { useEffect } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import {  useRouter } from "next/router";


const StudentMeetingsExhaustedModal = ({ show, onHide }) => {
    const router = useRouter();
    
  return (
    <div
      className={`modal soulwriting-bill change-companion-confirmation ${
        show ? "show" : ""
      } `}
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header" style={{border: 'none'}}>
          <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => onHide(false)}
              >
                <span className="icon close-icon"></span>
              </a>
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, 'errorMessageLimitReached')}
            </h4>
          </div>
          <div className="modal-body">
            
            <div className="d-flex align-center justify-end modal-footer" >
              <div className="d-flex align-center modal-btn-wrpr">
              
                <button
                  className={`btn-accent sm w-100`}
                  onClick = {() => {
                    onHide(false);
                    
                    router.push("/dashboard/kuby-companion?tabstatus=professionals");
                  }}
                >
                  {getLanguages(checklanguage, 'bookAProfessional')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentMeetingsExhaustedModal;
