import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  updateCharacters,
} from "../../../redux/action/soul-writing";
import _ from "lodash";
const ProtoganistModal = ({
  show,
  onHide,
  projectId,
  setCharacter,
  SingleCharacter,
  setcategoryListForm,
  setSingleCharacter,
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
  } = useForm({ shouldValidate: true });
  const [isFetching, setIsFetching] = useState(false);
  const [customKinnshipField, setCustomKinnshipField] = useState(false);
  watch("lifeStatus");
  useEffect(() => {
    if (SingleCharacter?.character != null) {
      //let result = _.find(KINSHIPARRAY[checklanguage], { value: SingleCharacter?.character?.degreeOfKinship });
      // if (!result) {
      //     setValue('degreeOfKinship', 'other')
      //     setValue('customKinship', SingleCharacter?.character?.degreeOfKinship);
      //     setCustomKinnshipField(true)
      // } else {
      setValue("degreeOfKinship", SingleCharacter?.character?.degreeOfKinship);
      // }
      setValue("title", SingleCharacter?.character?.title);
      setValue("name", SingleCharacter?.character?.name);
      setValue("lifeStatus", SingleCharacter?.character?.lifeStatus);

      setValue("age", SingleCharacter?.character?.age);
      setValue("Acronym", SingleCharacter?.character?.Acronym);
      setValue(
        "passAwayDate",
        SingleCharacter?.character?.passAwayDate != null
          ? new Date(SingleCharacter?.character?.passAwayDate)
          : null
      );
    }
  }, [SingleCharacter]);

  const onSubmit = async (formvalues) => {
    setIsFetching(true);
    let payload = { ...formvalues };
    payload.lifeStatus = parseInt(formvalues.lifeStatus);
    payload.age = formvalues.age;
    if (payload.lifeStatus == 1) {
      delete payload.passAwayDate;
    } else {
      payload.passAwayDate = formvalues.passAwayDate;
    }
    if (!payload.customKinship) {
      delete payload.customKinship;
    }
    payload.projectId = projectId;
    if (SingleCharacter != null) {
      payload.id = SingleCharacter?.character?.id;
    }
    payload.protogonistObject.defaultCharacter = true;
    let response;
    try {
      if (SingleCharacter?.character != null) {
        response = await updateCharacters(payload);
      } else {
        response = await addCharacters(payload);
      }
      onHide();
      reset({
        title: "",
        name: "",
        degreeOfKinship: "",
        age: "",
        lifeStatus: "",
        Acronym: "",
        passAwayDate: "",
      });
      setIsFetching(false);
      if (SingleCharacter?.character != null) {
        setCharacter((prev) => {
          let UpdateCharacter = [...prev];
          UpdateCharacter[SingleCharacter?.index] =
            response?.data?.responseData;
          return UpdateCharacter;
        });
        setcategoryListForm((prev) => {
          let updatecharacter = { ...prev };
          for (let iterator in updatecharacter) {
            for (let j = 0; j < updatecharacter[iterator]?.length; j++) {
              if (
                SingleCharacter?.character?.id ==
                updatecharacter[iterator][j].character?.id
              ) {
                updatecharacter[iterator][j].character =
                  response?.data?.responseData;
              }
            }
          }
          return updatecharacter;
        });
        setSingleCharacter(null);
      } else {
        setCharacter((prev) => [...prev, response?.data?.responseData]);
      }
    } catch (err) {
      setIsFetching(false);
    }
  };

  const resetClearForm = () => {
    onHide();
    reset({
      title: "",
      name: "",
      degreeOfKinship: "",
      age: "",
      lifeStatus: "",
      Acronym: "",
      passAwayDate: "",
    });
    clearErrors("title");
    clearErrors("degreeOfKinship");
    clearErrors("age");
    clearErrors("lifeStatus");
    clearErrors("Acronym");
  };

  const updateAcronym = (value) => {
    if (value?.length) {
      setValue("Acronym", value.substr(0, 3));
    }
  };

  const toggleCustomKinnshipField = (e) => {
    if (e.id == 8) {
      setCustomKinnshipField(true);
    } else {
      setCustomKinnshipField(false);
      setValue("customKinship", "");
    }
  };
  return (
    <div
      className={`modal protagonist-modal ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {SingleCharacter?.character
                ? getLanguages(checklanguage, "editProtogonistTitle")
                : getLanguages(checklanguage, "addProtogonistTitle")}
            </h4>
          </div>
          <div className="modal-body">
            <form
              className="protagonist-form-wrpr"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="form-inner">
                {/* <div className="w-100">
                                    <MultiRadioBtnField
                                        name="title"
                                        control={control}
                                        formclass={'w-50'}
                                        label={getLanguages(checklanguage, "title")}
                                        optionValue={"value"}
                                        rules={{
                                            required: {
                                                value: true,
                                                message: "Please select title ",
                                            },
                                        }}
                                        options={TitleArray[checklanguage]}
                                    />
                                </div> */}

                <div className="d-flex justify-between f-row">
                  <div className="w-50">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={getLanguages(
                        checklanguage,
                        "protagonistNameLabel"
                      )}
                      name="name"
                      onBlur={updateAcronym}
                      labelClass="f-label"
                      formInClass="w-100"
                      type="text"
                      placeholder={getLanguages(checklanguage, "name")}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "fullNamereq"),
                        },
                      }}
                    />
                  </div>
                  <div className="w-50">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={getLanguages(checklanguage, "acronymlbl")}
                      name="Acronym"
                      labelClass="f-label"
                      formInClass="w-100"
                      type="text"
                      maxLength={3}
                      placeholder={getLanguages(checklanguage, " ")}
                      className="form-control upper-case"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "acronymreq"),
                        },
                        minLength: {
                          value: 2,
                          message: "Use at least two characters",
                        },
                      }}
                    />
                  </div>
                </div>
                <div className="d-flex justify-between f-row">
                  <div className="w-50">
                    <MultiRadioBtnField
                      name="title"
                      control={control}
                      formclass={"w-100"}
                      label={getLanguages(checklanguage, "gender") + "*"}
                      optionValue={"id"}
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "required"),
                        },
                      }}
                      options={GENDER_TYPES[checklanguage]}
                    />
                  </div>
                </div>
                {/* <ReactSelectField
                                    control={control}
                                    tabIndex={0}
                                    label={getLanguages(checklanguage, "selectdegreekinship")}
                                    name="degreeOfKinship"
                                    type="text"
                                    placeholder={"Select degree of kinship"}
                                    autoComplete="off"
                                    optionLabel={"label"}
                                    optionValue={"value"}
                                    formInClass="w-100"
                                    onSelect={(e) => toggleCustomKinnshipField(e)}
                                    options={KINSHIPARRAY[checklanguage]?.map((title) => ({
                                        label: title?.label,
                                        value: title?.value,
                                        id: title?.id,
                                    }))}
                                    rules={{
                                        required: {
                                            value: true,
                                            message: getLanguages(checklanguage, "degreekinshipreq")
                                        },
                                    }}
                                /> */}
                <div className="d-flex justify-between f-row">
                  <div className="w-100">
                    <InputField
                      control={control}
                      autoComplete="off"
                      label={getLanguages(checklanguage, "degreekinshipreq")}
                      name="degreeOfKinship"
                      labelClass="f-label"
                      formInClass="w-100"
                      type="text"
                      placeholder={getLanguages(
                        checklanguage,
                        "degreeKinnshipPlaceholder"
                      )}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "fullNamereq"),
                        },
                      }}
                    />
                  </div>
                </div>

                {/* {
                                    customKinnshipField
                                        ?
                                        <div className='w-50'>
                                            <InputField
                                                control={control}
                                                autoComplete="off"
                                                label={getLanguages(checklanguage, "customKinship")}
                                                name="customKinship"
                                                labelClass="f-label"
                                                formInClass="w-100"
                                                type="text"
                                                placeholder={getLanguages(checklanguage, " ")}
                                                className="form-control "
                                                inputClassName="f-in w-100"
                                                rules={{
                                                    required: {
                                                        value: true,
                                                        message: getLanguages(checklanguage, "customKinnshipMessage"),
                                                    }

                                                }}
                                            />
                                        </div>
                                        :
                                        <></>
                                } */}
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "agelabel")}
                  name="age"
                  labelClass="f-label"
                  formInClass="w-100"
                  optionField={"age"}
                  type="number"
                  placeholder={getLanguages(checklanguage, "ageSelect")}
                  className="form-control"
                  inputClassName="f-in w-100"
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "ageReq"),
                    },
                  }}
                />
                {/* <ReactTimePickerField
                                name='age'
                                label={getLanguages(checklanguage, "agelabel")}
                                control={control}
                                mainClass={'w-100'}
                                placeholder={getLanguages(checklanguage, "ageSelect")}
                                maxDate={new Date()}
                                rules={{
                                    required: {
                                        value: true,
                                        message: getLanguages(checklanguage, "ageReq")
                                    },
                                }}
                            /> */}
                <div className="d-flex justify-between f-row">
                  <MultiRadioBtnField
                    name="lifeStatus"
                    control={control}
                    formclass={"w-50"}
                    label={getLanguages(checklanguage, "lifestatus")}
                    optionValue={"value"}
                    rules={{
                      required: {
                        value: true,
                        message: "Please select atleast lifestatus ",
                      },
                    }}
                    options={LIFE_STATUS[checklanguage]}
                  />
                  {/* {
                                        getValues('lifeStatus') == 0 &&
                                        <ReactTimePickerField
                                            name='passAwayDate'
                                            label={getLanguages(checklanguage, "passedAwayDate")}
                                            control={control}
                                            mainClass={'w-50'}
                                            placeholder={getLanguages(checklanguage, "ageSelect")}
                                            maxDate={new Date()}
                                            rules={{
                                                required: {
                                                    value: true,
                                                    message: getLanguages(checklanguage, "ageReq")
                                                },
                                            }}
                                        />
                                    } */}

                  {getValues("lifeStatus") == 0 && (
                    <InputField
                      control={control}
                      autoComplete="off"
                      name="passAwayDate"
                      label={getLanguages(checklanguage, "passedAwayDate")}
                      labelClass="f-label"
                      formInClass="w-50"
                      optionField={"age"}
                      type="number"
                      placeholder={getLanguages(
                        checklanguage,
                        "placeHolderEnterPassedAwayYear"
                      )}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={
                        {
                          // required: {
                          //     value: true,
                          //     message: getLanguages(checklanguage, "ageReq")
                          // },
                        }
                      }
                    />
                  )}
                </div>
              </div>
              <div className="d-flex align-center justify-end modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      resetClearForm();
                    }}
                    className="btn sm w-100"
                  >
                    {getLanguages(checklanguage, "cancel")}
                  </button>
                  <button
                    type="submit"
                    className={`btn-accent sm w-100 ${
                      isFetching && "btn-loader"
                    }`}
                  >
                    {SingleCharacter != null ? "Update" : "Save"}{" "}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtoganistModal;
