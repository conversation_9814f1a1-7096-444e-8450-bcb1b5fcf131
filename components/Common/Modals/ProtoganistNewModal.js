import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
//import ProtoganistMoreInfoModal from "./ProtoganistMoreInfoModal";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  updateCharacters,
  getProtagonistQuestions,
} from "../../../redux/action/soul-writing";
import _ from "lodash";

const ProtoganistNewModal = ({
  showCharacterPopup,
  setShowCharacterPopup,
  setshow,
  show,
  onHide,
  projectId,
  setCharacter,
  SingleCharacter,
  setcategoryListForm,
  setSingleCharacter,
  setProtagonistSurveyData,
  isAddProtagonistOpenFromS3,
}) => {
  const {
    register,
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
  } = useForm({ shouldValidate: true });
  const [isFetching, setIsFetching] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [questionObject, setQuestionObject] = useState(null);
  const [currentId, setCurrentId] = useState(1);
  const [previousId, setPreviousId] = useState(1);
  const [arrayDisplayedQuestions, setArrayDisplayedQuestions] = useState();
  //const [tempAnswer, setTempAnswer] = useState(null);
  const [surveyData, setSurveyData] = useState([]);
  const [showMoreInfoModal, setShowMoreInfoModal] = useState(false);

  useEffect(() => {
    getQuestions();
  }, []);

  const getQuestions = async () => {
    let response = await getProtagonistQuestions();
    let questionList = response?.data?.responseData?.questionList?.questions;
    setQuestions(questionList);
    //setQuestionObject(questionList[surveyNavigation.index]);
    setQuestionObject(questionList[0].options);
    setArrayDisplayedQuestions([questionList[0].options]);
  };

  const nextQuestion = (objArray, id) => {
    let prevQuestions = JSON.parse(JSON.stringify(arrayDisplayedQuestions));
    let sData = JSON.parse(JSON.stringify(surveyData));
    //check if answer is added
    let isValid = false;
    for (let k = 0; k < sData.length; k++) {
      if (sData[k].id == id) {
        if (sData[k].isText) {
          if (sData[k].text) {
            isValid = true;
          }
        } else {
          isValid = true;
        }
      }
    }
    if (!isValid) {
      return false;
    }

    for (let i = 0; i < objArray.length; i++) {
      if (objArray[i].id == id) {
        
        if (prevQuestions.length <= sData.length) {
          prevQuestions.push(objArray[i].options);
          setArrayDisplayedQuestions(prevQuestions);
        }
        
        setQuestionObject(objArray[i].options);
        return;
      } else {
        if (objArray[i].options && objArray[i].options.length > 0) {
          nextQuestion(objArray[i].options, id);
        } else {
        }
      }
    }

    //if all the question are done, open modal for more info
    // onHide();
    // showMoreInfoModal(true);
  };

  const getQuestionObject = (objArray, id) => {
    for (let i = 0; i < objArray.length; i++) {
      if (objArray[i].id == id) {
        return objArray[i];
      } else {
        if (objArray[i].options && objArray[i].options.length > 0) {
          let result = getQuestionObject(objArray[i].options, id);
          if (result) {
            return result;
          }
        }
      }
    }
  };

  const prevQuestion = (id) => {
    let sData = JSON.parse(JSON.stringify(surveyData));
    sData.pop();
    setSurveyData(sData);
    let result = getQuestionObject(questions, id);
    if (result?.id) {
      let resultObject = getQuestionObject(questions, result?.parentId);
      setQuestionObject(resultObject.options);

      if (result?.parentId < 2) {
        setCurrentId(2);
      } else {
        setCurrentId(resultObject?.id);
      }
    }
  };
  const updateAnswer = (parentId, id, type, text, isText, obj) => {
    let sData = JSON.parse(JSON.stringify(surveyData));
    let insertNew = true;
    for (let i = 0; i < sData.length; i++) {
      if (sData[i].parentId == parentId) {
        sData[i].forKinnship = obj.forKinnship ?? false;
        sData[i].q = obj.q ?? "";
        sData[i].id = id;
        sData[i].text = text ?? "";
        sData[i].isText = isText;
        sData[i].gender = obj?.gender ?? "";
        insertNew = false;

        break;
      }
    }
    if (insertNew) {
      sData.push({
        forKinnship: obj.forKinnship ?? false,
        q: obj.q ?? "",
        parentId,
        id,
        text: text ?? "",
        isText,
        gender: obj?.gender ?? "",
      });
    }

    setSurveyData(sData);
  };

  const getAnswer = (parentId, id, type) => {
    let sData = JSON.parse(JSON.stringify(surveyData));
    for (let i = 0; i < sData.length; i++) {
      if (sData[i].parentId == parentId) {
        if (type == "text") {
          return sData[i].text;
        } else if (type == "radio") {
          if (sData[i].id == id) {
            return true;
          }
        }
      }
    }
  };

  useEffect(() => {
    if (typeof questionObject == "undefined") {
      setProtagonistSurveyData(surveyData);
      onHide();
    }
  }, [questionObject]);
  return (
    <div
      className={`modal protagonist-modal new ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={(e) => {
                e.preventDefault();
                if (!isAddProtagonistOpenFromS3) {
                  setshow(14);
                } else {
                  setshow(0);
                  // setFethcCharForS3(!fetchCharForS3);
                }
              }}
            >
              <span className="icon close-icon"></span>
            </a>
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "addProtagonist")}
            </h4>
          </div>
          <div className="modal-body">
            <div className="protagonist-form-wrpr">
              <div className="text-center form-inner protogonist-form-inner">
                {/* <div className="question-wrpr">
                                    {<h4 className="h4 text-black-1">{questionObject?.q}</h4>}
                                </div> */}
                <div>
                  <div
                    className={`${"protogonist-opt-list"} ${
                      questionObject?.length > 4 ? "two-column" : ""
                    }`}
                  >
                    {questionObject?.map((obj, index) => {
                      return (
                        <>
                          {obj.type == "text" ? (
                            <div className="text-center form-inner">
                              <div className="question-wrpr">
                                {<h4 className="h4 text-black-1">{obj?.q}</h4>}
                              </div>
                              <div className="form-in w-100 ">
                                <div className="f-in w-100">
                                  <input
                                    name="question"
                                    placeholder=""
                                    type="text"
                                    className="form-control"
                                    onChange={(e) => {
                                      updateAnswer(
                                        obj.parentId,
                                        obj.id,
                                        "text",
                                        e.target.value,
                                        obj?.isText ?? false,
                                        obj
                                      );
                                      setPreviousId(currentId);
                                      setCurrentId(obj.id);
                                    }}
                                    value={getAnswer(
                                      obj.parentId,
                                      obj.id,
                                      "text"
                                    )}
                                  />
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div
                              className={`${"protogonist-opt-item"} ${
                                obj?.isText ? "full" : ""
                              }`}
                              
                            >
                              <label
                                className="protogonist-opt"
                                key={index}
                                htmlFor={"id_" + obj.id}
                              >
                                <input
                                  checked={getAnswer(
                                    obj.parentId,
                                    obj.id,
                                    "radio"
                                  )}
                                  type="radio"
                                  name={"radio_" + obj.parentId}
                                  id={"id_" + obj.id}
                                  onChange={() => {
                                    setPreviousId(currentId);
                                    setCurrentId(obj.id);

                                    updateAnswer(
                                      obj.parentId,
                                      obj.id,
                                      "radio",
                                      "",
                                      obj?.isText ?? false,
                                      obj
                                    );

                                    setTimeout(() => {
                                      document
                                        .querySelector(
                                          "#next_character_question"
                                        )
                                        .click();
                                    }, 300);
                                  }}
                                />
                                <div className="protogonist-content">
                                  <span>{obj.q}</span>
                                  {obj?.isText ? (
                                    <div className="form-in w-100 ">
                                      <div className="f-in w-100">
                                        <input
                                          disabled={
                                            !getAnswer(
                                              obj.parentId,
                                              obj.id,
                                              "radio"
                                            )
                                          }
                                          autoFocus={true}
                                          onChange={(e) =>
                                            updateAnswer(
                                              obj.parentId,
                                              obj.id,
                                              "text",
                                              e.target.value,
                                              obj?.isText ?? false,
                                              obj
                                            )
                                          }
                                          type="text"
                                          id="Hola"
                                          className="form-control"
                                          value={getAnswer(
                                            obj.parentId,
                                            obj.id,
                                            "text"
                                          )}
                                        />
                                      </div>
                                    </div>
                                  ) : (
                                    <></>
                                  )}
                                </div>
                              </label>
                            </div>
                          )}
                        </>
                      );
                    })}
                  </div>
                </div>
              </div>
              <div className="d-flex align-center justify-end bt-0 modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  {arrayDisplayedQuestions?.length > 1 ? (
                    <button
                      type="button"
                      onClick={() => {
                        prevQuestion(currentId);
                      }}
                      className={`btn-tertiary sm w-100 ${
                        isFetching && "btn-loader"
                      }`}
                    >
                      {getLanguages(checklanguage, "back")}
                    </button>
                  ) : (
                    <></>
                  )}

                  <button
                    id="next_character_question"
                    type="button"
                    onClick={() => {
                      nextQuestion(questions, currentId);
                    }}
                    className={`btn-accent sm w-100`}
                  >
                    {getLanguages(checklanguage, "next")}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtoganistNewModal;
