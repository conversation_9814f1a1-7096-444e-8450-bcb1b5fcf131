import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
  capitalizeAndReplace,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  updateCharacters,
  getSoulWritingCharacter,
  deleteCharacter,
} from "../../../redux/action/soul-writing";
import _ from "lodash";
const ProtagonistListModal = ({
  setProtagonistList,
  protagonistList,
  setshow,
  show,
  onHide,
  projectId,
  set<PERSON>haracter,
  SingleCharacter,
  categoryListForm,
  setcategoryListForm,
  setProtagonistObject,
  protangonist,
  setIsOpenedFromSoulWriteStep3,
  setIsOpenAddProtagonistOpenFromS3,
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
  } = useForm({ shouldValidate: true });
  const [isFetching, setIsFetching] = useState(false);
  // const [protagonistList, setProtagonistList] = useState([]);
  const [activeIndex, setActiveIndex] = useState(null);

  useEffect(() => {
    setTimeout(() => {
      getProtagonistList(projectId);
    }, 100);
    setIsOpenedFromSoulWriteStep3(false);
    setIsOpenAddProtagonistOpenFromS3(false);
  }, []);

  const searchCharacter = (characters, characterId) => {
    for (var i = 0; i < characters.length; i++) {
      if (characters[i].id == characterId) {
        return characters[i];
      }
    }
    return null;
  };
  const getProtagonistList = async (projectId) => {
    setIsFetching(true);
    try {
      let response = await getSoulWritingCharacter({ projectId: projectId });
      setProtagonistList(response?.data?.responseData?.characterList);
      setCharacter(response?.data?.responseData?.characterList);
      let characterList = response?.data?.responseData?.characterList;
      let contentlist = JSON.parse(JSON.stringify(categoryListForm));
      let result = null;
      if (Object.keys(contentlist).length) {
        Object.keys(contentlist).forEach((obj, index) => {
          contentlist[obj].forEach((obj1, index1) => {
            result = searchCharacter(characterList, obj1.characterId);
            if (result) {
              contentlist[obj][index1] = Object.assign(
                contentlist[obj][index1],
                { character: result }
              );
            }
          });
        });
      }

      setcategoryListForm(contentlist);
    } catch (err) {
      setIsFetching(false);
    }
  };

  const toggleAccordian = (index) => {
    if (activeIndex == index) {
      setActiveIndex(null);
    } else {
      setActiveIndex(index);
    }
  };

  const openEditForm = (obj) => {
    setProtagonistObject(obj);
    setshow(15);
  };

  const deleteProtagonist = async (id) => {
    try {
      let response = await deleteCharacter({ id: id });
      getProtagonistList(projectId);
    } catch (error) {}
  };


  return (
    <div
      className={`modal protagonist-list-modal  ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={(e) => {
                e.preventDefault();
                setshow(0);
              }}
            >
              <span className="icon close-icon"></span>
            </a>
            <h5 className="h5 text-dark-grey fw500 d-flex align-center">
              {SingleCharacter?.character
                ? getLanguages(checklanguage, "editProtogonistTitle")
                : getLanguages(checklanguage, "addProtogonistTitle")}{" "}
              {/* <span className="icon q-mark-icon"></span> */}
            </h5>
          </div>
          <div className="modal-body">
            <div className="modal-top-body">
              <p className="p mb-10">
                {getLanguages(checklanguage, "addProtogonistDesc")}
              </p>
              <button
                className="btn-accent footer-btn sm"
                onClick={(e) => {
                  setIsOpenAddProtagonistOpenFromS3(false);
                  e.preventDefault();
                  setshow(12);
                }}
              >
                {getLanguages(checklanguage, "addNewProtagonist")}
              </button>
            </div>

            <div className="modal-listing-wrpr">
              <div className="accordion">
                {protagonistList?.length > 0 &&
                  [
                    protagonistList[0],
                    ...protagonistList
                      .slice(1)
                      .sort((a, b) => a.name.localeCompare(b.name)),
                  ].map((obj, index) => {
                    let colorStyle = {};

                    if (obj?.color?.backgroundColor) {
                      colorStyle = {
                        background: obj.color.backgroundColor,
                        color: obj.color.fontColor,
                      };
                    }

                    return (
                      <div className="accordion-item" key={index}>
                        <div className="accordion-item-header protagonist-list">
                          <div className="d-flex justify-between w-100 protagonist-card">
                            <div
                              className="d-flex align-center protagonist-details"
                              style={{ cursor: "pointer" }}
                              onClick={() => {
                                toggleAccordian(index);
                              }}
                            >
                              <div className="protagonist-img">
                                <h6
                                  className="h6 fw500 person-card"
                                  style={colorStyle}
                                >
                                  {obj?.Acronym == "I"
                                    ? getLanguages(checklanguage, "i")
                                    : obj?.Acronym}
                                </h6>
                              </div>
                              <div className="">
                                <p className="p fw500 text-grey-1">
                                  {obj?.name == "I"
                                    ? getLanguages(checklanguage, "i")
                                    : obj?.name}
                                  {index > 0 ? (
                                    <>
                                      (
                                      {obj?.title == 1
                                        ? getLanguages(checklanguage, "male")
                                        : getLanguages(checklanguage, "female")}
                                      )
                                    </>
                                  ) : (
                                    ""
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="d-flex align-center edit-status-wrpr">
                              <button
                                type="button"
                                className={`${"status-badge"} ${
                                  activeIndex == index ? "gray" : "green"
                                }`}
                                onClick={() => {
                                  toggleAccordian(index);
                                }}
                              >
                                {activeIndex == index
                                  ? getLanguages(checklanguage, "hideInfo")
                                  : getLanguages(checklanguage, "moreInfo")}
                              </button>
                              <div className="d-flex align-center protogonist-action-btns">
                                <button
                                  type="button"
                                  className="btn edit-btn sm"
                                  onClick={() => {
                                    openEditForm(obj);
                                  }}
                                >
                                  <span className="icon edit-icon-grey"></span>
                                </button>
                                {obj?.isDefault ? (
                                  <></>
                                ) : (
                                  <button
                                    className="btn delete-btn sm"
                                    onClick={() => {
                                      deleteProtagonist(obj.id);
                                    }}
                                  >
                                    <span className="icon delete-icon"></span>
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="accordion-item-body protagonist-body">
                          <div
                            className={`${"protogonist-content"} ${
                              activeIndex == index ? "active" : ""
                            }`}
                          >
                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "gender")}:{" "}
                              <span className="text-grey-6">
                                {obj?.title == 1
                                  ? getLanguages(checklanguage, "male")
                                  : getLanguages(checklanguage, "female")}
                              </span>{" "}
                            </p>
                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "age")}:{" "}
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.age)}
                              </span>{" "}
                            </p>

                            {obj?.protogonistObject?.nativeLanguage && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "nativeLang")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(
                                    obj?.protogonistObject?.nativeLanguage
                                  )}
                                </span>{" "}
                              </p>
                            )}
                            {obj?.degreeOfKinship && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "conxn")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(obj?.degreeOfKinship)}
                                </span>{" "}
                              </p>
                            )}

                            {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "lifeStatus")}:
                              <span className="text-grey-6">
                                {obj?.lifeStatus
                                  ? "Alive"
                                  : "Passed away (" + obj?.passAwayDate + ")"}
                              </span>{" "}
                            </p> */}
                            {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "distance")}:
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.distance)}
                              </span>{" "}
                            </p> */}
                            {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "contact")}:
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.contact)}
                              </span>{" "}
                            </p> */}
                            {obj?.sympethetic && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "feeling")}:{" "}
                                <span className="text-grey-6">
                                  {getLanguages(
                                    checklanguage,
                                    `${obj.sympethetic}`
                                  )}
                                  {/* {capitalizeAndReplace(obj?.sympethetic)} */}
                                </span>
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProtagonistListModal;

// import React, { useEffect, useState } from "react";
// import { useForm } from "react-hook-form";
// import {
//   checklanguage,
//   getLanguages,
//   TitleArray,
//   KINSHIPARRAY,
//   LIFE_STATUS,
//   getYearExperience,
//   GENDER_TYPES,
//   capitalizeAndReplace,
// } from "../../../constant";
// import InputField from "../../FormFields/InputField";
// import MultiRadioBtnField from "../../FormFields/RadioBtnField";
// import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
// import ReactSelectField from "../../FormFields/SelectField";
// import {
//   addCharacters,
//   updateCharacters,
//   getSoulWritingCharacter,
//   deleteCharacter,
// } from "../../../redux/action/soul-writing";
// import _ from "lodash";
// import ProtagonistCard from "./ProtagonistCard"; // Import ProtagonistCard component

// const ProtagonistListModal = ({
//   setProtagonistList,
//   protagonistList,
//   setshow,
//   show,
//   onHide,
//   projectId,
//   setCharacter,
//   SingleCharacter,
//   categoryListForm,
//   setcategoryListForm,
//   setProtagonistObject,
//   protangonist,
//   setIsOpenedFromSoulWriteStep3,
//   setIsOpenAddProtagonistOpenFromS3,
// }) => {
//   const {
//     handleSubmit,
//     control,
//     setValue,
//     getValues,
//     clearErrors,
//     reset,
//     watch,
//   } = useForm({ shouldValidate: true });
//   const [isFetching, setIsFetching] = useState(false);
//   const [activeIndex, setActiveIndex] = useState(null);

//   useEffect(() => {
//     setTimeout(() => {
//       getProtagonistList(projectId);
//     }, 100);
//     setIsOpenedFromSoulWriteStep3(false);
//     setIsOpenAddProtagonistOpenFromS3(false);
//   }, []);

//   const searchCharacter = (characters, characterId) => {
//     for (var i = 0; i < characters.length; i++) {
//       if (characters[i].id == characterId) {
//         return characters[i];
//       }
//     }
//     return null;
//   };

//   const getProtagonistList = async (projectId) => {
//     setIsFetching(true);
//     try {
//       let response = await getSoulWritingCharacter({ projectId: projectId });
//       setProtagonistList(response?.data?.responseData?.characterList);
//       setCharacter(response?.data?.responseData?.characterList);
//       let characterList = response?.data?.responseData?.characterList;
//       let contentlist = JSON.parse(JSON.stringify(categoryListForm));
//       let result = null;
//       if (Object.keys(contentlist).length) {
//         Object.keys(contentlist).forEach((obj, index) => {
//           contentlist[obj].forEach((obj1, index1) => {
//             result = searchCharacter(characterList, obj1.characterId);
//             if (result) {
//               contentlist[obj][index1] = Object.assign(
//                 contentlist[obj][index1],
//                 { character: result }
//               );
//             }
//           });
//         });
//       }

//       setcategoryListForm(contentlist);
//     } catch (err) {
//       setIsFetching(false);
//     }
//   };

//   const toggleAccordian = (index) => {
//     if (activeIndex == index) {
//       setActiveIndex(null);
//     } else {
//       setActiveIndex(index);
//     }
//   };

//   const openEditForm = (obj) => {
//     setProtagonistObject(obj);
//     setshow(15);
//   };

//   const deleteProtagonist = async (id) => {
//     try {
//       let response = await deleteCharacter({ id: id });
//       getProtagonistList(projectId);
//     } catch (error) {}
//   };

//   return (
//     <div
//       className={`modal protagonist-list-modal  ${show ? "show" : ""}`}
//       id="protagonist"
//     >
//       <div className="overlay-div" id="modalOverlay"></div>
//       <div className="modal-content">
//         <div className="modal-dialog">
//           <div className="modal-header">
//             <a
//               href="javascript:;"
//               className="close-btn"
//               id="closeModal"
//               onClick={(e) => {
//                 e.preventDefault();
//                 setshow(0);
//               }}
//             >
//               <span className="icon close-icon"></span>
//             </a>
//             <h5 className="h5 text-dark-grey fw500 d-flex align-center">
//               {SingleCharacter?.character
//                 ? getLanguages(checklanguage, "editProtogonistTitle")
//                 : getLanguages(checklanguage, "addProtogonistTitle")}{" "}
//               <span className="icon q-mark-icon"></span>
//             </h5>
//           </div>
//           <div className="modal-body">
//             <div className="modal-top-body">
//               <p className="p mb-10">
//                 {getLanguages(checklanguage, "addProtogonistDesc")}
//               </p>
//               <button
//                 className="btn-accent footer-btn sm"
//                 onClick={(e) => {
//                   setIsOpenAddProtagonistOpenFromS3(false);
//                   e.preventDefault();
//                   setshow(12);
//                 }}
//               >
//                 {getLanguages(checklanguage, "addNewProtagonist")}
//               </button>
//             </div>

//             <div className="modal-listing-wrpr">
//               <div className="accordion">
//                 {protagonistList?.length > 0 &&
//                   [
//                     protagonistList[0],
//                     ...protagonistList
//                       .slice(1)
//                       .sort((a, b) => a.name.localeCompare(b.name)),
//                   ].map((obj, index) => (
//                     <ProtagonistCard
//                       key={index}
//                       obj={obj}
//                       index={index}
//                       toggleAccordian={toggleAccordian}
//                       activeIndex={activeIndex}
//                       openEditForm={openEditForm}
//                       deleteProtagonist={deleteProtagonist}
//                     />
//                   ))}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ProtagonistListModal;
