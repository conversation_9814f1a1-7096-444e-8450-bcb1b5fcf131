import CompanionDetailCard from "../Cards/CompanionDetailCard";
import { useEffect, useState } from "react";
import moment from "moment";
import {
  getYearExperience,
  getLanguages,
  checklanguage,
} from "../../../constant";
import { get } from "react-hook-form";
import { Number, Currency } from "react-intl-number-format";

function MeetingDetailModal({
  formatStatus,
  show,
  onHide,
  setShowModal,
  recordObject,
  setRecordObject,
}) {
  useEffect(() => {
  }, []);

  const hideModal = (event) => {
    setShowModal(0);
    setRecordObject(null);
  };

  const getCompanionInfo = (users) => {
    for (let i = 0; i < users.length; i++) {
      if (users[i]?.role == "companion") {
        return (
          <>
            {users[i]?.User?.firstName} {users[i]?.User?.lastName}
          </>
        );
      }
    }
    // users?.forEach((obj) => {

    //   if (obj.User.role == 'companion') {
    //     console.log('ddddddd', obj?.User?.firstName + " " + obj?.User?.lastName)
    //     return "Neeraj"

    //   }
    // })
  };

  return (
    <>
      <div
        className={`modal more-info-modal ${show ? "show" : ""}`}
        id="profileModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <h5>{getLanguages(checklanguage, "moreInformation")}</h5>
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => {
                  hideModal();
                }}
              >
                <span className="icon close-icon"></span>
              </a>
            </div>
            <div className="modal-body">
              <div className="more-info-wrpr">
                {recordObject?.eventStatus == 7 && (
                  <div className="d-flex align-start info-row">
                    <div className="info-left" style={{ marginTop: "5px" }}>
                      {getLanguages(checklanguage, "paymentPending")}
                    </div>
                    <div className="info-right">
                      {`${recordObject?.currency} ${recordObject?.invoices?.[1]?.amount}`}{" "}
                      <a
                        style={{ background: "green", color: "#fff" }}
                        className="btn btn-sm primary-btn ml-10"
                        href={recordObject?.invoices?.[1]?.paymentLink}
                      >
                        {getLanguages(checklanguage, "clickToPay")}
                      </a>
                    </div>
                  </div>
                )}
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "status")}
                  </div>
                  <div className="info-right">
                    {formatStatus(recordObject?.eventStatus)}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "date")}
                  </div>
                  <div className="info-right">
                    {/* {moment(recordObject?.startDate).format("D MMM, Y")} */}
                    {localStorage.getItem("language") == "en"
                      ? moment(recordObject?.startDate).format("D MMM, Y")
                      : moment(recordObject?.startDate).format("D. MMM Y")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "time")}
                  </div>
                  <div className="info-right">
                    {/* {moment(recordObject?.startDate).format("hh:mm a")} */}
                    {localStorage.getItem("language") == "en"
                      ? moment(recordObject?.startDate).format("hh:mm A")
                      : moment(recordObject?.startDate).format("HH:mm")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "duration")}
                  </div>
                  <div className="info-right">
                    {Math.round(recordObject?.zoomDuration)}{" "}
                    {getLanguages(checklanguage, "mins")}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "totalAmount")}
                  </div>
                  <div className="info-right">
                    <Currency locale={checklanguage} currency={"EUR"}>
                      {recordObject?.totalAmount}
                    </Currency>
                    {/* {recordObject?.currency} {recordObject?.totalAmount} */}
                    {/* <span className="text-accent">green text</span> */}
                  </div>
                </div>
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "companion")}
                  </div>
                  <div className="info-right">
                    {getCompanionInfo(recordObject?.Participants)}
                  </div>
                </div>
                {/* <div className="d-flex align-start info-row">
                  <div className="info-left">{getLanguages(checklanguage, 'companion')}</div>
                  <div className="info-right">Finished <button className="read-more">Read more</button></div>
                </div> */}
                <div className="d-flex align-start info-row">
                  <div className="info-left">
                    {getLanguages(checklanguage, "reasonLabel")}
                  </div>
                  <div className="info-right">{recordObject?.description}</div>
                </div>
                {/* <div className="d-flex align-start info-row">
                  <div className="info-left">{getLanguages(checklanguage, 'language')}</div>
                  <div className="info-right">Finished</div>
                </div> */}

                {recordObject?.eventStatus == 7 ? (
                  <div className="d-flex align-start info-row">
                    <div
                      className="info-left"
                      style={{ maxWidth: "100%", flex: "none" }}
                    >
                      {getLanguages(checklanguage, "recordingAvailableMessage")}
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="d-flex align-start info-row">
                      <div className="info-left">
                        {getLanguages(checklanguage, "audioRecording")}
                      </div>
                      <div className="info-right">
                        {recordObject?.audioRecording ? (
                          <a
                            style={{ color: "#D9512C" }}
                            target="_blank"
                            rel="noreferrer"
                            href={
                              recordObject?.audioRecording
                                ? process.env.NEXT_PUBLIC_API_BASE_URL +
                                  "/" +
                                  recordObject?.audioRecording?.path
                                : "#"
                            }
                          >
                            <u>{getLanguages(checklanguage, "audio")}</u>
                          </a>
                        ) : (
                          <>{getLanguages(checklanguage, "notAvailable")}</>
                        )}
                      </div>
                    </div>
                    <div className="d-flex align-start info-row">
                      <div className="info-left">
                        {getLanguages(checklanguage, "videoRecording")}
                      </div>
                      <div className="info-right">
                        {recordObject?.videoLink ? (
                          <>
                            <a
                              style={{ color: "#D9512C" }}
                              target="_blank"
                              rel="noreferrer"
                              href={recordObject?.videoLink}
                            >
                              <u>{getLanguages(checklanguage, "video")}</u>
                            </a>{" "}
                            {/* {recordObject?.videoPassword ? (
                              <>
                                <br />
                                [Pass&nbsp;code:&nbsp;
                                {recordObject?.videoPassword}]
                              </>
                            ) : (
                              <></>
                            )}{" "} */}
                          </>
                        ) : (
                          <>{getLanguages(checklanguage, "notAvailable")}</>
                        )}
                      </div>
                    </div>
                    <div className="d-flex align-start info-row">
                      <div className="info-left">
                        {getLanguages(checklanguage, "videoPassword")}
                      </div>
                      <div className="info-right">
                        {recordObject?.videoPassword}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default MeetingDetailModal;
