import React, { useEffect, useState } from "react";
import Vimeo from "@u-wave/react-vimeo";

const ReactVimeoModal = ({ vimeoSrc, vimeoOpen, onHideVimeo, onHide }) => {
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHideVimeo();
    }
  };
  return (
    <div
      className={`modal video-modal ${vimeoOpen ? "show" : ""}`}
      id="scheduleModal"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          {/* {
                        loading ?  'loading ....' : null} */}
          <a
            href="javascript:;"
            className="close-btn"
            id="closeModal"
            onClick={() => onHideVimeo()}
          >
            <span className="icon close-icon"></span>
          </a>
          <div className="view_video">
            <Vimeo
              video={vimeoSrc}
              width={800}
              height={"auto"}
              autoplay
              onReady={() => setLoading(!loading)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReactVimeoModal;
