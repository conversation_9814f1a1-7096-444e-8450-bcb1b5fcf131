import CompanionDetailCard from "../Cards/CompanionDetailCard";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import TextareaField from "../../FormFields/TextareaField";
import CheckBoxSelect from "../../FormFields/SingleCheckbox";
import { getLanguages, checklanguage } from "../../../constant";
import { useRouter } from "next/router";
import { getSetting } from "../../../redux/action/soul-writing";
function SoulWritingModal({ show, onHide, getVitaDetail, setModalOpen, prefilledReason, setPrefilledReason, callingFrom }) {
  const router = useRouter();
  const { handleSubmit, control, setValue, getValues, watch } = useForm();
  const [settings, setSettings] = useState([]);

  const [TextAreaCount, setTextAreaCount] = useState(0);
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    if (setModalOpen) {
      setModalOpen(false);
    }
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };
  const onsubmit = async (formvalues) => {
    if(!callingFrom || callingFrom != "companion_card") {
      setPrefilledReason(formvalues?.reason);
    }
    let url = `/dashboard/soulwriting/create-soulwriting?reason=${formvalues?.reason}`;
    if (getVitaDetail?.id) {
      url += `&companionId=${getVitaDetail?.id}`;
    }
    router.push(url);
  };
  useEffect(() => {
    getSettings();
    
  }, []);
  useEffect(() => {
    setValue('reason', prefilledReason)
    
  }, [prefilledReason])
  const getSettings = async () => {
    try {
      const request = await getSetting();
      setSettings(request?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };
  const getUrl = (key) => {
    const language = localStorage.getItem("language");
    if (!settings || settings?.length === 0) return "#";

    const setting = settings?.find(
      (s) => s.key === `${key}_${language.toUpperCase()}`
    );
    return setting ? setting?.value : "#";
  };
  return (
    <>
      <div
        className={`modal soulwriting-modal ${show ? "show" : ""}`}
        id="profileModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => {
                  onHide();
                }}
              >
                <span className="icon close-icon"></span>
              </a>
              <div className="schedule-top">
                <h4 className="h4 text-dark-grey fw500 text-center">
                  {getLanguages(checklanguage, "startYourSoulwriting")}:
                </h4>
              </div>
            </div>
            <form onSubmit={handleSubmit(onsubmit)}>
              <div className="modal-body">
                <div className="schedule-appointment-wrpr">
                  {/* {getVitaDetail ? (
                    <p className="label fw500 text-grey-5">
                      {getLanguages(checklanguage, "myCompanion")}:
                    </p>
                  ) : (
                    <></>
                  )} */}

                  {/* <div className="d-flex align-center justify-between modal-prof-card">
                    <CompanionDetailCard
                      getVitaDetail={getVitaDetail}
                      outerDivClass="w-50"
                      modalType="soul"
                    />
                  </div> */}

                  {/* <p className="label fw500 text-grey-5 appt-desc">
                    {getLanguages(checklanguage, "SoulWritingModalDesc")}
                  </p> */}
                  <div className="appt-form-wrpr">
                    <div className="form-in w-100 appt-textarea-wrpr">
                      <p className="d-flex align-center fw500 text-grey-5">
                        {getLanguages(checklanguage, "reasonLabel")}:{" "}
                        <span className="icon q-mark-icon"></span>
                      </p>
                      <TextareaField
                        name="reason"
                        control={control}
                        textAreaClass={"height-700"}
                        rules={{
                          required: {
                            value: true,
                            message: getLanguages(checklanguage, "required"),
                          },
                          maxLength: {
                            value: 500,
                            message: getLanguages(
                              checklanguage,
                              "reasonLengthMessage"
                            ),
                          },
                        }}
                        optionLabel={"reason"}
                        placeholder={getLanguages(
                          checklanguage,
                          "soulWritingReasonPlaceholder"
                        )}
                        type={1}
                        setTextAreaCount={setTextAreaCount}
                      />
                      <div className="d-flex align-center justify-between textare-desc">
                        {/* <p className="font-inter text-grey-6">
                          {getLanguages(checklanguage, "requestCantEmpty")}
                        </p> */}
                        <p className="font-inter text-grey-6">
                          {TextAreaCount}/500{" "}
                          {getLanguages(checklanguage, "characters")}
                        </p>
                      </div>
                    </div>
                    <div className="label termsConditions">
                      <CheckBoxSelect
                        control={control}
                        className="f-in w-100 form-check"
                        fieldClassName="form-check-input"
                        name="remeber_me"
                        labelClass="fw400 form-check-label"
                        trueValue={true}
                        label={""}
                        rules={{
                          required: {
                            value: true,
                            message: getLanguages(checklanguage, "required"),
                          },
                        }}
                      />
                      <div className="termsAndCondn">
                        <>
                          {getLanguages(
                            checklanguage,
                            "termsAndConditionsConsent",
                            ["terms", "privacy-policy"],
                            [`${getUrl("TERMS")}`, `${getUrl("PRIVACY")}`],

                            true
                          )}
                          {/* {getLanguages(
                            checklanguage,
                            "termsAndConditionsConsent",
                            null,
                            null,
                            true
                          )} */}
                        </>
                        {/* <>
                          {getLanguages(checklanguage, "read")}{" "}
                          <span onClick={(e) => e.stopPropagation()}>
                            {" "}
                            <a href="/terms" target="_blank">
                              {" "}
                              {getLanguages(
                                checklanguage,
                                "termsAndConditionConsent"
                              )}{" "}
                            </a>
                            {getLanguages(checklanguage, "and")}{" "}
                            <a href="/data-protection" target="_blank">
                              {getLanguages(
                                checklanguage,
                                "dataProtectionDeclaration"
                              )}
                            </a>{" "}
                          </span>{" "}
                          {getLanguages(checklanguage, "accept")}
                        </> */}
                        {/* <span className="icon q-mark-icon"></span> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="d-flex align-center justify-end modal-footer">
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => {
                      onHide();
                    }}
                  >
                    {getLanguages(checklanguage, "cancel")}
                  </button>
                  <button className="btn-accent sm w-100">
                    {getLanguages(checklanguage, "startSoulwriting")}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}

export default SoulWritingModal;
