import React from "react";
import { checklanguage, getLanguages } from "../../../constant";

const SendToCompanionModal = ({ show, onHide, isFetching, onsubmit }) => {
  return (
    <div
      className={`modal camcel-meeting-modal  ${show == 3 ? "show" : ""}`}
      id="scheduleModal"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="camcel-modal-content">
        <div className="modal-dialog">
          <div style={{ borderBottom: "none" }} className="modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={onHide}
            >
              <span className="icon close-icon"></span>
            </a>
          </div>
          <div className="modal-body">
            <h4 className="h4 text-dark-grey fw500 text-center">
              {getLanguages(checklanguage, "wantToSendCompanion")}
            </h4>
            <div className="form-btns-wrpr">
              <div className="d-flex align-center cancel_meeting_btn btn-wrpr">
                <button
                  type="button"
                  className="btn-secondary sm"
                  onClick={onHide}
                >
                  <span className="btn-text-wrpr">
                    {getLanguages(checklanguage, "no")}
                  </span>
                </button>
                <button
                  type="button"
                  onClick={onsubmit}
                  className={`btn-accent sm ${isFetching && "btn-loader"}`}
                >
                  <span className="btn-text-wrpr">
                    {getLanguages(checklanguage, "yes")}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendToCompanionModal;
