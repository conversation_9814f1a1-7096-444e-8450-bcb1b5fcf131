import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  updateCharacters,
  getProtagonistQuestions,
} from "../../../redux/action/soul-writing";
import _ from "lodash";

const ProtoganistMoreInfoModal = ({
  showCharacterPopup,
  setShowCharacterPopup,
  setshow,
  show,
  onHide,
  projectId,
  set<PERSON>haracter,
  SingleCharacter,
  setcategoryListForm,
  set<PERSON>ingleCharacter,
  setProtagonistSurveyData,
  protagonistSurveyData,
  setFethcCharForS3,
  fetchCharForS3,
  isAddProtagonistOpenFromS3,
}) => {
  const {
    register,
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
    formState: { errors },
  } = useForm({ shouldValidate: true });
  watch("isAlive");
  watch("protagonistDistance");
  watch("meetings");
  const [isFetching, setIsFetching] = useState(false);
  const [nameOfProtagonist, setNameOfProtagonist] = useState(null);
  const [activeForm, setActiveForm] = useState(1);
  const [arrayDisplayedQuestions, setArrayDisplayedQuestions] = useState();
  //const [tempAnswer, setTempAnswer] = useState(null);
  const [surveyData, setSurveyData] = useState([]);
  const [ageTouched, setAgeTouched] = useState(false);
  const [errorAge, setErrorAge] = useState(false);

  const handleAgeBlur = () => {
    setAgeTouched(true);
  };
  const handleAgeFocus = () => {
    setAgeTouched(false);
  };
  useEffect(() => {
    setNameOfProtagonist(protagonistSurveyData?.[0]?.text);
  }, []);

  const nextForm = () => {
    if (activeForm < 2) {
      setActiveForm(activeForm + 1);
    }
  };

  const prevForm = () => {
    setErrorAge(false);
    if (activeForm > 1) {
      setActiveForm(activeForm - 1);
    }
  };
  const availableColors = [
    { backgroundColor: "#1f77b4", fontColor: "White" },
    { backgroundColor: "#aec7e8", fontColor: "Black" },
    { backgroundColor: "#ff7f0e", fontColor: "Black" },
    { backgroundColor: "#ffbb78", fontColor: "Black" },
    { backgroundColor: "#2ca02c", fontColor: "White" },
    { backgroundColor: "#98df8a", fontColor: "Black" },
    { backgroundColor: "#ff9896", fontColor: "Black" },
    { backgroundColor: "#9467bd", fontColor: "Black" },
    { backgroundColor: "#bcbd22", fontColor: "Black" },
    { backgroundColor: "#17becf", fontColor: "Black" },
    { backgroundColor: "#dbdb8d", fontColor: "Black" },
    { backgroundColor: "#e377c2", fontColor: "Black" },
  ];

  function getRandomColor() {
    return availableColors[Math.floor(Math.random() * availableColors.length)];
  }
  const onSubmit = (formvalues) => {
    // if (ageTouched && formvalues.ageInTheScene == "") {
    //   setErrorAge(true);
    //   console.log("Age is required.");
    //   return;
    // } else if (formvalues.ageInTheScene != "") {
    if (activeForm < 2) {
      setActiveForm(activeForm + 1);
      return;
    } else {
      // console.log(formvalues, "formValues");

      let payload = {
        surveyData: protagonistSurveyData,
        moreInfo: formvalues,
        defaultCharacter: true,
      };

      console.log(protagonistSurveyData, "pppppppayload");
      // let kinnship =
      //   protagonistSurveyData?.[2]?.text == ""
      //     ? protagonistSurveyData?.[2]?.q
      //     : protagonistSurveyData?.[2]?.text;
      // //check if last question is gender question

      // if (
      //   protagonistSurveyData?.[protagonistSurveyData.length - 1]?.gender != ""
      // ) {
      //   kinnship =
      //     kinnship +
      //     " (" +
      //     protagonistSurveyData?.[protagonistSurveyData.length - 1]?.q +
      //     ")";
      // }

      let kinnship = "";

      protagonistSurveyData?.forEach((question, index) => {
        if (question.forKinnship) {
          kinnship +=
            question.text === "" ? question.q + " - " : question.text + " - ";
        }
      });
      kinnship = kinnship.trim();

      if (kinnship.endsWith("-")) {
        kinnship = kinnship.slice(0, -1);
      }
      let name = protagonistSurveyData?.[0]?.text;
      let Acronym = name.replaceAll(" ", "").toUpperCase().substring(0, 3);
      let contact = formvalues?.meetings;
      let noOfMeetings = formvalues?.[contact];
      let formattedContact = "";
      if (contact == "only_at") {
        formattedContact = noOfMeetings;
      } else if (contact == "daily") {
        formattedContact = contact;
      } else {
        formattedContact = contact + " (" + noOfMeetings + ")";
      }
      let gender =
        protagonistSurveyData[protagonistSurveyData.length - 1]?.gender;
      if (
        protagonistSurveyData[protagonistSurveyData.length - 1]?.gender == ""
      ) {
        gender = protagonistSurveyData[protagonistSurveyData.length - 1]?.q;
      } else {
        gender =
          protagonistSurveyData[protagonistSurveyData.length - 1]?.gender;
      }
      // return;
      if (gender.trim().toLowerCase() == "male" || gender == "Männlich") {
        gender = "1";
      } else {
        gender = "2";
      }
      let distance = formvalues?.protagonistDistance;
      if (distance == "address") {
        distance = formvalues?.address;
      } else if (distance == "far_away") {
        distance = formvalues?.far_away;
      }
      if (!formvalues?.ageInTheScene) return;
      let finalPayload = {
        projectId: projectId,
        age: formvalues?.ageInTheScene,
        lifeStatus: formvalues?.isAlive == "yes" ? 1 : 0,
        title: gender,
        name: name,
        distance: distance,
        degreeOfKinship: kinnship,
        Acronym: Acronym,
        noOfMeetings: noOfMeetings,
        sympethetic: formvalues?.sympatheticValue,
        protogonistObject: payload,

        color: getRandomColor(),
      };
      try {
        setIsFetching(true);
        console.log(`finalpayloadAddCharacters`, finalPayload);
        let response = addCharacters(finalPayload);
        // if (response) {
        //   setFethcCharForS3(!fetchCharForS3);
        // }
        setIsFetching(false);
        if (showCharacterPopup) {
          setTimeout(() => {
            setShowCharacterPopup(false);
            setshow(4);
          }, 1500);
        } else {
          setTimeout(() => {
            onHide();
          }, 2000);
        }
      } catch (error) {
        setIsFetching(false);
      }
    }
    // }
  };

  return (
    <div
      className={`modal protagonist-modal new ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={(e) => {
                e.preventDefault();
                if (!isAddProtagonistOpenFromS3) {
                  setshow(14);
                } else {
                  setshow(0);
                  // setFethcCharForS3(!fetchCharForS3);
                }
              }}
            >
              <span className="icon close-icon"></span>
            </a>
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "moreInfoAboutProtagonist")}
            </h4>
          </div>
          <div className="modal-body">
            <form
              className="protagonist-form-wrpr"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="protagonist-form-wrpr">
                <div className="form-inner protogonist-form-inner">
                  {activeForm == 1 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 mb-0">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "sympatheticValueQuestion",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div
                          className="protogonist-opt-list"
                          onClick={() =>
                            setTimeout(() => {
                              document
                                .querySelector("#submit_from_radioBtn")
                                .click();
                            }, 300)
                          }
                        >
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="very_trustworthy"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "veryTrustWorthy"
                                  )}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="sympathetic"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "sympathetic")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="neutral"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "neutral")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="unsympathetic"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "unsympathetic")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="repulsive"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "repulsive")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="fear_occupied"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "fearOccupied")}
                                </span>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  {activeForm == 2 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 ">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "protagonistMoreInfoHowOld",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div
                          className={`${
                            errors.ageInTheScene ? "f-error" : ""
                          } ${"f-in w-100"}`}
                        >
                          {/* <input
                            placeholder="e.g. 22"
                            type="text"
                            className="form-control sm"
                            {...register("ageInTheScene", { required: true })}
                          /> */}
                          <input
                            placeholder={getLanguages(checklanguage, "ex22")}
                            type="text"
                            className="form-control sm"
                            {...register("ageInTheScene", {
                              required: ageTouched,
                            })}
                            onBlur={handleAgeBlur}
                            onFocus={handleAgeFocus}
                          />
                          {errors?.ageInTheScene && (
                            <p className="error d-flex align-center">
                              <span className="icon info-icon"></span>
                              {getLanguages(checklanguage, "required")}
                            </p>
                          )}
                        </div>
                      </div>
                      {/* <div className="form-in w-100 ">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "yearOfTheSceneQuestion"
                          )}
                        </label>
                        <div
                          className={`${
                            errors.yearOfTheScene ? "f-error" : ""
                          } ${"f-in w-100"}`}
                        >
                          <input
                            placeholder="e.g. 2002"
                            type="text"
                            className="form-control sm"
                            {...register("yearOfTheScene", { required: true })}
                          />
                          {errors.yearOfTheScene && (
                            <p className="error d-flex align-center">
                              <span className="icon info-icon"></span>
                              {getLanguages(checklanguage, "required")}
                            </p>
                          )}
                        </div>
                      </div> */}
                      {/* <div className="form-in w-100 mb-0">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "isProtagonistStillAlive",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div className="protogonist-opt-list">
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="yes"
                                {...register("isAlive", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "yes")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="no"
                                {...register("isAlive", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>{getLanguages(checklanguage, "no")}</span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.yearOfDeath ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      placeholder={getLanguages(
                                        checklanguage,
                                        "yearOfDeath"
                                      )}
                                      {...register("yearOfDeath", {
                                        required: getValues("isAlive") == "no",
                                      })}
                                    />
                                    {errors.yearOfDeath && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          
                        </div>
                      </div> */}
                    </div>
                  )}
                  {/* {activeForm == 2 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 mb-0">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "howFarProtagonistLive",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div className="protogonist-opt-list">
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="address"
                                {...register("protagonistDistance", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "liveInApartmentHouse"
                                  )}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.address ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue(
                                          "protagonistDistance",
                                          "address"
                                        )
                                      }
                                      {...register("address", {
                                        required:
                                          getValues("protagonistDistance") ==
                                          "address",
                                      })}
                                    />
                                    {errors.address && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="walking_distance"
                                {...register("protagonistDistance", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "withInWalkingDistance"
                                  )}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="far_away"
                                {...register("protagonistDistance", {
                                  required: true,
                                })}
                              />

                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "distanceInMilesOrTime"
                                  )}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.distance ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue(
                                          "protagonistDistance",
                                          "far_away"
                                        )
                                      }
                                      {...register("far_away", {
                                        required:
                                          getValues("protagonistDistance") ==
                                          "far_away",
                                      })}
                                    />
                                    {errors.far_away && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )} */}
                  {/* {activeForm == 3 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 mb-0">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "howOftenYouMeet",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div className="protogonist-opt-list">
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="daily"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "daily")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="weekly"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "weekly")}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.weekly ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue("meetings", "weekly")
                                      }
                                      {...register("weekly", {
                                        required:
                                          getValues("meetings") == "weekly",
                                      })}
                                    />
                                    {errors.weekly && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="monthly"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "monthly")}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.monthly ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue("meetings", "monthly")
                                      }
                                      {...register("monthly", {
                                        required:
                                          getValues("meetings") == "monthly",
                                      })}
                                    />
                                    {errors.monthly && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="annually"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "annually")}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.annually ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue("meetings", "annually")
                                      }
                                      {...register("annually", {
                                        required:
                                          getValues("meetings") == "annually",
                                      })}
                                    />
                                    {errors.annually && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="only_at"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "onlyAt")}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.only_at ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue("meetings", "only_at")
                                      }
                                      {...register("only_at", {
                                        required:
                                          getValues("meetings") == "only_at",
                                      })}
                                    />
                                    {errors.only_at && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="times"
                                {...register("meetings", { required: true })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "soFarOnlyThisMuchTimes"
                                  )}
                                </span>
                                <div className="form-in w-100 m-0">
                                  <div
                                    className={`${
                                      errors.times ? "f-error" : ""
                                    } ${"f-in w-100"}`}
                                  >
                                    <input
                                      type="text"
                                      className="form-control sm"
                                      onFocus={() =>
                                        setValue("meetings", "times")
                                      }
                                      {...register("times", {
                                        required:
                                          getValues("meetings") == "times",
                                      })}
                                    />
                                    {errors.times && (
                                      <p className="error d-flex align-center">
                                        <span className="icon info-icon"></span>
                                        {getLanguages(
                                          checklanguage,
                                          "required"
                                        )}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )} */}
                </div>
                <div className="d-flex align-center justify-end bt-0 modal-footer">
                  <div className="d-flex align-center modal-btn-wrpr">
                    {activeForm > 1 ? (
                      <button
                        onClick={() => prevForm()}
                        type="button"
                        className={`btn-tertiary sm w-100`}
                      >
                        {getLanguages(checklanguage, "back")}
                      </button>
                    ) : (
                      <></>
                    )}

                    <button
                      type="submit"
                      id="submit_from_radioBtn"
                      className={`btn-accent sm w-100 ${
                        isFetching && "btn-loader"
                      }`}
                    >
                      {activeForm < 2
                        ? getLanguages(checklanguage, "next")
                        : getLanguages(checklanguage, "save")}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ProtoganistMoreInfoModal;
