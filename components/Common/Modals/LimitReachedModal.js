import { useRouter } from "next/router";
import { checklanguage, getLanguages } from "../../../constant";

function LimitReachedModal({ show, onHide }) {
  const router = useRouter()
  const handleRoute = () => {
    onHide();
    router.push("/dashboard/kuby-companion?tabstatus=professionals&language=4")
  }
  return (
    <>
      <div className={`modal  ${show ? "show" : ""}`} id="limitModal">
               
        <div className="overlay-div" id="modalOverlay"  onClick={onHide}></div>
        <div className="modal-content">
          <div className="modal-dialog" style={{ height: "230px" }}>
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={onHide}
              >
                <span className="icon close-icon"></span>
              </a>
              <div className="schedule-top">
                <h4 className="h4 text-dark-grey fw500 text-center">
                  {getLanguages(checklanguage, "somethingWentWrong")}
                </h4>
              </div>
            </div>
            <div className="modal-body">
              <div className="errorMessage">
                {getLanguages(checklanguage, "errorMessageLimitReached")}
                <button
                  type="submit"
                  className={`btn-accent sm `}
                  onClick={handleRoute}
                >
                  {getLanguages(checklanguage, "bookProffesional")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default LimitReachedModal;
