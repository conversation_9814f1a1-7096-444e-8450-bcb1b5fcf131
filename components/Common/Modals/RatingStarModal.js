import React, { useEffect } from 'react'
import RatingStarComponent from "../ReactStarCompnonent"
import { useDispatch } from 'react-redux';
import { GET_REVIEW_lIST } from '../../../redux/action/kuby-courses';
const ReactStarModal = ({ show, onHide , SubVideosLesson , setSubVideosLesson ,setOverAllRating }) => {
    const dispatch = useDispatch()
    const getAllVideoReviewss = async (id) => {
        try {
            let response = await getReviews({ videoId: SubVideosLesson?.id, pageNumber: 1, limit: 10 });
                dispatch({
                    type: GET_REVIEW_lIST,
                    payload: response?.data?.responseData
                })
        }
        catch (err) {

        }
    }
    useEffect(() => {

        document.addEventListener('click', hideModal)
    
        return () => document.removeEventListener('click', hideModal)
      }, []);
    
      const hideModal = (event) => {
        const modal = document.querySelector('#modalOverlay');
        if (event.target == modal) {
          onHide()
        }
      }
    return (
        <div
            className={`modal rating_modal  ${show ? "show" : ""}`}
            id="scheduleModal"
        >
            <div className="overlay-div" id="modalOverlay"></div>
            <div className="rating_modal-content">
                <div className="modal-dialog">
                    <div style={{ borderBottom: "none" }} className="modal-header">
                        <a
                            href="javascript:;"
                            className="close-btn"
                            id="closeModal"
                            onClick={onHide}
                        >
                            <span className="icon close-icon"></span>
                        </a>
                        {/* <div className="schedule-top">
                            <h4 className="h4 text-dark-grey fw500">
                            {getLanguages(checklanguage, "ratingExp")}
                            </h4>
                        </div> */}
                    </div>
                    <div className="modal-body">
                    <RatingStarComponent ratingComment={2} SubVideosLesson = {SubVideosLesson} onHide={onHide} show={show} setSubVideosLesson = {setSubVideosLesson} getAllVideoReviewss ={getAllVideoReviewss} setOverAllRating = {setOverAllRating}/>
                        </div>
                </div>
            </div>
        </div>

    )
}

export default ReactStarModal