import { useRouter } from "next/router";
import { checklanguage, getLanguages } from "../../../constant";
import { useEffect, useState } from "react";

function SoulwritingPurchaseModal({ show, onHide, values, getValues, isSoulwritingEnabledData }) {
    const router = useRouter();
    const [soulwritingCheckData, setSoulwritingCheckData] = useState(isSoulwritingEnabledData || null);
    let vv = getValues();
    const { reason, title, consent } = vv;
    const [isVideoLoading, setIsVideoLoading] = useState(true);

    useEffect(() => {
        setSoulwritingCheckData(isSoulwritingEnabledData);
    }, [isSoulwritingEnabledData])

    useEffect(() => {
        if (show) {
            const script = document.createElement('script');
            script.src = 'https://www.checkout-ds24.com/service/digistore.js';
            script.async = true;
            document.body.appendChild(script);

            return () => {
                document.body.removeChild(script);
            };
        }
    }, [show]);

    return (
        <>
            <div className={`modal ${show ? "show" : ""}`}>
                <div className="overlay-div" id="modalOverlay" onClick={onHide}></div>
                <div className="modal-content soulwriting-purchase-modal-content" style={{
                    maxHeight: '80vh',
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '90%',
                    maxWidth: '600px',
                    padding: '25px 20px 0'
                }}>
                    <style>{`
                        @media (max-width: 768px) {
                            .modal-content {
                                position: absolute !important;
                                top: 0 !important;
                                left: 0 !important;
                                right: 0 !important;
                                bottom: 0 !important;
                                width: 100% !important;
                                height: 100vh !important;
                                max-height: none !important;
                                transform: none !important;
                                padding: 0 !important;
                                margin: 0 !important;
                                overflow-y: auto !important;
                                -webkit-overflow-scrolling: touch !important;
                            }
                            .modal-dialog {
                                border-radius: 0 !important;
                                height: 100% !important;
                                margin: 0 !important;
                                display: flex !important;
                                flex-direction: column !important;
                            }
                            .modal-body {
                                flex: 1 1 auto !important;
                                max-height: none !important;
                            }
                        }
                        .custom-scrollbar {
                            scrollbar-width: auto;
                            scrollbar-color: #499557 #ffffff;
                        }
                        .custom-scrollbar::-webkit-scrollbar {
                            width: 8px;
                        }
                        .custom-scrollbar::-webkit-scrollbar-track {
                            background: #ffffff;
                        }
                        .custom-scrollbar::-webkit-scrollbar-thumb {
                            background-color: #499557;
                            border-radius: 4px;
                        }

                        @keyframes shimmer {
                            0% {
                                background-position: -1000px 0;
                            }
                            100% {
                                background-position: 1000px 0;
                            }
                        }
                        .video-skeleton {
                            background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
                            background-size: 2000px 100%;
                            animation: shimmer 2s infinite linear;
                        }
                    `}</style>
                    <div className="modal-dialog" style={{
                        margin: '0',
                        background: '#fff',
                        borderRadius: '12px',
                        overflow: 'hidden'
                    }}>
                        <div className="modal-header" style={{ padding: '0 20px 8px', minHeight: 'auto', position: 'relative' }}>
                            <a
                                href="javascript:;"
                                className="close-btn"
                                id="closeModal"
                                onClick={onHide}
                                style={{
                                    position: 'absolute',
                                    top: '15px',
                                    right: '20px',
                                    padding: '8px',
                                    fontSize: '20px',
                                    lineHeight: '1',
                                    display: 'flex',
                                    alignItems: 'center'
                                }}
                            >
                                <span className="icon close-icon"></span>
                            </a>
                            <div className="schedule-top" style={{
                                marginBottom: '0',
                                paddingTop: '15px',
                                paddingRight: '40px'
                            }}>
                                <h4 className="h4 text-dark-grey fw500 text-center" style={{ margin: '0', padding: '0' }}>
                                    {getLanguages(checklanguage, "soulwritingPurchaseModalTitle")}
                                </h4>
                            </div>
                        </div>
                        <div className="modal-body custom-scrollbar" >
                            <div className="soulwriting-purchase-modal-body">
                                <div style={{ padding: '56.25% 0 0 0', position: 'relative', marginTop: '10px' }}>
                                    <div className="video-skeleton" style={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%',
                                        display: isVideoLoading ? 'block' : 'none'
                                    }} />
                                    <iframe
                                        src="https://player.vimeo.com/video/1041735335?badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479"
                                        frameBorder="0"
                                        allow="autoplay; fullscreen; picture-in-picture; clipboard-write"
                                        onLoad={() => setIsVideoLoading(false)}
                                        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
                                        title="20416 Zweifel ausräumen">
                                    </iframe>
                                </div>

                                <p>{getLanguages(checklanguage, "soulwritingPurchaseModalDescription", ['price'], [isSoulwritingEnabledData?.soulWritingProductPrice?.value], true)}</p>

                                <ul>
                                    <li>
                                        <span className="tick"></span>
                                        <div className="li-content">
                                            <strong>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint1Title")}</strong>
                                            <span>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint1Detail")}</span>
                                        </div>
                                    </li>
                                    <li>
                                        <span className="tick"></span>
                                        <div className="li-content">
                                            <strong>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint2Title")}</strong>
                                            <span>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint2Detail")}</span>
                                        </div>
                                    </li>
                                    <li>
                                        <span className="tick"></span>
                                        <div className="li-content">
                                            <strong>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint3Title")}</strong>
                                            <span>{getLanguages(checklanguage, "soulwritingPurchaseModalContentPoint3Detail")}</span>
                                        </div>
                                    </li>
                                </ul>

                                <div style={{ marginTop: '20px' }}>
                                    <my-order-form query="ds24tr=12Stationen_Portal" product-id='586106' width='100%' base-url='https://www.checkout-ds24.com' order-form-id='204410'></my-order-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

export default SoulwritingPurchaseModal;
