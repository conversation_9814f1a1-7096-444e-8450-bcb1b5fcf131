import moment from "moment";
import { useState, useEffect } from "react";
import {
  STEPS_DATA,
  filterSlotArray,
  dynamicSort,
  checklanguage,
  getLanguages,
  convertUTCToLocalMinutes,
} from "../../../constant";
import CompanionDetailCard from "../Cards/CompanionDetailCard";
import ScheduleAppointment from "../ScheduleAppointment";
import StepsNavBar from "../Steps/stepsNavBar";
import {
  createMeeting,
  rescheduleMeeting,
} from "../../../redux/action/kuby-companion";
import {
  editUserProfile,
  SAVE_PROFILE_INFO,
} from "../../../redux/action/user/user";
import { omit } from "lodash";
import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import LimitReachedModal from "./LimitReachedModal";
import { useDispatch, useSelector } from "react-redux";
import { Tooltip } from "react-tooltip";
import CheckBoxSelect from "../../../components/FormFields/SingleCheckbox";
import { useForm } from "react-hook-form";
import { getSetting } from "../../../redux/action/soul-writing";
import ScheduleAppointmentCopy from "../ScheduleAppointmentCopy";
function CompanionScheduleModal({
  setModalOpen,
  show,
  onHide,
  getVitaDetail,
  getTimeSlotData,
  CompanionId,
  MeetingSlot,
  resccheduleMeeting,
  meetingPopupSuccess,
  setmeetingPopupSuccess,
  queryID,
  errorModal,
  hideErrorModal,
  setErrorModal,
  showErrorModal,
}) {

  const { handleSubmit, control, setValue, getValues, watch } = useForm();
  // local variables
  const { userInfo } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  const cookies = useCookie();
  const router = useRouter();
  const { query } = router;
  const { tabstatus } = { ...query };
  const [settings, setSettings] = useState([]);
  const [step, setStep] = useState(1);
  let [TimeSlotArray, setTimeSlotArray] = useState(null);
  const [textAreaCount, setTextAreaCount] = useState(
    getVitaDetail?.description ? getVitaDetail?.description?.length : 0
  );
  const [errors, setErrors] = useState({});
  const [submitting, setsubmitting] = useState(false);
  const [countryCode, setCountryCode] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [freeSlots, setFreeSlots] = useState(true);

  let [bookTimeSlot, setbookTimeSlot] = useState({
    startDate: query?.startDate ? atob(query?.startDate) : "",
    endDate: query?.endDate ? atob(query?.endDate) : "",
    description: getVitaDetail?.description ? getVitaDetail?.description : "",
    remmber_me: 0,
    consent: 0,
    timings: [
      {
        startTime: "",
        endTime: "",
      },
    ],
    participant: [
      {
        userId: getVitaDetail?.userId
          ? getVitaDetail?.userId
          : query?.companionId
            ? query?.companionId
            : CompanionId,
        role: "companion",
      },
    ],
  });



  useEffect(() => {
    console.log(getTimeSlotData, 'fffffff4444')
    if (getTimeSlotData && Object.keys(getTimeSlotData).length > 0) {
      setFreeSlots(true)
    } else {
      setFreeSlots(false)
    }
  }, [getTimeSlotData]);


  const hideModal = (event) => {
    setModalOpen(false);
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
      setbookTimeSlot({
        startDate: "",
        endDate: "",
        timings: [
          {
            startTime: "",
            endTime: "",
          },
        ],
        participant: [
          {
            userId: CompanionId,
            role: 1,
          },
        ],
      });
    }
  };
  const validate = (event, name, value) => {
    switch (name) {
      case "description":
        if (value.length === 0) {
          setErrors({
            ...errors,
            [name]: getLanguages(checklanguage, "empatyRequest"),
          });
        } else if (value.length > 500) {
          setErrors({
            ...errors,
            [name]: getLanguages(checklanguage, "reasonLengthMessage"),
          });
        } else {
          let newObj = omit(errors, "description");
          setErrors(newObj);
        }
        break;
      case "consent":
        if (!event?.target.checked) {
          setErrors({
            ...errors,
            [name]: getLanguages(checklanguage, "acceptTerm"),
          });
        } else {
          let newObj = omit(errors, "consent");
          setErrors(newObj);
        }
        break;
      case "remmber_me":
        if (!event?.target.checked) {
          setErrors({
            ...errors,
            [name]: getLanguages(checklanguage, "acceptTerm"),
          });
        } else {
          let newObj = omit(errors, "remmber_me");
          setErrors(newObj);
        }
        break;
      default:
        break;
    }
  };
  const handleChange = (event) => {
    let name = event.target.name;
    let val = event.target.value;
    validate(event, name, val);
    if (name == "description") {
      setTextAreaCount(val?.length);
    }
    if (name == "consent" || name == "remmber_me") {
      if (event.target.checked) {
        setbookTimeSlot({
          ...bookTimeSlot,
          [name]: 1,
        });
      } else {
        setbookTimeSlot({
          ...bookTimeSlot,
          [name]: 0,
        });
      }
    } else {
      setbookTimeSlot({
        ...bookTimeSlot,
        [name]: val,
      });
    }
  };

  const updatePhoneNumber = async (e) => {
    e.preventDefault();
    if (phoneNumber) {
      let data = {
        countryCode: countryCode,
        phoneNumber: phoneNumber,
      };
      if(!countryCode){
        delete data.countryCode
      }
      setsubmitting(true);
      try {
        let response = await editUserProfile(data);
        let userProfileData = { ...userInfo };
        userProfileData.User.countryCode = countryCode;
        userProfileData.User.phoneNumber = phoneNumber;
        dispatch({
          type: SAVE_PROFILE_INFO,
          payload: userProfileData,
        });
      } catch (err) { }
    }
  };

  const validateFormData = () => {
    if (bookTimeSlot.description == "") {
      setErrors({
        ...errors,
        description: getLanguages(checklanguage, "empatyRequest"),
      });
      return false;
    }
    if (bookTimeSlot.description.length > 500) {
      setErrors({
        ...errors,
        description: getLanguages(checklanguage, "reasonLengthMessage"),
      });
      return false;
    }
    if(!userInfo?.User?.phoneNumber){
      if (!phoneNumber) {
        setErrors({
          ...errors,
          phoneNumber: getLanguages(checklanguage, "phoneNumberValid"),
        });
        return false;
      }
    }
    if (bookTimeSlot.remmber_me == 0) {
      setErrors({
        ...errors,
        remmber_me: getLanguages(checklanguage, "acceptTerm"),
      });
      return false;
    }
    if (bookTimeSlot.consent == 0) {
      setErrors({
        ...errors,
        consent: getLanguages(checklanguage, "acceptTerm"),
      });
      return false;
    }
    
    return true;
  }
  const scheduleMeeting = async (e) => {

    e.preventDefault();
    
    if(!validateFormData()){
      return false;
    }
    
    setsubmitting(true);
    try {
      let data = { ...bookTimeSlot };
      data.title = getLanguages(checklanguage, "one_to_one_meeting");
      data.groupId = 2;
      data.allDay = 0;
      delete data.consent;
      delete data.remmber_me;
      // console.log(data, 'aaaaaa');
      // return;
      if (resccheduleMeeting === 1) {
        data.eventId = getVitaDetail?.eventId;
        let response = await rescheduleMeeting(data);
        setsubmitting(false);
        onHide();
        //setmeetingPopupSuccess(true)
      } else {
        let response = await createMeeting(data);
        setsubmitting(false);
        onHide();
        setmeetingPopupSuccess(true);
        setTimeout(() => {
          window.location.href = response?.data?.responseData?.paymentLink;
        }, 500);
      }
    } catch (err) {
      if (err?.response?.data?.message === "STUDENT_MEETING_LIMIT_REACHED") {
        onHide();
        setErrorModal(true);
      }
      if (err?.response?.data?.responseData?.pendingInvoice?.paymentLink) {
        setTimeout(() => {
          window.location.href =
            err?.response?.data?.responseData?.pendingInvoice?.paymentLink;
        }, 500);
      }
      setsubmitting(false);
    }
  };
  const tooltipContent = getLanguages(
    checklanguage,
    "contentForRecordingConsent"
  ).replace(/\n/g, "<br>");

  useEffect(() => {
    getSettings();
  }, []);

  const getSettings = async () => {
    try {
      const request = await getSetting();
      setSettings(request?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  const getUrl = (key) => {
    const language = localStorage.getItem("language");
    if (!settings || settings?.length === 0) return "#";

    const setting = settings?.find(
      (s) => s.key === `${key}_${language.toUpperCase()}`
    );
    return setting ? setting?.value : "#";
  };

  
  return (
    <>
      <div
        className={`modal schedule-modal ${show ? "show" : ""}`}
        id="scheduleModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="modal-header">
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => onHide()}
              >
                <span className="icon close-icon"></span>
              </a>
              <div className="schedule-top">
                <h4 className="h4 text-dark-grey fw500 text-center">
                  {resccheduleMeeting === 1
                    ? getLanguages(checklanguage, "reScheduleAnAppointment")
                    : getLanguages(checklanguage, "scheduleAnAppointment")}
                </h4>
                <StepsNavBar
                  currentStep={step}
                  array={STEPS_DATA}
                  totalSteps="2"
                />
              </div>
            </div>
            <div className="modal-body">
              <div className="schedule-appointment-wrpr">
                <p className="label fw500 text-grey-5">
                  {getLanguages(checklanguage, "myCompanion")}:
                </p>
                <div className="d-flex align-center justify-between modal-prof-card">
                  <CompanionDetailCard
                    outerDivClass="w-50"
                    modalType="schedule"
                    currentStep={step}
                    totalStep={2}
                    getVitaDetail={getVitaDetail}
                    bookTimeSlot={bookTimeSlot}
                  />
                </div>
                {step === 1 ? (
                  <ScheduleAppointment
                    onHide={onHide}
                    fSlots={freeSlots}
                    getVitaDetail={getVitaDetail}
                    TimeSlotArray={TimeSlotArray}
                    setbookTimeSlot={setbookTimeSlot}
                    bookTimeSlot={bookTimeSlot}
                    scheduleTime={getVitaDetail?.UserProfile?.scheduleTime}
                    timeSlotData={getTimeSlotData}
                  />
                  // <ScheduleAppointmentCopy
                  //  timeSlotData={getTimeSlotData}
                  //   TimeSlotArray={TimeSlotArray}
                  //   setTimeSlotArray={setTimeSlotArray}
                  //   setbookTimeSlot={setbookTimeSlot}
                  //   bookTimeSlot={bookTimeSlot}
                  //   scheduleTime={getVitaDetail?.UserProfile?.scheduleTime}
                  // />
                ) : (
                  <>
                    <p className="label fw500 text-grey-5 appt-desc">
                      {getLanguages(checklanguage, "oneOnOneMeetingText")}
                    </p>
                    <div className="appt-form-wrpr">
                      <div className="form-in w-100 appt-textarea-wrpr">
                        <p className="d-flex align-center fw500 text-grey-5">
                          {getLanguages(checklanguage, "reasonLabel")}:
                        </p>

                        <div className="f-in w-100">
                          <textarea
                            className="form-control textAreaClass"
                            onChange={handleChange}
                            name="description"
                            value={bookTimeSlot?.description}
                          ></textarea>
                        </div>
                        <div className="d-flex align-center justify-between textare-desc">
                          <div
                            className={`form-in  ${errors?.description !== undefined ? "f-error" : ""
                              }`}
                          >
                            {errors.description && (
                              <p className="error d-flex">
                                <span className="icon info-icon"></span>
                                {errors?.description}
                              </p>
                            )}
                          </div>
                          {/* <p className="font-inter f-error text-grey-6">
                            The request cannot be empty. Please fill out first!
                          </p> */}
                          <p className="font-inter text-grey-6">
                            <p className="text-right">
                              {" "}
                              {`${textAreaCount}`}/500{" "}
                              {getLanguages(checklanguage, "characters")}{" "}
                            </p>
                          </p>
                        </div>
                      </div>
                      {userInfo?.User?.phoneNumber ? (
                        <></>
                      ) : (
                        <>
                          <p className="d-flex align-center fw500 text-grey-5 mb-15">
                            {getLanguages(
                              checklanguage,
                              "phoneNumberInstructions"
                            )}
                          </p>
                          <div className="d-flex justify-between f-row mb-20">
                            <div className="form-in countryCode ">
                              <div className="f-in">
                                <input
                                  name="countryCode"
                                  placeholder="+49"
                                  type="text"
                                  className="form-control"
                                  value={countryCode}
                                  onChange={(e) =>
                                    setCountryCode(e.target.value)
                                  }
                                />
                              </div>
                              
                            </div>
                            <div className="form-in w-100 ">
                              <div className="f-in w-100">
                                <input
                                  name="phoneNumber"
                                  placeholder={getLanguages(
                                    checklanguage,
                                    "phoneNumberPlaceholder"
                                  )}
                                  type="text"
                                  className="form-control"
                                  value={phoneNumber}
                                  onChange={(e) =>
                                    setPhoneNumber(e.target.value)
                                  }
                                />
                              </div>
                              <div
                                className={`form-in  ${errors?.phoneNumber !== undefined ? "f-error" : ""
                                  }`}
                              >
                                {errors.phoneNumber && (
                                  <p className="error d-flex">
                                    <span className="icon info-icon"></span>
                                    {errors?.phoneNumber}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                      <div className="userConsent">
                        <div
                          className="checkbox_tooltip"
                          style={{ display: "flex", minHeight: "31px" }}
                        >
                          <div className="f-in w-100 form-check">
                            <input
                              type="checkbox"
                              onChange={handleChange}
                              className="form-check-input"
                              name="consent"
                            />
                            <label className="fw400 form-check-label">
                              {getLanguages(checklanguage, "recordingConsent")}{" "}
                            </label>
                          </div>
                          <span
                            className="icon q-mark-icon"
                            data-tooltip-id="my-tooltip-data-html"
                            data-tooltip-html={tooltipContent}
                          ></span>
                          <Tooltip id="my-tooltip-data-html" />
                        </div>
                        <div
                          className="label termsConditions"
                          style={{ minHeight: "31px" }}
                        >
                          <CheckBoxSelect
                            control={control}
                            name="remmber_me"
                            label=""
                            trueValue={1}
                            falseValue={0}
                            labelClass="fw400 form-check-label"
                            onSelect={(val) =>
                              setbookTimeSlot({
                                ...bookTimeSlot,
                                remmber_me: val ? 1 : 0,
                              })
                            }
                            rules={{
                              required: {
                                value: true,
                                message: getLanguages(
                                  checklanguage,
                                  "required"
                                ),
                              },
                            }}
                          />
                          <div className="termsAndCondn">
                            <>
                              {getLanguages(
                                checklanguage,
                                "termsAndConditionsConsent",
                                ["terms", "privacy-policy"],
                                [`${getUrl("TERMS")}`, `${getUrl("PRIVACY")}`],

                                true
                              )}
                            </>
                          </div>
                        </div>

                        <div
                          className={`form-in  ${errors?.remmber_me !== undefined ||
                              errors?.consent !== undefined
                              ? "f-error"
                              : ""
                            }`}
                        >
                          {errors.consent ? (
                            <p className="error d-flex mt-20">
                              <span className="icon info-icon"></span>
                              {errors?.consent}
                            </p>
                          ) : errors.remmber_me ? (
                            <p className="error d-flex mt-20">
                              <span className="icon info-icon"></span>
                              {errors?.remmber_me}
                            </p>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
            <div className="d-flex align-center justify-between modal-footer" style={{ display: freeSlots ? 'flex' : 'none' }}>
              <div className="price-wrpr">
                {step == 1 && bookTimeSlot?.startDate != "" && (
                  <>
                    <p className="text-grey-5 fw500">
                      {getLanguages(checklanguage, "chosenDateTime")}:
                    </p>
                    <h6 className="h6 fw600 text-black-1">
                      {localStorage.getItem("language") == "en"
                        ? moment(bookTimeSlot?.startDate)?.format(
                          "MMMM D, YYYY & HH:mm"
                        )
                        : moment(bookTimeSlot?.startDate)?.format(
                          "D. MMM YYYY [um] HH:mm"
                        )}
                    </h6>
                  </>
                )}
              </div>
              {step == 1 ? (
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => {
                      onHide();
                    }}
                  >
                    {getLanguages(checklanguage, "cancel")}
                  </button>
                  <button
                    className={`btn-accent sm w-100 ${bookTimeSlot?.startDate == "" && "disabled"
                      }`}
                    onClick={() => {
                      if (
                        typeof cookies.get("jwtToken") !== "undefined" &&
                        cookies.get("jwtToken") !== null
                      ) {
                        setStep(2);
                      } else {
                        const startDateISO = new Date(
                          bookTimeSlot?.startDate
                        ).toISOString();
                        const endDateISO = new Date(
                          bookTimeSlot?.endDate
                        ).toISOString();

                        if (startDateISO && endDateISO) {
                          let encodedData = `startDate=${btoa(
                            startDateISO
                          )}&endDate=${btoa(endDateISO)}`;
                          let url = btoa(`/dashboard/kuby-companion?tabstatus=${tabstatus}&companionId=${queryID?.id}&type=2`)
                          router.push(`/signin?redirectToUpdatedUrl=${url}`)
                          // router.push(
                          //   `/signin?redirectTourl=${router.asPath}&companionId=${queryID?.id}&${encodedData}&type=${queryID?.type}`
                          // );
                        } else {
                          console.error(
                            "Invalid date provided for bookTimeSlot."
                          );
                        }
                        // let encodedData = `startDate=${btoa(
                        //   bookTimeSlot?.startDate
                        // )}&endDate=${btoa(bookTimeSlot?.endDate)}`;
                        // router.push(
                        //   `/signin?redirectTourl=${router.asPath}&companionId=${queryID?.id}&${encodedData}&type=${queryID?.type}`
                        // );
                      }
                    }}
                  >
                    {getLanguages(checklanguage, "continue")}
                  </button>
                </div>
              ) : (
                <div className="d-flex align-center modal-btn-wrpr">
                  <button
                    className="btn-secondary sm w-100"
                    onClick={() => {
                      setStep(1);
                    }}
                  >
                    {getLanguages(checklanguage, "back")}
                  </button>
                  <button
                    type="submit"
                    className={`btn-accent sm w-100 ${submitting && "btn-loader"
                      }`}
                    onClick={(e) => {
                      if (!userInfo?.User?.phoneNumber) {
                        if(!validateFormData()){
                          return false;
                        }
                        updatePhoneNumber(e);
                      }
                      setTimeout(() => {
                        scheduleMeeting(e);
                      }, 200);
                    }}
                  >
                    {getLanguages(checklanguage, "scheduleAnAppointment")}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <LimitReachedModal show={showErrorModal} onHide={errorModal} />
    </>
  );
}

export default CompanionScheduleModal;
