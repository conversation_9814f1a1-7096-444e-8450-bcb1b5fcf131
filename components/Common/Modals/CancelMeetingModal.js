import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Text<PERSON>reaField from "../../FormFields/TextareaField";
import { checklanguage, getLanguages } from "../../../constant";
import { cancelMeeting } from "../../../redux/action/kuby-companion";
const CancelMeetingModal = ({
  show,
  onHide,
  id,
  setmeetingPopupSuccess,
  setsuccessMeetingType,
  setisCancelModalOpen,
}) => {
  const { handleSubmit, control } = useForm();
  const [isFetching, setisFetching] = useState(false);

  const onSubmit = async (formvalues) => {
    setisFetching(true);
    try {
      let response = await cancelMeeting({ id: id, reason: formvalues.reason });
      setisFetching(false);
      onHide();
      setmeetingPopupSuccess(true);
      setsuccessMeetingType(2);
    } catch (err) {
      setisFetching(false);
    }
  };
  return (
    <div
      className={`modal camcel-meeting-modal  ${show ? "show" : ""}`}
      id="scheduleModal"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="camcel-modal-content">
        <div className="modal-dialog">
          <div style={{ borderBottom: "none" }} className="modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={() => setisCancelModalOpen(false)}
            >
              <span className="icon close-icon"></span>
            </a>
            <div className="schedule-top">
              <h4 className="h4 text-dark-grey fw500 text-center">
                {getLanguages(checklanguage, "cancelMeeting")}
              </h4>
            </div>
          </div>
          <label className="label_cancel">
            {getLanguages(checklanguage, "reasonLabel")}
          </label>
          <div className="modal-body">
            <form onSubmit={handleSubmit(onSubmit)}>
              <TextAreaField
                control={control}
                autoComplete="off"
                name="reason"
                labelClass="f-label"
                type="text"
                className="form-control"
                inputClassName="f-in w-100"
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "reasonRequired"),
                  },
                }}
              />
              <div className="form-btns-wrpr">
                <div className="d-flex align-center cancel_meeting_btn btn-wrpr">
                  <button
                    type="button"
                    className="btn-secondary sm"
                    onClick={() => setisCancelModalOpen(false)}
                  >
                    <span className="btn-text-wrpr">
                      {getLanguages(checklanguage, "back")}
                    </span>
                  </button>
                  <button
                    type="submit"
                    className={`btn-accent sm ${isFetching && "btn-loader"}`}
                  >
                    <span className="btn-text-wrpr">
                      {getLanguages(checklanguage, "cancelAMeeting")}
                    </span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CancelMeetingModal;
