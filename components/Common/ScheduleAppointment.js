import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useRouter } from "next/router";
import {
  APPOINTMENT_SCHEDULE_SETTINGS,
  checklanguage,
  convertUTCToLocalMinutes,
  getLanguages,
  timeZoneConversion,
} from "../../constant";
import { useEffect, useState } from "react";
import moment from "moment";
import _ from "lodash";
import Calendar from "react-calendar";
import { useSelector } from "react-redux";
import { addToWaitingList, checkWaitingList } from "../../redux/action/kuby-companion";

function ScheduleAppointment({
  onHide,
  fSlots,
  TimeSlotArray,
  setbookTimeSlot,
  bookTimeSlot,
  scheduleTime,
  getVitaDetail,
  timeSlotData
}) {
  var timezone = moment.tz.guess();
  const { userInfo } = useSelector((state) => state.user);
  const [freeSlots, setFreeSlots] = useState(true);
  const router = useRouter();
  const { tabstatus } = router.query;
  let tabType = 2;
  let companionType = tabstatus

  let currentDate = moment(new Date()).format("MMMM Y");
  const [addedToWaitingList, setAddedToWaitingList] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(currentDate);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [checkingWaitingList, setCheckingWaitingList] = useState(true);
  const [value, onChange] = useState(new Date());
  const [daySlots, setDaySlots] = useState(null);

  useEffect(() => {
    if (userInfo?.User?.email) {
      checkIfUserAlreadyAdded(getVitaDetail?.UserProfile?.userId);
    } else {
      setTimeout(() => {
        setAddedToWaitingList(false)
        setCheckingWaitingList(false)
      }, 500)

    }

  }, [getVitaDetail])

  useEffect(() => {
    setFreeSlots(fSlots)
  }, [fSlots])

  const checkIfUserAlreadyAdded = async (companionId) => {
    try {
      let response = await checkWaitingList({ companionId: companionId });
      if (response?.data?.responseData) {
        setAddedToWaitingList(true)
      } else {
        setAddedToWaitingList(false)
      }
      setCheckingWaitingList(false)
    } catch (err) {
      setCheckingWaitingList(false)
      console.log(err);
      setIsSubmitting(false);
    }
  }
  



  const onSelectTimeSlot = (e, time) => {
    e.preventDefault();
    setbookTimeSlot((prevState) => ({
      ...prevState,
      startDate: moment(time?.s_startTime).toISOString(),
      endDate: moment(time?.s_endTime).toISOString(),
      timings: [
        {
          startTime: time?.startTime,
          endTime: time?.endTime,
        },
      ],
    }));
  };

  const fetchSlots = (date) => {


    //let obj_to_locate = { date: date };
    console.log(date, 'dddddd8888');
    console.log(moment(date).toISOString(), 'dddddd');
    console.log(timeZoneConversion(date, timezone), 'dddddd222');
    let dd = timeZoneConversion(date, timezone, true);
    dd = moment(dd).format("Y-MM-DD");
    console.log(dd, 'dddddd333');
    let slotsArray = [];
    if (timeSlotData[dd] && timeSlotData[dd].length > 0) {
      console.log(timeSlotData[dd], 'dddddd33334444')
      for (const [index, obj] of timeSlotData[dd].entries()) {
        //slotsArray.push(moment(obj?.s_startTime).format("HH:mm"))
        slotsArray.push(obj);
      }

    }
    setDaySlots(slotsArray)
    
    setTimeout(() => {
      document.getElementById("slots-container")?.scrollIntoView();
    }, 100);
  };

  const addToSlotList = async (companionId) => {
    setIsSubmitting(true)
    let payload = { companionId };
    try {
      let resonse = await addToWaitingList(payload);
      setAddedToWaitingList(true)
      setIsSubmitting(false)
    } catch (err) {
      console.log(err);
      setIsSubmitting(false);
    }
  }

  console.log(timeSlotData, 'TimeSlotArrayTimeSlotArrayTimeSlotArrayTimeSlotArray')
  return (
    <>
      <p className="label fw500 text-grey-5">
        {getLanguages(checklanguage, "selectADateAndTime")}:
      </p>
      <div className="appointment-calender-wrpr">
        <div className="d-flex align-center justify-between">
          
          <p className="text-dark-grey fw600 h6"> {currentMonth}</p>
        </div>
        
        <div className="calendar-container-div">
          {
            timeSlotData
              ?
              <Calendar
                className={"meeting-schedule-calendar"}
                tileClassName={"calender-day-tile"}
                onChange={onChange}
                value={value}
                minDate={new Date()}
                minDetail="month"
                showFixedNumberOfWeeks={false}
                showNeighboringMonth={false}
                showNavigation={true}
                onClickDay={fetchSlots}
                onActiveStartDateChange={(e) => {
                  setCurrentMonth(moment(e.activeStartDate).format("MMMM, Y"));
                }}
                nextLabel={<span className="icon arrow-icon"></span>}
                prevLabel={<span className="icon arrow-icon"></span>}
                tileDisabled={({ activeStartDate, date, view }) => {
                  //console.log(moment(date).toISOString(), 'dddddd');
                  //console.log(timeZoneConversion(date, timezone), 'dddddd222');
                  let dd = timeZoneConversion(date, timezone, true);
                  dd = moment(dd).format("Y-MM-DD");
                  //console.log(dd, 'dddddd333');
                  if (timeSlotData[dd]) {
                    return false;
                  } else {
                    return true;
                  }

                }}
              />
              :
              <></>
          }

          {
            freeSlots
              ?
              <></>
              :
              addedToWaitingList
                ?
                <div className="waiting-list-container">
                  <div className="waiting-list-inner">
                    <figure className="wt_figure">
                      <img src="/images/added-to-waiting-list.png" />
                    </figure>
                    <div className="waiting-wrap-content">
                      <h3 className="waiting-wrap-title">{getLanguages(checklanguage, "addedToWaitingListSuccessMessageTitle")}</h3>
                      <p className="waiting-wrap-text">{getLanguages(checklanguage, "addedToWaitingListSuccessMessageDescription", ["nameOfCompanion"], [getVitaDetail?.UserProfile?.firstName + " " + getVitaDetail?.UserProfile?.lastName], true)}</p>
                    </div>
                    <button className={"btn-accent notify_btn"} onClick={() => {
                      //document.getElementById("#closeModal").click();
                      onHide();
                    }}>{getLanguages(checklanguage, "addedToWaitingListSuccessMessageButton")}</button>
                  </div>
                </div>
                :
                checkingWaitingList == false &&
                <div className="waiting-list-container">
                  <div className="waiting-list-inner">
                    <figure className="wt_figure">
                      <img src="/images/No-slots.png" />
                    </figure>
                    <div className="waiting-wrap-content">
                      <h3 className="waiting-wrap-title">{getLanguages(checklanguage, "noSlotsAvailable")}</h3>
                      <p className="waiting-wrap-text">{getLanguages(checklanguage, "noSlotsAvailableMessage", ["nameOfCompanion"], [getVitaDetail?.UserProfile?.firstName + " " + getVitaDetail?.UserProfile?.lastName], true)}</p>
                    </div>
                    <button className={`${"btn-accent notify_btn"} ${isSubmitting ? "btn-loader" : ""}`} onClick={() => {
                      if (userInfo?.User?.email) {
                        addToSlotList(getVitaDetail?.UserProfile?.userId);
                      } else {
                        let redirectToUpdatedUrl = btoa(`/dashboard/kuby-companion?tabstatus=${companionType}&companionId=${getVitaDetail?.UserProfile?.userId}&type=2`);
                        router.push(
                          `/signin?redirectToUpdatedUrl=${redirectToUpdatedUrl}`
                        );
                      }

                    }}>

                      {
                        userInfo?.User?.email
                          ?
                          getLanguages(checklanguage, "waitingListButton", ["emailOfCustomer"], [userInfo?.User?.email], true)
                          :
                          getLanguages(checklanguage, "waitingListNonLoggedInButton")

                      }

                    </button>
                  </div>
                </div>
          }
        </div>
        <div className="slot-wrpr">
          {daySlots?.length > 0 ? (
            <>
              <p className="p fw600 text-dark-grey">
                {getLanguages(checklanguage, "timeSlots")}
              </p>
              <div className="d-flex align-center cal-row" id="slots-container">
                {
                  daySlots?.map((time, i) => {
                    console.log(time.s_startTime, bookTimeSlot?.startDate, 'llllllll');
                    return (
                      <div className={`time  ${
                        time?.s_startTime == bookTimeSlot?.startDate
                          ? "active"
                          : ""
                        }`}>
                        {moment(time?.s_startTime).format("HH:mm")}
                        <a
                          href="javascript:void(0)"
                          onClick={(e) => onSelectTimeSlot(e, time)}
                          className="time-link"
                        ></a>
                      </div>
                    )
                  })
                }
                {/* {daySlots?.map((time, i) => {
                  let isDisable = time.isEnable
                    ? time?.start <
                    moment().add(scheduleTime, "hours").utc().toDate()
                    : true;
                  return !isDisable ? (
                    <div
                      className={`time  ${isDisable
                        ? "disabled"
                        : time?.start == bookTimeSlot?.startDate
                          ? "active"
                          : ""
                        }`}
                      key={i}
                    >
                      {moment(time?.start).format("HH:mm")}
                      <a
                        href="javascript:void(0)"
                        onClick={(e) => onSelectTimeSlot(e, time)}
                        className="time-link"
                      ></a>
                    </div>
                  ) : (
                    <></>
                  );
                })} */}
              </div>
              {/* <div id="slots-container"></div> */}
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
}

export default ScheduleAppointment;
