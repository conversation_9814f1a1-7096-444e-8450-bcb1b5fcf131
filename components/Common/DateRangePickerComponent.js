import React from "react";
import DateRangePicker from "@wojtekmaj/react-daterange-picker/dist/entry.nostyle";
import "@wojtekmaj/react-daterange-picker/dist/DateRangePicker.css";
import "react-calendar/dist/Calendar.css";
export const DateRangePickerComponent = ({
  dateSelected,
  setdateSelected,
  minDDate,
  maxDate,
}) => {
  const onChangeDate = (value) => {
    setdateSelected(value);
  };
  const language = localStorage.getItem("language");
  const dateFormat = language === "en" ? "MM/dd/y" : "dd.MM.y";
  return (
    <DateRangePicker
      disableClock={false}
      dayPlaceholder={"dd"}
      monthPlaceholder={"mm"}
      yearPlaceholder={"yyyy"}
      calendarIcon={<img src="/images/calender-icon.svg"></img>}
      format={dateFormat}
      clearIcon={false}
      clockClassName=""
      onChange={onChangeDate}
      minDate={minDDate}
      className="form-control"
      maxDate={maxDate}
      value={dateSelected}
    />
  );
};
