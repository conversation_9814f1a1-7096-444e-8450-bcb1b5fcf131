import { useState } from "react";
import { getLanguages, checklanguage } from "../../constant";

const CookieConsentCard = () => {
    const [cookieConsent, setCookieConsent] = useState(localStorage?.getItem('cookies_consent'));
    const [cookieMoreSettings, setCookieMoreSettings] = useState(false);
    const allowAllCookies = () => {
        localStorage.setItem('cookies_consent', true);
        setCookieConsent(true)
    }

    const nextCookieSlide = () => {
        setCookieMoreSettings(true)
    }
    return (
        <div>
            {
                !cookieConsent
                    ?
                    <div>

                        {!cookieMoreSettings ? (
                            <div className="cookie-popup">
                                <h4>{getLanguages(checklanguage, "cookiesConsentTitle")}</h4>
                                <p>
                                {getLanguages(checklanguage, "cookieConsentDescription")}
                                </p>
                                <div className="d-flex align-center justify-center btn-block mb-20">
                                    <button
                                        className="btn_accept_all btn-accent sm"
                                        onClick={() => {
                                            allowAllCookies();
                                        }}
                                    >
                                        {getLanguages(checklanguage, "allowAll")}
                                    </button>
                                    <button className="btn page_link sm" target="_blank" onClick={() => {
                                        nextCookieSlide();
                                    }}>
                                        <span><strong>{getLanguages(checklanguage, "moreSettings")}</strong></span>
                                    </button>
                                </div>
                                <div className="Popup_footer_links">
                                    <a href="/terms" target="_blank" className="page_link">
                                    {getLanguages(checklanguage, "termsAndConditions")}
                                    </a>
                                </div>
                                <div className="Popup_footer_links mt-20">

                                   
                                </div>

                            </div>
                        ) :
                            <div>
                                <div className="cookie-popup">
                                    <h4>{getLanguages(checklanguage, "cookiesSettings")}</h4>
                                    <div className="cookie-cat-wrpr">
                                        <h5 className="cat-name">Essential</h5>
                                        <ul>
                                            <li><div className="check-wrpr mb-10">
                                                <input type='checkbox' checked name='sessionCookies' id='sessionCookies' disabled />
                                                &nbsp;
                                                <span>{getLanguages(checklanguage, "sessionCookiesAccepted")}</span>
                                            </div>
                                            </li>
                                            <li><div className="check-wrpr mb-10">
                                                <input type='checkbox' checked name='intercomCookies' id='intercomCookies' disabled />
                                                &nbsp;
                                                <span>{getLanguages(checklanguage, "intercomCookiesAccepted")}</span>
                                            </div>
                                            </li>
                                            <li><div className="check-wrpr mb-20">
                                                <input type='checkbox' checked name='vimeoCookies' id='vimeoCookies' disabled />
                                                &nbsp;
                                                <span>{getLanguages(checklanguage, "vimeoCookiesAccepted")}</span>
                                            </div>
                                            </li>
                                            
                                        </ul>
                                    </div>
                                    <div>
                                        
                                        
                                        
                                        <div className="btn-block">
                                            <button
                                                className="btn-accent sm"
                                                onClick={() => {
                                                    allowAllCookies();
                                                }}
                                            >
                                                {getLanguages(checklanguage, 'allow')}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                    :
                    <></>
            }
        </div>
    )
}

export default CookieConsentCard;