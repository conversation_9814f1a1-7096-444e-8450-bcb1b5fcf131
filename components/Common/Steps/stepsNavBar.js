function StepsNavBar({ array, currentStep }) {
  return (
    <>
      <ul className="d-flex align-center justify-center schedule-list desktop-only">
        {array?.map((arr, index) => (
          <>
            <li
              key={index}
              className={`schedule-points ${
                arr?.step == currentStep ? "active" : ""
              }  ${arr?.step < currentStep ? "complete" : ""}`}
            >
              <p className="d-flex align-center schedule-item">
                <span className="point">{arr?.step}</span>
                {arr?.title}
              </p>
            </li>
            {index < array?.length - 1 && (
              <hr className="schedule-points-gap" />
            )}
          </>
        ))}
      </ul>
    </>
  );
}

export default StepsNavBar;
