
import React from 'react'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
import Slider from 'react-slick'
const CurrentAppointmentsSkeleton = ({ listsToRender }) => {
    return (
        <>
                {Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div className="hs_card active-star student">
                            <div className="cr_flex flex-1" >
                                <div className="image-container">
                                    <Skeleton circle={true} height={50} width={50} />
                                </div>
                                <div className="flex-1 title_price">
                                    <div className="title flex">
                                        <h3 className="truncate"><Skeleton count={1} height={25} width={200} /></h3>
                                        <div className="student_status">
                                            <div className="st_wrap">
                                                <span className=""><Skeleton count={1} height={25} width={50} /></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="price price_per_min student">
                                        <div><Skeleton count={1} height={25} width={100} /></div>

                                    </div>
                                </div>
                                <div className="cr_time">
                                    <div className="hours"><Skeleton count={1} height={25} width={100} /></div>
                                    <div className="label"><Skeleton count={1} height={25} width={50} /></div>
                                </div>
                            </div>
                            <Skeleton count={1} height={35} width={200} />
                        </div>
                    ))
                }
        </>

    )
}

export default CurrentAppointmentsSkeleton