import React from 'react'
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
const SeminarSkeleton = ({ listsToRender, type }) => {
    return (
        <>
            {
                Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div key={index}>
                            {
                                type === 1 ?
                                    <div className="img-name-card seminar-card" key={index} style={{minWidth: '220px'}}>
                                        <div className="relative img-card">
                                            <Skeleton count={1} className="img-fluid cover-img" />
                                            <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                                                <span className="lock"></span>
                                            </div>
                                        </div>
                                        <div className="seminar-card-details">
                                            <div className="seminar-card-wrpr">
                                                <h6 className="h6 text-grey-3 mb-0 seminar-name"><span className="fw600">  <Skeleton count={1} /></span>   <Skeleton count={1} /></h6>
                                                <p className="fs12 text-grey-5"><Skeleton count={1} /></p>
                                            </div>
                                        </div>
                                    </div>
                                    :
                                    <div className="img-name-card seminar-card" key={index}>
                                        <div className="relative img-card">
                                            <Skeleton count={1} className="img-fluid cover-img" />
                                            <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                                                <span className="lock"></span>
                                            </div>
                                        </div>
                                        <div className="seminar-card-details">
                                            <div className="seminar-card-wrpr">
                                                <h6 className="h6 text-grey-3 mb-0 seminar-name"><span className="fw600">  <Skeleton count={1} /></span>   <Skeleton count={1} /></h6>
                                                <p className="fs12 text-grey-5"><Skeleton count={1} /></p>
                                            </div>
                                        </div>
                                        <div className="d-flex align-center justify-between seminar-card-bottom">
                                            <p className="h6 fw600 text-grey-1">  <Skeleton count={1} /></p>
                                            <div className="d-flex align-center seminar-card-btns react-loading-skeleton">
                                                <button style={{ border: "none" ,marginLeft:"4px" }} className="btn w-100 react-loading-skeleton"> <Skeleton count={1} className="icon wishlist-icon-green" /> </button>
                                                <button style={{ border: "none" }} className="btn w-100 react-loading-skeleton"> <Skeleton count={1} className="icon wishlist-icon-green" /> </button>
                                            </div>
                                        </div>
                                    </div>
                            }
                        </div>
                    ))
            }
        </>
    )
}

export default SeminarSkeleton

export const SkeletonVemio = () => {
    return (

        <div className="h-100 tab-right-aside">
            <Skeleton count={1} className="sekeleton-video-wrpr" />
        </div>
    );
};

export const LessonSkeleten = () => {
    return (
        <div className="tab-listing-wrap">
            <div className="product-list-wrap accordion-items">
            <Skeleton count={3}  />
            </div>
            <div className="product-list-wrap accordion-items">
            <Skeleton count={3}  />
            </div>
            <div className="product-list-wrap accordion-items">
            <Skeleton count={3}  />
            </div>
            <div className="product-list-wrap accordion-items">
            <Skeleton count={3}  />
            </div>
        </div>
    )
}