import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
export const ProductSkeleton = ({ listsToRender }) => {
    return (
        <>
            {
                Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div className="d-flex align-center product-card" key={index}>
                            <div className="product-image-wrpr">
                                <Skeleton count={15} />
                            </div>
                            <div className="product-details-wrpr">
                                <div className="d-flex fd-col product-top-wrpr">
                                    <p className="text-grey-6 product-cat"><span className="cat-name">   <Skeleton count={1} /></span></p>
                                    <h6 className="text-grey-3 fw600 product-title">   <Skeleton count={1} /></h6>
                                    <p className="text-grey-5 product-desc" >   <Skeleton count={1} /></p>
                                </div>
                                <div className="d-flex fd-col product-bottom-wrpr">
                                    <p className="text-grey-1 fw600 product-price"><Skeleton count={1} /></p>
                                    <div className="d-flex align-center prod-btn-wrpr">
                                        <a style={{ border: 'none' }} href="javascript:void(0)" className="btn w-50 prod-btn cart-btn react-loading-skeleton"><span className="icon cart-icon-green react-loading-skeleton"><Skeleton count={1} /></span><Skeleton count={1} width="200px" /></a>
                                        <a style={{ border: 'none' }} href="javascript:void(0)" className="btn-accent prod-btn w-50 buy-now-btn"><Skeleton count={1} /><Skeleton count={1} /></a>

                                    </div>
                                </div>
                            </div>
                        </div>
                    ))
            }
        </>
    )
}

export const ProductDetailSkeleton = ({ listsToRender }) => {
    return (
        <>
            {
                Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div className="audio-option-block" key={index}>
                            <p className="fw500 text-grey-5 label"> <Skeleton count={1} width={300} height={20} /> </p>
                            <div className="audio-opt-wrpr" >
                                {
                                    Array(listsToRender)
                                        .fill(1)
                                        .map((card, index) => (
                                            <div className="form-group" key={index}>
                                                <input
                                                    type="radio"
                                                    name={"attri_"}
                                                    className={'audio-input'}
                                                />
                                                <label
                                                    className="language-label" htmlFor="dvd">
                                                    <span className=""> <Skeleton count={1} width={50} height={20} /></span>
                                                </label>
                                            </div>
                                        ))
                                }
                            </div>

                        </div>
                    ))
            }
        </>
    )
}