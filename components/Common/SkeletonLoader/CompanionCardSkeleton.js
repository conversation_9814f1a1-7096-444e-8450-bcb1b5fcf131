import React from 'react'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
const CompanionCardSkeleton = ({ listsToRender }) => {
    return (
        <>
            {
                Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div className="companion-card" key={index}>
                            <div className="text-center companion-details">
                                <div className="comp-img">
                                    <Skeleton count={10} />
                                </div>
                                <div className="d-flex align-center justify-center comp-name-wrpr">
                                    <h6 className="h6 fw600 comp-name">{ } </h6>
                                </div>
                                <p className="text-grey-5 comp-desc">
                                    <Skeleton count={1} />
                                </p>
                                <div className="rating-stars">
                                    <Skeleton count={1} />
                                </div>
                                <div className="d-flex align-center justify-center">
                                    <button
                                        className="btn-tertiary"
                                        id="profile"
                                    >
                                        <span className="icon user-icon"><Skeleton count={1} /></span>
                                    </button>
                                    <button className="btn-tertiary" >
                                        <span className="icon video-icon"><Skeleton count={1} /></span>
                                    </button>
                                </div>
                            </div>
                            <div className="comp-card-button-wrpr">
                                <div className="d-flex align-center justify-between">
                                    <p className="text-grey-6">
                                        <Skeleton count={1} />
                                    </p>
                                    <p className="h6 text-grey-6">
                                        <Skeleton count={1} />
                                    </p>
                                </div>
                                <Skeleton count={2} />
                                <Skeleton count={2} />
                            </div>
                        </div>
                    ))
            }
        </>
    )
}

export default CompanionCardSkeleton