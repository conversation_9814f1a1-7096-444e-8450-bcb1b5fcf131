
import React from 'react'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
import Slider from 'react-slick'
import { SlickSettings } from '../../../constant'
const ReceptionSkeleton = ({ listsToRender }) => {
    return (
        <>
            <Slider {...SlickSettings}>
                {Array(listsToRender)
                    .fill(1)
                    .map((card, index) => (
                        <div className="comp-card" key={index}>
                            <div className="comp-card-top">
                                <div className="comp-img">
                                    <Skeleton count={5} />
                                </div>

                                <div className="d-flex align-center justify-center comp-name-wrpr">
                                    <h6 className="h6 fw500 comp-name"> <Skeleton count={1} /> </h6>
                                    <Skeleton count={2} />
                                </div>
                                <div className="comp-name-wrpr">
                                    <Skeleton count={1} />
                                </div>
                                <div className="d-flex align-center justify-center comp-name-wrpr">
                                    <Skeleton count={1} />
                                </div>
                                <p className="text-grey-5 comp-desc">
                                    <Skeleton count={1} />
                                </p>
                                <div className="rating-stars">
                                    <Skeleton count={1} />
                                </div>
                            </div>
                            <div className="comp-price-details">
                                <p className="comp-price">
                                    <Skeleton count={1} />
                                </p>
                            </div>
                        </div>
                    ))
                }
            </Slider>
        </>

    )
}

export default ReceptionSkeleton