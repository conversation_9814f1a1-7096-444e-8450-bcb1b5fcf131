// import React, { useEffect, useState } from "react";
// import { useForm } from "react-hook-form";
// import InputField from "../FormFields/InputField";
// import { getLanguages, checklanguage } from "../../constant";
// import Link from "next/link";
// import { useRouter } from "next/router";
// import { SAVE_PROFILE_INFO, siginIn } from "../../redux/action/user/user";
// import { useCookie } from "next-cookie";
// import { useDispatch } from "react-redux";
// import NavLink from "../../helpers/ActiveLink";
// const SignIn = () => {
//   const [hasWindow, setHasWindow] = useState(false);
//   const [isFetching, setisFetching] = useState(false);
//   const [showPasswordShown, setshowPasswordShown] = useState(false);
//   const router = useRouter();
//   const { query } = router;
//   const cookies = useCookie();
//   const dispatch = useDispatch();
//   useEffect(() => {
//     if (typeof window !== "undefined") {
//       setHasWindow(true);
//     }
//     if (cookies.get("jwtToken") != undefined) {
//       router.push("/");
//     }
//   }, []);
//   const { handleSubmit, control, setValue, getValues, clearErrors } = useForm();
//   // This method is used to get Login Form Data and call api to submit login data.

//   const onSubmit = async (formValues) => {
//     console.log("Helloooo");
//     try {
//       setisFetching(true);
//       formValues.role = 2;
//       var logindetail = await siginIn(formValues);
//       console.log(`logggg`, logindetail);
//       let { data, status } = logindetail;

//       let { responseData } = data;
//       if (status === 200) {
//         cookies.set("jwtToken", responseData?.token, {
//           path: "/",
//           maxAge: 1000000000000,
//         });
//         if (typeof window != undefined) {
//           localStorage.setItem("jwtToken", responseData?.token);
//         }
//         dispatch({
//           type: SAVE_PROFILE_INFO,
//           payload: responseData,
//         });
//         setisFetching(false);
//         if (query?.redirectTourl) {
//           if (query?.companionId) {
//             router.push(
//               `${query?.redirectTourl}&companionId=${parseInt(
//                 query.companionId
//               )}&startDate=${query.startDate}&endDate=${query?.endDate}&type=${
//                 query?.type
//               }`
//             );
//           } else {
//             router.push(query?.redirectTourl);
//           }
//         } else {
//           router.push("/");
//         }
//       }
//     } catch (err) {
//       setisFetching(false);
//       console.log(`loginErrror`, err.response.data.responseData.isImportedUser);
//     }
//   };
//   return (
//     <div className="w-100 onboard-tab-wrpr">
//       <ul id="tabs" className="tabs">
//         <li className="tab-item">
//           <NavLink
//             path={"/signin"}
//             activeClassName="fw500 tab-link"
//             name={getLanguages(checklanguage, "login")}
//           ></NavLink>
//         </li>
//         <li className="tab-item">
//           <NavLink
//             path="/signup"
//             activeClassName="fw500 tab-link"
//             name={getLanguages(checklanguage, "newregistration")}
//           ></NavLink>
//         </li>
//       </ul>
//       <div className="tab-contents active">
//         <div className="form-title-wrpr">
//           <h2 className="fw500 text-grey-2 form-head">
//             {getLanguages(checklanguage, "logincontinue")}
//           </h2>
//           <p className="text-grey-6 form-desc">
//             {getLanguages(checklanguage, "loginPageText")}
//           </p>
//         </div>
//         {hasWindow && (
//           <form onSubmit={handleSubmit(onSubmit)}>
//             <div className="w-100 form-wrpr">
//               <InputField
//                 control={control}
//                 autoComplete="off"
//                 label={getLanguages(checklanguage, "email")}
//                 name="email"
//                 labelClass="f-label"
//                 type="text"
//                 placeholder={getLanguages(checklanguage, "emailPlaceholder")}
//                 className="form-control"
//                 inputClassName="f-in w-100"
//                 autoFocus={true}
//                 rules={{
//                   required: {
//                     value: true,
//                     message: getLanguages(checklanguage, "emailRequired"),
//                   },
//                   pattern: {
//                     value:
//                       /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
//                     message: getLanguages(checklanguage, "validEmail"),
//                   },
//                 }}
//               />
//               <div className="form-in w-100">
//                 <InputField
//                   control={control}
//                   autoComplete="off"
//                   label={getLanguages(checklanguage, "password")}
//                   name="password"
//                   setshowPasswordShown={setshowPasswordShown}
//                   showPasswordShown={showPasswordShown}
//                   type={showPasswordShown ? "text" : "password"}
//                   inputClassName="f-in right-icon w-100"
//                   className="form-control"
//                   placeholder={getLanguages(
//                     checklanguage,
//                     "passwordPlaceholder"
//                   )}
//                   labelClass="f-label"
//                   hideandshow={true}
//                   rules={{
//                     required: {
//                       value: true,
//                       message: getLanguages(checklanguage, "psRequired"),
//                     },
//                     minLength: {
//                       value: 8,
//                       message: getLanguages(checklanguage, "passwordAtleast8"),
//                     },
//                     maxLength: {
//                       value: 20,
//                       message: getLanguages(checklanguage, "passwordAtleast20"),
//                     },
//                   }}
//                 />
//                 <div className="d-block text-right frgt-pass">
//                   {" "}
//                   <Link legacyBehavior href="/forget-password">
//                     <a className="frgt-pass-link">
//                       {getLanguages(checklanguage, "forgotpasswrd")}
//                     </a>
//                   </Link>
//                 </div>
//               </div>
//               {/* <div className="f-in w-100 form-check">
//                 <input type="checkbox" className="form-check-input" />
//                 <label className="form-check-label">
//                     Remember information
//                 </label>
//             </div> */}
//               <div className="form-blk w-100 form-btn-wrpr">
//                 <button
//                   type="submit"
//                   className={`btn-accent fw500 w-100 ${
//                     isFetching && "btn-loader"
//                   }`}
//                 >
//                   <span className="btn-text-wrpr">
//                     {getLanguages(checklanguage, "login")}
//                   </span>
//                 </button>
//               </div>
//             </div>
//           </form>
//         )}
//       </div>
//     </div>
//   );
// };

// export default SignIn;

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import InputField from "../FormFields/InputField";
import { getLanguages, checklanguage } from "../../constant";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  SAVE_PROFILE_INFO,
  resendVerificationCode,
  siginIn,
} from "../../redux/action/user/user";
import { useCookie } from "next-cookie";
import { useDispatch } from "react-redux";
import NavLink from "../../helpers/ActiveLink";
import { setLanguage } from "../../utils/language";

const SignIn = () => {
  const [hasWindow, setHasWindow] = useState(false);
  const [isFetching, setisFetching] = useState(false);
  const [showPasswordShown, setshowPasswordShown] = useState(false);
  const [isImportedUser, setIsImportedUser] = useState(false);
  const [importedUserEmail, setImportedUserEmail] = useState("");
  const router = useRouter();
  const { query } = router;
  const cookies = useCookie();
  const dispatch = useDispatch();

  useEffect(() => {
    if (typeof window !== "undefined") {
      setHasWindow(true);
    }
    if (cookies.get("jwtToken") !== undefined) {
      router.push("/");
    }
    setLanguage(router, cookies);
  }, []);

  const { handleSubmit, control, setValue, getValues, clearErrors } = useForm();

  const onSubmit = async (formValues) => {
    try {
      setisFetching(true);
      formValues.role = 2;
      formValues.email = formValues.email.trim().toLowerCase();
      formValues.password = formValues.password.trim();
      const logindetail = await siginIn(formValues);
      const { data, status } = logindetail;
      const { responseData } = data;

      if (status === 200) {
        cookies.set("jwtToken", responseData?.token, {
          path: "/",
          maxAge: 1000000000000,
        });
        if (typeof window !== "undefined") {
          localStorage.setItem("jwtToken", responseData?.token);
        }
        dispatch({
          type: SAVE_PROFILE_INFO,
          payload: responseData,
        });
        setisFetching(false);
        if(query?.redirectToUpdatedUrl){
          let qq = atob(query?.redirectToUpdatedUrl);
          router.push(qq);
        }
        if (query?.redirectTourl) {
          if (query?.companionId) {
            // const queryParams = new URLSearchParams(window.location.search);
            // const startDateEncoded = queryParams.get("startDate");
            // const endDateEncoded = queryParams.get("endDate");

            // const startDate = atob(startDateEncoded);
            // const endDate = atob(endDateEncoded);

            // // const startDate = new Date(startDateISO);
            // // const endDate = new Date(endDateISO);

            // console.log(startDate, endDate, "xx");

            // router.push(
            //   `${query?.redirectTourl}&companionId=${parseInt(
            //     query.companionId
            //   )}&startDate=${startDate}&endDate=${endDate}&type=${query?.type}`
            // );

            //router.push("/dashboard/kuby-companion?tabstatus=professionals");
            //alert(query?.redirectTourl)
            router.push(query?.redirectTourl);
          } else {
            router.push(query?.redirectTourl);
            //alert(query?.redirectTourl)
            //window.location.href = query?.redirectTourl;
          }
        } else {
          router.push("/");
        }
      }
    } catch (err) {
      setisFetching(false);
      if (err.response?.data?.responseData?.isImportedUser) {
        setIsImportedUser(true);
        setImportedUserEmail(formValues.email);
      } else {
        console.log(`loginError`, err);
      }
    }
  };
  const handleContinueToLogin = () => {
    setIsImportedUser(false);
    router.push("/signin");
  };
  const resendEmail = async (e) => {
    e.preventDefault();
    try {
      setisFetching(true);
      const verifySignUp = await resendVerificationCode({
        email: importedUserEmail,
      });
      toast.success(getLanguages(checklanguage, "emailSentSuccessfully"));
      setisFetching(false);
    } catch (error) {
      console.error("Error resending email:", error);
      setisFetching(false);
    }
  };
  return (
    <div className="w-100 onboard-tab-wrpr">
      {!isImportedUser && (
        <>
          <ul id="tabs" className="tabs">
            <li className="tab-item">
              <NavLink
                path={"/signin"}
                activeClassName="fw500 tab-link"
                name={getLanguages(checklanguage, "login")}
              />
            </li>
            <li className="tab-item">
              <NavLink
                path="/signup"
                activeClassName="fw500 tab-link"
                name={getLanguages(checklanguage, "newregistration")}
              />
            </li>
          </ul>
          <div className="tab-contents active">
            <div className="form-title-wrpr">
              <h2 className="fw500 text-grey-2 form-head">
                {getLanguages(checklanguage, "logincontinue")}
              </h2>
              <p className="text-grey-6 form-desc">
                {getLanguages(checklanguage, "loginPageText")}
              </p>
            </div>
          </div>
        </>
      )}
      {hasWindow && (
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="w-100 form-wrpr">
            {!isImportedUser ? (
              <>
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "email")}
                  name="email"
                  labelClass="f-label"
                  type="text"
                  placeholder={getLanguages(checklanguage, "emailPlaceholder")}
                  className="form-control"
                  inputClassName="f-in w-100"
                  autoFocus={true}
                  onBlur={(e) => {
                    setValue("email", e.trim())
                  }}
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "emailRequired"),
                    },
                    pattern: {
                      value:
                        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                      message: getLanguages(checklanguage, "validEmail"),
                    },
                  }}
                />
                <div className="form-in w-100">
                  <InputField
                    control={control}
                    autoComplete="off"
                    label={getLanguages(checklanguage, "password")}
                    name="password"
                    setshowPasswordShown={setshowPasswordShown}
                    showPasswordShown={showPasswordShown}
                    type={showPasswordShown ? "text" : "password"}
                    inputClassName="f-in right-icon w-100"
                    className="form-control"
                    
                    placeholder={getLanguages(
                      checklanguage,
                      "passwordPlaceholder"
                    )}
                    labelClass="f-label"
                    hideandshow={true}
                    rules={{
                      required: {
                        value: true,
                        message: getLanguages(checklanguage, "psRequired"),
                      },
                      minLength: {
                        value: 8,
                        message: getLanguages(
                          checklanguage,
                          "passwordAtleast8"
                        ),
                      },
                      maxLength: {
                        value: 20,
                        message: getLanguages(
                          checklanguage,
                          "passwordAtleast20"
                        ),
                      },
                    }}
                  />
                  <div className="d-block text-right frgt-pass">
                    <Link legacyBehavior href="/forget-password">
                      <a className="frgt-pass-link">
                        {getLanguages(checklanguage, "forgotpasswrd")}
                      </a>
                    </Link>
                  </div>
                </div>
                <div className="form-blk w-100 form-btn-wrpr">
                  <button
                    type="submit"
                    className={`btn-accent fw500 w-100 ${
                      isFetching && "btn-loader"
                    }`}
                  >
                    <span className="btn-text-wrpr">
                      {getLanguages(checklanguage, "login")}
                    </span>
                  </button>
                </div>
              </>
            ) : (
              <>
                <div className="form-title-wrpr">
                  <h2 className="fw500 text-grey-1 text-center form-head">
                    {getLanguages(checklanguage, "checkMailtitle")}
                  </h2>
                  <p className="text-grey-6 text-center form-desc">
                    {getLanguages(checklanguage, "resetPasswordLinkSentTo")}
                    <br />
                    <span className="h6 fw700">{importedUserEmail}</span>
                  </p>
                </div>
                <div className="w-100 form-wrpr">
                  <div className="form-blk w-100">
                    <a
                      href={`mailto:${importedUserEmail}`}
                      className="btn btn-accent fw500 w-100"
                    >
                      {getLanguages(checklanguage, "open_mail")}
                    </a>
                    <div className="w-100 text-center resend-msg-wrpr">
                      <div className="w-100 text-center resend-msg-wrpr">
                        <p className="link-grey-3 fw500 resend-msg">
                          {getLanguages(checklanguage, "didnt_recieve_mail")}{" "}
                          <a
                            onClick={resendEmail}
                            href="javascript:void(0)"
                            className="fw600 text-accent"
                          >
                            {getLanguages(checklanguage, "clickresend")}
                          </a>
                        </p>
                      </div>
                    </div>
                    <div className="w-100 text-center back-to-login-wrpr">
                      <Link legacyBehavior href="/signin">
                        <a
                          onClick={handleContinueToLogin}
                          className="link-gray back-to-login"
                        >
                          <span className="icon gray-back"></span>
                          {getLanguages(checklanguage, "continuetoLogin")}
                        </a>
                      </Link>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </form>
      )}
    </div>
  );
};

export default SignIn;
