import React, { useEffect } from 'react'
import Link from 'next/link'
import { getLanguages, checklanguage } from "../../constant"
import { useDispatch, useSelector } from 'react-redux'
import { RESET_PASSWORD_ATATUS } from '../../redux/action/user/user'
import { useRouter } from 'next/router'
import { useCookie } from 'next-cookie'
const ResetPasswordSuccessfull = () => {
  const { resetpasswordstatus } = useSelector(state => state.user)
  const router = useRouter()
  const cookies = useCookie()
  const dispatch = useDispatch()
  useEffect(()=>{
    if ( cookies.get('jwtToken') != undefined) {
        router.push('/')
    }
},[])
  return (
    <div className="d-flex fd-col align-center onboarding-form-wrpr">
      <div className="form-title-wrpr succesfull-msg-wrpr">
        <div className="verification-status">
          <img src="/images/verification-check.svg" className="img-fluid" />
          <img src="/images/verification-fail.png" className="img-fluid d-none" />
        </div>
        <h2 className="fw500 text-grey-1 text-center form-head">{getLanguages(checklanguage, "successfullPasswordReset")}</h2>
        <p className="text-grey-6 text-center form-desc">{getLanguages(checklanguage, "successfullPasswordResetDesc")}</p>
      </div>
      <div className="form-blk w-100">
        <Link legacyBehavior href="/signin"><a onClick={() => dispatch({
          type: RESET_PASSWORD_ATATUS,
          payload: false
        })} className="btn btn-accent fw500 w-100">{getLanguages(checklanguage, "continuetoLogin")}</a></Link>
      </div>
    </div>
  )
}

export default ResetPasswordSuccessfull