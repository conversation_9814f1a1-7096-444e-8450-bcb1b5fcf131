import React, { useEffect, useState } from "react";
import InputField from "../FormFields/InputField";
import SingleCheckBox from "../FormFields/SingleCheckbox";
import SelectField from "../FormFields/SelectField";
import { useForm } from "react-hook-form";
import { getLanguages, checklanguage, TitleArray } from "../../constant";
import { siginUp, CHECK_SIGNUP_ATATUS } from "../../redux/action/user/user";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { useCookie } from "next-cookie";
import toast from "react-hot-toast";
import ReactSelectField from "../FormFields/SelectField";
import NavLink from "../../helpers/ActiveLink";

const SignUp = () => {
  const dispatch = useDispatch();
  const [settings, setSettings] = useState([]);
  const router = useRouter();
  const cookies = useCookie();
  const [isFetching, setisFetching] = useState(false);
  const [showPasswordShown, setshowPasswordShown] = useState(false);
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    setError,
    watch,
  } = useForm();
  watch("remeber_me");
  // This method is used to get Login Form Data and call api to submit login data.
  const [hasWindow, setHasWindow] = useState(false);
  let lang;
  useEffect(() => {
    if (typeof window !== "undefined") {
      setHasWindow(true);
    }
    if (cookies.get("jwtToken") != undefined) {
      router.push("/");
    }
  }, []);
  const getSettings = async () => {
    try {
      const request = await getSetting();
      setSettings(request?.data?.responseData?.records);
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };
  useEffect(() => {
    getSettings();
  }, []);
  const onSubmit = async (formValues) => {
    if (getValues("remeber_me") === undefined || !getValues("remeber_me")) {
      setError("remeber_me", {
        type: "required",
        message: "Please accept terms and conditions",
      });
      return;
    }
    delete formValues.remeber_me;
    setisFetching(true);
    try {
      let signupresponseData = await siginUp(formValues);
      let { data, status } = signupresponseData;
      let { responseData } = data;
      if (status === 200) {
        setisFetching(false);
        toast.success(getLanguages(checklanguage, "emailSentSuccessfully"));
        router.push(`/signup-email-sent?email=${encodeURIComponent(formValues.email)}`);
        // cookies.set("jwtToken", responseData?.token, {
        //     path: "/",
        //     maxAge: 1000000000000,
        // });

        dispatch({
          type: CHECK_SIGNUP_ATATUS,
          payload: true,
        });
        reset("", {
          keepValues: false,
        });
        setValue("title", "");
      }
    } catch (err) {
      setisFetching(false);
    }
  };
  const getUrl = (key) => {
    const language = localStorage.getItem("language");
    if (!settings || settings?.length === 0) return "#";

    const setting = settings?.find(
      (s) => s.key === `${key}_${language.toUpperCase()}`
    );
    return setting ? setting?.value : "#";
  };
  return (
    <div className="w-100 onboard-tab-wrpr">
      <ul id="tabs" className="tabs">
        <li className="tab-item">
          <NavLink
            path={"/signin"}
            activeClassName="fw500 tab-link"
            name={getLanguages(checklanguage, "login")}
          ></NavLink>
        </li>
        <li className="tab-item">
          <NavLink
            path="/signup"
            activeClassName="fw500 tab-link"
            name={getLanguages(checklanguage, "newregistration")}
          ></NavLink>
        </li>
      </ul>
      <div className="tab-contents active">
        <div className="form-title-wrpr">
          <h2 className="fw500 text-grey-2 form-head">
            {getLanguages(checklanguage, "registertostart")}
          </h2>
        </div>
        {hasWindow && (
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="w-100 form-wrpr">
              <ReactSelectField
                control={control}
                tabIndex={0}
                label={getLanguages(checklanguage, "title") + " *"}
                name="title"
                type="text"
                placeholder={getLanguages(checklanguage, "selectTitle")}
                autoComplete="off"
                optionLabel={"label"}
                autoFocus={true}
                options={TitleArray[checklanguage]?.map((title) => ({
                  label: title?.label,
                  value: title?.value,
                  id: title?.id,
                }))}
                rules={{
                  required: {
                    value: true,
                    message: getLanguages(checklanguage, "required"),
                  },
                }}
              />
              {/* <div className="form-in w-100">
                        <label className="f-label">Title *</label>
                        <div className="f-in w-100">
                            <select className="form-control">
                                <option value="Select Title">Select Title</option>
                                <option value="Select Title">Select Title</option>
                                <option value="Select Title">Select Title</option>
                                <option value="Select Title">Select Title</option>
                                <option value="Select Title">Select Title</option>
                            </select>
                        </div>
                    </div> */}

              <div className="d-flex justify-between f-row">
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "fsNamelabel")}
                  name="firstName"
                  labelClass="f-label"
                  formInClass="w-50"
                  type="text"
                  placeholder={getLanguages(checklanguage, "fsNamePlaceholder")}
                  className="form-control"
                  inputClassName="f-in w-100"
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "fsNameValid"),
                    },
                  }}
                />

                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "lsNamelabel")}
                  name="lastName"
                  labelClass="f-label"
                  formInClass="w-50"
                  type="text"
                  placeholder={getLanguages(checklanguage, "lsNamePlaceholder")}
                  className="form-control"
                  inputClassName="f-in w-100"
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "lsNameValid"),
                    },
                  }}
                />
              </div>
              <div className="form-in w-100">
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "email")}
                  name="email"
                  labelClass="f-label"
                  type="text"
                  placeholder={getLanguages(checklanguage, "emailPlaceholder")}
                  className="form-control"
                  inputClassName="f-in w-100"
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "emailRequired"),
                    },
                    pattern: {
                      value:
                        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                      message: getLanguages(checklanguage, "validEmail"),
                    },
                  }}
                />
              </div>
              <div className="form-in w-100">
                <InputField
                  control={control}
                  autoComplete="off"
                  label={getLanguages(checklanguage, "password")}
                  name="password"
                  setshowPasswordShown={setshowPasswordShown}
                  showPasswordShown={showPasswordShown}
                  type={showPasswordShown ? "text" : "password"}
                  inputClassName="f-in right-icon w-100"
                  className="form-control"
                  placeholder={getLanguages(
                    checklanguage,
                    "passwordPlaceholder"
                  )}
                  labelClass="f-label"
                  hideandshow={true}
                  rules={{
                    required: {
                      value: true,
                      message: getLanguages(checklanguage, "psRequired"),
                    },
                    minLength: {
                      value: 8,
                      message: getLanguages(checklanguage, "passwordAtleast8"),
                    },
                    maxLength: {
                      value: 20,
                      message: getLanguages(checklanguage, "passwordAtleast20"),
                    },
                  }}
                />
              </div>
              {/* <div className="termsAndCondn">
                <>
                  {getLanguages(
                    checklanguage,
                    "termsAndConditionsConsent",
                    ["terms", "privacy-policy"],
                    [`${getUrl("TERMS")}`, `${getUrl("PRIVACY")}`],

                    true
                  )}
                </>
              </div> */}
              <SingleCheckBox
                control={control}
                className="f-in w-100 form-check"
                fieldClassName="form-check-input"
                name="remeber_me"
                labelClass="fw400 form-check-label"
                trueValue={true}
                // label={
                //   <>
                //     {getLanguages(checklanguage, "Iagree")}{" "}
                //     <span className="fw600">
                //       &nbsp;{getLanguages(checklanguage, "termcondition")}
                //     </span>{" "}
                //     <span className="icon q-mark-icon"></span>
                //   </>
                // }
                label={
                  <div className="termsAndCondn">
                    {getLanguages(
                      checklanguage,
                      "termsAndConditionsConsent",
                      ["terms", "privacy-policy"],
                      [`${getUrl("TERMS")}`, `${getUrl("PRIVACY")}`],

                      true
                    )}
                  </div>
                }
                rules={{
                  required: {
                    value:
                      getValues("remeber_me") === undefined ||
                      !getValues("remeber_me")
                        ? false
                        : true,
                    message: getLanguages(
                      checklanguage,
                      "acceptTermsAndConditions"
                    ),
                  },
                }}
              />
              {/* <div className="f-in w-100 form-check">
                      <input type="checkbox" className="form-check-input"/>
                      <label className="fw400 form-check-label">
                        I agree with the <span className="fw600">&nbsp;terms and conditions</span> <span className="icon q-mark-icon"></span>
                      </label>
                    </div> */}
              <div className="form-blk w-100 form-btn-wrpr">
                <button
                  type="submit"
                  className={`btn btn-accent fw500 w-100 ${
                    isFetching && "btn-loader"
                  }`}
                >
                  <span className="btn-text-wrpr">
                    {getLanguages(checklanguage, "becomemember")}
                  </span>
                </button>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default SignUp;
