import Link from "next/link";
import { Router, useRouter } from "next/router";
import React from "react";
import NavLink from "../../helpers/ActiveLink";
const SiginloginNavigation = ({ children }) => {
  const router = useRouter();
  return (
    <div className="main-wraper">
      <div className="d-flex align-center onboarding-row">
        <div className="w-50 h-full over-auto left-half">
          <div className="onboarding-img-wrpr h-full over-hidden">
            <img
              src="/images/onboading-img.jpg"
              alt=""
              className="img-fluid cover-img"
            />
          </div>
        </div>
        <div className="w-50 h-full over-auto d-flex align-center right-half">
          <div className="h-full fd-col d-flex align-center onboarding-form-wrpr">
            <div className="logo-wrpr">
              <Link href={"/"} legacyBehavior>
                <a href="javascript:;">
                  <img
                    src="/images/logo.png"
                    alt="LOGO"
                    className="img-fluid"
                  />
                </a>
              </Link>
            </div>
            <div className="w-100 onboard-tab-wrpr">
              {(router.pathname == "/" ||
                router.pathname == "/signup" ||
                router.pathname == "/signin") && (
                <ul id="tabs" className="tabs">
                  <li className="tab-item">
                    {router.pathname === "/" ? (
                      <NavLink
                        path={"/"}
                        activeClassName="fw500 tab-link"
                        name={"Login"}
                      ></NavLink>
                    ) : (
                      <NavLink
                        path={"/signin"}
                        activeClassName="fw500 tab-link"
                        name={"Login"}
                      ></NavLink>
                    )}
                  </li>
                  <li className="tab-item">
                    <NavLink
                      path="/signup"
                      activeClassName="fw500 tab-link"
                      name={"New Registration"}
                    ></NavLink>
                  </li>
                </ul>
              )}
              <section
                id="tab-contents-wrpr"
                className="w-100 tab-contents-wrpr"
              >
                {children}
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SiginloginNavigation;
