import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import InputField from "../FormFields/InputField";
import Link from "next/link";
import { getLanguages, checklanguage } from "../../constant";
import { useCookie } from "next-cookie";
import { useRouter } from "next/router";
import { useDispatch, useSelector } from "react-redux";
import {
  resetPassword,
  RESET_PASSWORD_ATATUS,
} from "../../redux/action/user/user";
import toast from "react-hot-toast";
const CreatePassword = () => {
  const [hasWindow, setHasWindow] = useState(false);
  const [showPasswordShown, setshowPasswordShown] = useState(false);
  const [confirmshowPasswordShown, setconfirmshowPasswordShown] =
    useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const slug = router.query;
  const cookies = useCookie();
  const [isFetching, setisFetching] = useState(false);
  const { resetpasswordstatus } = useSelector((state) => state.user);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setHasWindow(true);
    }
    if (cookies.get("jwtToken") != undefined) {
      router.push("/");
    }
  }, []);
  const { handleSubmit, control, setValue, getValues, clearErrors, watch } =
    useForm();
  // This method is used to get Login Form Data and call api to submit login data.
  const onSubmit = async (formValues) => {
    var token = slug?.token;
    setisFetching(true);
    delete formValues.confirmpassword;
    formValues.token = token;
    try {
      let createPassword = await resetPassword(formValues);
      toast.success("Password reset Successfully");
      router.push("/successfull");
      setisFetching(false);
    } catch (err) {
      setisFetching(false);
    }
  };
  return (
    <>
      <div className="form-title-wrpr">
        <h2 className="fw500 text-grey-1 text-center form-head">
          {getLanguages(checklanguage, "crNewpassword")}
        </h2>
        <p className="text-grey-6 text-center form-desc">
          {getLanguages(checklanguage, "crNewpassrdInst")}
        </p>
      </div>
      {hasWindow && (
        <form className="w-100" onSubmit={handleSubmit(onSubmit)}>
          <div className="w-100 form-wrpr">
            <InputField
              control={control}
              autoComplete="off"
              label={getLanguages(checklanguage, "NewPasswordLabel") + " *"}
              name="password"
              setshowPasswordShown={setshowPasswordShown}
              showPasswordShown={showPasswordShown}
              type={showPasswordShown ? "text" : "password"}
              watch={watch}
              inputClassName="f-in right-icon w-100"
              className="form-control"
              placeholder={getLanguages(checklanguage, "NewasswordPlaceholder")}
              labelClass="f-label"
              hideandshow={true}
              rules={{
                required: {
                  value: true,
                  message: getLanguages(checklanguage, "NewpsRequired"),
                },
                minLength: {
                  value: 8,
                  message: getLanguages(checklanguage, "NewpasswordAtleast8"),
                },
                maxLength: {
                  value: 20,
                  message: getLanguages(checklanguage, "NewpasswordAtleast20"),
                },
              }}
            />
            <InputField
              control={control}
              autoComplete="off"
              label={getLanguages(checklanguage, "confirmPasswordlabel")}
              name="confirmpassword"
              setshowPasswordShown={setconfirmshowPasswordShown}
              showPasswordShown={confirmshowPasswordShown}
              type={confirmshowPasswordShown ? "text" : "password"}
              inputClassName="f-in right-icon w-100"
              className="form-control"
              placeholder={getLanguages(
                checklanguage,
                "confirmPasswordPlaceholder"
              )}
              labelClass="f-label"
              hideandshow={true}
              rules={{
                required: {
                  value: true,
                  message: getLanguages(checklanguage, "ConfirmpsRequired"),
                },
                minLength: {
                  value: 8,
                  message: getLanguages(
                    checklanguage,
                    "ConfirmpasswordAtleast8"
                  ),
                },
                maxLength: {
                  value: 20,
                  message: getLanguages(
                    checklanguage,
                    "ConfirmpasswordAtleast20"
                  ),
                },
                validate: (value) =>
                  value === getValues("password") ||
                  "Password and Confirm password do not match",
              }}
            />

            <div className="form-blk w-100">
              <button
                type="submit"
                className={`btn btn-accent fw500 w-100 ${
                  isFetching && "btn-loader"
                }`}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "resetpassrd")}
                </span>
              </button>
              <div className="w-100 text-center back-to-login-wrpr">
                <Link legacyBehavior href="/signin">
                  <a
                    onClick={() =>
                      dispatch({
                        type: RESET_PASSWORD_ATATUS,
                        payload: false,
                      })
                    }
                    className="link-gray back-to-login"
                  >
                    <span className="icon gray-back"></span>
                    {getLanguages(checklanguage, "backlogin")}
                  </a>
                </Link>
              </div>
            </div>
          </div>
        </form>
      )}
    </>
  );
};

export default CreatePassword;
