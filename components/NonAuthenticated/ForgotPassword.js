// import React, { useEffect, useState } from "react";
// import { useForm } from "react-hook-form";
// import InputField from "../FormFields/InputField";
// import { getLanguages, checklanguage } from "../../constant";
// import Link from "next/link";
// import {
//   forgotPassword,
//   RESET_PASSWORD_ATATUS,
// } from "../../redux/action/user/user";
// import { useRouter } from "next/router";
// import { useDispatch } from "react-redux";
// import { useCookie } from "next-cookie";
// const ForgotPassword = () => {
//   const { handleSubmit, control, setValue, getValues, clearErrors } = useForm();
//   const dispatch = useDispatch();
//   const [isFetching, setisFetching] = useState(false);
//   const router = useRouter();
//   const { query } = router;
//   console.log(`fffff`, query);
//   const [migrate, setMigrate] = useState(query?.content);
//   const cookies = useCookie();
//   useEffect(() => {
//     if (cookies.get("jwtToken") != undefined) {
//       router.push("/");
//     }
//   }, []);
//   // This method is used to get Login Form Data and call api to submit login data.
//   const onSubmit = async (formValues) => {
//     try {
//       setisFetching(true);
//       let forgotPasswordRes = await forgotPassword(formValues);
//       let { data, status } = forgotPasswordRes;
//       let { responseData } = data;
//       setisFetching(false);
//       router.push(`/mail-sent?email=${formValues.email}`);
//       dispatch({
//         type: RESET_PASSWORD_ATATUS,
//         payload: true,
//       });
//     } catch (err) {
//       setisFetching(false);
//     }
//   };
//   return (
//     <>
//       <div className="form-title-wrpr">
//         <h2 className="fw500 text-grey-1 text-center form-head">
//           {migrate
//             ? getLanguages(checklanguage, "forgotpassrdImported")
//             : getLanguages(checklanguage, "forgotpassrd")}
//         </h2>
//         <p className="text-grey-6 text-center form-desc">
//           {migrate
//             ? getLanguages(checklanguage, "forgotpassrdinstructionImported")
//             : getLanguages(checklanguage, "forgotpassrdinstruction")}
//         </p>
//       </div>
//       <form className="w-100" onSubmit={handleSubmit(onSubmit)}>
//         <div className="w-100 form-wrpr">
//           <InputField
//             control={control}
//             autoComplete="off"
//             label={getLanguages(checklanguage, "email")}
//             name="email"
//             labelClass="f-label"
//             type="text"
//             placeholder={getLanguages(checklanguage, "emailPlaceholder")}
//             className="form-control"
//             inputClassName="f-in w-100"
//             autoFocus={true}
//             rules={{
//               required: {
//                 value: true,
//                 message: getLanguages(checklanguage, "emailRequired"),
//               },
//               pattern: {
//                 value:
//                   /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
//                 message: getLanguages(checklanguage, "validEmail"),
//               },
//             }}
//           />
//           <div className="form-blk w-100">
//             <button
//               type="submit"
//               className={`btn btn-accent fw500 w-100 ${
//                 isFetching && "btn-loader"
//               }`}
//             >
//               <span className="btn-text-wrpr">
//                 {migrate
//                   ? getLanguages(checklanguage, "resetpassrdImported")
//                   : getLanguages(checklanguage, "resetpassrd")}
//               </span>
//             </button>
//             <div className="w-100 text-center back-to-login-wrpr">
//               <Link legacyBehavior href="/signin">
//                 <a className="link-gray back-to-login">
//                   <span className="icon gray-back"></span>
//                   {getLanguages(checklanguage, "backlogin")}
//                 </a>
//               </Link>
//             </div>
//           </div>
//         </div>
//       </form>
//     </>
//   );
// };

// export default ForgotPassword;

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import InputField from "../FormFields/InputField";
import { getLanguages, checklanguage } from "../../constant";
import Link from "next/link";
import {
  forgotPassword,
  RESET_PASSWORD_ATATUS,
} from "../../redux/action/user/user";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { useCookie } from "next-cookie";

const ForgotPassword = () => {
  const { handleSubmit, control } = useForm();
  const dispatch = useDispatch();
  const [isFetching, setIsFetching] = useState(false);
  const router = useRouter();
  const { query } = router;
  const [migrate, setMigrate] = useState(query?.content);
  const cookies = useCookie();

  useEffect(() => {
    if (cookies.get("jwtToken") !== undefined) {
      router.push("/");
    }
  }, [cookies, router]);

  useEffect(() => {
    if (query?.content) {
      setMigrate(query.content);
    }
  }, [query]);

  const onSubmit = async (formValues) => {
    try {
      setIsFetching(true);
      let forgotPasswordRes = await forgotPassword(formValues);
      let { data, status } = forgotPasswordRes;
      let { responseData } = data;
      setIsFetching(false);
      router.push(`/mail-sent?email=${encodeURIComponent(formValues.email)}`);
      dispatch({
        type: RESET_PASSWORD_ATATUS,
        payload: true,
      });
    } catch (err) {
      setIsFetching(false);
    }
  };

  return (
    <>
      <div className="form-title-wrpr">
        <h2 className="fw500 text-grey-1 text-center form-head">
          {migrate
            ? getLanguages(checklanguage, "forgotpassrdImported")
            : getLanguages(checklanguage, "forgotpassrd")}
        </h2>
        <p className="text-grey-6 text-center form-desc">
          {migrate
            ? getLanguages(checklanguage, "forgotpassrdinstructionImported")
            : getLanguages(checklanguage, "forgotpassrdinstruction")}
        </p>
      </div>
      <form className="w-100" onSubmit={handleSubmit(onSubmit)}>
        <div className="w-100 form-wrpr">
          <InputField
            control={control}
            autoComplete="off"
            label={getLanguages(checklanguage, "email")}
            name="email"
            labelClass="f-label"
            type="text"
            placeholder={getLanguages(checklanguage, "emailPlaceholder")}
            className="form-control"
            inputClassName="f-in w-100"
            autoFocus={true}
            rules={{
              required: {
                value: true,
                message: getLanguages(checklanguage, "emailRequired"),
              },
              pattern: {
                value:
                  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                message: getLanguages(checklanguage, "validEmail"),
              },
            }}
          />
          <div className="form-blk w-100">
            <button
              type="submit"
              className={`btn btn-accent fw500 w-100 ${
                isFetching && "btn-loader"
              }`}
            >
              <span className="btn-text-wrpr">
                {migrate
                  ? getLanguages(checklanguage, "resetpassrdImported")
                  : getLanguages(checklanguage, "resetpassrd")}
              </span>
            </button>
            <div className="w-100 text-center back-to-login-wrpr">
              <Link legacyBehavior href="/signin">
                <a className="link-gray back-to-login">
                  <span className="icon gray-back"></span>
                  {getLanguages(checklanguage, "backlogin")}
                </a>
              </Link>
            </div>
          </div>
        </div>
      </form>
    </>
  );
};

export default ForgotPassword;
