import React, { useEffect } from "react";
import Link from "next/link";
import { getLanguages, checklanguage } from "../../constant";
import {
  resendVerificationCode,
  RESET_PASSWORD_ATATUS,
} from "../../redux/action/user/user";
import toast from "react-hot-toast";
import { useRouter } from "next/router";
import { useSelector, useDispatch } from "react-redux";
import { useCookie } from "next-cookie";
const CheckMail = () => {
  // const { resetpasswordstatus } = useSelector((state) => state.user);
  const router = useRouter();
  const dispatch = useDispatch();
  const slug = router.query;
  const cookies = useCookie();
  useEffect(() => {
    if (cookies.get("jwtToken") != undefined) {
      router.push("/");
    }
  }, []);
  const resendVerifyCode = async (e) => {
    e.preventDefault();
    try {
      const verifySignUp = await resendVerificationCode({ email: slug?.email });
      toast.success(getLanguages(checklanguage, 'emailSentSuccessfully'));
    } catch (err) {}
  };
  return (
    <>
      <div className="form-title-wrpr">
        <h2 className="fw500 text-grey-1 text-center form-head">
          {getLanguages(checklanguage, "checkMailtitle")}
        </h2>
        <p className="text-grey-6 text-center form-desc">
          {getLanguages(checklanguage, "checkMaildesc")}
          <br />
          <span className="h6 fw700">{slug?.email}</span>
        </p>
      </div>
      <div className="w-100 form-wrpr">
        <div className="form-blk w-100">
          <a
            href={`mailto:${slug?.email}`}
            className="btn btn-accent fw500 w-100"
          >
            {getLanguages(checklanguage, "open_mail")}
          </a>
          <div className="w-100 text-center resend-msg-wrpr">
            <div className="w-100 text-center resend-msg-wrpr">
              <p className="link-grey-3 fw500 resend-msg">
                {getLanguages(checklanguage, "didnt_recieve_mail")}{" "}
                <a
                  onClick={resendVerifyCode}
                  href="javascript:void(0)"
                  className="fw600 text-accent"
                >
                  {getLanguages(checklanguage, "clickresend")}
                </a>
              </p>
            </div>
          </div>
          <div className="w-100 text-center back-to-login-wrpr">
            <Link legacyBehavior href="/signin">
              <a
                onClick={() =>
                  dispatch({
                    type: RESET_PASSWORD_ATATUS,
                    payload: false,
                  })
                }
                className="link-gray back-to-login"
              >
                <span className="icon gray-back"></span>
                {getLanguages(checklanguage, "continuetoLogin")}
              </a>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default CheckMail;
