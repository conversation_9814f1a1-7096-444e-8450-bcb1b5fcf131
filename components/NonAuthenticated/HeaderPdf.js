import Link from "next/link";
import React, { useEffect, useState } from "react";
import { setLanguage } from "../../utils/language";
import { useRouter } from "next/router";
import { useCookie } from "next-cookie";


const HeaderPdf = () => {
  const router = useRouter();
  const cookies = useCookie();
  

  
  
  return (
    <>
      <div className={`d-flex justify-between align-center bg-white header`} style = {{borderTop: 'none'}}>
        <div className="d-flex align-center left-wrpr">
          <div className="logo-wrpr">
            <Link href={"/"}>
              <img src="/images/logo.png" alt="KUBY" className="img-fluid" />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default HeaderPdf;
