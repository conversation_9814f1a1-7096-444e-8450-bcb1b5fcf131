import { useRouter } from 'next/router';
import Script from 'next/script';
import React, { useEffect, useState } from 'react';
import { getFaqs, getSalesPage } from '../redux/action/kuby-courses';
import { TESTIMONIALS } from '../constant';

import Faq from './StaticPage/Faq';
import Link from 'next/link';
const SelfPracticeSalesComponent = () => {
    const router = useRouter();

    const { query } = router;
    const [faqs, setFqs] = useState(null);
    const [data, setData] = useState(null);

    useEffect(() => {
        getSalesPageData();
        getFaqsList();

    }, []);


    //Category id 1 for sales page faqs
    const getFaqsList = async () => {
        try {
            let response = await getFaqs({ categoryId: 1 })
            setFqs(response?.data?.responseData);

        }
        catch (err) {

        }
    }

    const getSalesPageData = async () => {
        try {
            let response = await getSalesPage({ videoId: query?.id })
            setData(response?.data?.responseData);

        }
        catch (err) {

        }
    }


    return (
        <div className="over-auto right-section expand">
            <Script src="https://event.webinarjam.com/register/37169sw/embed-button" />
            <div className="h-100 w-100 sales-page-wrpr">
                <ul className="d-flex align-center cd-breadcrumb">
                    <li className="breadcumb-item"><Link href="/dashboard/self-practice"><a className="breadcumb-link">Self Practice</a></Link></li>
                    <li className="breadcumb-item current"><em>Reincarnation</em></li>
                </ul>
                <div className="banner-wrpr">
                    <div className="text-center banner-text-wrpr">
                        {/* <p className="h6 fw500 text-dark-grey banner-subhead">
                            The seminar on the secret of self-healing
                        </p> */}
                        <h1 className="h1 fw600 text-grey-1 sales-page-ttl">
                            {data?.title}
                        </h1>
                        <p className="h6 text-grey-6 banner-subhead">{data?.subTitle}</p>
                        <a href="#" className="btn-accent banner-btn">Get started</a>
                    </div>
                    <div className="banner-video">
                        <iframe src={data?.videoLink} width="100%" height="500" frameBorder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                    </div>
                    <div className="text-center banner-text-wrpr">
                        <h3 className="h3 text-grey-1">In this seminar you will learn how Clemens healed from his paraplegia</h3>
                        <h3 className="h3 text-grey-6">Contrary to the diagnosis of 40 doctors!</h3>
                    </div>
                </div>
                <div className="text-center seminar-avail-card">
                    
                    <div className="seminar-card-content">
                        <p className="fw600 sales-page-ttl text-grey-7">The seminar is available both <span className="fw600 text-grey-1">on-site and online.</span></p>
                        <p className="p text-grey-6">Clemens Kuby&apos;s self-healing method is based on natural and scientifically explainable processes that can be used by anyone who is open to it.</p>
                        <a href="#" className="btn-accent">Watch free webinar now</a>
                    </div>
                </div>
                {
                    <div dangerouslySetInnerHTML={{__html: data?.staticContent}}  />
                }
                {/* <div className="d-flex align-center online-seminar-card">
                    <div className="img-wrpr">
                        <img src="/images/onboading-img.jpg" className="cover-img" />
                    </div>
                    <div className="online-seminar-content">
                        <h2 className="text-grey-1 fw600 online-seminar-ttl">After the online seminar</h2>
                        <ul className="online-seminar-list">
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you always have an approach to solving problems and illnesses on your own responsibility</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you know how to turn your problem into a motivating project</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you know how to track down the cause of your problem?</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you know how to write down the cause of your problem as a script-style scene?</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you know how to create a new, harmonious reality out of your suffering?</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">Do you know what changes are necessary to implement your self-healing in everyday life</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className="d-flex align-center fd-row-r online-seminar-card">
                    <div className="img-wrpr">
                        <img src="/images/onboading-img.jpg" className="cover-img" />
                    </div>
                    <div className="online-seminar-content">
                        <h2 className="text-grey-1 fw600 online-seminar-ttl">Why online seminars?</h2>
                        <ul className="online-seminar-list">
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You have no travel and accommodation costs.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You protect the environment.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You save a lot of precious time.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You can start the online seminar immediately.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You can watch, repeat and pause the content as many times as you want. The online seminar is available to you indefinitely.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">If you don&apos;t make any progress, you can speak to a KUBY attendant for support.</span>
                            </li>
                            <li className="d-flex online-seminar-item">
                                <span className="chevron-icon">
                                    <img src="/images/right-chevron.svg" className="img-fluid" />
                                </span>
                                <span className="text-grey-5">You don&apos;t have to prepare yourself and you&apos;re undisturbed.</span>
                            </li>
                        </ul>
                    </div>
                </div> */}
                <div className="d-flex align-center justify-center watch-buy-seminar-wrpr">
                    <div className="d-flex align-center fd-col justify-center watch-buy-inner">
                        <div className="d-flex align-center justify-center watch-buy-img-wrpr">
                            <img src="/images/play-video-icon.svg" className="img-fluid" />
                        </div>
                        <h5 className="h5 fw500 text-grey-1">Watch the free webinar now</h5>
                    </div>
                    <div className="d-flex align-center fd-col justify-center watch-buy-inner">
                        <div className="d-flex align-center justify-center watch-buy-img-wrpr">
                            <img src="/images/cart-icon-grad.svg" className="img-fluid" />
                        </div>

                        <div style={{ textAlign: "center" }}>
                            <button type="button" data-webinarHash="37169sw" style={{ border: "2px solid rgb(2, 131, 55)", background: "rgb(2, 131, 55)", color: "rgb(255, 255, 255)", fontSize: "24px", padding: "18px 80px", boxShadow: "none", borderRadius: "4px", whiteSpace: "normal", fontWeight: "700", lineHeight: "1.3" }}>Hier zum kostenlosen Webinar anmelden</button>
                            {/* <script src="https://event.webinarjam.com/register/37169sw/embed-button"></script> */}
                        </div>
                        <h5 className="h5 fw500 text-grey-1">Book the online seminar now</h5>
                    </div>
                </div>
                <div className="text-center logos-section">
                    <h2 className="h2 fw600 text-grey-1 sales-page-ttl">Clemens Kuby</h2>
                    <p className="p text-grey-6">Speaker, author and filmmaker - known for</p>
                    <div className="d-flex kuby-logos-wrpr">
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/swr.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/welt.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/yt.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/ard.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/2df.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/filmpreis.png" className="img-fluid" />
                        </div>
                        <div className="d-flex align-center justify-center kuby-logos">
                            <img src="/images/bayerischer.png" className="img-fluid" />
                        </div>
                    </div>
                </div>
                <div className="text-center testimonial-section">
                    <h2 className="h2 fw600 text-grey-1 sales-page-ttl"><span className="text-grey-7">See what our</span><br /> members have to say</h2>
                    <p className="p text-grey-6">Speaker, author and filmmaker - known for</p>
                    <div className="d-flex testimonial-slider">
                        {
                            TESTIMONIALS?.length &&
                            TESTIMONIALS.map((obj, index) => {
                                return (
                                    <div className="testimonial-slide" key = {index}>
                                        <div className="d-flex justify-between testimonial-top">
                                            <div className="testimonial-auther-name">
                                                <p className="fw600 text-grey-3 auther-name">Louise 65</p>
                                                <span className="text-grey-6 auther-loc">Vienna</span>
                                            </div>
                                            <div className="d-flex align-center testi-star-wrpr">
                                                <img src="/images/star.svg" className="img-fluid" />
                                                <img src="/images/star.svg" className="img-fluid" />
                                                <img src="/images/star.svg" className="img-fluid" />
                                                <img src="/images/star.svg" className="img-fluid" />
                                                <img src="/images/star.svg" className="img-fluid" />
                                            </div>
                                        </div>
                                        <div className="testimonial-bottom">
                                            <span className="quote-icon-wrpr">
                                                <img src="/images/green-qoute.svg" className="img-fluid" />
                                            </span>
                                            <p className="testimonial-text">“Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.”</p>
                                        </div>
                                    </div>
                                )
                            })
                        }

                        
                    </div>
                </div>
                <div className="text-center steps-section">
                    <div className="step-sec-content">
                        <h2 className="h2 fw600 text-grey-1 sales-page-ttl">Are you ready for this next step?</h2>
                        <p className="p text-grey-6">Do you want to develop your awareness to heal yourself and you, also to get rid of problems without depending on others!</p>
                    </div>
                    <div className="d-flex steps-block">
                        <p className="step">Finally get a grip on a problem or illness that you have not been able to cure so far?</p>
                        <p className="step">Get a tool with which you can solve your problems yourself from now on?</p>
                        <p className="step">Go through life more relaxed, happier and healthier?</p>
                    </div>
                    <div className="text-center">
                        <a href={query?.paymentlink} className="btn-accent">Book now</a>
                    </div>
                </div>
                <Faq data={faqs} title={"Questions about the online seminar"} />
            </div>
        </div>

    )
}

export default SelfPracticeSalesComponent