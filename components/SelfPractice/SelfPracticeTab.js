import React, { useEffect, useState } from 'react'
import CommoNavigation from "../../helpers/CommonNavigation"
import { SELF_PRACTICE_NAV_LABELS } from "../../constant"
import Overview from './SelfPracticeMarketPlace/Overview'
import DownloadFiles from './SelfPracticeMarketPlace/DownloadFiles'
import Comments from './SelfPracticeMarketPlace/Comments'
const SelfPracticeTab = ({ SubVideosLesson, show, setSubVideosLesson, setOverAllRating }) => {
    const [tabChange, setTabChnage] = useState(1)
    useEffect(() => {
        setTabChnage(1);
    }, [SubVideosLesson])

    return (
        <div className="selfpractice-tab-wrpr" id="selfpractice-tab-wrpr">

            <CommoNavigation data={SELF_PRACTICE_NAV_LABELS} kubynavbarStatus={3} setMeetingTab={setTabChnage} MeetingTab={tabChange} SubVideosLesson={SubVideosLesson} />
            {
                (() => {
                    switch (tabChange) {
                        case 1:
                            return <Overview SubVideosLesson={SubVideosLesson} />
                        case 2:
                            console.log('SubVideosLesson', SubVideosLesson);
                            if(SubVideosLesson?.attachments && SubVideosLesson?.attachments?.length ){
                                return (
                                    <DownloadFiles SubVideosLesson={SubVideosLesson} />
                                )
                            }else{
                                return (
                                    <></>
                                )
                            }
                        case 3:
                            return <Comments SubVideosLesson={SubVideosLesson} show={show} setSubVideosLesson={setSubVideosLesson} setOverAllRating={setOverAllRating} />
                    }
                })()}

        </div>
    )
}

export default SelfPracticeTab