import React from "react";
import { checklanguage, getLanguages } from "../../../constant";

const DownloadFiles = ({ SubVideosLesson }) => {
  async function download(url, fileType) {
    const a = document.createElement("a");
    a.href = await toDataURL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/${url}`);
    a.download = fileType;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  function toDataURL(url) {
    return fetch(url)
      .then((response) => {
        return response.blob();
      })
      .then((blob) => {
        return URL.createObjectURL(blob);
      });
  }

  const AttachementPreview = (src) => {
    let extension = src.match(/\.([^\.]*)$/);
    extension = extension?.[1] ?? null;
    switch (extension.toLowerCase()) {
      case "png":
        return (
          <img
            src="/images/icons8-png-96 1.png"
            className="img-fluid cover-img"
          />
        );
      case "jpg":
        return (
          <img src="/images/iconsImage.png" className="img-fluid cover-img" />
        );
      case "jpeg":
        return (
          <img src="/images/iconsImage.png" className="img-fluid cover-img" />
        );
      case "svg":
        return (
          <img src="/images/icons-svg.png" className="img-fluid cover-img" />
        );
      case "xlsx":
        return <img src="/images/excel.png" className="img-fluid cover-img" />;
      case "pdf":
        return <img src="/images/pdf.png" className="img-fluid cover-img" />;
      case "docx":
        return (
          <img src="/images/doc-icon.svg" className="img-fluid cover-img" />
        );
      case "pptx":
        return <img src="/images/ppt.png" className="img-fluid cover-img" />;
      default:
        return;
    }
  };
  return (
    <div className="tab-downloads tab-contents active" id="downloads">
      {SubVideosLesson?.attachments?.length > 0 ? (
        SubVideosLesson?.attachments?.map((item, index) => (
          <div className="d-flex align-center download-card" key={index}>
            <div className="d-flex align-center file-wrpr">
              <div className="doc-icon">{AttachementPreview(item.path)}</div>
              {item?.originalName}
            </div>
            <a
              className="downlod-icon"
              target="_blank"
              onClick={() => download(item.path, item.originalName)}
            >
              <span className="icon download-icon"></span>
            </a>
          </div>
        ))
      ) : (
        <>{getLanguages(checklanguage, "noData")}</>
      )}
    </div>
  );
};

export default DownloadFiles;
