import moment from "moment";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { InfinitySpin } from "react-loader-spinner";
import {
  getReviews,
  GET_REVIEW_lIST,
  getSingleReviews,
  SINGLE_REVIEW,
} from "../../../redux/action/kuby-courses";
import RatingComponent from "../../Common/ReactStarCompnonent";
import { useSelector, useDispatch } from "react-redux";
import RatingStar from "../../Common/Rating";
import { checklanguage, getLanguages, nFormatter } from "../../../constant";
import { useCookie } from "next-cookie";
const Comments = ({
  SubVideosLesson,
  setSubVideosLesson,
  setOverAllRating,
}) => {
  const state = useSelector((state) => state.courses);
  const { reviewRatingList, singleReview } = state;
  const dispatch = useDispatch();
  const cookies = useCookie();
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNext, setHasNext] = useState(false);
  const [totalPage, settotalPage] = useState(null);
  useEffect(() => {
    if (SubVideosLesson?.id) {
      if (
        typeof cookies.get("jwtToken") !== "undefined" &&
        cookies.get("jwtToken") !== null
      ) {
        getSingleVideoReviews(SubVideosLesson?.id);
      }
    }
  }, [reviewRatingList]);
  useEffect(() => {
    if (SubVideosLesson?.id) {
      getAllVideoReviewss(SubVideosLesson?.id);
    }
  }, [SubVideosLesson, currentPage, dispatch]);
  const getAllVideoReviewss = async (id) => {
    try {
      let response = await getReviews({
        videoId: id,
        pageNumber: currentPage,
        limit: 10,
      });
      if (currentPage > 1) {
        let upDatePayload = { ...reviewRatingList };
        upDatePayload.reviewList = [
          ...upDatePayload?.reviewList,
          ...response?.data?.responseData?.reviewList,
        ];
        dispatch({
          type: GET_REVIEW_lIST,
          payload: upDatePayload,
        });
      } else {
        dispatch({
          type: GET_REVIEW_lIST,
          payload: response?.data?.responseData,
        });
      }
      setHasNext(response?.data?.responseData?.loadMoreFlag);
      settotalPage(response?.data?.responseData?.totalPages);
    } catch (err) {}
  };
  const getSingleVideoReviews = async (id) => {
    try {
      let response = await getSingleReviews({ videoId: id });
      dispatch({
        type: SINGLE_REVIEW,
        payload: response?.data?.responseData,
      });
    } catch (err) {}
  };

  const fetchData = async () => {
    if (totalPage > 1) {
      setTimeout(() => {
        setHasNext(false);
        setCurrentPage((prev) => prev + 1);
      }, 500);
    }
  };
  return (
    <div className="tab-comment tab-contents active" id="comment">
      <div className="d-flex comment-block-wrpr">
        <div className="comment-block-inner comment-wrpr">
          <RatingComponent
            ratingComment={1}
            SubVideosLesson={SubVideosLesson}
            setSubVideosLesson={setSubVideosLesson}
            getAllVideoReviewss={getAllVideoReviewss}
            setOverAllRating={setOverAllRating}
          />
          <div className="comment-card-wrpr">
            <InfiniteScroll
              style={{ overflowY: "auto", overflowX: "hidden" }}
              dataLength={
                reviewRatingList != null
                  ? reviewRatingList?.reviewList?.length
                  : []
              } //This is important field to render the next data
              next={fetchData}
              hasMore={hasNext}
              loader={
                <div className="loader-svg">
                  <InfinitySpin width="200" color="#248BFF" />
                </div>
              }
            >
              {reviewRatingList?.reviewList?.map((comment, i) => {
                return (
                  <div className="d-flex comment-card" key={i}>
                    <div className="comment-user-img">
                      <img
                        src={
                          comment?.User?.profilePhotoUrl != null
                            ? `${comment?.User?.profilePhotoUrl}`
                            : "/images/userplace_holder.png"
                        }
                        className="img-fluid cover-img"
                      />
                    </div>
                    <div className="comment-content">
                      <p className="fw600 text-grey-1 comment-user">
                        {comment?.User?.firstName} {comment?.User?.lastName}
                        <span className="fw400 font-inter text-grey-6 comment-date">
                          {moment(comment?.updatedAt)?.fromNow()}
                        </span>
                      </p>
                      {comment?.rating && (
                        <div className="d-flex align-center rating-star-wrpr">
                          <RatingStar
                            count={comment?.rating}
                            iconsCount={5}
                            className={"rating-star-wrpr"}
                          />
                          <p className="text-grey-6 fw500 star-count">
                            {getLanguages(checklanguage, "stars")}
                          </p>
                        </div>
                      )}
                      <p className="text-dark-grey font-inter comment-text">
                        {comment?.comment}
                      </p>
                    </div>
                  </div>
                );
              })}
            </InfiniteScroll>
          </div>
        </div>
        <div className="comment-block-inner rating-wrap">
          <div className="rating-pt">
            <div className="rating-star">
              <span className="as-rating">
                {SubVideosLesson?.rating?.average?.toFixed(1)}
              </span>
              <RatingStar count={1} className={"rt-star"} iconsCount={1} />
            </div>
          </div>
          <div className="rating-list">
            <ul className="ul-rating">
              {SubVideosLesson?.rating?.details?.map((_rating, i) => (
                <li className="li-rating" key={i}>
                  <div className="star-rating">
                    <span className="star-count">{_rating?.rating}</span>
                    <RatingStar count={1} iconsCount={1} />
                  </div>
                  <div className="rating-bar">
                    <div className="progress-bar">
                      <div
                        className={`progress-data-bar bar-${
                          SubVideosLesson?.rating?.details?.length - i
                        }`}
                        role="progressbar"
                        aria-valuenow="70"
                        aria-valuemin="0"
                        aria-valuemax="100"
                        style={{
                          width: `${parseInt(_rating?.percentage)?.toFixed(
                            0
                          )}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className="rating-reviews">
                    <span className="review-content">
                      {nFormatter(_rating?.count)}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Comments;
