import React, { useEffect } from 'react';
import { convertTextLinksToClickableLinks } from '../../../constant';



const Overview = ({ SubVideosLesson }) => {
    useEffect(() => {
        if(document.querySelector("#video_overview a")?.length){
            document.querySelector("#video_overview a").addEventListener('click', () => {
                
                return false;
            })
        }
        document.querySelectorAll("#video_overview a").forEach(function(a){
            a.setAttribute('target', '_blank');
          })
        
    }, [SubVideosLesson])
    return (
        <div className="tab-overview tab-contents active" id="overview">
            {
                SubVideosLesson?.description != undefined && 
                <div className="overview-wrpr" id = "video_overview">
                    {/* <h6 className="h6 fw600 text-grey-1 overview-title">About the course</h6> */}
                    <p className="p text-dark-grey" dangerouslySetInnerHTML={{ __html: SubVideosLesson?.description?.split('\n').join('<br>') }}></p>
                </div>
            }
        </div>
    )
}
export default Overview