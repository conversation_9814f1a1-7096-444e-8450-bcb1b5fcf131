import moment from "moment";
import React, { useEffect } from "react";
import { currencyFormat } from "../constant";
import { checklanguage, getLanguages } from "../constant";

const OrderDetailModal = ({ show, onHide, details }) => {
  const { custom, orderDetails } = details;
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };
  return (
    <div className={`modal notification-modal  ${show ? "show" : ""}`}>
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content order_content">
        <div className="modal-header">
          <p className="h4 fw500">
            {getLanguages(checklanguage, "orederDetail")}
          </p>
          <p className="p fw500  text-dark-grey">
            {getLanguages(checklanguage, "totalProducts")}:{" "}
            {orderDetails?.items?.length}
          </p>
          <div className="order_info">
            <p style={{ color: "#A2A2A2" }} className="fw500">
              {getLanguages(checklanguage, "orderDate")}:{" "}
              {moment(orderDetails?.created_at)?.format("Do MMM, YYYY")}{" "}
            </p>
            <div
              onClick={() => window.open(orderDetails?.invoice_url, "_blank")}
              className="invoice_wrp"
            >
              <img src="/images/invoice.png" />
              <a href="javascript:void(0)">
                {getLanguages(checklanguage, "invoice")}
              </a>
            </div>
          </div>
          <a
            href="javascript:;"
            className="close-btn"
            id="closeModal"
            onClick={() => onHide()}
          >
            <span className="icon close-icon"></span>
          </a>
        </div>
        <div className="modal-body order-details">
          <ul className="noti-list order-list">
            {orderDetails?.items.map((item, i) => (
              <li className="noti-item order-item" key={i}>
                <div className="order_wrpr">
                  <div
                    className="product_desc"
                    onClick={() =>
                      window.open(
                        `/dashboard/shop/product-detail?pid=${custom?.[i]?.product?.id}`,
                        "_blank"
                      )
                    }
                  >
                    <img
                      className="prod-img"
                      src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${custom?.[i]?.product?.attachment?.path}`}
                    />
                    <div className="product_dom">
                      <p className="fw500">{item?.product_name}</p>
                      <div className="category_wrp d-flex">
                        <img
                          className="category-image"
                          src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${custom?.[i]?.product?.category?.attachment?.path}`}
                        />
                        <p className="fw400">
                          {custom?.[i]?.product?.category?.content?.name}
                        </p>
                        <span
                          style={{
                            color: "#A2A2A2",
                            marginLeft: "16px",
                          }}
                          className="fw500"
                        >
                          Qty: {item?.quantity}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="price_wr">
                    <span className="text-grey-1 fw500">
                      {" "}
                      {currencyFormat(
                        item?.amounts?.first?.total_brutto_amount?.toFixed(2),
                        details?.data?.currency
                      )}
                    </span>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          <div className="cart_total_wrpr order_total">
            <div className="price-table">
              <div className="d-flex align-center justify-between w-100">
                <p className="h6 text-grey-5">
                  {getLanguages(checklanguage, "subtotal")} :
                </p>{" "}
                <span className="text-grey-1">
                  {currencyFormat(orderDetails?.amount, orderDetails?.currency)}
                </span>
              </div>
              <div className="d-flex align-center justify-between w-100">
                <p className="h6 text-grey-5">
                  {getLanguages(checklanguage, "vat")} (20%)::
                </p>{" "}
                <span className="text-grey-1">
                  {currencyFormat(
                    orderDetails?.vat_rate,
                    orderDetails?.currency
                  )}
                </span>
              </div>
              {/* <div className='d-flex align-center justify-between w-100'>
                                <p className="h6 text-grey-5">Shipping::</p> <span className='text-grey-1'>€ 0.1</span>
                            </div> */}
              <div className="d-flex align-center justify-between w-100">
                <p className="h6 text-grey-1 fw500">
                  {getLanguages(checklanguage, "totalAmount")}:
                </p>{" "}
                <span className="text-grey-1 fw500">
                  {currencyFormat(orderDetails?.amount, orderDetails?.currency)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailModal;
