import { useRouter } from "next/router";
import React, { useState, useEffect } from "react";
import {
  getProjectById,
  getsoulWritingContent,
  getSoulWritingCategory,
  getsoulProjectVersion,
} from "../../redux/action/soul-writing";
import { getCompanionById } from "../../redux/action/kuby-companion";
export default function SoulwritingHook() {
  const router = useRouter();
  const { query } = router;
  const urlParams = new URLSearchParams(window.location.search);
  const projectId = urlParams.get("projectId");
  const version = urlParams.get("version");
  // const { projectId, version } = query;
  const [categoryListForm, setcategoryListForm] = useState(null);
  const [getCompanion, setcompanion] = useState({});
  const [projectInfo, setprojectInfo] = useState(null);
  const [dateInfo, setDateInfo] = useState({
    trigger_date: null,
    projection_date: null,
  });
  useEffect(() => {
    getSingleProject();
    modifiedValue();
    handleProjectVersion();
  }, []);
  const getSingleProject = async () => {
    let payload = {};
    payload.id = projectId;
    try {
      let response = await getProjectById(payload);
      if (response?.data?.responseData?.companionId != null) {
        await getSingleCompanion(response?.data?.responseData?.companionId);
      }
      // if (response?.data?.responseData?.paymentLink != null) {
      //   window.location = response?.data?.responseData?.paymentLink;
      // }
      setprojectInfo(response?.data?.responseData);
      setDateInfo({
        ...response?.data?.responseData?.projectMeta?.bridgeCharacter?.[2],
        ...response?.data?.responseData?.projectMeta?.bridgeCharacter?.[3],
      });
    } catch (err) {}
  };

  const getSingleCompanion = async (id = null) => {
    if (id != null) {
      try {
        let response = await getCompanionById({ id });
        setcompanion(response?.data?.responseData);
      } catch (err) {}
    }
  };

  const handleProjectVersion = async () => {
    let payloadResponse;
    let response = await getSoulWritingCategory();
    let sliceArray = response.data?.responseData?.categoryList?.slice(
      2,
      response.data?.responseData?.categoryList.length
    );
    if (projectId != undefined) {
      let data = {
        projectId: projectId,
      };
      if (version != undefined) {
        data.version = version;
      }
      try {
        let response = await getsoulProjectVersion(data);
        payloadResponse = [...response?.data?.responseData?.content];
        var newList = {};
        for (let i = 0; i < sliceArray?.length; i++) {
          if (payloadResponse?.length > 0) {
            let arrayData = matchData(sliceArray[i]?.id, payloadResponse);
            if (arrayData.length > 0) {
              newList[`categoryForms${i + 1}`] = arrayData;
            }
          }
        }
        setcategoryListForm(newList);
      } catch (err) {}
    }
  };

  const modifiedValue = async () => {
    let payloadResponse;
    let response = await getSoulWritingCategory();
    let sliceArray = response.data?.responseData?.categoryList?.slice(
      2,
      response.data?.responseData?.categoryList.length
    );
    if (projectId != undefined) {
      let data = {
        projectId: projectId,
      };
      if (version != undefined) {
        data.version = version;
      }
      try {
        let response = await getsoulWritingContent(data);
        payloadResponse = [...response?.data?.responseData?.content];
        var newList = {};
        for (let i = 0; i < sliceArray?.length; i++) {
          if (payloadResponse?.length > 0) {
            let arrayData = matchData(sliceArray[i]?.id, payloadResponse);
            if (arrayData.length > 0) {
              newList[`categoryForms${i + 1}`] = arrayData;
            }
          }
        }
        setcategoryListForm(newList);
      } catch (err) {}
    }
  };

  const matchData = (categoryId, payloadResponse) => {
    let arrayData = [];
    for (var j = 0; j < payloadResponse?.length; j++) {
      if (categoryId == parseInt(payloadResponse[j].categoryId)) {
        arrayData.push({
          content: payloadResponse[j].content,
          categoryId: payloadResponse[j].categoryId,
          character: payloadResponse[j].character,
          lineNumber: payloadResponse[j].lineNumber,
          characterId: payloadResponse[j].characterId,
          uuid: payloadResponse[j].uuid,
          errorMessage: "",
          isRedLine: payloadResponse[j].isRedLine,
          comments:
            payloadResponse[j].comments != null
              ? payloadResponse[j].comments
              : [],
          painpictureCollapse: payloadResponse[j].painpictureCollapse,
        });
      }
    }
    return arrayData;
  };
  return [categoryListForm, getCompanion, projectInfo, dateInfo];
}
