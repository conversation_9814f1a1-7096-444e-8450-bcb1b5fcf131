import moment from "moment";
import React, { useEffect, useState } from "react";
import { checklanguage, getLanguages } from "../constant";

function NotificationComponent({ show, onHide, notificationList }) {
  useEffect(() => {
    document.addEventListener("click", hideModal);

    return () => document.removeEventListener("click", hideModal);
  }, []);

  const hideModal = (event) => {
    const modal = document.querySelector("#modalOverlay");
    if (event.target == modal) {
      onHide();
    }
  };
  return (
    <>
      <div className={`modal notification-modal  ${show ? "show" : ""}`}>
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-header">
            <h4 className="h4 fw500 text-dark-grey">
              {getLanguages(checklanguage, "notifications")}
            </h4>
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={() => onHide()}
            >
              <span className="icon close-icon"></span>
            </a>
          </div>
          <div className="modal-body">
            <ul className="noti-list">
              {notificationList?.length > 0 &&
                notificationList?.map((noti, i) => {
                  let content = noti?.content;
                  Object.keys(noti?.replacements).forEach((item, i) => {
                    content = content.replace(
                      `{{${item}}}`,
                      item == "start_date"
                        ? moment(noti?.replacements[item]).format(
                            "MMMM D, YYYY & HH:mm"
                          )
                        : noti?.replacements[item]
                    );
                  });
                  return (
                    <li className="d-flex noti-item" key={i}>
                      <div className="noti-img-wrpr">
                        <span className="noti-img"></span>
                      </div>
                      <div className="noti-text-wrpr">
                        <h6 className="text-grey-3 font-inter noti-text">
                          {content}
                        </h6>
                        <p className="text-grey-5 d-flex align-center">
                          <span className="icon clock-icon"></span>
                          {moment(noti?.createdAt).fromNow()}
                        </p>
                      </div>
                    </li>
                  );
                })}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}
export default NotificationComponent;
