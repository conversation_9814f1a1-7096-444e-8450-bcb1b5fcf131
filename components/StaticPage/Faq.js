import React from 'react'

const Faq = ({ title, data }) => {
    const hideAndShowFaq = (e) => {
        const elem = e.target.closest('.accordion-items').classList;
        const isActive = elem.contains('active');
        document.querySelectorAll(`.accordion-items`).forEach(elem => elem.classList.remove('active'));
        isActive ? elem.remove('active') : elem.add('active');
    }
    return (
        <div className="text-center faq-section">
            <div className="faq-main-block">
                <h2 className="h2 fw600 text-grey-1 sales-page-ttl">{title}</h2>
                <div className="faq-block">

                    {
                        data?.faqList?.length &&
                        data.faqList.map((obj, index) => {
                            return (
                                <div key = {index} className="product-list-wrap accordion-items" onClick={(e) => hideAndShowFaq(e)}>
                                    <div className="wrap-heading accordion-heading">
                                        <h6 className="h6 fw500 text-dark-grey faq-head">{obj.title}</h6>
                                        <span className="icon faq-icon"></span>
                                        <a href="javascript:void(0)" className="arrow-action"></a>
                                    </div>
                                    <div className="wrap-content accordion-content">
                                        <p className="p text-grey-6 faq-desc faq-p">{obj.description}</p>
                                    </div>
                                </div>
                            )
                        })
                    }
                    {/* <div className="product-list-wrap accordion-items" onClick={(e) => hideAndShowFaq(e)}>
                        <div className="wrap-heading accordion-heading">
                            <h6 className="h6 fw500 text-dark-grey faq-head">What does the online seminar Healthy without Medicine include?</h6>
                            <span className="icon faq-icon"></span>
                            <a href="javascript:void(0)" className="arrow-action"></a>
                        </div>
                        <div className="wrap-content accordion-content">
                            <p className="p text-grey-6 faq-desc faq-p">Lorem ipsum dolor sit amet consectetur adipisicing elit. Id ad saepe ipsum et iure vitae consectetur accusantium, neque laboriosam delectus illum pariatur? Numquam.</p>
                        </div>
                    </div>
                    <div className="product-list-wrap accordion-items" onClick={(e) => hideAndShowFaq(e)}>
                        <div className="wrap-heading accordion-heading">
                            <h6 className="h6 fw500 text-dark-grey faq-head">Does Clemens have medical training?</h6>
                            <span className="icon faq-icon"></span>
                            <a href="javascript:void(0)" className="arrow-action"></a>
                        </div>
                        <div className="wrap-content accordion-content">
                            <p className="p text-grey-6 faq-desc">Lorem ipsum dolor sit amet consectetur adipisicing elit. Id ad saepe ipsum et iure vitae consectetur accusantium, neque laboriosam delectus illum pariatur? Numquam.</p>
                        </div>
                    </div>
                    <div className="product-list-wrap accordion-items" onClick={(e) => hideAndShowFaq(e)}>
                        <div className="wrap-heading accordion-heading">
                            <h6 className="h6 fw500 text-dark-grey faq-head">How do I get access to the online seminar?</h6>
                            <span className="icon faq-icon"></span>
                            <a href="javascript:void(0)" className="arrow-action"></a>
                        </div>
                        <div className="wrap-content accordion-content">
                            <p className="p text-grey-6 faq-desc">Lorem ipsum dolor sit amet consectetur adipisicing elit. Id ad saepe ipsum et iure vitae consectetur accusantium, neque laboriosam delectus illum pariatur? Numquam.</p>
                        </div>
                    </div>
                    <div className="product-list-wrap accordion-items" onClick={(e) => hideAndShowFaq(e)}>
                        <div className="wrap-heading accordion-heading">
                            <h6 className="h6 fw500 text-dark-grey faq-head">If I don&apos;t like the seminar, how can I return it?</h6>
                            <span className="icon faq-icon"></span>
                            <a href="javascript:void(0)" className="arrow-action"></a>
                        </div>
                        <div className="wrap-content accordion-content">
                            <p className="p text-grey-6 faq-desc">Lorem ipsum dolor sit amet consectetur adipisicing elit. Id ad saepe ipsum et iure vitae consectetur accusantium, neque laboriosam delectus illum pariatur? Numquam.</p>
                        </div>
                    </div> */}
                </div>
                {/* <p className="text-center">Further questions are available in our <a href="#" className="fw600 link">FAQs</a> page</p> */}
            </div>
        </div>
    )
}

export default Faq