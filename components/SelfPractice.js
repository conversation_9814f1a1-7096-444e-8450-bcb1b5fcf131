import React, { useEffect, useState } from "react";
import { getLanguages, checklanguage } from "../constant";
import { getTopic } from "../redux/action/kuby-courses";
import SeminarSkeleton from "./Common/SkeletonLoader/SeminarSkeleton";
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import { useRouter } from "next/router";
import { useCookie } from "next-cookie";
const SelfPractice = ({ showTrainings }) => {
  const [ListCourses, setListCourses] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const router = useRouter();
  const cookies = useCookie();
  useEffect(() => {
    getTopicCourses();
  }, []);
  const getTopicCourses = async () => {
    setIsFetching(true);
    try {
      let response = await getTopic();
      setIsFetching(false);
      setListCourses(response?.data?.responseData);
    } catch (err) {}
  };
  const checkRedirection = (seminar) => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      if (!seminar?.isPurchased) {
        if (seminar.salesPageLink) {
          //router.push(seminar.salesPageLink);
          window.location.href = seminar.salesPageLink;
        } else {
          router.push(
            `/dashboard/self-practice-sales?id=${seminar?.id}&paymentlink=${seminar?.purchaseLink}`
          );
        }
      } else if (seminar?.isPurchased == 1) {
        router.push(`/dashboard/self-practice-details/${seminar?.id}`);
      } else {
        router.push(`/dashboard/self-practice-sales`);
      }
    } else {
      router.push(
        `/signin?redirectTourl=/dashboard/self-practice-details/${seminar?.id}`
      );
    }
  };
  return (
    <>
      {/* <div className="over-auto right-section"> */}
      <div className=" w-100 d-flex fd-col self-practice-wrpr">
        <div className="seminar-row">
          <div className="w-100 d-flex align-center justify-between heading-row">
            <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
              {getLanguages(checklanguage, "seminars")}
            </h4>
          </div>
          <div className="d-flex w-100 seminar-wrpr reception-card-block">
            {isFetching ? (
              <SeminarSkeleton listsToRender={2} type={1} />
            ) : (
              ListCourses?.Seminars?.topicList?.map((seminar, index) => (
                <div
                  className="img-name-card seminar-card"
                  key={index}
                  onClick={() => checkRedirection(seminar)}
                >
                  <div className="relative img-card">
                    <img
                      src={
                        seminar?.posterImage?.attachmentUrl != null
                          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${seminar?.posterImage?.attachmentUrl}`
                          : "/images/test.jpeg"
                      }
                      alt=""
                      className="img-fluid cover-img"
                    />
                    {seminar?.isPurchased ? (
                      <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                        <span className="lock check"></span>
                      </div>
                    ) : (
                      <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                        <span className="lock"></span>
                      </div>
                    )}
                  </div>
                  <div className="seminar-card-details">
                    <div className="seminar-card-wrpr">
                      <h6 className="h6 text-grey-3 mb-0 seminar-name">
                        <span className="fw600">{seminar?.name}</span> –{" "}
                        {seminar?.subTitle}
                      </h6>
                      <p className="fs12 text-grey-5">{seminar?.shorttext}</p>
                      {seminar.isPurchased ? (
                        <p className="text-accent fw500 in-progress">
                          {getLanguages(checklanguage, "inProgress")}
                        </p>
                      ) : (
                        <></>
                      )}
                    </div>
                    {seminar?.percentageWatched != null && (
                      <div className="loaderView">
                        <CircularProgressbarWithChildren
                          styles={buildStyles({
                            pathColor: "#FC7900",
                            trailColor: "#FEF0DB",
                            strokeLinecap: "butt",
                            textColor: "#404040",
                          })}
                          text={`${seminar?.percentageWatched?.toFixed(0)}%`}
                          value={seminar?.percentageWatched?.toFixed(0)}
                        ></CircularProgressbarWithChildren>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        {showTrainings == true &&
          ListCourses?.Trainings?.topicList?.length > 0 && (
            <div className="seminar-row" style={{ marginTop: "56px" }}>
              <div className="w-100 d-flex align-center justify-between heading-row">
                <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                  {getLanguages(checklanguage, "trainings")}
                </h4>
              </div>
              <div className="d-flex w-100 seminar-wrpr reception-card-block">
                {isFetching ? (
                  <SeminarSkeleton listsToRender={2} type={1} />
                ) : (
                  ListCourses?.Trainings?.topicList?.map((seminar, index) => (
                    <div
                      className="img-name-card seminar-card"
                      key={index}
                      onClick={() => checkRedirection(seminar)}
                    >
                      <div className="relative img-card">
                        <img
                          src={
                            seminar?.posterImage?.attachmentUrl != null
                              ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${seminar?.posterImage?.attachmentUrl}`
                              : "/images/test.jpeg"
                          }
                          alt=""
                          className="img-fluid cover-img"
                        />
                        {seminar?.isPurchased ? (
                          <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                            <span className="lock check"></span>
                          </div>
                        ) : (
                          <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                            <span className="lock"></span>
                          </div>
                        )}
                      </div>
                      <div className="seminar-card-details">
                        <div className="seminar-card-wrpr">
                          <h6 className="h6 text-grey-3 mb-0 seminar-name">
                            <span className="fw600">{seminar?.name}</span> –{" "}
                            {seminar?.subTitle}
                          </h6>
                          <p className="fs12 text-grey-5">
                            {seminar?.shorttext}
                          </p>
                          {seminar.isPurchased ? (
                            <p className="text-accent fw500 in-progress">
                              {getLanguages(checklanguage, "inProgress")}
                            </p>
                          ) : (
                            <></>
                          )}
                        </div>
                        {seminar?.percentageWatched != null && (
                          <div className="loaderView">
                            <CircularProgressbarWithChildren
                              styles={buildStyles({
                                pathColor: "#FC7900",
                                trailColor: "#FEF0DB",
                                strokeLinecap: "butt",
                                textColor: "#404040",
                              })}
                              text={`${seminar?.percentageWatched?.toFixed(
                                0
                              )}%`}
                              value={seminar?.percentageWatched?.toFixed(0)}
                            ></CircularProgressbarWithChildren>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        {ListCourses?.KUBYstudy?.topicList?.length > 0 && (
          <div className="study-row">
            <div className="w-100 d-flex align-center justify-between heading-row">
              {ListCourses?.KUBYstudy?.topicList?.length ? (
                <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                  {getLanguages(checklanguage, "kubyStudy")}
                </h4>
              ) : (
                <></>
              )}
            </div>
            <div className="d-flex w-100 study-wrpr reception-card-block">
              {isFetching ? (
                <SeminarSkeleton listsToRender={3} type={2} />
              ) : (
                ListCourses?.KUBYstudy?.topicList?.map((kubystudy, i) => (
                  <div
                    className="img-name-card seminar-card"
                    key={i}
                    onClick={() => checkRedirection(kubystudy)}
                  >
                    <div className="relative img-card">
                      <img
                        src={
                          kubystudy?.posterImage?.attachmentUrl != null
                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${kubystudy?.posterImage?.attachmentUrl}`
                            : "/images/test.jpeg"
                        }
                        alt=""
                        className="img-fluid cover-img"
                      />
                      {kubystudy?.isPurchased ? (
                        <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                          <span className="lock check"></span>
                        </div>
                      ) : (
                        <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                          <span className="lock"></span>
                        </div>
                      )}
                    </div>
                    <div className="seminar-card-details">
                      <div className="seminar-card-wrpr">
                        <h6 className="h6 text-grey-3 mb-0 seminar-name">
                          <span className="fw600">{kubystudy?.name}</span> –{" "}
                          {kubystudy?.subTitle}
                        </h6>
                        <p className="fs12 text-grey-5">
                          {kubystudy?.shorttext}
                        </p>
                      </div>
                    </div>
                    <div className="d-flex align-center justify-between seminar-card-bottom">
                      {/* {kubystudy?.price != null && <p className="h6 fw600 text-grey-1">€ {kubystudy?.price}</p>} */}
                      {/* <div className="d-flex align-center seminar-card-btns">
                                            <button className="btn w-100"> <span className="icon wishlist-icon-green"></span> Wishlist</button>
                                            <button className="btn w-100"> <span className="icon cart-icon-green"></span>Add to cart</button>
                                        </div> */}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
        {ListCourses?.shopProduct?.topicList?.length > 0 && (
          <div className="study-row">
            <div className="w-100 d-flex align-center justify-between heading-row">
              {ListCourses?.shopProduct?.topicList?.length ? (
                <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                  {getLanguages(checklanguage, "movies")}
                </h4>
              ) : (
                <></>
              )}
            </div>
            <div className="d-flex w-100 study-wrpr reception-card-block">
              {isFetching ? (
                <SeminarSkeleton listsToRender={3} type={2} />
              ) : (
                ListCourses?.shopProduct?.topicList?.map((kubystudy, i) => (
                  <div
                    className="img-name-card seminar-card"
                    key={i}
                    onClick={() => checkRedirection(kubystudy)}
                  >
                    <div className="relative img-card">
                      <img
                        src={
                          kubystudy?.posterImage?.attachmentUrl != null
                            ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${kubystudy?.posterImage?.attachmentUrl}`
                            : "/images/test.jpeg"
                        }
                        alt=""
                        className="img-fluid cover-img"
                      />
                      {kubystudy?.isPurchased ? (
                        <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                          <span className="lock check"></span>
                        </div>
                      ) : (
                        <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                          <span className="lock"></span>
                        </div>
                      )}
                    </div>
                    <div className="seminar-card-details">
                      <div className="seminar-card-wrpr">
                        <h6 className="h6 text-grey-3 mb-0 seminar-name">
                          <span className="fw600">{kubystudy?.name}</span> –{" "}
                          {kubystudy?.subTitle}
                        </h6>
                        <p className="fs12 text-grey-5">
                          {kubystudy?.shorttext}
                        </p>
                      </div>
                    </div>
                    <div className="d-flex align-center justify-between seminar-card-bottom">
                      {kubystudy?.price != null && (
                        <p className="h6 fw600 text-grey-1">
                          {/* € {kubystudy?.price} */}
                        </p>
                      )}
                      {/* <div className="d-flex align-center seminar-card-btns">
                                            <button className="btn w-100"> <span className="icon wishlist-icon-green"></span> Wishlist</button>
                                            <button className="btn w-100"> <span className="icon cart-icon-green"></span>Add to cart</button>
                                        </div> */}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
      {/* </div> */}
    </>
  );
};

export default SelfPractice;
