import { useRouter } from "next/router";
import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Number, Currency } from "react-intl-number-format";
import { getProductDetailById } from "../../redux/action/product";
import { addProductToCart } from "../../redux/action/cart";
import CommonNavigation from "../../helpers/CommonNavigation";
import {
  PRODUCT_DETAILS_LABELS,
  getLanguages,
  checklanguage,
} from "../../constant";
import { ProductDetailSkeleton } from "../Common/SkeletonLoader/ProductSkeleton";
import { USER_CART } from "../../redux/action/cart";
import Skeleton from "react-loading-skeleton";
//import Vimeo from '@u-wave/react-vimeo';
import Player from "@vimeo/player";
import _ from "lodash";
import { useCookie } from "next-cookie";
const ProductQuickDetail = ({ pData, id }) => {
  const [loading, setLoading] = useState(true);
  const userCart = useSelector((state) => state.cart);
  const {
    userInfo: { User },
  } = useSelector((state) => state.user);
  const cookies = useCookie();
  const [productdetail, setProductdetail] = useState();
  const [tabChange, setTabChnage] = useState(1);
  const [IsFetching, setIsFetching] = useState(false);
  const router = useRouter();
  const { query } = router;
  const { pid } = query;
  const dispatch = useDispatch();
  const [possibleVariantValues, updatePossibleVariantValues] = useState([]);
  useEffect(() => {
    if (productdetail?.allProducts?.videoLink) {
      var iframe = document.querySelector("iframe");
      var player = null;
      var options = {
        width: 640,
        loop: true,
        controls: true,
        url: SubVideosLesson?.videoLink,
      };
      player = new Player(iframe, options);
    }
    if (parseInt(pid) != parseInt(id)) {
      getProductsDetailById(pid);
    } else {
      setProductdetail(pData);
      //rearrangePossibleVariantValues(pData);
    }
  }, [pid]);

  const getProductsDetailById = async (params) => {
    setIsFetching(true);
    try {
      let response = await getProductDetailById({ id: params });
      setProductdetail(response?.data?.responseData);
      //rearrangePossibleVariantValues(response?.data?.responseData);
      setIsFetching(false);
    } catch (err) {}
  };
  // const checkIfDefaultSelected = (arrayAttributes, value, attributeId) => {
  //     if (arrayAttributes && arrayAttributes.length > 0) {

  //         for (var i = 0; i < arrayAttributes.length; i++) {
  //             if (arrayAttributes[i].content.value == value && attributeId == arrayAttributes[i].attributeId) {
  //                 return true;
  //             }
  //         }
  //     }

  //     return false;
  // }

  // const rearrangePossibleVariantValues = (pDetail) => {
  //     const { allProducts, variationAttributes, dataSendToFrontEnd } = { ...pDetail };
  //     const productData = allProducts;
  //     const possibleVariantValues = variationAttributes;
  //     const combinationfinalDataToSend = dataSendToFrontEnd;

  //     var arrayDefautAttributes = [];
  //     for (var i = 0; i < possibleVariantValues.length; i++) {
  //         for (var j = 0; j < possibleVariantValues[i].value.length; j++) {
  //             var $result = checkIfDefaultSelected(productData.attribute, possibleVariantValues[i].value[j], possibleVariantValues[i].id);
  //             if ($result) {
  //                 arrayDefautAttributes.push(j);
  //             }
  //         }
  //     }
  //     for (var i = 0; i < possibleVariantValues.length; i++) {
  //         possibleVariantValues[i] = Object.assign(possibleVariantValues[i], { updatedValues: [] });
  //         for (var j = 0; j < possibleVariantValues[i].value.length; j++) {
  //             var tempArray = [...arrayDefautAttributes];
  //             tempArray[i] = j;
  //             var $result = checkIfCombinationAvailable(combinationfinalDataToSend, tempArray, i);
  //             possibleVariantValues[i].updatedValues.push($result);
  //         }
  //     }

  //     updatePossibleVariantValues(possibleVariantValues);

  // }

  // const checkIfCombinationAvailable = (availableCombinations, arrayAttributeIndexes, parentIndex) => {

  //     var matched = false;
  //     var tempMatch = {};
  //     if (availableCombinations && availableCombinations.length > 0) {
  //         for (var i = 0; i < availableCombinations.length; i++) {
  //             if (JSON.stringify(availableCombinations[i].index) == JSON.stringify(arrayAttributeIndexes)) {
  //                 matched = { productData: { available: true, productId: availableCombinations[i].productId } };
  //                 break;
  //             } else {
  //                 if (availableCombinations[i].index[parentIndex] == arrayAttributeIndexes[parentIndex]) {
  //                     tempMatch = { productData: { available: false, productId: availableCombinations[i].productId } };
  //                 }
  //             }
  //         }
  //     }

  //     if (!matched) {
  //         return tempMatch;
  //     } else {
  //         return matched;
  //     }

  // }
  const changeQuantity = useCallback(
    async (productdetail, action) => {
      if (
        typeof cookies.get("jwtToken") !== "undefined" &&
        cookies.get("jwtToken") !== null
      ) {
        try {
          let response = await addProductToCart({
            productId: productdetail?.allProducts?.id,
            count: 1,
          });
          dispatch({
            type: USER_CART,
            payload: response?.data?.responseData,
          });
        } catch (err) {}
      } else {
        router.push(`/signin?redirectTourl=/${router.asPath}`);
      }
    },
    [dispatch, productdetail]
  );

  const checkProductInCart = (id) => {
    let userCartData = { ...userCart };
    let findProduct = _.find(userCartData?.userCart?.records, [
      "productId",
      id,
    ]);
    if (findProduct != undefined) {
      return true;
    } else {
      return false;
    }
  };
  const buyNowProduct = (id) => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      //window.location.href = `https://www.checkout-ds24.com/product/${parseInt(id)}`;
      window.location.href = `https://www.digistore24.com/product/${parseInt(
        id
      )}`;
    } else {
      router.push(`/signin?redirectTourl=/${router.asPath}`);
    }
  };
  return (
    <>
      <ul className="d-flex align-center cd-breadcrumb">
        <li className="breadcumb-item">
          <a
            href="javascript:void(0)"
            onClick={() => router.push("/dashboard/shop")}
            className="breadcumb-link"
          >
            {getLanguages(checklanguage, "kubyShop")}
          </a>
        </li>
        <li className="breadcumb-item current">
          <em>{productdetail?.allProducts?.content?.name}</em>
        </li>
      </ul>
      <div className="d-flex prod-main-section">
        <h3 className="mobile-title h3 fw600 prod-title">
          {productdetail?.allProducts?.content?.name || <Skeleton count={1} />}
        </h3>
        <div className="product-main-img">
          {IsFetching ? (
            <Skeleton height={900} className="cover-img" />
          ) : (
            <img
              src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${productdetail?.allProducts?.attachment?.path}`}
              alt="Product image"
              className="contain-img detail-img"
            />
          )}
        </div>
        <div className="prod-right-block">
          <div className="main-details-wrpr">
            {/* <div className='audio-block-wrpr'>
                        {
                            IsFetching ? <ProductDetailSkeleton listsToRender={2} />
                                :
                                possibleVariantValues?.map((obj, index) => {
                                    return (
                                        <div className="audio-option-block" key={index}>
                                            <p className="fw500 text-grey-5 label">{obj?.name} </p>
                                            <div className="audio-opt-wrpr">
                                                {
                                                    obj?.value.map((obj1, index1) => {
                                                        return (
                                                            <div className={`form-group ${obj.updatedValues[index1] && obj.updatedValues[index1].productData && obj.updatedValues[index1].productData.available == false && 'disable'}`} key={index + "_" + index1}>
                                                                <input
                                                                    type="radio"
                                                                    name={"attri_" + obj.id}
                                                                    value={obj1}
                                                                    checked={checkIfDefaultSelected(productdetail?.allProducts?.attribute, obj1, obj.id)}
                                                                    className={'audio-input'}

                                                                />
                                                                <label
                                                                    onClick={(e) => router.push("/dashboard/shop/product-detail?pid=" + obj.updatedValues[index1].productData.productId, undefined, { shallow: true })}
                                                                    key={index}
                                                                    className="language-label" htmlFor="dvd">
                                                                    <span className=""></span>{obj1}
                                                                </label>
                                                            </div>
                                                        )
                                                    })
                                                }
                                            </div>
                                        </div>
                                    )
                                })
                        }
                        </div> */}
            <div className="product-desc-block">
              <h3 className="h3 fw600 prod-title">
                {productdetail?.allProducts?.content?.name || (
                  <Skeleton count={1} />
                )}
              </h3>
              {IsFetching ? (
                <Skeleton count={4} />
              ) : (
                <p
                  className="prod-desc"
                  dangerouslySetInnerHTML={{
                    __html:
                      productdetail?.allProducts?.content?.descriptionHtml,
                  }}
                ></p>
              )}
            </div>
          </div>
          {IsFetching ? (
            <></>
          ) : (
            <div className="price-block">
              <p className="fw500 price-text">
                {getLanguages(checklanguage, "price")}:
                <span className="fw600 prod-price">
                  {" "}
                  <Currency locale={checklanguage} currency={"EUR"}>
                    {productdetail?.allProducts?.price?.[0]?.price.toFixed(2)}
                  </Currency>
                </span>
              </p>
              <div className="d-flex align-center price-btn-quantity">
                <div className="d-flex align-center prod-btn-wrpr">
                  {checkProductInCart(productdetail?.allProducts?.id) ? (
                    <a
                      href="javascript:;"
                      className="btn prod-btn cart-btn"
                      onClick={() => {
                        router.push("/dashboard/shop/cart");
                      }}
                    >
                      <span className="icon cart-icon-green"></span>
                      {getLanguages(checklanguage, "viewCart")}
                    </a>
                  ) : (
                    <a
                      href="javascript:;"
                      className="btn prod-btn cart-btn"
                      onClick={() => {
                        changeQuantity(productdetail);
                      }}
                    >
                      <span className="icon cart-icon-green"></span>
                      {getLanguages(checklanguage, "addToCart")}
                    </a>
                  )}

                  <a
                    onClick={() =>
                      buyNowProduct(productdetail?.allProducts?.dgStoreId)
                    }
                    href="javascript:;"
                    className="btn-accent prod-btn buy-now-btn"
                  >
                    {getLanguages(checklanguage, "buyNow")}
                  </a>
                </div>
              </div>
            </div>
          )}
          {/* {productdetail?.allProducts?.Tags?.length > 0 && <div className="tags-wrpr">
                        <p className="p text-grey-5 font-inter">Tags:&nbsp;
                            <span className="fw600 prod-tags">
                                {productdetail?.allProducts?.Tags?.map(tag => tag?.content?.name).join(', ')}
                            </span>
                        </p>
                    </div>} */}
          {/* {
                        productdetail?.normalAttributes?.length > 0 && productdetail?.normalAttributes?.map((_noratribute, index) => (
                            <div className="tags-wrpr" key={index}>
                                <p className="p text-grey-5 font-inter">{_noratribute?.name}:&nbsp;
                                    <span className="fw600 prod-tags">
                                        {_noratribute?.value?.[0]}
                                    </span>
                                </p>
                            </div>
                        ))
                    } */}
          {productdetail?.allProducts?.highlights ? (
            <div className="tags-wrpr highlights-wrapper">
              {
                <p
                  dangerouslySetInnerHTML={{
                    __html: productdetail?.allProducts?.highlights,
                  }}
                />
              }
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
      {productdetail?.allProducts?.videoLink && (
        <div className="view_video product_detail_video">
          <iframe
            id={`top_right_video`}
            src={productdetail?.allProducts?.videoLink}
            width="100%"
            height="auto"
            allowFullScreen="allowfullscreen"
            className="img-fluid"
          >
            {" "}
          </iframe>
          {/* <Vimeo
                            style={{width: "100%"}}
                            video={productdetail?.allProducts?.videoLink}
                            width={"800"}
                            height={'auto'}
                            autoplay
                            onReady={() => setLoading(!loading)}
                            controls={true}
                            muted={true}
                            dnt={true}
                            showByline={true}
                        /> */}
        </div>
      )}

      <div className="prod-tabs-wrpr">
        {productdetail && (
          <CommonNavigation
            data={
              Object.keys(productdetail?.allProducts?.additionalInformation)
                ?.length > 0
                ? PRODUCT_DETAILS_LABELS
                : [PRODUCT_DETAILS_LABELS[0]]
            }
            kubynavbarStatus={2}
            setMeetingTab={setTabChnage}
            MeetingTab={tabChange}
          />
        )}
        <div className="tab-contents">
          {(() => {
            switch (tabChange) {
              case 1:
                return (
                  <div className="desc-wrpr">
                    <p
                      className="prod-desc"
                      dangerouslySetInnerHTML={{
                        __html:
                          productdetail?.allProducts?.content
                            ?.longDescriptionHtml,
                      }}
                    ></p>
                  </div>
                );
              case 2:
                return (
                  <div className="desc-wrpr">
                    <p className="prod-desc">
                      {getLanguages(checklanguage, "whoAmI")}
                    </p>
                  </div>
                );
            }
          })()}
        </div>
      </div>
    </>
  );
};

export default ProductQuickDetail;
