import React, { useEffect, useState } from 'react'
import ProductFilter from './ProductFilter'
import { getProductList, addToWishLists } from "../../redux/action/product"
import { addProductToCart, USER_CART } from "../../redux/action/cart"
import { useDispatch, useSelector } from 'react-redux'
import _ from 'lodash'
import ProductShop from '../Common/ProductShop'
import { useCookie } from 'next-cookie'
import { useRouter } from 'next/router'

import EmptyScreen from '../Common/EmptyScreen'
const Kubyshop =  ({ CategoryList} ) => {
    const dispatch = useDispatch();
    const router = useRouter();
    const { query } = router;
    const [currentCategory, setCurrentCategory] = useState(query?.categoryId ?? CategoryList?.[0]?.id);
    const [searchText, setSearchText] = useState(query?.name ?? "");
    const userCart = useSelector(state => state.cart);
    const { userInfo: { User } } = useSelector((state) => state.user);
    const [CurrentPage, setCurrentPage] = useState(1)
    const [hasNext, setHasNext] = useState(false)
    const cookies = useCookie();
    
    console.log(query, 'qqqqq');
    let [ProductLists, setProductLists] = useState(null)
    let [IsFetching, setIsFetching] = useState(false)
    useEffect(() => {
        
        getProducts();
    }, [CurrentPage, query, currentCategory])

    const getProducts = async () => {
        let id;
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            id = User?.id
        }
        if (CurrentPage > 1) {
        }
        else {
            setIsFetching(true)
        }
        try {
            let payload = { userId: id, ...query, pageNumber: CurrentPage, limit: 100, categoryId:   currentCategory, orderByParameter: "ordering", orderByValue: "ASC"}
            if(!query?.name || query?.name == ""){
                
                delete payload.name
            }
            let response = await getProductList(payload);
            if (CurrentPage > 1) {
                setProductLists(prev => (({ ...ProductLists, products: [...ProductLists?.products, ...response?.data?.responseData?.products] })))
            }
            else {
                setProductLists(response?.data?.responseData);
            }
            setIsFetching(false);
            //setHasNext(response?.data?.responseData?.loadMore)

        }
        catch (err) {

        }
    }
    const addToCart = async (product, index) => {
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            try {
                let response = await addProductToCart({
                    productId: product?.id,
                    count: 1
                })
                dispatch({
                    type: USER_CART,
                    payload: response?.data?.responseData
                })
            }
            catch (err) {

            }
        }
        else {
            router.push(`/signin?redirectTourl=/dashboard/shop`)
        }
    }
    const checkProductInCart = (id) => {
        let userCartData = { ...userCart }
        let findProduct = _.find(userCartData?.userCart?.records, ["productId", id])
        if (findProduct != undefined) {
            return true
        }
        else {
            return false
        }
    }
    const addToWishList = async (id, index, WishLists) => {
        if (typeof cookies.get('jwtToken') !== 'undefined' && cookies.get('jwtToken') !== null) {
            try {
                let response = await addToWishLists({ productId: id })
                let updateresponseObject = { ...ProductLists }
                if (WishLists?.length > 0) {
                    updateresponseObject.products[index].WishLists = []
                }
                else {
                    updateresponseObject.products[index].WishLists = [response?.data?.responseData?.productId]
                }
                setProductLists(updateresponseObject)

            }
            catch (err) {

            }
        }
        else {
            router.push(`/signin?redirectTourl=/dashboard/shop`)
        }
    }

    

    return (
        <>
        <div className={ProductLists?.products?.length > 0 ? "h-100" :""}>
            <ProductFilter ProductLists={ProductLists}  setSearchText = {setSearchText} searchText={searchText} CategoryList ={CategoryList} currentCategory={currentCategory} setCurrentCategory = {setCurrentCategory} />
            
            <ProductShop IsFetching={IsFetching} ProductLists={ProductLists} checkProductInCart={checkProductInCart} addToCart={addToCart} addToWishList={addToWishList} setCurrentPage={setCurrentPage} hasNext={hasNext} setHasNext={setHasNext} />
        </div>
            {ProductLists?.products?.length === 0 && <EmptyScreen productType={1} />}
        </>
    )
}

export default Kubyshop