import { useRouter } from "next/router";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { checklanguage, getLanguages } from "../../constant";
const ProductFilter = ({
  ProductLists,
  CategoryList,
  currentCategory,
  searchText,
  setSearchText,
  setCurrentCategory,
}) => {
  const router = useRouter();
  const { query } = router;

  useEffect(() => {}, [currentCategory, searchText]);

  const handleChangeFilterProduct = (e) => {
    const { name, value } = e.target;
    let newQuery = { ...router.query };
    if (name == "categoryId") {
      newQuery.categoryId = value;
    } else if (value == "DESC") {
      newQuery.orderByValue = value;
      newQuery.orderByParameter = "price";
    } else if (value == "ASC") {
      newQuery.orderByValue = value;
      newQuery.orderByParameter = "price";
    } else if (value == "createdAt") {
      newQuery.orderByValue = "DESC";
      newQuery.orderByParameter = value;
    }
    router.push(
      {
        pathname: `${router?.pathname}`,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };
  console.log("currentCategory", currentCategory);
  return (
    <div className="comp-inner-header ">
      <div className="d-flex align-center justify-between inner-header b-0">
        <div className="d-flex align-center title-wrpr">
          <span className="icon title-icon shop-icon"></span>
          <h4 className="fw500 page-title">
            {getLanguages(checklanguage, "kubyShop")}
          </h4>
          <p className="text-grey-7 page-desc">
            ({ProductLists?.totalRecords})
          </p>
        </div>
        {/* <div className="d-flex align-center text-right inner-header-btns">
                    <div onClick={()=> router.push(router.pathname, '', { scroll: false })} style={{color :"#499557" ,cursor:"pointer"}} className="d-flex align-center">
                        Reset
                    </div>
                    <div className="d-flex align-center header-filter">
                        <span className="icon cat-icon"></span>
                        <p className="result-ct">Category:</p>
                        <select name='categoryId' className="filter-select" onChange={handleChangeFilterProduct} value={query?.categoryId}>
                            <option value="all" className='d-none' >All</option>
                            {
                                CategoryList?.map((category, index) => (
                                    <option key={index} value={category?.id}>{category?.CategoryContents?.name}</option>

                                ))
                            }
                        </select>
                    </div>
                    <div className="d-flex align-center header-filter">
                        <span className="icon sort-icon"></span>
                        <p className="result-ct">Sort by : </p>
                        <select className="filter-select" value={query?.orderByValue} onChange={handleChangeFilterProduct}>
                            <option value="createdAt">Newly List</option>
                            <option value="DESC">High to Low</option>
                            <option value="ASC">Low to High</option>
                            <option value="createdAt">Newly List</option>
                        </select>
                    </div>
                </div> */}
      </div>
      <div className="d-flex align-center justify-between tab-list-search-wrpr">
        <ul className="d-flex tab-list-wrpr">
          {CategoryList?.length &&
            CategoryList.map((obj, index) => {
              return (
                <li className="tab-item" key={index}>
                  <Link
                    href={
                      "/dashboard/shop?categoryId=" +
                      obj.id +
                      "&name=" +
                      searchText
                    }
                    // onClick = {(e) => {
                    //     e.preventDefault();
                    //     setCurrentCategory(obj.id);
                    // }}
                  >
                    <a
                      className={`${"tab-link"} ${
                        currentCategory == obj?.id ? "active" : ""
                      }`}
                    >
                      {obj?.CategoryContents?.name}
                    </a>
                  </Link>
                </li>
              );
            })}
          {/* <li className="tab-item">
                        <a href="javascript:void(0);" className="tab-link active">Testing Category</a>
                    </li>
                    <li className="tab-item">
                        <a href="javascript:void(0);" className="tab-link ">Paperback</a>
                    </li>
                    <li className="tab-item">
                        <a href="javascript:void(0);" className="tab-link ">eBook</a>
                    </li>
                    <li className="tab-item">
                        <a href="javascript:void(0);" className="tab-link ">Audiobook</a>
                    </li> */}
        </ul>
        <div className="search-wrpr">
          {/* <input type='text' className='form-control'/> */}
          <div className="form-in  ">
            <div className="f-in w-100">
              <input
                name="email"
                placeholder=""
                type="text"
                autoComplete="off"
                className="form-control"
                value={searchText}
                onChange={(e) => {
                  setSearchText(e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.which == 13 && e.target?.value?.length) {
                    window.document.getElementById("search-button").click();
                  }
                }}
              />
              {searchText?.length ? (
                <button
                  className="clear-button"
                  onClick={() => {
                    router.push(
                      "/dashboard/shop?categoryId=" + currentCategory
                    );
                  }}
                >
                  <span className="icon close-icon"></span>
                </button>
              ) : (
                <></>
              )}
            </div>
          </div>
          <button
            className="search-button"
            id="search-button"
            onClick={() => {
              router.push(
                "/dashboard/shop?categoryId=" +
                  currentCategory +
                  "&name=" +
                  searchText
              );
            }}
          >
            <span className="icon search-icon"></span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductFilter;
