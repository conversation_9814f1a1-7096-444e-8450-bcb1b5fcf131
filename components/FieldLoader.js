import { ThreeDots } from 'react-loader-spinner'
import LoadingOverlay from 'react-loading-overlay';
import { ClipLoader } from 'react-spinners';
function FieldLoader() {
  return (
    <div className="img-loader active">
      <div className="loader">
        <div className="loader-item loader1"></div>
        <div className="loader-item loader2"></div>
        <div className="loader-item loader3"></div>
        <div className="loader-item loader4"></div>
        <div className="loader-item loader5"></div>
        <div className="loader-item loader6"></div>
        <div className="loader-item loader7"></div>
        <div className="loader-item loader8"></div>
      </div>
    </div>
  );
}

export default FieldLoader;

export const CartScreenLoader = () => {
  return (
    <div className='cart_loader-svg'>
      <ThreeDots
        height="80"
        width="100"
        color="#499557"
        ariaLabel="three-dots-loading"
        radius={9}
        wrapperStyle={{}}
        wrapperClassName=""
        visible={true}
      /></div>
  )
}

export const OverLayLoader = ({children ,Loading } ) => {
  return (
      <LoadingOverlay
        active={Loading}
        classNamePrefix="MyLoader_"
        className='h-100'
        spinner={
          <ClipLoader color="#499557" 
          cssOverride={{
            margin:"0 auto",
            marginLeft :"50px",
            marginRight :"50px",
            marginBottom :"50px"

          }}
          />}
      >
        {children}
      </LoadingOverlay>
  )
}