import React from "react";
import { checklanguage, getLanguages } from "../../constant";

const LeaveActionPoppup = ({ show, onHide, router }) => {
  return (
    <div
      className={`modal camcel-meeting-modal  ${show == 5 ? "show" : ""}`}
      id="scheduleModal"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="camcel-modal-content">
        <div className="modal-dialog">
          {/* <div style={{ borderBottom: "none" }} className="modal-header p-0">
                        <a
                            href="javascript:;"
                            className="close-btn"
                            id="closeModal"
                            onClick={onHide}
                        >
                            <span className="icon close-icon"></span>
                        </a>
                    </div> */}
          <div className="modal-body">
            <h4 className="h4 text-dark-grey fw500 text-center">
              {getLanguages(checklanguage, "sureYouWantToLeave")}
            </h4>
            <div className="d-flex align-center form-btns-wrpr">
              <button
                type="button"
                className="btn-secondary sm"
                onClick={onHide}
              >
                <span className="btn-text-wrspr">
                  {getLanguages(checklanguage, "no")}
                </span>
              </button>
              <button
                type="button"
                onClick={() => router.push("/dashboard/soulwriting")}
                className={`btn-accent sm`}
              >
                <span className="btn-text-wrpr">
                  {getLanguages(checklanguage, "yes")}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveActionPoppup;
