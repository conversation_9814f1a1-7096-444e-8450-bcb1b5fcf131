import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import _ from "lodash";

const SoulWritingTab = ({
  componentWrapper,
  categoryList,
  setcomponentWrapper,
  setcategoryId,
  setqueryData,
  setselectedTextWrraperCategory1,
  bridgeArrayChar,
  setcategoryListForm,
  categoryListForm,
  memberstatusInfo,
  setNextClick,
  nextClick,
  setOpenPopup,
  lastOpenedStage,
  setStep4Video,
}) => {
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // const bridgeHighlights = () => {
  //     if (memberstatusInfo?.customerStatus == 2) return;
  //     let dataArrayBridge = [...bridgeArrayChar];
  //     let categoryArray = { ...categoryListForm };
  //     for (let i = 0; i < bridgeArrayChar.length; i++) {
  //         for (let iterator in categoryArray) {
  //             for (let j = 0; j < categoryArray[iterator]?.length; j++) {
  //                 if (
  //                     dataArrayBridge[i]?.lineNumber ==
  //                     categoryArray[iterator][j].lineNumber
  //                 ) {
  //                     categoryArray[iterator][j].content = categoryArray[iterator][
  //                         j
  //                     ].content?.replace(
  //                         dataArrayBridge[i]?.selectionText,
  //                         `<span class="bridgeHighlight">${dataArrayBridge[i]?.selectionText}</span>`
  //                     );
  //                 }
  //             }
  //         }
  //     }
  //     setcategoryListForm(categoryArray);

  // };
  return (
    <div className="soulwriting-steps-wrpr-outer">
      <button
        className={`toggle-sidebar ${isSidebarOpen ? "" : "closed"}`}
        onClick={(e) => {
          e.preventDefault();
          setIsSidebarOpen(!isSidebarOpen);
        }}
      >
        <span className="icon arrow-icon"></span>
      </button>
      <div
        className={`${"soulwriting-steps-wrpr"} ${
          isSidebarOpen ? "" : "closed"
        }`}
      >
        {categoryList?.map((category, i) => {
          return (
            <a
              id={"soulwriting-tab-id-" + category?.id}
              step={`#${category?.type?.toLowerCase()?.split(" ")?.join("")}`}
              onClick={(e) => {
                setNextClick(false);
                setOpenPopup(false);
                e.preventDefault();
                if (memberstatusInfo?.customerStatus == 2) {
                  return;
                }

                componentWrapper?.length > 2 &&
                  setcomponentWrapper((prev) => {
                    let findId = _.find(componentWrapper, [
                      "categoryId",
                      category?.id,
                    ]);
                    if (findId != undefined && findId.categoryId != null) {
                      return prev;
                    } else {
                      return [...prev, { categoryId: category.id }];
                    }
                  });

                setcategoryId(category.id);
                if (category?.type?.toLowerCase() == "redline") {
                  setNextClick(false);
                  setOpenPopup(false);
                  setqueryData((prev) => {
                    return { ...prev, redline: true, bridge: false };
                  });
                  setselectedTextWrraperCategory1((prev) => {
                    return {
                      ...prev,
                      redlineId: category?.id,
                    };
                  });
                  setTimeout(() => {
                    document.querySelector(`#redline`)?.scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                      inline: "nearest",
                    });
                  }, 500);
                } else if (category?.type?.toLowerCase() == "bridge") {
                  setNextClick(false);
                  setOpenPopup(false);
                  // componentWrapper?.length > 2 &&
                  //     setqueryData((prev) => {
                  //         return {
                  //             ...prev,
                  //             bridge: true,
                  //             redline: false,
                  //             date: new Date(),
                  //         };
                  //     });
                  setselectedTextWrraperCategory1((prev) => {
                    return {
                      ...prev,
                      bridgeId: category?.id,
                    };
                  });

                  // setTimeout(() => {
                  //     document
                  //         .querySelector(`#reason`)
                  //         ?.scrollIntoView({
                  //             behavior: "smooth",
                  //             block: "center",
                  //             inline: "nearest",
                  //         });
                  // }, 1000);
                  //bridgeHighlights();
                } else if (category?.type?.toLowerCase() == "occasion") {
                  setNextClick(false);
                  setOpenPopup(false);
                  setStep4Video(false);
                } else {
                  setNextClick(false);
                  setOpenPopup(false);
                  setqueryData((prev) => {
                    return {
                      ...prev,
                      bridge: false,
                      // redline: false,
                      redline: false,
                      date: new Date(),
                    };
                  });
                  setTimeout(() => {
                    document
                      .querySelector(
                        `#${category?.type
                          ?.toLowerCase()
                          ?.split(" ")
                          ?.join("")}`
                      )
                      ?.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                        inline: "nearest",
                      });
                  }, 500);
                  if (category?.type?.toLowerCase() == "protagonist") {
                    componentWrapper?.length > 2 &&
                      setselectedTextWrraperCategory1((prev) => {
                        return {
                          ...prev,
                          protogonistId: category?.id,
                        };
                      });
                  }
                }
                setTimeout(() => {
                  if (e?.target?.getAttribute("href")) {
                    router.push(e.target.getAttribute("href"));
                  } else {
                    router.push(e.target.parentElement.getAttribute("href"));
                  }
                  // router.push(e.target.getAttribute("href"));
                }, 300);
              }}
              key={i}
              
              href={
                category?.id == componentWrapper[i]?.categoryId
                  ? `#${category?.type?.toLowerCase()?.split(" ")?.join("")}-${
                      componentWrapper[i]?.categoryId
                    }`
                  : "javascript:void(0)"
              }
              // className={`step-btn btn${i + 1} ${
              //   _.find(componentWrapper, ["categoryId", category?.id]) !=
              //     undefined && "active"
              // }`}
              className={`step-btn btn${i + 1} ${
                category?.id <= lastOpenedStage ? "active" : ""
              }`}
            >
              <span className="step-text">{i + 1}.</span>
              <span className={`soul-step step${i + 1}`}></span>
              <span className="step-text">{category?.name}</span>
            </a>
          );
        })}
      </div>
    </div>
  );
};
export default SoulWritingTab;
