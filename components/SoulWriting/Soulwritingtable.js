import moment from "moment";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import {
  SOUL_WRITING_STATUS,
  SOUL_WRITING_CLASS,
  getLanguages,
  checklanguage,
} from "../../constant";

const Soulwritingtable = ({
  projectData,
  curremtPage,
  setCurrentPage,
  showOldSoulwriting,
}) => {
  console.log(`pppppp`, projectData?.projectList);
  // useEffect(() => {
  //   sessionStorage.setItem("currentStage3", false);
  // }, []);
  return (
    projectData != null && (
      <div className="soulwriting-table inner-header">
        {projectData &&
        projectData.projectList &&
        projectData.projectList.length > 0 ? (
          <table className="soul-table w-100">
            <thead>
              <tr className="tr soul-trow">
                <th className="th soul-tcol">
                  {" "}
                  {getLanguages(checklanguage, "action")}
                </th>

                <th className="th soul-tcol">
                  {getLanguages(checklanguage, "project")}
                </th>
                <th className="th soul-tcol">
                  {getLanguages(checklanguage, "dateOfOrigin")}
                </th>
                <th className="th soul-tcol">
                  {getLanguages(checklanguage, "characters")}
                </th>
                <th className="th soul-tcol">
                  {getLanguages(checklanguage, "stage")}
                </th>
                {/* <th className="th soul-tcol">
                {getLanguages(checklanguage, "version")}
              </th> */}

                {/* <th className="th soul-tcol">Last Contacted</th> */}

                {/* <th className="th soul-tcol">
                {getLanguages(checklanguage, "recording")}
              </th> */}
                {/* <th className="th soul-tcol">
                {getLanguages(checklanguage, "companion")}
              </th> */}
                {/* <th className="th soul-tcol">
                {getLanguages(checklanguage, "paymentAmount")}
              </th> */}
                <th className="th soul-tcol">
                  {getLanguages(checklanguage, "status")}
                </th>
              </tr>
            </thead>
            <tbody>
              {projectData?.projectList?.map((item, i) => (
                <tr className="tr soul-trow" key={i}>
                  <td className="td soul-tcol">
                    <Link
                      href={`/dashboard/soulwriting/create-soulwriting?projectId=${item?.id}`}
                    >
                      <a className="link">
                        {" "}
                        {getLanguages(checklanguage, "continueWriting")}
                      </a>
                    </Link>
                  </td>

                  <td className="td soul-tcol">{item?.title}</td>
                  <td className="td soul-tcol">
                    {localStorage.getItem("language") == "en"
                      ? moment(item?.lastSubmittedDate || item?.createdAt)?.format("MMMM D, YYYY")
                      : moment(item?.lastSubmittedDate || item?.createdAt)?.format("D. MMM YYYY")}
                  </td>
                  <td className="td soul-tcol">{item?.characters}</td>
                  <td className="td soul-tcol">
                    {item?.stage} <span className="soul-step-icon step3"></span>
                  </td>
                  {/* <td className="td soul-tcol">{item.version}</td> */}

                  {/* <td className="td soul-tcol">04.07.2022 </td> */}

                  {/* <td className="td soul-tcol">-</td> */}
                  {/* <td className="td soul-tcol">
                  {item?.companion?.firstName} {item?.companion?.lastName}
                </td> */}
                  {/* <td className="td soul-tcol">
                  {item?.paymentAmount != null ? item?.paymentAmount : " -"}{" "}
                </td> */}
                  {/* {
                                    item?.paymentLink != null ?
                                        <td onClick={(e) => { e.preventDefault(); window.location = item?.paymentLink }} className="td soul-tcol">
                                            <a href="javascript:void(0);" className="link">Continue writing</a>
                                        </td>
                                        :
                                        <td className="td soul-tcol">
                                            <Link href={`/dashboard/soulwriting/create-soulwriting?projectId=${item?.id}`}><a className="link"> Continue writing</a></Link>
                                        </td>
                                } */}

                  <td className="td soul-tcol">
                    <p
                      className={`soul-status ${
                        SOUL_WRITING_CLASS[item?.customerStatus]
                      }`}
                    >
                      <span
                        className={`icon soul-status-${
                          SOUL_WRITING_CLASS[item?.customerStatus]
                        }`}
                      ></span>
                      {item?.customerStatus != null
                        ? SOUL_WRITING_STATUS[item?.customerStatus]
                        : "-"}
                    </p>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <h4
            style={{ margin: "auto", marginTop: "100px" }}
            className="h3 text-dark-grey text-center"
          >
            {showOldSoulwriting
              ? getLanguages(
                  checklanguage,
                  "noNewSoulWriting",
                  null,
                  null,
                  true
                )
              : getLanguages(checklanguage, "noResultFound")}
          </h4>
        )}
      </div>
    )
  );
};

export default Soulwritingtable;
