import React, { forwardRef, useEffect, useState } from "react";
import moment from "moment";
import { exportSoulwritingPDF } from "../../redux/action/soul-writing";
import { checklanguage, getLanguages } from "../../constant";
// import jsPDF from "jspdf";
// import logo from "../../public/images/logo.png";
// import Base64 from "base-64";



const SoulWritingHeaderBar = forwardRef(
  ({
    projectInfo,
    pdfRef,
    setshow,
    componentWrapper,
    categoryListForm,
    character,
  }) => {
    const [pdfFilePath, setPdfFilePath] = useState("");
    const [generatingPdf, setGeneratingPdf] = useState(false);
    const [generatingWord, setGeneratingWord] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    useEffect(() => {
      const userAgent = typeof window.navigator === "undefined" ? "" : navigator.userAgent;
      const mobileCheck = /Android|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop|BlackBerry/i.test(userAgent);
      setIsMobile(mobileCheck);
    }, []);
    // const [logaUnit8Arr, setLogoUnit8Arr] = useState();
    // console.log(`xxxx`, projectInfo);

    // const logoToUint8Array = () => {
    //   return new Promise((resolve, reject) => {
    //     const xhr = new XMLHttpRequest();
    //     xhr.onload = function () {
    //       const arrayBuffer = xhr.response;
    //       const uint8Array = new Uint8Array(arrayBuffer);
    //       resolve(uint8Array);
    //     };
    //     xhr.onerror = reject;
    //     xhr.open("GET", logo);
    //     xhr.responseType = "arraybuffer";
    //     xhr.send();
    //   });
    // };
    // useEffect(() => {
    //   logoToUint8Array().then((logoUint8Array) => {
    //     console.log("Uint8Array", logoUint8Array);
    //     setLogoUnit8Arr(logoUint8Array);
    //   });
    // }, []);
    // const organizeContent = () => {
    //   const headings = [
    //     "Title",
    //     "Reason",
    //     "Protagonist",
    //     "Occasion",
    //     "Trigger",
    //     "Pain Picture",
    //     "Rewrite",
    //     "Affirmation",
    //     "Projection",
    //   ];

    //   const organizedContent = {};

    //   headings.forEach((heading) => {
    //     organizedContent[heading] = [];
    //   });
    //   organizedContent["Title"].push(projectInfo.title);
    //   organizedContent["Reason"].push(projectInfo.reason);
    //   character.forEach((char) => {
    //     const content = `Acronym: ${char.Acronym} \n   Gender: ${
    //       char.title == 1 ? "male" : "female"
    //     } \n   Age: ${char.age}`;
    //     organizedContent["Protagonist"].push(content);
    //   });

    //   Object.values(categoryListForm).forEach((items) => {
    //     items.forEach((item) => {
    //       switch (item.categoryId) {
    //         case 4:
    //           organizedContent["Occasion"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         case 5:
    //           organizedContent["Trigger"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         case 6:
    //           organizedContent["Pain Picture"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         case 9:
    //           organizedContent["Rewrite"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         case 10:
    //           organizedContent["Affirmation"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         case 11:
    //           organizedContent["Projection"].push(
    //             item.content.replace(/<\/?[^>]+(>|$)/g, "")
    //           );
    //           break;
    //         default:
    //           break;
    //       }
    //     });
    //   });

    //   return organizedContent;
    // };

    const exportPDFWithMethod = async (payload) => {
      try {
        let projectId = payload?.id;
        let version = payload?.version;
        let language = localStorage.getItem("language");

        let data = {
          projectId: projectId,
          version: version,
          language: language || "de",
        };
        setGeneratingPdf(true);
        let response = await exportSoulwritingPDF(data);
        setGeneratingPdf(false);
        if (response?.data?.responseData?.filePath) {
          setPdfFilePath(
            process.env.NEXT_PUBLIC_API_BASE_URL +
            "/" +
            response?.data?.responseData?.filePath
          );
          setTimeout(() => {
            document.getElementById("pdf_link").click();
          }, 200);
        }
      } catch (e) {
        setGeneratingPdf(false);
      }
    };

    const handleWordExport = async () => {
      try {
        setGeneratingWord(true);
        const generateWordDocument = (await import('./SoulWrittingStep/WordComponent')).default;
        await generateWordDocument({
          projectInfo,
          getCompanion: null, // Falls Sie diese Daten haben, fügen Sie sie hinzu
          categoryListForm,
          dateInfo: null, // Falls Sie diese Daten haben, fügen Sie sie hinzu
        });
        setGeneratingWord(false);
      } catch (error) {
        console.error('Error generating Word document:', error);
        setGeneratingWord(false);
      }
    };

    const [versionHistory, setversionHistory] = useState(null);
    useEffect(() => {
      let version = [];
      if (Object.keys(projectInfo)?.length > 0) {
        for (var i = projectInfo?.submittedVersion; i > 0; i--) {
          version.push(i);
        }
      }
      setversionHistory(version.sort());
    }, [projectInfo]);

    function redirectToPage(e) {
      var url = `/dashboard/soulwriting/script-view?projectId=${projectInfo?.id}&version=${e.target.value}`;
      window.open(url, "_blank");
    }

    return (
      <div className="d-flex align-center justify-between soul-form-header">

        <p className="text-grey-1 font-inter project-name tool-tip" data-title={projectInfo?.title}>{projectInfo?.title?.length > (isMobile ? 40 : 100) ? projectInfo?.title?.substring(0, (isMobile ? 40 : 100)) + "..." : projectInfo?.title}</p>

        {/* <p className="text-grey-1 font-inter project-name">
          {projectInfo?.title?.length > 40
            ? projectInfo?.title?.substring(0, 40) + "..."
            : projectInfo?.title}
        </p> */}
        <div className="d-flex align-center soul-header-right">
          <p className="text-grey-5 font-inter origin-date">
            {getLanguages(checklanguage, "dateOfOrigin")}:{" "}
            <span className="text-grey-1">
              {localStorage.getItem("language") == "en"
                ? moment(projectInfo?.createdAt)?.format("MMMM D, YYYY")
                : moment(projectInfo?.createdAt)?.format("D. MMM YYYY")}
            </span>
          </p>
          {/* <div className="d-flex align-center header-filter">
                    <img src="/images/recording.svg" className="img-fluid filter-icon" />
                    <select className="filter-select">
                        <option value="Recording">Recording</option>
                        <option value="Recording">Recording</option>
                        <option value="Recording">Recording</option>
                        <option value="Recording">Recording</option>
                    </select>
                </div> */}
          {versionHistory?.length > 1 && (
            <div className="d-flex align-center header-filter">
              <img
                src="/images/versions.svg"
                className="img-fluid filter-icon"
              />
              {versionHistory != null && (
                <select
                  value={""}
                  className="filter-select"
                  onChange={redirectToPage}
                >
                  <option value={""} className="d-none">
                    {getLanguages(checklanguage, "selectVersion")}
                  </option>
                  {versionHistory?.map((item, i) => (
                    <option key={i} value={item}>
                      {`Version history${item}`}{" "}
                    </option>
                  ))}
                </select>
              )}
            </div>
          )}

          {componentWrapper?.length > 3 && (
            <>
              <div className="d-flex align-center header-filter">
                <a
                  href={`/dashboard/soulwriting/script-view?projectId=${projectInfo?.id} `}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="fw500 filter-btn"
                >
                  <img
                    src="/images/script.svg"
                    className="img-fluid filter-icon"
                  />
                  {getLanguages(checklanguage, "scriptView")}
                </a>
              </div>
              <div className="d-flex align-center header-filter">
                <button
                  type="button"
                  onClick={handleWordExport}
                  className={`fw500 filter-btn ${generatingWord ? "btn-loader-pdf" : ""}`}
                  disabled={generatingWord}
                >
                  <u></u>
                  {getLanguages(checklanguage, "downloadWord") || "Word"}
                </button>
              </div>
              <div className="d-flex align-center header-filter">
                <button
                  type="button"
                  onClick={(e) => exportPDFWithMethod(projectInfo)}
                  className={`${"fw500 filter-btn"} ${generatingPdf ? "btn-loader-pdf" : ""
                    }`}
                >
                  <u></u>
                  {getLanguages(checklanguage, "download")}
                </button>
                <a href={pdfFilePath} target="_blank" rel="noreferrer" id="pdf_link"></a>
              </div>
            </>
          )}
        </div>
      </div>
    );
  }
);
SoulWritingHeaderBar.displayName = "SoulWritingHeaderBar";

export default SoulWritingHeaderBar;
