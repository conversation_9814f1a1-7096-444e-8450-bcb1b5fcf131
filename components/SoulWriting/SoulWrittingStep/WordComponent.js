import {
    Document,
    Paragraph,
    TextRun,
    Table,
    TableRow,
    TableCell,
    WidthType,
    BorderStyle,
    VerticalAlign,
    AlignmentType,
    convertInchesToTwip,
    PageOrientation,
    Numbering,
    LevelFormat,
    AlignmentType as ParagraphAlignment,
    Packer,
    HeadingLevel,
    TableLayoutType,
    ExternalHyperlink
  } from "docx";
  import { BLUE_COLOR } from "../../../constant";
  import { getSoulWritingCharacter } from "../../../redux/action/soul-writing";
  import moment from 'moment';
  import { getLanguages, checklanguage } from "../../../constant";
  
  // Helper function to create a blue heading
  const createHeading = (text) => new Paragraph({
    children: [
      new TextRun({
        text: text,
        bold: true,
        size: 28,
        color: BLUE_COLOR,
      }),
    ],
    spacing: { after: 200 },
  });
  
  // Helper function to create a table
  const createTable = (headers, data) => {
    return new Table({
      width: { size: 100, type: WidthType.PERCENTAGE },
      columnWidths: [800, 1200, 5000, 2500],
      borders: {
        top: { style: BorderStyle.SINGLE, size: 1 },
        bottom: { style: BorderStyle.SINGLE, size: 1 },
        left: { style: BorderStyle.SINGLE, size: 1 },
        right: { style: BorderStyle.SINGLE, size: 1 },
        insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
        insideVertical: { style: BorderStyle.SINGLE, size: 1 },
      },
      rows: [
        // Header row
        new TableRow({
          children: headers.map(header =>
            new TableCell({
              children: [new Paragraph({ children: [new TextRun({ text: header, bold: true, size: 24 })], spacing: { before: 0, after: 0 } })],
              verticalAlign: VerticalAlign.TOP,
              margins: { top: 0, bottom: 0, left: 0, right: 0 },
              width: {
                size: header === getLanguages(checklanguage, "comment") ? 2500 : 
                      header === getLanguages(checklanguage, "literalSpeechDirection") ? 5000 : 
                      header === getLanguages(checklanguage, "number") ? 800 :
                      1200, // Protagonisten
                type: WidthType.DXA,
              },
              shading: { fill: "EFEFEF" }
            })
          )
        }),
        // Data rows
        ...data.map((row, index) =>
          new TableRow({
            children: [
              // No. column with automatic numbering
              new TableCell({
                children: [
                  new Paragraph({
                    numbering: {
                      reference: "my-numbering",
                      level: 0
                    },
                    spacing: { before: 0, after: 0 }
                  })
                ],
                verticalAlign: VerticalAlign.TOP,
                margins: { top: 0, bottom: 0, left: 0, right: 0 },
                width: {
                  size: 800,
                  type: WidthType.DXA,
                },
              }),
              // Other columns
              ...row.slice(1).map((cell, index) =>
                new TableCell({
                  children: [
                    new Paragraph({
                      children: [new TextRun({ text: cell, size: 24 })],
                      spacing: { before: 0, after: 0 }
                    })
                  ],
                  verticalAlign: VerticalAlign.TOP,
                  margins: { top: 0, bottom: 0, left: 0, right: 0 },
                  width: {
                    size: index === 1 ? 5000 : 2500,
                    type: WidthType.DXA,
                  },
                })
              )
            ]
          })
        )
      ]
    });
  };
  
  // Helper function to create a standard section with table
  const createStandardSection = (title, data, extraContent = null) => {
    // Check if there's at least one entry with actual content
    const hasContent = data?.some(field => 
      (field.content?.replace(/<[^>]+>/g, '')?.replace(/&nbsp;/g, ' ')?.trim() || 
       field.comments?.length > 0)  
    );
    
    // If no content and no extra content, don't create section at all
    if (!hasContent && !extraContent) {
      return [];
    }
    
    const elements = [];
    
    // Add title
    elements.push(
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, title) || title, bold: true, size: 32 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1
      })
    );
  
    // Add any extra content (like dates)
    if (extraContent) {
      elements.push(...extraContent);
    }
  
    // Add table with ALL entries (including empty ones) if we have at least one real entry
    if (Array.isArray(data) && data.length > 0) {
      const rows = data.map(field => {
        // Nur die Kommentartexte, ohne Autor und Zeit
        const comments = field.comments?.map(comment => 
          comment.comment?.replace(/<[^>]+>/g, '')?.replace(/&nbsp;/g, ' ')?.trim() || ''
        ).join('\n\n') || '';
        
        return [
          "",
          field.character?.name === "I" ? getLanguages(checklanguage, "me") : field.character?.name || "",
          field.content?.replace(/<[^>]+>/g, '')?.replace(/&nbsp;/g, ' ')?.trim() || "",
          comments
        ];
      });
  
      // For Pain Picture section, check if we need to add a red line
      if (title === getLanguages(checklanguage, "painPicture") || title === "Pain Picture") {
        const redLineIndex = data.findIndex(field => field.isRedLine === 1 && field.painpictureCollapse === 1);
        if (redLineIndex !== -1) {
          rows.splice(redLineIndex + 1, 0, [
            "",
            "",
            `------${getLanguages(checklanguage, "redline")}------`,
            ""
          ]);
        }
      }
  
      elements.push(
        createTable(
          [
            getLanguages(checklanguage, "number") || "No.", 
            getLanguages(checklanguage, "protagonist") || "Protagonist", 
            getLanguages(checklanguage, "literalSpeechDirection") || "Literal speech/direction", 
            getLanguages(checklanguage, "comment") || "Comment"
          ], 
          rows
        )
      );
    }
  
    return elements;
  };
  
  // Helper function to create bridge table
  const createBridgeTable = (bridgeData, projectInfo) => {
    if (!Array.isArray(bridgeData)) {
      return [];
    }
  
    const elements = [];
  
    // Title
    elements.push(
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "bridge") || "Bridge", bold: true, size: 32 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1
      })
    );
  
    // Bridge Comment
    if (projectInfo?.comments?.bridge) {
      elements.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${getLanguages(checklanguage, "comment")}: `, bold: true, size: 24 }),
            new TextRun({ text: projectInfo.comments.bridge.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim(), size: 24 })
          ],
          spacing: { after: 200 }
        })
      );
    }
  
    const pastEntries = bridgeData.filter(item => item.type === "past");
    const presentEntries = bridgeData.filter(item => item.type === "present");
    const maxEntries = Math.max(pastEntries.length, presentEntries.length);
  
    if (maxEntries > 0) {
      // Create rows for all entries
      const rows = [];
      for (let i = 0; i < maxEntries; i++) {
        rows.push(
          new TableRow({
            children: [
              // Past column
              new TableCell({
                width: { size: 50, type: WidthType.PERCENTAGE },
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({ 
                        text: pastEntries[i]?.selectionText || "", 
                        size: 24 
                      })
                    ],
                    spacing: { before: 0, after: 0 }
                  })
                ],
                verticalAlign: VerticalAlign.TOP,
                margins: { top: 0, bottom: 0, left: 0, right: 0 }
              }),
              // Present column
              new TableCell({
                width: { size: 50, type: WidthType.PERCENTAGE },
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({ 
                        text: presentEntries[i]?.selectionText || "", 
                        size: 24 
                      })
                    ],
                    spacing: { before: 0, after: 0 }
                  })
                ],
                verticalAlign: VerticalAlign.TOP,
                margins: { top: 0, bottom: 0, left: 0, right: 0 }
              })
            ]
          })
        );
      }
  
      elements.push(
        new Table({
          width: { size: 100, type: WidthType.PERCENTAGE },
          columnWidths: [4500, 4500],
          tableLayout: TableLayoutType.FIXED,
          borders: {
            top: { style: BorderStyle.SINGLE, size: 1 },
            bottom: { style: BorderStyle.SINGLE, size: 1 },
            left: { style: BorderStyle.SINGLE, size: 1 },
            right: { style: BorderStyle.SINGLE, size: 1 },
            insideHorizontal: { style: BorderStyle.SINGLE, size: 1 },
            insideVertical: { style: BorderStyle.SINGLE, size: 1 },
          },
          rows: [
            // Header row
            new TableRow({
              children: [
                new TableCell({
                  width: { size: 50, type: WidthType.PERCENTAGE },
                  children: [
                    new Paragraph({
                      children: [new TextRun({ text: getLanguages(checklanguage, "past") || "Past", bold: true, size: 24 })]
                    })
                  ],
                  verticalAlign: VerticalAlign.TOP,
                  margins: { top: 0, bottom: 0, left: 0, right: 0 },
                  shading: { fill: "EFEFEF" }
                }),
                new TableCell({
                  width: { size: 50, type: WidthType.PERCENTAGE },
                  children: [
                    new Paragraph({
                      children: [new TextRun({ text: getLanguages(checklanguage, "present") || "Present", bold: true, size: 24 })]
                    })
                  ],
                  verticalAlign: VerticalAlign.TOP,
                  margins: { top: 0, bottom: 0, left: 0, right: 0 },
                  shading: { fill: "EFEFEF" }
                })
              ]
            }),
            // Data rows
            ...rows
          ]
        })
      );
    }
  
    return elements;
  };
  
  const generateWordDocument = async ({
    projectInfo,
    getCompanion,
    categoryListForm,
    dateInfo,
  }) => {
    console.log('========= DEBUG LOGGING START =========');
    console.log('projectInfo:', JSON.stringify(projectInfo, null, 2));
    console.log('getCompanion:', JSON.stringify(getCompanion, null, 2));
    console.log('categoryListForm:', JSON.stringify(categoryListForm, null, 2));
    console.log('dateInfo:', JSON.stringify(dateInfo, null, 2));
    console.log('========= DEBUG LOGGING END =========');
  
    console.log('Generating Word document with:');
    console.log('projectInfo:', projectInfo);
    console.log('getCompanion:', getCompanion);
    console.log('categoryListForm:', JSON.stringify(categoryListForm, null, 2));
    console.log('dateInfo:', dateInfo);
  
    // Get all protagonists from API
    let protagonistList = [];
    try {
      const response = await getSoulWritingCharacter({ projectId: projectInfo?.id });
      protagonistList = response?.data?.responseData?.characterList || [];
      console.log("Protagonist List Details:", protagonistList.map(p => ({
        name: p.name,
        title: p.title,
        gender: p.gender
      })));
    } catch (error) {
      console.error("Error fetching protagonists:", error);
    }
  
    // Create document sections
    const children = [
      // Title
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "soulwriting_title") || "The 12 Stages of Soulwriting", bold: true, size: 40 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1,
        alignment: AlignmentType.CENTER
      }),
  
      // Downloaded from
      new Paragraph({
        children: [
          new TextRun({ text: getLanguages(checklanguage, "downloadedFrom", ["portal"], ["portal.kuby.info"]).split("portal.kuby.info")[0], size: 24 }),
          new ExternalHyperlink({
            children: [
              new TextRun({
                text: "portal.kuby.info",
                size: 24,
                style: "Hyperlink"
              })
            ],
            link: "https://portal.kuby.info"
          }),
          new TextRun({ text: getLanguages(checklanguage, "downloadedFrom", ["portal"], ["portal.kuby.info"]).split("portal.kuby.info")[1], size: 24 })
        ],
        spacing: { before: 200, after: 400 },
        alignment: AlignmentType.CENTER
      }),
  
      // Station 1: Reason
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "reasonLabel") || "Reason", bold: true, size: 32 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1
      }),
      new Paragraph({
        children: [new TextRun({ text: projectInfo?.reason || "", size: 24 })],
        spacing: { after: 200 },
      }),
      // Kommentar für Reason
      ...(projectInfo?.comments?.reason ? [
        new Paragraph({
          children: [
            new TextRun({ text: `${getLanguages(checklanguage, "comment")}: `, bold: true, size: 24 }),
            new TextRun({ text: projectInfo.comments.reason.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim(), size: 24 })
          ],
          spacing: { after: 200 }
        })
      ] : []),
  
      // Station 2: Project
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "project") || "Project", bold: true, size: 32 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1
      }),
      new Paragraph({
        children: [new TextRun({ text: projectInfo?.title || "", size: 24 })],
        spacing: { after: 200 },
      }),
      // Kommentar für Project
      ...(projectInfo?.comments?.project ? [
        new Paragraph({
          children: [
            new TextRun({ text: `${getLanguages(checklanguage, "comment")}: `, bold: true, size: 24 }),
            new TextRun({ text: projectInfo.comments.project.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim(), size: 24 })
          ],
          spacing: { after: 200 }
        })
      ] : []),
  
      // Station 3: Protagonist
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "protagonist") || "Protagonist", bold: true, size: 32 })],
        spacing: { before: 400, after: 200 },
        heading: HeadingLevel.HEADING_1
      })
    ];
  
    // Add protagonist information from API response
    if (protagonistList.length > 0) {
      children.push(
        new Table({
          width: { size: 90, type: WidthType.PERCENTAGE },
          columnWidths: [2000, 7000],
          borders: {
            top: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
            bottom: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
            left: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
            right: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
            insideHorizontal: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
            insideVertical: { style: BorderStyle.SINGLE, size: 1, color: "808080" },
          },
          rows: protagonistList.map(char => new TableRow({
            children: [
              new TableCell({
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: char.name === "I" ? getLanguages(checklanguage, "me") : char.name || "",
                        size: 24,
                        bold: true,
                        color: BLUE_COLOR
                      })
                    ],
                    spacing: { before: 0, after: 0 }
                  })
                ],
                verticalAlign: VerticalAlign.TOP,
                margins: { top: 0, bottom: 0, left: 0, right: 0 }
              }),
              new TableCell({
                children: [
                  // Gender for all characters
                  new Paragraph({
                    children: [
                      new TextRun({ 
                        text: `${getLanguages(checklanguage, "gender")}: `, 
                        size: 24,
                        bold: true
                      }),
                      new TextRun({ 
                        text: getLanguages(checklanguage, char.title === "1" ? "male" : "female"), 
                        size: 24 
                      })
                    ],
                    spacing: { before: 0, after: 0 }
                  }),
                  // Age for all characters
                  new Paragraph({
                    children: [
                      new TextRun({ 
                        text: `${getLanguages(checklanguage, "age")}: `, 
                        size: 24,
                        bold: true
                      }),
                      new TextRun({ 
                        text: char.age || "", 
                        size: 24 
                      })
                    ],
                    spacing: { before: 0, after: 0 }
                  }),
                  // Native Language only for "Ich"
                  ...(char.name === 'Ich' ? [
                    new Paragraph({
                      children: [
                        new TextRun({ 
                          text: `${getLanguages(checklanguage, "nativeLang")}: `, 
                          size: 24,
                          bold: true
                        }),
                        new TextRun({ 
                          text: char.protogonistObject?.nativeLanguage || "", 
                          size: 24 
                        })
                      ],
                      spacing: { before: 0, after: 0 }
                    })
                  ] : []),
                  // Connection and Feeling only for other characters
                  ...(char.name !== 'Ich' ? [
                    ...(char.degreeOfKinship ? [
                      new Paragraph({
                        children: [
                          new TextRun({ 
                            text: `${getLanguages(checklanguage, "connection")}: `, 
                            size: 24,
                            bold: true
                          }),
                          new TextRun({ 
                            text: char.degreeOfKinship, 
                            size: 24 
                          })
                        ],
                        spacing: { before: 0, after: 0 }
                      })
                    ] : []),
                    ...(char.sympethetic ? [
                      new Paragraph({
                        children: [
                          new TextRun({ 
                            text: `${getLanguages(checklanguage, "feeling")}: `, 
                            size: 24,
                            bold: true
                          }),
                          new TextRun({ 
                            text: getLanguages(checklanguage, (() => {
                              switch(char.sympethetic?.toLowerCase()) {
                                case "very trusting":
                                case "very trustworthy":
                                  return "veryTrustWorthy";
                                case "sympathetic":
                                  return "sympathetic";
                                case "neutral":
                                  return "neutral";
                                case "unsympathetic":
                                  return "unsympathetic";
                                case "repulsive":
                                  return "repulsive";
                                case "fearful":
                                case "fear occupied":
                                  return "fearOccupied";
                                default:
                                  return char.sympethetic?.toLowerCase() || "";
                              }
                            })()), 
                            size: 24 
                          })
                        ],
                        spacing: { before: 0, after: 0 }
                      })
                    ] : [])
                  ] : [])
                ],
                verticalAlign: VerticalAlign.TOP,
                margins: { top: 0, bottom: 0, left: 0, right: 0 }
              })
            ]
          }))
        })
      );
  
      // Add spacing after the table
      children.push(
        new Paragraph({
          spacing: { before: 200, after: 200 }
        })
      );
    }
  
    // Station 4: Occasion
    children.push(...createStandardSection(
      getLanguages(checklanguage, "occasion") || "Occasion",
      categoryListForm?.categoryForms2
    ));
  
    // Station 5: Trigger
    const triggerDate = projectInfo?.projectMeta?.bridgeCharacter?.[2]?.trigger_date;
    const triggerDateContent = triggerDate && triggerDate.trim() ? [
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "dateOfProjectEnteringInLife") || "When did the project enter your life?", bold: true, size: 24, color: "666666" })],
        spacing: { before: 200, after: 40 }
      }),
      new Paragraph({
        children: [new TextRun({ text: triggerDate, size: 24 })],
        spacing: { after: 200 }
      })
    ] : null;
  
    children.push(...createStandardSection(
      getLanguages(checklanguage, "trigger") || "Trigger",
      categoryListForm?.categoryForms3,
      triggerDateContent
    ));
  
    // Station 6: Pain Picture
    children.push(...createStandardSection(
      getLanguages(checklanguage, "painPicture") || "Pain Picture",
      categoryListForm?.categoryForms4
    ));
  
    // Station 7: Bridge
    const bridgeData = projectInfo?.projectMeta?.bridgeCharacter?.[0]?.data?.selectedText || [];
    if (bridgeData.length > 0) {
      children.push(...createBridgeTable(bridgeData, projectInfo));
    }
  
    // Station 8: Rewrite
    children.push(...createStandardSection(
      getLanguages(checklanguage, "rewrite") || "Rewrite",
      categoryListForm?.categoryForms7
    ));
  
    // Station 9: Affirmation
    if (categoryListForm?.categoryForms8?.length > 0 && categoryListForm.categoryForms8[0]?.content?.trim()) {
      children.push(
        new Paragraph({
          children: [new TextRun({ text: getLanguages(checklanguage, "affirmation") || "Affirmation", bold: true, size: 32 })],
          spacing: { before: 400, after: 200 },
          heading: HeadingLevel.HEADING_1
        })
      );
  
      children.push(
        new Paragraph({
          children: [
            new TextRun({ 
              text: categoryListForm.categoryForms8[0].content.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim(), 
              size: 24 
            })
          ],
          spacing: { after: 300 },
        })
      );
    }
  
    // Station 10: Projection
    children.push(...createStandardSection(
      getLanguages(checklanguage, "projection") || "Projection",
      categoryListForm?.categoryForms9
    ));
  
    // Station 11: Realization
    const targetDate = projectInfo?.projectMeta?.bridgeCharacter?.[3]?.projection_date;
    const targetDateContent = targetDate && targetDate.trim() ? [
      new Paragraph({
        children: [new TextRun({ text: getLanguages(checklanguage, "targetDate") || "Target Date", bold: true, size: 24, color: "666666" })],
        spacing: { before: 200, after: 40 }
      }),
      new Paragraph({
        children: [
          new TextRun({ 
            text: moment(targetDate).format("ll"),
            size: 24 
          })
        ],
        spacing: { after: 200 }
      })
    ] : null;
  
    children.push(...createStandardSection(
      getLanguages(checklanguage, "realization") || "Realization",
      categoryListForm?.categoryForms10,
      targetDateContent
    ));
  
    const doc = new Document({
      numbering: {
        config: [
          {
            reference: "my-numbering",
            levels: [
              {
                level: 0,
                format: LevelFormat.DECIMAL,
                text: "%1.",
                alignment: ParagraphAlignment.START,
                style: {
                  paragraph: {
                    indent: { left: 0, hanging: 0 }
                  }
                }
              }
            ]
          }
        ]
      },
      sections: [{
        properties: {
          page: {
            margin: {
              top: convertInchesToTwip(1),
              right: convertInchesToTwip(1),
              bottom: convertInchesToTwip(1),
              left: convertInchesToTwip(1),
            },
            orientation: PageOrientation.PORTRAIT,
          },
        },
        children: children,
      }]
    });
  
    const blob = await Packer.toBlob(doc);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${projectInfo?.title || "soulwriting"}.docx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };
  
  export default generateWordDocument;
  