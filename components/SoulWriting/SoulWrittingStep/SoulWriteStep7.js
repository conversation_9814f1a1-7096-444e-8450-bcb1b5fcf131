import React, { useEffect, useState } from "react";
import BridgeList from "../../Common/Modals/BridgeList";
import { getLanguages, checklanguage } from "../../../constant";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import CommentCardFixedStages from "./CommentCardFixedStages";

const SoulWriteStep7 = ({
  projectInfo,
  setUnsavedChanges,
  setqueryData,
  bridgeArrayChar,
  removeBridge,
  memberstatusInfo,
  categoryListForm,
  setcategoryListForm,
  setmemberstatusInfo,
  categoryList,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[6]?.videoLink);
    }
    setDescription(categoryList[6]?.description);
  }, [categoryList]);
  return (
    <div
      className="d-flex soul-step-outer soul-step5 bridge-step-wrpr"
      id="bridge-7"
    >
      <div className="soul-step-comment-col create_Bridge">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="soul-step-inner step-left">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "bridge")}{" "}
            <span
              className="icon q-mark-icon playSvg"
              onClick={() => setvimeoOpen(1)}
            ></span>
          </h5>

          <BridgeList
            setUnsavedChanges={setUnsavedChanges}
            setqueryData={setqueryData}
            bridgeArrayChar={bridgeArrayChar}
            removeBridge={removeBridge}
            memberstatusInfo={memberstatusInfo}
            categoryListForm={categoryListForm}
            setcategoryListForm={setcategoryListForm}
            setmemberstatusInfo={setmemberstatusInfo}
          />
        </div>
      )}
        <div className="step-right">
          <CommentCardFixedStages
              projectInfo={projectInfo}
              stage="bridge"
            />
        </div>
        </div>
    </div>
  );
};

export default SoulWriteStep7;
