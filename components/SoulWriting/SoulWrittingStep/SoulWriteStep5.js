import React, { useEffect, useState } from "react";
import ReactDateTimePicker from "../DateTimePicker";
import CharcterComponent from "./CharcterComponent";
import CkEditorCompnonent from "./CkEditorCompnonent";
import { getLanguages, checklanguage } from "../../../constant";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import InputField from "../../FormFields/InputField";
import moment from "moment";
const SoulWriteStep5 = ({
  setUnsavedChanges,
  setshow,
  categoryListForm,
  projectId,
  character,
  setcharacterIndex,
  setcategoryListForm,
  setchooseStep,
  queryData,
  createBridgetextSelection,
  setDateInfo,
  dateInfo,
  memberstatusInfo,
  setSingleCharacter,
  categoryList,
  control,
  setqueryData,
  projectInfo,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);
  const [triggerDate, setTriggerDate] = useState(dateInfo?.trigger_date);
  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[4]?.videoLink);
    }
    setDescription(categoryList[4]?.description);
  }, [categoryList]);
  const handleTriggerDateChange = (event) => {
    setTriggerDate(event.target.value);
    setDateInfo({ trigger_date: event.target.value });
  };
  
  return (
    <div className="d-flex soul-step-outer soul-step4" id="trigger-5">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="soul-step-inner">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "trigger")}{" "}
            <span
              className="icon q-mark-icon playSvg"
              onClick={() => setvimeoOpen(1)}
            ></span>
          </h5>
          <label className="f-label">
            {getLanguages(checklanguage, "dateOfProjectEnteringInLife")}
          </label>
          <div className="d-flex align-center date-in-wrpr">
            <div className="form-in">
              <div className="form-in w-100 ">
                <div className="f-in w-100">
                  <input
                    name="trigger_date"
                    //control={control}
                    autoComplete="off"
                    placeholder={
                      localStorage.getItem("language") == "en"
                        ? moment(projectInfo?.createdAt).format("DD MMM, YYYY")
                        : moment(projectInfo?.createdAt).format("DD. MMM YYYY")
                    }
                    type="text"
                    className="form-control"
                    //inputClassName="f-in w-100"
                    value={triggerDate}
                    onChange={handleTriggerDateChange}
                  />
                </div>
              </div>
            </div>
          </div>
          {/* <ReactDateTimePicker
            name={"trigger_date"}
            setDateInfo={setDateInfo}
            dateInfo={dateInfo?.trigger_date}
            maxDate={new Date()}
            memberstatusInfo={memberstatusInfo}
          /> */}
          {/* <CharcterComponent
            setshow={setshow}
            character={character}
            queryData={queryData}
            memberstatusInfo={memberstatusInfo}
            setSingleCharacter={setSingleCharacter}
          /> */}
          <CkEditorCompnonent
            setUnsavedChanges={setUnsavedChanges}
            step={3}
            isBridge={queryData?.bridge && queryData?.type == "present"}
            bridgeType={queryData?.type}
            projectId={projectId}
            setshow={setshow}
            setcharacterIndex={setcharacterIndex}
            categoryListForm={categoryListForm}
            setcategoryListForm={setcategoryListForm}
            setchooseStep={setchooseStep}
            queryData={queryData}
            createBridgetextSelection={createBridgetextSelection}
            memberstatusInfo={memberstatusInfo}
            character={character}
            setqueryData={setqueryData}
          />
        </div>
      )}
    </div>
  );
};

export default SoulWriteStep5;
