import React, { forwardRef } from "react";
import ScriptViewComponent from "./ScriptViewComponent";
import { PDFExport } from "@progress/kendo-react-pdf";
const PdfComponent = forwardRef(
  ({
    pdfRef,
    pdfExportComponent,
    projectInfo,
    getCompanion,
    categoryListForm,
    type,
  }) => {
    return (
      <PDFExport
        ref={pdfExportComponent}
        fileName={`${projectInfo?.title}.pdf`}
        author="KendoReact Team"
        className="k-pdf-export"
      >
        <div className="save_pdf" ref={pdfRef}>
          <ScriptViewComponent
            projectInfo={projectInfo}
            getCompanion={getCompanion}
            categoryListForm={categoryListForm}
            type={type}
          />
        </div>
      </PDFExport>
    );
  }
);

PdfComponent.displayName = "PdfComponent";
export default PdfComponent;
