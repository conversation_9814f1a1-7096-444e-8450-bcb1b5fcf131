import moment from "moment";
import React, { useEffect, useState } from "react";
import {
  checklanguage,
  GENDER_TYPES,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  capitalizeAndReplace,
} from "../../../constant";
import _ from "lodash";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import {
  deleteCharacter,
  getSoulWritingCharacter,
} from "../../../redux/action/soul-writing";

const SoulWriteStep3 = ({
  setCharacter,
  categoryListForm,
  character,
  setshow,
  categoryList,
  setProtagonistObject,
  projectId,
  setIsOpenedFromSoulWriteStep3,
  setIsOpenAddProtagonistOpenFromS3,
  setcategoryListForm,
  protagonistList,
  setProtagonistList,
  setFethcCharForS3,
  fetchCharForS3,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);
  const [activeIndex, setActiveIndex] = useState(null);

  const [deletedProtagonist, setDeletedProtagonist] = useState(null);
  const [showUndoButton, setShowUndoButton] = useState(false);
  const [timer, setTimer] = useState(null);
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    let timer;
    if (showUndoButton && timer === undefined) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1100);
    }

    return () => {
      clearInterval(timer);
    };
  }, [showUndoButton]);
  useEffect(() => {
    setProtagonistList(protagonistList)
  }, [protagonistList])

  const searchCharacter = (characters, characterId) => {
    for (var i = 0; i < characters.length; i++) {
      if (characters[i].id == characterId) {
        return characters[i];
      }
    }
    return null;
  };
  const fetchProtagonistList = async () => {
    try {
      let response = await getSoulWritingCharacter({ projectId: projectId });
      setProtagonistList(response?.data?.responseData?.characterList);
      setCharacter(response?.data?.responseData?.characterList);
    } catch (error) {
      console.log(`Error from fetchProtagonistList`, error);
    }
  };
  const getProtagonistList = async (projectId) => {
    // setIsFetching(true);
    try {
      let response = await getSoulWritingCharacter({ projectId: projectId });
      setProtagonistList(response?.data?.responseData?.characterList);
      setCharacter(response?.data?.responseData?.characterList);
      let characterList = response?.data?.responseData?.characterList;
      let contentlist = JSON.parse(JSON.stringify(categoryListForm));
      let result = null;
      if (Object.keys(contentlist).length) {
        Object.keys(contentlist).forEach((obj, index) => {
          contentlist[obj].forEach((obj1, index1) => {
            result = searchCharacter(characterList, obj1.characterId);
            if (result) {
              contentlist[obj][index1] = Object.assign(
                contentlist[obj][index1],
                { character: result }
              );
            }
          });
        });
      }

      setcategoryListForm(contentlist);
    } catch (err) {
      console.log(`helloError from getProtagonist`, err);
    }
  };

  const deleteProtagonist = async (id) => {
    try {
      let response = await deleteCharacter({ id: id });
      getProtagonistList(projectId);
    } catch (error) {}
  };
  // const deleteProtagonist = (id) => {
  //   const protagonistToDelete = protagonistList.find((pro) => pro.id === id);
  //   // return;
  //   setDeletedProtagonist(protagonistToDelete);
  //   setShowUndoButton(true);
  //   setCountdown(5);

  //   const undoTimeout = setTimeout(() => {
  //     confirmDelete(id);
  //   }, 5000);
  //   setTimer(undoTimeout);
  // };

  const cancelDelete = () => {
    setShowUndoButton(false);
    clearTimeout(timer);
    setDeletedProtagonist(null);
    setTimer(null);
    setCountdown(5);
  };

  const confirmDelete = async (deletedProtagonistId) => {
    try {
      let res = await deleteCharacter({ id: deletedProtagonistId });
      if (res) {
        setShowUndoButton(false);
        setDeletedProtagonist(null);
        fetchProtagonistList();
      }
    } catch (error) {
      console.error("Error deleting protagonist:", error);
    }
  };

  useEffect(() => {
    setFethcCharForS3(!fetchCharForS3);
  }, []);
  useEffect(() => {
    getProtagonistList(projectId);
    setIsOpenedFromSoulWriteStep3(true);
    setIsOpenAddProtagonistOpenFromS3(true);
  }, [fetchCharForS3]);

  const toggleAccordian = (index) => {
    try {
      if (activeIndex == index) {
        setActiveIndex(null);
      } else {
        setActiveIndex(index);
      }
    } catch (error) { }
  };

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[2]?.videoLink);
    }
    setDescription(categoryList[2]?.description);
  }, [categoryList]);

  const openEditForm = (obj) => {
    try {
      setIsOpenedFromSoulWriteStep3(true);
      setProtagonistObject(obj);
      setshow(15);
    } catch (error) { }
  };


  return (
    <div className="d-flex soul-step-outer soul-step6" id="protagonist-3">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="step-left">
          <div className="d-flex align-center justify-between">
            <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
              {getLanguages(checklanguage, "protagonist")}{" "}
              <span
                className="icon q-mark-icon playSvg"
                onClick={() => setvimeoOpen(1)}
              ></span>
            </h5>

          </div>
          <div className="modal-top-body">
            <p className="p mb-10">
              {protagonistList[0]?.protogonistObject?.defaultCharacter}
              {getLanguages(checklanguage, "addProtogonistDesc")}
            </p>
            <button
              className="btn-accent footer-btn sm"
              onClick={(e) => {
                e.preventDefault();
                setshow(12);
                setIsOpenAddProtagonistOpenFromS3(true);
              }}
            >
              {getLanguages(checklanguage, "addNewProtagonist")}
            </button>
          </div>
          
              <div className="modal-listing-wrpr">
                {/* <div className="accordion">
                {protagonistList &&
                  [
                    protagonistList[0],
                    ...protagonistList
                      .slice(1)
                      .sort((a, b) => a.name.localeCompare(b.name)),
                  ].map((obj, index) => {
                    let colorStyle = {};
                    if (obj?.color?.backgroundColor) {
                      colorStyle = {
                        background: obj.color.backgroundColor,
                        color: obj.color.fontColor,
                      };
                    }

                    return (
                      <div className="accordion-item" key={index}>
                        <div className="accordion-item-header protagonist-list">
                          <div className="d-flex justify-between w-100 protagonist-card">
                            <div
                              className="d-flex align-center protagonist-details"
                              style={{ cursor: "pointer" }}
                              onClick={() => {
                                toggleAccordian(index);
                              }}
                            >
                              <div className="protagonist-img">
                                <h6
                                  className="h6 fw500 person-card"
                                  style={colorStyle}
                                >
                                  {obj?.Acronym == "I"
                                    ? getLanguages(checklanguage, "i")
                                    : obj?.Acronym}
                                </h6>
                              </div>
                              <div className="">
                                <p className="p fw500 text-grey-1">
                                  {obj?.name == "I"
                                    ? getLanguages(checklanguage, "i")
                                    : obj?.name}{" "}
                                  {index > 0 ? (
                                    <>
                                      (
                                      {obj?.title == 1
                                        ? getLanguages(checklanguage, "male")
                                        : getLanguages(checklanguage, "female")}
                                      )
                                    </>
                                  ) : (
                                    ""
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="d-flex align-center edit-status-wrpr">
                              <a
                                href="javascript:void(0)"
                                className={`${"status-badge"} ${
                                  activeIndex == index ? "gray" : "green"
                                }`}
                                onClick={() => {
                                  toggleAccordian(index);
                                }}
                              >
                                {activeIndex == index
                                  ? getLanguages(checklanguage, "hideInfo")
                                  : getLanguages(checklanguage, "moreInfo")}
                              </a>
                              <div className="d-flex align-center protogonist-action-btns">
                                <button
                                  href="javascript:void(0)"
                                  type="button"
                                  className="btn edit-btn sm"
                                  onClick={() => {
                                    openEditForm(obj);
                                  }}
                                >
                                  <span className="icon edit-icon-grey"></span>
                                </button>
                                {obj?.isDefault ? (
                                  <></>
                                ) : (
                                  <>
                                    {showUndoButton &&
                                    deletedProtagonist.id == obj.id ? (
                                      <button
                                        className="btn undo-btn sm"
                                        onClick={cancelDelete}
                                      >
                                        {getLanguages(checklanguage, "undo")}(
                                        {countdown})
                                      </button>
                                    ) : (
                                      <div
                                        className="btn delete-btn sm"
                                        onClick={(e) => {
                                          deleteProtagonist(e, obj.id);
                                        }}
                                      >
                                        <span className="icon delete-icon"></span>
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="accordion-item-body protagonist-body">
                          <div
                            className={`${"protogonist-content"} ${
                              activeIndex == index ? "active" : ""
                            }`}
                          >
                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "gender")}:{" "}
                              <span className="text-grey-6">
                                {obj?.title == 1
                                  ? getLanguages(checklanguage, "male")
                                  : getLanguages(checklanguage, "female")}
                              </span>{" "}
                            </p>

                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "age")}:{" "}
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.age)}
                              </span>{" "}
                            </p>
                            {obj?.protogonistObject?.nativeLanguage && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "nativeLang")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(
                                    obj?.protogonistObject?.nativeLanguage
                                  )}
                                </span>{" "}
                              </p>
                            )}

                            {obj?.degreeOfKinship && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "conxn")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(obj?.degreeOfKinship)}
                                </span>{" "}
                              </p>
                            )}

                            
                            {obj?.sympethetic && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "feeling")}:{" "}
                                <span className="text-grey-6">
                                  {getLanguages(
                                    checklanguage,
                                    `${obj.sympethetic}`
                                  )}
                                  
                                </span>{" "}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div> */}
                <div className="accordion">
                  {protagonistList?.length > 0 &&
                    [
                      protagonistList[0],
                      ...protagonistList
                        .slice(1)
                        .sort((a, b) => a.name.localeCompare(b.name)),
                    ].map((obj, index) => {
                      let colorStyle = {};

                      if (obj?.color?.backgroundColor) {
                        colorStyle = {
                          background: obj.color.backgroundColor,
                          color: obj.color.fontColor,
                        };
                      }

                      return (
                        <div className="accordion-item" key={index}>
                          <div className="accordion-item-header protagonist-list">
                            <div className="d-flex justify-between w-100 protagonist-card">
                              <div
                                className="d-flex align-center protagonist-details"
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                  toggleAccordian(index);
                                }}
                              >
                                <div className="protagonist-img">
                                  <h6
                                    className="h6 fw500 person-card"
                                    style={colorStyle}
                                  >
                                    {obj?.Acronym == "I"
                                      ? getLanguages(checklanguage, "i")
                                      : obj?.Acronym}
                                  </h6>
                                </div>
                                <div className="">
                                  <p className="p fw500 text-grey-1">
                                    {obj?.name == "I"
                                      ? getLanguages(checklanguage, "i")
                                      : obj?.name}
                                    {index > 0 ? (
                                      <>
                                        (
                                        {obj?.title == 1
                                          ? getLanguages(checklanguage, "male")
                                          : getLanguages(checklanguage, "female")}
                                        )
                                      </>
                                    ) : (
                                      ""
                                    )}
                                  </p>
                                </div>
                              </div>
                              <div className="d-flex align-center edit-status-wrpr">
                                <button
                                  type="button"
                                  className={`${"status-badge"} ${activeIndex == index ? "gray" : "green"
                                    }`}
                                  onClick={() => {
                                    toggleAccordian(index);
                                  }}
                                >
                                  {activeIndex == index
                                    ? getLanguages(checklanguage, "hideInfo")
                                    : getLanguages(checklanguage, "moreInfo")}
                                </button>
                                <div className="d-flex align-center protogonist-action-btns">
                                  <button
                                    type="button"
                                    className="btn edit-btn sm"
                                    onClick={() => {
                                      openEditForm(obj);
                                    }}
                                  >
                                    <span className="icon edit-icon-grey"></span>
                                  </button>
                                  {obj?.isDefault ? (
                                    <></>
                                  ) : (
                                    <button
                                      type="button"
                                      className="btn delete-btn sm"
                                      onClick={() => {
                                        deleteProtagonist(obj.id);
                                      }}
                                    >
                                      <span className="icon delete-icon"></span>
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="accordion-item-body protagonist-body">
                            <div
                              className={`${"protogonist-content"} ${activeIndex == index ? "active" : ""
                                }`}
                            >
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "gender")}:{" "}
                                <span className="text-grey-6">
                                  {obj?.title == 1
                                    ? getLanguages(checklanguage, "male")
                                    : getLanguages(checklanguage, "female")}
                                </span>{" "}
                              </p>
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(checklanguage, "age")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(obj?.age)}
                                </span>{" "}
                              </p>

                              {obj?.protogonistObject?.nativeLanguage && (
                                <p className="text-grey-1 protogonist-text">
                                  {getLanguages(checklanguage, "nativeLang")}:{" "}
                                  <span className="text-grey-6">
                                    {capitalizeAndReplace(
                                      obj?.protogonistObject?.nativeLanguage
                                    )}
                                  </span>{" "}
                                </p>
                              )}
                              {obj?.degreeOfKinship && (
                                <p className="text-grey-1 protogonist-text">
                                  {getLanguages(checklanguage, "conxn")}:{" "}
                                  <span className="text-grey-6">
                                    {capitalizeAndReplace(obj?.degreeOfKinship)}
                                  </span>{" "}
                                </p>
                              )}

                              {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "lifeStatus")}:
                              <span className="text-grey-6">
                                {obj?.lifeStatus
                                  ? "Alive"
                                  : "Passed away (" + obj?.passAwayDate + ")"}
                              </span>{" "}
                            </p> */}
                              {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "distance")}:
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.distance)}
                              </span>{" "}
                            </p> */}
                              {/* <p className="text-grey-1 protogonist-text">
                              {getLanguages(checklanguage, "contact")}:
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.contact)}
                              </span>{" "}
                            </p> */}
                              {obj?.sympethetic && (
                                <p className="text-grey-1 protogonist-text">
                                  {getLanguages(checklanguage, "feeling")}:{" "}
                                  <span className="text-grey-6">
                                    {getLanguages(
                                      checklanguage,
                                      `${obj.sympethetic}`
                                    )}
                                    {/* {capitalizeAndReplace(obj?.sympethetic)} */}
                                  </span>
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            
        </div>
      )}
    </div>
  );
};

export default SoulWriteStep3;
