import React, { useEffect, useState } from "react";
import CharcterComponent from "./CharcterComponent";
import ReactDateTimePicker from "../DateTimePicker";
import CkEditorCompnonent from "./CkEditorCompnonent";
import { getLanguages, checklanguage } from "../../../constant";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";

const SoulWriteStep12 = ({
  setqueryData,
  queryData,
  setUnsavedChanges,
  setshow,
  categoryListForm,
  projectId,
  character,
  setcharacterIndex,
  setcategoryListForm,
  setchooseStep,
  setDateInfo,
  dateInfo,
  memberstatusInfo,
  setSingleCharacter,
  categoryList,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[11]?.videoLink);
    }
    setDescription(categoryList[11]?.description);
  }, [categoryList]);
  return (
    <div className="d-flex soul-step-outer soul-step12" id="implementation-12">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="soul-step-inner">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "realization")}{" "}
            <span
              className="icon q-mark-icon playSvg"
              onClick={() => setvimeoOpen(1)}
            ></span>
          </h5>
          <p className="p text-grey-6 fw500 d-flex align-center step-subtitle">
            {getLanguages(checklanguage, "targetDate")}{" "}
            {/* <span className="icon q-mark-icon"></span> */}
          </p>
          <ReactDateTimePicker
            name={"projection_date"}
            setDateInfo={setDateInfo}
            dateInfo={dateInfo?.projection_date}
            // minDate={new Date()}
            // minDate={new Date(new Date().setMonth(new Date().getMonth() + 12))}
            memberstatusInfo={memberstatusInfo}
            realization="true"
          />
          {/* <CharcterComponent
            setshow={setshow}
            character={character}
            memberstatusInfo={memberstatusInfo}
            setSingleCharacter={setSingleCharacter}
          /> */}
          <CkEditorCompnonent
            setUnsavedChanges={setUnsavedChanges}
            step={10}
            projectId={projectId}
            setshow={setshow}
            setcharacterIndex={setcharacterIndex}
            categoryListForm={categoryListForm}
            setcategoryListForm={setcategoryListForm}
            setchooseStep={setchooseStep}
            memberstatusInfo={memberstatusInfo}
            character={character}
          />
        </div>
      )}
    </div>
  );
};

export default SoulWriteStep12;
