import React, { useEffect, useState } from "react";
import { postsoulwritingComment } from "../../../redux/action/soul-writing";
import dynamic from "next/dynamic";
import { useSelector } from "react-redux";



const CommentCardFixedStages = ({ projectInfo, stage }) => {
  const [commentText, setcommentText] = useState(
    projectInfo?.comments?.[stage] || ""
  );

  useEffect(() => {
    setcommentText(projectInfo?.comments?.[stage]);
  }, [projectInfo])

  return (
    <div className="comment-card-block">
      {commentText ? (
        <div className="comment-card">
          <div className={`comment-card-inner companion_message`}>
            <p className="p fw500 text-grey-5 comment-name">
              {projectInfo?.companion?.firstName}{" "}
              {projectInfo?.companion?.lastName} :
            </p>
            <p
              className="p text-dark-grey bg-white comment-text"
              dangerouslySetInnerHTML={{
                __html: projectInfo?.comments?.[stage],
              }}
            />
          </div>
        </div>
      )
        :
        <></>
      }


    </div>
  );
};

export default CommentCardFixedStages;
