import React, { useEffect, useState } from "react";
import moment from "moment";
import SoulWritingTableData from "./SoulWritingTableData";
import { getSoulWritingCharacter } from "../../../redux/action/soul-writing";
import { useRouter } from "next/router";
import { getLanguages,checklanguage, capitalizeAndReplace } from "../../../constant";
import ReactDateTimePicker from "../DateTimePicker"
const ScriptViewComponent = ({
  view,
  projectInfo,
  getCompanion,
  categoryListForm,
  dateInfo,
  type,
  language
}) => {
  const router = useRouter();
  const [protagonistList, setProtagonistList] = useState([]);
  const [bridgeArrayChar, setbridgeArrayChar] = useState(null);
  let countPast = 0;
  let countPresent = 0;
  const { query } = router;
  useEffect(() => {
    if (projectInfo?.id) {
      fetchProtagonistList();
      setbridgeArrayChar(
        projectInfo?.projectMeta?.bridgeCharacter?.[0]?.data?.selectedText || []
      );
    }

  }, [projectInfo])
  const fetchProtagonistList = async () => {
    try {
      let response = await getSoulWritingCharacter({ projectId: projectInfo?.id });
      setProtagonistList(response?.data?.responseData?.characterList);
      //setCharacter(response?.data?.responseData?.characterList);
    } catch (error) {
      console.log(`Error from fetchProtagonistList`, error);
    }
  };
  return (
    <div className="script-view-wrpr">
      {view == "pdf" ? (
        <div className="script-inner-wrpr">
          {getCompanion?.id && (
            <div className="d-flex align-center profile-card">
              <div className="comp-img">
                <img
                  src={
                    getCompanion?.profilePhotoUrl
                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${getCompanion?.profilePhotoUrl}`
                      : "images/user-image.jpg"
                  }
                  alt="companion image"
                  className="cover-img"
                />
              </div>
              <div className="text-left companion-details">
                <div className="d-flex align-center comp-name-wrpr">
                  <h6 className="h6 fw600 comp-name">
                    {getCompanion?.firstName} {getCompanion?.lastName}
                  </h6>
                </div>
                {getCompanion?.gender === 1
                  ? getLanguages(language, "male")
                  : getLanguages(language, "female")}{" "}
                ·{" "}
                <span className="text-grey-1">
                  {getLanguages(language, "professional")}
                </span>

              </div>
            </div>
          )}
        </div>
      ) : (
        <>
          {Object.keys(getCompanion)?.length > 0 && (
            <div className="script-inner-wrpr">
              <div className="d-flex align-center profile-card">
                <div className="comp-img">
                  <img
                    src={
                      getCompanion?.UserProfile?.attachment?.path
                        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${getCompanion?.UserProfile?.attachment?.path}`
                        : "images/user-image.jpg"
                    }
                    alt="companion image"
                    className="cover-img"
                  />
                </div>
                <div className="text-left companion-details">
                  <div className="d-flex align-center comp-name-wrpr">
                    <h6 className="h6 fw600 comp-name">
                      {getCompanion?.UserProfile?.firstName}{" "}
                      {getCompanion?.UserProfile?.lastName}
                    </h6>
                  </div>
                  {getCompanion?.UserProfile?.gender === 1
                    ? getLanguages(language, "male")
                    : getLanguages(language, "female")}{" "}
                  ·{" "}
                  <span className="text-grey-1">
                    {getLanguages(language, "professional")}
                  </span>
                  <div className="f-start rating-stars">

                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      <div className={`script-inner-wrpr ${type == 1 && "pdf_font"}`}>
        <h5 className="h5 text-grey-3 fw500">
          Date:{" "}
          <span className="h6 fw400 text-grey-5">
            {moment(projectInfo?.createdAt)?.format("MMMM D, YYYY")}
          </span>
        </h5>
      </div>
      <div className={`script-inner-wrpr ${type == 1 && "pdf_font"}`}>
        <h5 className={"h5 text-grey-3 fw500"}>
          {getLanguages(language, "reasonLabel")}
        </h5>
        <h6 className="h6 fw400 text-grey-5">{projectInfo?.reason}</h6>
      </div>
      <div className={`script-inner-wrpr ${type == 1 && "pdf_font"}`}>
        <div className="d-flex">
          <h5 className="h5 text-grey-3 fw500">
            {getLanguages(language, "project")}
          </h5>
          {!query?.version ? (
            <> </>
          ) : (
            <h5 className={"h5 version_cls text-grey-5 fw500"}>
              (Version {query?.version})
            </h5>
          )}
        </div>
        <div className="d-flex">
          <h6 className="h6 fw400 text-grey-5">{projectInfo?.title}</h6>
        </div>
      </div>
      <div className="script-inner-wrpr">
        <div className="d-flex align-center justify-between">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(language, "protagonist")}{" "}
            
          </h5>

        </div>
        <div className="accordion-item">
          
            <div className="modal-listing-wrpr">
              <div className="accordion">
                {protagonistList &&
                  [
                    protagonistList[0],
                    ...protagonistList
                      .slice(1)
                      .sort((a, b) => a.name.localeCompare(b.name)),
                  ].map((obj, index) => {
                    let colorStyle = {};
                    if (obj?.color?.backgroundColor) {
                      colorStyle = {
                        background: obj.color.backgroundColor,
                        color: obj.color.fontColor,
                      };
                    }
                    return (
                      <div className="accordion-item" key={index}>
                        <div className="accordion-item-header protagonist-list">
                          <div className="d-flex justify-between w-100 protagonist-card" style={{background: "inherit"}}>
                            <div
                              className="d-flex align-center protagonist-details"
                              style={{ cursor: "pointer" }}
                            >
                              <div className="protagonist-img">
                                <h6
                                  className="h6 fw500 person-card"
                                  style={colorStyle}
                                >
                                  {obj?.Acronym == "I"
                                    ? getLanguages(language, "i")
                                    : obj?.Acronym}
                                </h6>
                              </div>
                              <div className="">
                                <p className="p fw500 text-grey-1">
                                  {obj?.name == "I"
                                    ? getLanguages(language, "i")
                                    : obj?.name}{" "}
                                  {index > 0 ? (
                                    <>
                                      (
                                      {obj?.title == 1
                                        ? getLanguages(language, "male")
                                        : getLanguages(language, "female")}
                                      )
                                    </>
                                  ) : (
                                    ""
                                  )}
                                </p>
                              </div>
                            </div>

                          </div>
                        </div>
                        <div className="accordion-item-body protagonist-body">
                          <div
                            className={`${"protogonist-content"} ${index == index ? "active" : ""
                              }`}
                          >
                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(language, "gender")}:{" "}
                              <span className="text-grey-6">
                                {obj?.title == 1
                                  ? getLanguages(language, "male")
                                  : getLanguages(language, "female")}
                              </span>{" "}
                            </p>

                            <p className="text-grey-1 protogonist-text">
                              {getLanguages(language, "age")}:{" "}
                              <span className="text-grey-6">
                                {capitalizeAndReplace(obj?.age)}
                              </span>{" "}
                            </p>
                            {obj?.protogonistObject?.nativeLanguage && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(language, "nativeLang")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(
                                    obj?.protogonistObject?.nativeLanguage
                                  )}
                                </span>{" "}
                              </p>
                            )}

                            {obj?.degreeOfKinship && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(language, "conxn")}:{" "}
                                <span className="text-grey-6">
                                  {capitalizeAndReplace(obj?.degreeOfKinship)}
                                </span>{" "}
                              </p>
                            )}


                            {obj?.sympethetic && (
                              <p className="text-grey-1 protogonist-text">
                                {getLanguages(language, "feeling")}:{" "}
                                <span className="text-grey-6">
                                  {getLanguages(
                                    language,
                                    `${obj.sympethetic}`
                                  )}
                                  {/* {capitalizeAndReplace(obj?.sympethetic)} */}
                                </span>{" "}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          

        </div>
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500">
          {getLanguages(language, "occasion")}
        </h5>
        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={2}
          type={type}
          language={language}
        />
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500 d-flex align-center">
          {getLanguages(language, "trigger")}

        </h5>

        {dateInfo?.trigger_date != null && (
          <>
            <p className="p text-grey-6 d-flex align-center step-subtitle">
              {getLanguages(language, "dateOfProjectEntering")}{" "}

            </p>
            <div className="d-flex align-center view-date-wrpr">
              <span className="icon cal-icon"></span>
              <p className="font-inter fw400 view-date">
                {moment(dateInfo?.trigger_date).format("ll")}
              </p>
            </div>
          </>
        )}

        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={3}
          type={type}
          language={language}
        />
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500">
          {getLanguages(language, "painPicture")}
        </h5>
        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={4}
          type={type}
          language={language}
        />
      </div>

      <div className="script-inner-wrpr">
        <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
          {getLanguages(language, "bridge")}{" "}

        </h5>
        <div className="d-flex bridge-table-wrpr">
          <div className="bridge-block bridge-past-block">
            <div className="bridgeTime">
              <h5 className="fw500 bridgePast ">
                {getLanguages(language, "past")}
              </h5>
              <h6 className="text-grey-6 bridge-subtitle ">
                {getLanguages(language, "pastFromPainPic")}
              </h6>
            </div>
            <ul className="bridge-list">
              {bridgeArrayChar?.length > 0 &&
                bridgeArrayChar.map((obj, index) => {
                  if (obj.type == "present") {
                    return <></>;
                  }
                  countPast++;
                  return (
                    <li
                      key={index}
                      className="d-flex align-center bridge-list-item past"
                    >
                      <span className="bridge-count">{countPast}</span>
                      <div
                        className={`${obj.selectionText
                          ? "d-flex align-center bridge-card"
                          : "new_bridge"
                          } `}
                      >


                        {obj.selectionText ? (
                          <p
                            title={obj.selectionText}
                            className="p fw500 text-grey-5 bridge-content"
                          >
                            {obj.selectionText}
                          </p>
                        ) : (
                          <></>
                        )}


                      </div>
                    </li>
                  );
                })}

            </ul>
          </div>

          <div className="bridge-block bridge-present-block">
            <div className="bridgeTime">
              <h5 className="bridgePersent fw500">
                {getLanguages(language, "present")}
              </h5>
              <h6 className="text-grey-6 bridge-subtitle ">
                {getLanguages(language, "presentFromProjectOccasion")}
              </h6>
            </div>

            <ul className="bridge-list">
              {bridgeArrayChar?.length > 0 &&
                bridgeArrayChar.map((obj, index) => {
                  if (obj.type == "past") {
                    return <></>;
                  }
                  countPresent++;
                  return (
                    <li
                      key={index}
                      className="d-flex align-center bridge-list-item present"
                    >
                      <div
                        className={`${obj.selectionText
                          ? "d-flex align-center bridge-card"
                          : "new_bridge"
                          } `}
                      >
                        {obj.selectionText && (
                          <></>
                        )}
                        {obj.selectionText ? (
                          <p
                            title={obj.selectionText}
                            className="p fw500 text-grey-5 bridge-content"

                          >
                            {obj.selectionText}
                          </p>
                        ) : (
                          <></>
                        )}


                      </div>

                    </li>
                  );
                })}

            </ul>
          </div>
        </div>
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500 d-flex align-center">
          {getLanguages(language, "rewrite")}{" "}
        </h5>
        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={7}
          type={type}
          language={language}
        />
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500 d-flex align-center">
          {getLanguages(language, "affirmation")}{" "}
        </h5>
        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={8}
          type={type}
          language={language}
        />
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500 d-flex align-center">
          {getLanguages(language, "projection")}{" "}
        </h5>
        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={9}
          type={type}
          language={language}
        />
      </div>
      <div className="script-inner-wrpr">
        <h5 className="h5 text-grey-3 fw500 d-flex align-center">
        {getLanguages(language, "realization")}{" "}
        </h5>
        <p className="p text-grey-6 fw500 d-flex align-center step-subtitle">
            {getLanguages(language, "targetDate")}{" "}
            {/* <span className="icon q-mark-icon"></span> */}
          </p>
          <ReactDateTimePicker
            name={"projection_date"}
            disableCalendar={true}
            dateInfo={dateInfo?.projection_date}
            // minDate={new Date()}
            // minDate={new Date(new Date().setMonth(new Date().getMonth() + 12))}
            
            realization="true"
          />

        <SoulWritingTableData
          categoryListForm={categoryListForm}
          step={10}
          type={type}
          language={language}
        />
      </div>
    </div>
  );
};

export default ScriptViewComponent;
