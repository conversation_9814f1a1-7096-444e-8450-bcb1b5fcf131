import React, { useEffect, useState } from "react";
import _ from "lodash";
import { getLanguages, checklanguage } from "../../../constant";
import { getSoulWritingCharacter } from "../../../redux/action/soul-writing";
const ChooseCharacterPopup = ({
  projectId,
  showCharacterPopup,
  setShowCharacterPopup,
  setshow,
  onHide,
  show,
  character,
  step,
  characterIndex,
  setcategoryListForm,
  setProtagonistList,
  protagonistList,
}) => {
  const [isFetching, setIsFetching] = useState();
  // const [characterList, setCharacterList] = useState(character);
  useEffect(() => {
    getProtagonistList({ projectId: projectId });
    console.log("character", character);
  }, []);

  const getProtagonistList = async (data) => {
    setIsFetching(true);
    try {
      let response = await getSoulWritingCharacter(data);
      console.log("ffffff77", response.data.responseData);
      if (response?.data?.responseData?.characterList) {
        setProtagonistList(response?.data?.responseData?.characterList);
      }
    } catch (err) {
      setIsFetching(false);
    }
  };
  return (
    <div
      className={`modal choose_character ${show ? "show" : ""}`}
      id="scheduleModal"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "protagonist")}
            </h4>
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={() => onHide()}
            >
              <span className="icon close-icon"></span>
            </a>
          </div>
          <div className="modal-body">
            <ul className="noti-list order-list">
              {protagonistList?.map((item, i) => {
                let colorStyle = {};
                if (item?.color?.backgroundColor) {
                  colorStyle = {
                    background: item.color.backgroundColor,
                    color: item.color.fontColor,
                  };
                }
                return (
                  <li
                    onClick={() => {
                      setcategoryListForm((prev) => {
                        let formValues = { ...prev };
                        formValues[`categoryForms${step}`][characterIndex][
                          "characterId"
                        ] = item?.id;
                        formValues[`categoryForms${step}`][characterIndex][
                          "character"
                        ] = item;
                        return formValues;
                      });
                      onHide();
                    }}
                    className="noti-item character_item"
                    key={i}
                  >
                    <div className="order_wrpr">
                      <div className="product_desc">
                        <div className="person-cards d-flex">
                          <h6
                            className="h6 fw500 person-card"
                            style={colorStyle}
                          >
                            {item?.Acronym
                              ? item?.Acronym?.substr(0, 3)?.toUpperCase()
                              : item?.name?.split(" ")?.length > 1
                              ? item?.name
                                  ?.split(" ")?.[0]
                                  ?.charAt(0)
                                  ?.toUpperCase() +
                                item?.name
                                  ?.split(" ")?.[1]
                                  ?.charAt(0)
                                  ?.toUpperCase()
                              : item?.name
                                  ?.split(" ")?.[0]
                                  ?.charAt(0)
                                  ?.toUpperCase()}
                          </h6>
                          <p className="fw500">{item?.name}</p>
                        </div>
                      </div>
                      <div className="price_wr">
                        {/* <span className="text-grey-1 fw500">
                          {item?.lifeStatus == 0 ? (
                            <p className="text-red">
                              {getLanguages(checklanguage, "passAway")}
                            </p>
                          ) : (
                            <p
                              style={{ color: "#094E26" }}
                              className="text-green"
                            >
                              {getLanguages(checklanguage, "alive")}
                            </p>
                          )}
                        </span> */}
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
          <div className="modal-footer">
            <div className="d-flex align-center modal-btn-wrpr">
              <button
                className="btn-accent sm "
                onClick={(e) => {
                  e.preventDefault();
                  setShowCharacterPopup(true);
                  setshow(12);
                }}
                type="button"
              >
                {getLanguages(checklanguage, "addNewProtagonist")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChooseCharacterPopup;
