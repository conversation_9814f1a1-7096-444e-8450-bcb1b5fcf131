import React, { useState, useEffect } from "react";
import SoulWriteStep1 from "./SoulWriteStep1";
import SoulWriteStep2 from "./SoulWriteStep2";
import SoulWriteStep3 from "./SoulWriteStep3";
import SoulWriteStep4 from "./SoulWriteStep4";
import SoulWriteStep5 from "./SoulWriteStep5";
import SoulWriteStep6 from "./SoulWriteStep6";
import SoulWriteStep7 from "./SoulWriteStep7";
import SoulWriteStep9 from "./SoulWriteStep9";
import SoulWriteStep10 from "./SoulWriteStep10";
import SoulWriteStep11 from "./SoulWriteStep11";
import SoulWriteStep12 from "./SoulWriteStep12";
import BridgeListModal from "../../Common/Modals/BridgeListModal";
import { useRouter } from "next/router";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import ProtagonistQuesModal from "../../Common/Modals/ProtagonistQuesModal";

const SoulWritingStep = ({
  submit,
  setProtagonistObject,
  unsavedChanges,
  setUnsavedChanges,
  show,
  showTermsPopup,
  setValue,
  control,
  setshow,
  getComPanionList,
  componentWrapper,
  companionId,
  getCompanion,
  defaultValues,
  getValues,
  projectId,
  character,
  setCharacter,
  setcharacterIndex,
  categoryListForm,
  setcategoryListForm,
  setchooseStep,
  queryData,
  setbridgeArrayChar,
  bridgeArrayChar,
  setselectedTextWrraperCategory1,
  selectedTextWrraperCategory1,
  setqueryData,
  setDateInfo,
  dateInfo,
  projectInfo,
  memberstatusInfo,
  setmemberstatusInfo,
  setSingleCharacter,
  setError,
  clearErrors,
  projectList,
  setProjectList,
  categoryList,
  videoLink,
  descriptions,
  currentCategoryId,
  SingleCharacter,
  nextClick,
  setNextClick,
  openPopup,
  setIsOpenedFromSoulWriteStep3,
  isModalShow,
  protagonistList,
  setProtagonistList,
  setIsOpenAddProtagonistOpenFromS3,
  setFethcCharForS3,
  fetchCharForS3,
  lastOpenedStage,
  setLastOpenedStage,
  setIsStep3VideoOpen,
  setSubmitClicked,
  checkValidation,
  setShowCompanionCost,
  setStep4Video,
  step4Video,
}) => {
  const [showVideo, setShowVideo] = useState(false);
  const router = useRouter();
  const { query, asPath } = router;

  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);
  const [openModel, setOpenModel] = useState(false);
  const [showProtagonistQues, setShowProtagonistQues] = useState(false);
  const [category, setCategory] = useState();
  // const [step4Video, setStep4Video] = useState(false);

  useEffect(() => {
    if (Object.keys(query).length != 0) {
      setShowVideo(true);
    }
  }, [query]);

  useEffect(() => {

    if (asPath.includes("reason")) {
      setVimeoSrc(categoryList[0]?.videoLink);
      setDescription(categoryList[0]?.description);
      setStep4Video(false);
      setvimeoOpen(1);
      setOpenModel(false);
      setCategory("#reason-1");
    }
    if (asPath.includes("projectId")) {
      setVimeoSrc(categoryList[2]?.videoLink);
      setDescription(categoryList[2]?.description);
      setStep4Video(false);
      setvimeoOpen(1);
      setOpenModel(true);
      setIsStep3VideoOpen(true);
    }
    // if (asPath.includes("projectId") && asPath.includes("projectId")) {
    //   sessionStorage.setItem("currentStage3", true);
    // }
    if (asPath.includes("occasion-4")) {
      setVimeoSrc(categoryList[3]?.videoLink);
      setDescription(categoryList[3]?.description);
      setvimeoOpen(1);
      setStep4Video(true);
      setOpenModel(false);
      setCategory("#occasion-4");
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("trigger-5")) {
      setVimeoSrc(categoryList[4]?.videoLink);
      setDescription(categoryList[4]?.description);
      setStep4Video(true);
      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#trigger-5`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("painpicture-6")) {
      setVimeoSrc(categoryList[5]?.videoLink);
      setDescription(categoryList[5]?.description);
      setStep4Video(true);

      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#painpicture-6`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("#bridge-7")) {
      setVimeoSrc(categoryList[6]?.videoLink);
      setDescription(categoryList[6]?.description);
      setStep4Video(true);

      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#bridge-7`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("redline-8")) {
      setVimeoSrc(categoryList[7]?.videoLink);
      setDescription(categoryList[7]?.description);
      setStep4Video(true);

      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#painpicture-6`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("rewrite")) {
      setVimeoSrc(categoryList[8]?.videoLink);
      setDescription(categoryList[8]?.description);
      setStep4Video(true);

      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#rewrite-9`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("affirmation-10")) {
      setVimeoSrc(categoryList[9]?.videoLink);
      setDescription(categoryList[9]?.description);
      setvimeoOpen(1);
      setStep4Video(true);

      setOpenModel(false);
      setCategory(`#affirmation-10`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("projection-11")) {
      setVimeoSrc(categoryList[10]?.videoLink);
      setDescription(categoryList[10]?.description);
      setvimeoOpen(1);
      setOpenModel(false);
      setStep4Video(true);

      setCategory(`#projection-11`);
      sessionStorage.setItem("currentStage3", false);
    }
    if (asPath.includes("implementation-12")) {
      setVimeoSrc(categoryList[11]?.videoLink);
      setDescription(categoryList[11]?.description);
      setStep4Video(true);

      setvimeoOpen(1);
      setOpenModel(false);
      setCategory(`#implementation-12`);
      sessionStorage.setItem("currentStage3", false);
    }
  }, [asPath]);

  useEffect(() => { }, [queryData?.redline]);
  const dynamicRenderedComponents = () => {
    return componentWrapper
      ?.sort((a, b) => a.categoryId - b.categoryId)
      .map(function (element, index) {
        let visibleStage =
          parseInt(element?.categoryId) <= lastOpenedStage
            ? parseInt(element?.categoryId)
            : null;
        switch (visibleStage) {
          case 1:
            return (
              <>
                <SoulWriteStep1
                  setShowCompanionCost={setShowCompanionCost}
                  submit={submit}
                  checkValidation={checkValidation}
                  categoryList={categoryList}
                  setUnsavedChanges={setUnsavedChanges}
                  showTermsPopup={showTermsPopup}
                  projectInfo={projectInfo}
                  control={control}
                  setshow={setshow}
                  getComPanionList={getComPanionList}
                  companionId={companionId}
                  getCompanion={getCompanion}
                  defaultValues={defaultValues}
                  getValues={getValues}
                  projectId={projectId}
                  character={character}
                  setcharacterIndex={setcharacterIndex}
                  categoryListForm={categoryListForm}
                  setcategoryListForm={setcategoryListForm}
                  setchooseStep={setchooseStep}
                  queryData={queryData}
                  createBridgetextSelection={createBridgetextSelection}
                  bridgeArrayChar={bridgeArrayChar}
                  selectedTextWrraperCategory1={selectedTextWrraperCategory1}
                  removeBridge={removeBridge}
                  setError={setError}
                  clearErrors={clearErrors}
                  memberstatusInfo={memberstatusInfo}
                  setNextClick={setNextClick}
                />
                <SoulWriteStep2
                  setUnsavedChanges={setUnsavedChanges}
                  setValue={setValue}
                  projectInfo={projectInfo}
                  control={control}
                  setshow={setshow}
                  getComPanionList={getComPanionList}
                  companionId={companionId}
                  getCompanion={getCompanion}
                  defaultValues={defaultValues}
                  getValues={getValues}
                  projectId={projectId}
                  character={character}
                  setcharacterIndex={setcharacterIndex}
                  categoryListForm={categoryListForm}
                  setcategoryListForm={setcategoryListForm}
                  setchooseStep={setchooseStep}
                  queryData={queryData}
                  createBridgetextSelection={createBridgetextSelection}
                  selectedTextWrraperCategory1={selectedTextWrraperCategory1}
                  setqueryData={setqueryData}
                  projectList={projectList}
                  setProjectList={setProjectList}
                  memberstatusInfo={memberstatusInfo}
                  categoryList={categoryList}
                  setNextClick={setNextClick}
                />
              </>
            );
          // case 2:
          //     return <SoulWriteStep2 projectInfo={projectInfo} control={control} setshow={setshow} getComPanionList={getComPanionList} companionId={companionId} getCompanion={getCompanion} defaultValues={defaultValues} getValues={getValues} projectId={projectId} character={character} setcharacterIndex={setcharacterIndex} categoryListForm={categoryListForm} setcategoryListForm={setcategoryListForm} setchooseStep={setchooseStep} queryData={queryData} createBridgetextSelection={createBridgetextSelection} selectedTextWrraperCategory1={selectedTextWrraperCategory1} setqueryData={setqueryData}  projectList = {projectList} memberstatusInfo={memberstatusInfo}  />

          case 3:
            return (
              <SoulWriteStep3
                protagonistList={protagonistList}
                setProtagonistList={setProtagonistList}
                setIsOpenedFromSoulWriteStep3={setIsOpenedFromSoulWriteStep3}
                setProtagonistObject={setProtagonistObject}
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                show={show}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setCharacter={setCharacter}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                queryData={queryData}
                setSingleCharacter={setSingleCharacter}
                memberstatusInfo={memberstatusInfo}
                SingleCharacter={SingleCharacter}
                setIsOpenAddProtagonistOpenFromS3={
                  setIsOpenAddProtagonistOpenFromS3
                }
                setFethcCharForS3={setFethcCharForS3}
                fetchCharForS3={fetchCharForS3}
              />
            );

          case 4:
            return (
              <SoulWriteStep4
                control={control}
                setValue={setValue}
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                queryData={queryData}
                createBridgetextSelection={createBridgetextSelection}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                setqueryData={setqueryData}
              />
            );

          case 5:
            return (
              <SoulWriteStep5
                projectInfo={projectInfo}
                control={control}
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                queryData={queryData}
                createBridgetextSelection={createBridgetextSelection}
                setDateInfo={setDateInfo}
                dateInfo={dateInfo}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                setqueryData={setqueryData}
              />
            );
          case 6:
            return (
              <SoulWriteStep6
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                queryData={queryData}
                createBridgetextSelection={createBridgetextSelection}
                setqueryData={setqueryData}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
              />
            );
          case 7:
            return (
              <SoulWriteStep7
                projectInfo={projectInfo}
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                memberstatusInfo={memberstatusInfo}
                setqueryData={setqueryData}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                setmemberstatusInfo={setmemberstatusInfo}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                queryData={queryData}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                bridgeArrayChar={bridgeArrayChar}
                removeBridge={removeBridge}
              />
            );

          case 9:
            return (
              <SoulWriteStep9
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                createBridgetextSelection={createBridgetextSelection}
                setqueryData={setqueryData}
                queryData={queryData}
              />
            );
          case 10:
            return (
              <SoulWriteStep10
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                setqueryData={setqueryData}
                queryData={queryData}
              />
            );
          case 11:
            return (
              <SoulWriteStep11
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                setqueryData={setqueryData}
                queryData={queryData}
              />
            );
          case 12:
            return (
              <SoulWriteStep12
                categoryList={categoryList}
                setUnsavedChanges={setUnsavedChanges}
                setshow={setshow}
                getComPanionList={getComPanionList}
                companionId={companionId}
                getCompanion={getCompanion}
                projectId={projectId}
                character={character}
                setcharacterIndex={setcharacterIndex}
                categoryListForm={categoryListForm}
                setcategoryListForm={setcategoryListForm}
                setchooseStep={setchooseStep}
                setDateInfo={setDateInfo}
                dateInfo={dateInfo}
                memberstatusInfo={memberstatusInfo}
                setSingleCharacter={setSingleCharacter}
                setqueryData={setqueryData}
                queryData={queryData}
              />
            );

          default:
            <></>;
        }
      });
  };

  const createBridgetextSelection = (
    index,
    step,
    type,
    lineNumber,
    bridgeType,
    allData
  ) => {

    console.log(
      "dddddddddd555577",
      index,
      step,
      type,
      lineNumber,
      bridgeType,
      allData
    );
    if (memberstatusInfo?.customerStatus == 2) return;
    let selection = window.getSelection().toString();
    if (!selection || !selection.trim()) {
      return;
    }
    var range = window.getSelection().getRangeAt(0);
    let mark = document.createElement("mark");
    mark.classList.add("bridgeHighlight");
    const isSafeRange = range.startContainer === range.endContainer;
    if (!isSafeRange) return;
    range.surroundContents(mark);
    //let allText = document.querySelector(`#selection${index}`);
    let tempObject = {};
    setbridgeArrayChar((prev) => {
      let makeObject = {
        lineNumber: type == "project" ? 0 : lineNumber,
        selectionText: selection,
        category: step == undefined ? null : step,
        step: step == undefined ? null : step,
        type: bridgeType,
      };
      if (bridgeType == "past") {
        tempObject = {
          lineNumber: 0,
          selectionText: "",
          category: "",
          step: "",
          type: "present",
        };
      } else {
        tempObject = {
          lineNumber: 0,
          selectionText: "",
          category: "",
          step: "",
          type: "past",
        };
      }

      let removeDuplicateChar;

      if (allData && allData?.case == "update_bridge") {
        let currentData = [...prev];
        console.log("ddddddddd1", currentData);
        console.log("ddddddddd2", currentData[allData.bridgeIndex]);
        currentData[allData.bridgeIndex] = makeObject;
        removeDuplicateChar = [...currentData];
      } else {
        if (bridgeType == "past") {
          removeDuplicateChar = [...prev, makeObject, tempObject];
        } else {
          removeDuplicateChar = [...prev, tempObject, makeObject];
        }
      }
      return removeDuplicateChar;

      // removeDuplicateChar = [
      //     ...new Map(
      //         removeDuplicateChar.map((v) => [
      //             `${v.selectionText}-${v.lineNumber}`,
      //             v,
      //         ])
      //     ).values(),
      // ];
      // return removeDuplicateChar;
    });

    // if (index != undefined && index != "project") {
    //   setcategoryListForm((prev) => {
    //     let formValues = { ...prev };
    //     formValues[`categoryForms${step}`][index]["content"] =
    //       document.querySelector(`#texthtml${lineNumber}`).innerHTML;
    //     return formValues;
    //   });
    // } else if (index == "project") {
    // }
    setUnsavedChanges(true);
  };

  const removeBridge = (e, data, index, type) => {
    e.preventDefault();
    setUnsavedChanges(true);
    let bridArray = [...bridgeArrayChar];

    if (type == "delete_complete_bridge") {
      bridArray.splice(index - 1, 2);
      setbridgeArrayChar(bridArray);
      return;
    }

    //check if corresponding bridge is empty or not
    if (data.selectionText) {
      bridArray[index] = Object.assign(bridArray[index], { selectionText: "" });
      setbridgeArrayChar(bridArray);
      return;
    }

    bridArray.splice(index, 1);
    let parentEl;
    if (data.category == 0) {
      parentEl = document.getElementById("texthtml_project");
    } else {
      parentEl = document.getElementById("texthtml" + data.lineNumber);
    }

    var arr = parentEl.querySelectorAll("mark.bridgeHighlight");
    arr.forEach((el) => {
      var parent = el.parentNode;
      //if(data.selectionText == "el.innerHTML"){
      parent.replaceChild(el.firstChild, el);
      parent.normalize();
      //}
    });
    bridArray.forEach((obj, i) => {
      if (obj.lineNumber == data.lineNumber) {
        var myHilitor = new window.Hilitor("texthtml" + obj.lineNumber, "MARK");
        if (myHilitor?.setMatchType) {
          myHilitor.setMatchType("open");
          myHilitor.apply(obj.selectionText);
        }
      }
    });
    setbridgeArrayChar(bridArray);

    return;
    /*let bridArray = [...bridgeArrayChar];
        let categoryArray = { ...categoryListForm };

        let projectCategoryHtml = { ...selectedTextWrraperCategory1 };
        let projectInfo = { ...memberstatusInfo };
        if (data?.category && data?.category == 2) {
            projectInfo.title = projectInfo?.title.replaceAll(
                `<span class="bridgeHighlight">${data?.selectionText}</span>`,
                data?.selectionText
            );
            bridArray.splice(index, 1);
        } else {

            if (data.lineNumber == 0) {
                projectCategoryHtml.title = projectCategoryHtml?.title.replaceAll(
                    `<span class="bridgeHighlight">${data?.selectionText}</span>`,
                    data?.selectionText
                );
                bridArray.splice(index, 1);
            }
            for (let iterator in categoryArray) {
                for (let j = 0; j < categoryArray[iterator]?.length; j++) {
                    if (categoryArray[iterator][j]?.lineNumber == data.lineNumber) {
                        bridArray.splice(index, 1);
                        categoryArray[iterator][j].content = categoryArray[iterator][
                            j
                        ].content?.replaceAll(
                            `<span class="bridgeHighlight">${data.selectionText}</span>`,
                            data.selectionText
                        );
                    }
                }
            }
        }

        setcategoryListForm(categoryArray);
        setselectedTextWrraperCategory1({ ...projectCategoryHtml });
        setbridgeArrayChar(bridArray);
        setmemberstatusInfo(projectInfo);*/

  };
  return (
    <>
      <div className="bg-white steps-wrpr">

        {vimeoOpen == 1 &&
          showVideo &&
          (nextClick || !projectId || step4Video) && vimeoSrc != null
          ? (
            <SoulwritingVimeoModal
              vimeoOpen={vimeoOpen == 1}
              vimeoSrc={vimeoSrc}
              onHideVimeo={() => {
                if (vimeoSrc != "") {
                  setvimeoOpen(0);
                } else {
                  // setIsModalShow(1);
                  setvimeoOpen(0);
                }
              }}
              description={description}
              openModel={openModel}
              setShowProtagonistQues={setShowProtagonistQues}
              setvimeoOpen={setvimeoOpen}
              category={category}
            />
          ) : (
            <>
              {dynamicRenderedComponents()}
              {show == 16 && (
                <BridgeListModal
                  setqueryData={setqueryData}
                  bridgeArrayChar={bridgeArrayChar}
                  removeBridge={removeBridge}
                  memberstatusInfo={memberstatusInfo}
                  categoryListForm={categoryListForm}
                  setcategoryListForm={setcategoryListForm}
                  setmemberstatusInfo={setmemberstatusInfo}
                  setshow={setshow}
                  show={show}
                  projectId={projectId}
                  onHide={() => {
                    setshow(null);
                  }}
                />
              )}
            </>
          )}
      </div>

      {showProtagonistQues && openPopup && (
        <ProtagonistQuesModal
          show={showProtagonistQues}
          onHide={() => setShowProtagonistQues(false)}
          setvimeoOpen={setvimeoOpen}
          projectId={projectId}
          character={character}
          setCharacter={setCharacter}
          lastOpenedStage={lastOpenedStage}
          setLastOpenedStage={setLastOpenedStage}
          setSubmitClicked={setSubmitClicked}
          fetchCharForS3={fetchCharForS3}
          setFethcCharForS3={setFethcCharForS3}
          protagonistList={protagonistList}
          setProtagonistList={setProtagonistList}
          setNextClick={setNextClick}
        />
      )}
    </>
  );
};

export default SoulWritingStep;
