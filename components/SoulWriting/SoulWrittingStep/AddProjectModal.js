import React, { Fragment, useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { addProjectList } from "../../../redux/action/soul-writing";
import { getLanguages, checklanguage } from "../../../constant";
import InputField from "../../FormFields/InputField";
import _ from "lodash";
const AddProjectModal = ({
  show,
  onHide,
  setProjectList,
  setcomponentWrapper,
  categoryList,
  projectList,
}) => {
  const { handleSubmit, control, setValue } = useForm();
  const [submitting, setsubmitting] = useState(false);
  const onsubmit = async (formvalues) => {
    setsubmitting(true);
    const { projectname } = formvalues;

    try {
      let response = await addProjectList({ projectList: [projectname] });
      setValue("projectname", "");
      setProjectList(response?.data?.responseData);
      setsubmitting(false);
      setcomponentWrapper((prev) => {
        let findId = _.find(prev, ["categoryId", categoryList[1]?.id]);
        if (findId != undefined) {
          return prev;
        } else {
          return [...prev, { categoryId: categoryList[1]?.id }];
        }
      });
    } catch (err) {}
  };


  return (
    <>
      <div
        className={`modal project_add_modal ${show ? "show" : ""}`}
        id="scheduleModal"
      >
        <div className="overlay-div" id="modalOverlay"></div>
        <div className="modal-content">
          <div className="modal-dialog">
            <div className="text-center modal-header">
              <h4 className="h4 text-dark-grey fw500"></h4>
              <a
                href="javascript:;"
                className="close-btn"
                id="closeModal"
                onClick={() => onHide()}
              >
                <span className="icon close-icon"></span>
              </a>
            </div>
            <div className="modal-body">
              <form onSubmit={handleSubmit(onsubmit)}>
                <div className="list_wrapper">
                  <label className="f-label">
                    {getLanguages(checklanguage, "projectlabel")}
                  </label>
                  <div className="project_list">
                    <InputField
                      control={control}
                      autoComplete="off"
                      name={`projectname`}
                      formInClass="w-100"
                      type="text"
                      placeholder={getLanguages(checklanguage, "projectname")}
                      className="form-control"
                      inputClassName="f-in w-100"
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(
                            checklanguage,
                            "projectnamereq"
                          ),
                        },
                      }}
                    />
                  </div>
                </div>
                <div className="d-flex align-center justify-end modal-footer">
                  <div className="d-flex align-center modal-btn-wrpr">
                    <button
                      type="button"
                      onClick={() => onHide()}
                      className="btn sm w-100"
                    >
                      {getLanguages(checklanguage, "cancel")}
                    </button>
                    <button
                      type="submit"
                      className={`btn-accent sm w-100 ${
                        submitting && "btn-loader"
                      }`}
                    >
                      {getLanguages(checklanguage, "save")}
                    </button>
                  </div>
                </div>
              </form>
              {projectList != null && (
                <ul className="project_list_wrapper">
                  {projectList?.map((item, index) => (
                    <li key={index}>
                      <p className="project_name fw500">{item?.title}</p>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AddProjectModal;
