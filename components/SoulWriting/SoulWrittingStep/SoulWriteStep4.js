import { useEffect, useState } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import CharcterComponent from "./CharcterComponent";
import CkEditorCompnonent from "./CkEditorCompnonent";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
const SoulWriteStep4 = ({
  setUnsavedChanges,
  setshow,
  categoryListForm,
  projectId,
  character,
  setcharacterIndex,
  setcategoryListForm,
  setchooseStep,
  queryData,
  createBridgetextSelection,
  memberstatusInfo,
  setSingleCharacter,
  categoryList,
  setqueryData,
  setValue,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[3]?.videoLink);
    }
    setDescription(categoryList[3]?.description);
  }, [categoryList]);

  // useEffect(() => {
  //   setValue("occasion", projectInfo?.title);
  // }, [projectInfo?.title]);
  
  return (
    <>
      <div className="d-flex soul-step-outer soul-step3" id="occasion-4">
        <div className="soul-step-comment-col create_Occasion">
        {vimeoOpen === 1 && vimeoSrc != null && (
          <SoulwritingVimeoModal
            vimeoOpen={vimeoOpen === 1}
            vimeoSrc={vimeoSrc}
            onHideVimeo={() => {
              if (vimeoSrc != "") {
                setvimeoOpen(0);
              } else {
                // setIsModalShow(1);
                setvimeoOpen(0);
              }
            }}
            description={description}
          />
        )} 
          <div className="soul-step-inner">
            <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
              {getLanguages(checklanguage, "occasion")}{" "}
              <span
                className="icon q-mark-icon playSvg"
                onClick={() => setvimeoOpen(1)}
              ></span>
            </h5>
            {/* <CharcterComponent
              setshow={setshow}
              character={character}
              queryData={queryData}
              memberstatusInfo={memberstatusInfo}
              setSingleCharacter={setSingleCharacter}
            /> */}

            <CkEditorCompnonent
              setUnsavedChanges={setUnsavedChanges}
              step={2}
              isBridge={queryData?.bridge && queryData?.type == "present"}
              bridgeType={queryData?.type}
              projectId={projectId}
              setshow={setshow}
              setcharacterIndex={setcharacterIndex}
              categoryListForm={categoryListForm}
              setcategoryListForm={setcategoryListForm}
              setchooseStep={setchooseStep}
              queryData={queryData}
              setqueryData={setqueryData}
              createBridgetextSelection={createBridgetextSelection}
              memberstatusInfo={memberstatusInfo}
              character={character}
            />
          </div>
        
      </div>
      </div>
    </>
  );
};

export default SoulWriteStep4;
