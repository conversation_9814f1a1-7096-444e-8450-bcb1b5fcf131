import { useEffect, useState } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import CharcterComponent from "./CharcterComponent";
import CkEditorCompnonent from "./CkEditorCompnonent";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
const SoulWriteStep6 = ({
  setUnsavedChanges,
  setshow,
  categoryListForm,
  projectId,
  character,
  setcharacterIndex,
  setcategoryListForm,
  setchooseStep,
  queryData,
  createBridgetextSelection,
  setqueryData,
  memberstatusInfo,
  setSingleCharacter,
  categoryList,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[5]?.videoLink);
    }
    setDescription(categoryList[5]?.description);
  }, [categoryList]);
  const handleDragStart = (e, index) => {
    highlightDraggableContainer(true)
  };

  const handleDragEnter = (e, index) => {
    highlightDraggableContainer(true)
  };

  const handleDragLeave = (e, index) => {
    highlightDraggableContainer(false)
  };

  const handleDrop = (e, index) => {
    highlightDraggableContainer(false)
    if (
      categoryListForm[`categoryForms${4}`] != null &&
      categoryListForm[`categoryForms${4}`]?.length > 1
    ) {
      setcategoryListForm((prev) => {
        let formValues = { ...prev };
        let arrayIndex = index + 1;
        for (var i = 0; i < formValues[`categoryForms${4}`].length; i++) {
          if (formValues[`categoryForms${4}`][i].isRedLine == 1) {
            formValues[`categoryForms${4}`][i].isRedLine = 0;
            formValues[`categoryForms${4}`][i].painpictureCollapse = 0;
          }
          if (arrayIndex > i) {
            if (formValues[`categoryForms${4}`][arrayIndex] != undefined) {
              formValues[`categoryForms${4}`][arrayIndex].isRedLine = 0;
              formValues[`categoryForms${4}`][
                arrayIndex
              ].painpictureCollapse = 1;
              arrayIndex++;
            }
          }
        }

        formValues[`categoryForms${4}`][index]["isRedLine"] = 1;
        formValues[`categoryForms${4}`][index]["painpictureCollapse"] = 1;
        return formValues;
      });
      setqueryData((prevState) => ({ collapsetoggle: true }));
    }
  };
  const onDragOver = (e) => {
    highlightDraggableContainer(true)
    e.preventDefault();
  };
  const highlightDraggableContainer = (highlight = true) => {
    // let el = document.getElementById("painpicture-6");
    // if (highlight) {
    //   el.classList.add("highlighted-draggable-container");
    // }else{
    //   el.classList.remove("highlighted-draggable-container");
    // }

    // let el = document.getElementsByClassName("scene-row-redline");
    // for (let i = 0; i < el.length; i++) {
    //   if (highlight) {
    //     el[i].classList.add("highlighted-draggable-container");
    //   } else {
    //       el[i].classList.remove("highlighted-draggable-container");
    //   }
      
    // }
  }
  return (
    <div className="d-flex soul-step-outer soul-step5" id="painpicture-6">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="soul-step-inner">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "painPicture")}{" "}
            <span
              className="icon q-mark-icon playSvg"
              onClick={() => setvimeoOpen(1)}
            ></span>
          </h5>

          {/* <CharcterComponent
            setshow={setshow}
            character={character}
            queryData={queryData}
            memberstatusInfo={memberstatusInfo}
            setSingleCharacter={setSingleCharacter}
          /> */}
          <CkEditorCompnonent
            setUnsavedChanges={setUnsavedChanges}
            step={4}
            isBridge={queryData?.bridge && queryData?.type == "past"}
            bridgeType={queryData?.type}
            projectId={projectId}
            setshow={setshow}
            setcharacterIndex={setcharacterIndex}
            categoryListForm={categoryListForm}
            setcategoryListForm={setcategoryListForm}
            setchooseStep={setchooseStep}
            queryData={queryData}
            handleDragStart={handleDragStart}
            handleDrop={handleDrop}
            handleDragLeave={handleDragLeave}
            handleDragEnter={handleDragEnter}
            onDragOver={onDragOver}
            createBridgetextSelection={createBridgetextSelection}
            setqueryData={setqueryData}
            memberstatusInfo={memberstatusInfo}
            character={character}
          />
        </div>
      )}
    </div>
  );
};

export default SoulWriteStep6;
