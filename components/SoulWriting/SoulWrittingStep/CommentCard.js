import React, { useState } from "react";
import { postsoulwritingComment } from "../../../redux/action/soul-writing";
import dynamic from "next/dynamic";
import { useSelector } from "react-redux";
import toast from "react-hot-toast";
import { checklanguage, getLanguages } from "../../../constant";

let CKEditor;
if (typeof window !== "undefined") {
  CKEditor = dynamic(() => import("../../FormFields/CKEditor"), {
    ssr: false,
  });
}
const CommentCard = ({ fieldData, step, setcategoryListForm, index }) => {
  const [commentText, setcommentText] = useState(
    fieldData?.comments?.[1]?.comment
  );
  const { userInfo } = useSelector((state) => state.user);
  // const postComment = async (e) => {
  //   e.preventDefault();
  //   if (commentText == "") return;
  //   let dataPayload = {
  //     lineUuid: fieldData?.uuid,
  //     comment: commentText,
  //   };
  //   try {
  //     let response = await postsoulwritingComment(dataPayload);

  //     toast.success(response?.data?.message);
  //     console.log(response);
  //   } catch (err) {}
  // };

  const onBlurCallback = (index, data) => {
    if (data == "") return;
    let dataPayload = {
      lineUuid: fieldData?.uuid,
      comment: data,
    };
    try {
      let response = postsoulwritingComment(dataPayload);
      // setcategoryListForm((prev) => {
      //     let updateComments = { ...prev }
      //     updateComments[`categoryForms${step}`][index]['comments'] = [...updateComments[`categoryForms${step}`][index]['comments'], resonse?.data?.responseData]
      //     return updateComments
      // })
      // //jasetcommentText('')
      //toast.success(response?.data?.message);
    } catch (err) {}
  };
  const handleChangeFormData = (index, data) => {
    setcommentText(data);
  };
  return (
    <div className="comment-card-block">
      {fieldData?.comments?.[0]?.comment && (
        <div className="comment-card">
          <div className={`comment-card-inner companion_message`}>
            <p className="p fw500 text-grey-5 comment-name">
              {fieldData?.comments?.[0]?.author?.firstName}{" "}
              {fieldData?.comments?.[0]?.author?.lastName} :
            </p>
            <p
              className="p text-dark-grey bg-white comment-text"
              dangerouslySetInnerHTML={{
                __html: fieldData?.comments?.[0]?.comment,
              }}
            />
          </div>
        </div>
      )}

      {/* {fieldData?.comments?.[0]?.comment && (
        <div className="comment-card">
          <div className="comment-card-inner">
            <p className="p fw500 text-grey-5 comment-name">
              {getLanguages(checklanguage, "myComment")}
            </p>
            <CKEditor
              ckeditorValue={commentText}
              disabled={false}
              index={index}
              handleChangeFormData={handleChangeFormData}
              //hideControlsOnBlur={true}
              onBlurCallback={onBlurCallback}
            />
          </div>
          // <div className="text-right cmnt-btn-wrpr">
          //   <button onClick={(e) => postComment(e)} className=" btn btn-sm">
              
          //     {getLanguages(checklanguage, "submit")}
          //   </button>
          // </div>
        </div>
      )} */}
    </div>
  );
};

export default CommentCard;
