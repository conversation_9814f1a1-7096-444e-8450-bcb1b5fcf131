import React, { useState, useEffect } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import TextAreaField from "../../FormFields/SoulTextAreaField";
import { updateProject } from "../../../redux/action/soul-writing";
import CheckboxField from "../../FormFields/SingleCheckbox";
import { useForm } from "react-hook-form";
import SoulStepsVideos from "../../Common/Modals/SoulStepsVideos";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import CommentCardFixedStages from "./CommentCardFixedStages";
import { isMobile } from "react-device-detect";
const SoulWriteStep1 = ({
  projectId,
  control,
  getComPanionList,
  getCompanion,
  getValues,
  queryData,
  createBridgetextSelection,
  bridgeArrayChar,
  selectedTextWrraperCategory1,
  projectInfo,
  removeBridge,
  setError,
  clearErrors,
  setshow,
  memberstatusInfo,
  categoryList,
  setNextClick,
  categoryListForm,
  checkValidation,
  companionId,
  submit,
  setShowCompanionCost,
}) => {
  // const [isModalOpen, setIsModalOpen] = useState(true);
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);
  const { register } = useForm();
  const [submitting, setSubmitting] = useState(false);
  const [textAreaCount, setTextAreaCount] = useState(
    getValues("reason")?.length || 0
  );
  useEffect(() => {
    if (getValues("reason")?.length > 500) {
      setError("reason", {
        type: "required",
        message: getLanguages(checklanguage, "reasonLengthMessage"),
      });
    }
  }, [getValues("reason")]);

  const onsubmit = async () => {
    let payload = getValues();
    let data = {
      reason: payload?.reason,
      id: memberstatusInfo?.id,
      title: payload?.title,
    };
    try {
      setSubmitting(true);
      let response = await updateProject(data);

      setSubmitting(false);
    } catch (err) {
      setSubmitting(false);
    }
  };
  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[0]?.videoLink);
    }
    setDescription(categoryList[0]?.description);
  }, [categoryList]);
  console.log(memberstatusInfo, 'memberstatusInfo?.customerStatus')
  return (
    <>
      <div className="d-flex soul-step-outer soul-step1" id="reason-1" style = {{paddingTop: Object.keys(getCompanion)?.length > 0 && isMobile ? "250px": "0"}}>
      <div className="soul-step-comment-col">
        {vimeoOpen === 1 && vimeoSrc != null ? (
          <SoulwritingVimeoModal
            vimeoOpen={vimeoOpen === 1}
            vimeoSrc={vimeoSrc}
            onHideVimeo={() => {
              if (vimeoSrc != "") {
                setvimeoOpen(0);
              } else {
                // setIsModalShow(1);
                setvimeoOpen(0);
              }
            }}
            description={description}
          />
        ) : (
          <>
            <div className="step-left">
              <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
                {getLanguages(checklanguage, "reasonLabel")}{" "}
                {/* <span
                  className="icon q-mark-icon playSvg"
                  onClick={() => setvimeoOpen(1)}
                ></span> */}
              </h5>
              {queryData?.bridge ? (
                <p
                  id={`reasonstep1`}
                  dangerouslySetInnerHTML={{
                    __html:
                      selectedTextWrraperCategory1?.reason != null
                        ? selectedTextWrraperCategory1?.reason
                        : getValues("reason"),
                  }}
                />
              ) : (
                <>
                  <div className="form-in">
                    <TextAreaField
                      name="reason"
                      control={control}
                      rules={{
                        required: {
                          value: true,
                          message: getLanguages(checklanguage, "required"),
                        },
                      }}
                      onSelect={() =>
                        getValues("reason")?.length < 500 &&
                        clearErrors("reason")
                      }
                      textAreaClassName="font-inter textarea aaaa"
                      //readOnly={Object.keys(projectInfo)?.length > 0 ? true : false}
                      // readOnly={(memberstatusInfo?.submittedVersion)}
                      readOnly={(memberstatusInfo?.customerStatus == 2) ? true : false}
                      placeholder={getLanguages(
                        checklanguage,
                        "soulWritingReasonPlaceholder"
                      )}
                      type={1}
                      setTextAreaCount={setTextAreaCount}
                      // onBlur={
                      //   !memberstatusInfo?.submittedVersion && projectId
                      //     ? onsubmit
                      //     : null
                      // }
                      onBlur={
                        (memberstatusInfo?.customerStatus != 2) && projectId
                          ? onsubmit
                          : null
                      }
                    />
                    <span className="text-grey-7 font-inter char-count">
                      {textAreaCount} /500{" "}
                    </span>
                    {/* {
                            !memberstatusInfo?.submittedVersion && projectId
                                ?
                                <div className="text-right">
                                  <button
                                      type="button"
                                      className={`${" d-flex align-end btn sm "} ${submitting && 'btn-loader'}`}
                                      onClick={(e) => onsubmit()}
                                  >
                                      Update reason
                                  </button>
                                </div>
                                :
                                <></>

                        } */}
                  </div>
                </>
              )}
              {/* {
            Object.keys(projectInfo)?.length > 0
              ?
              <></>
              :
              <CheckboxField
                name="consent"
                control={control}
                labelClass={"fw400 form-check-label"}
                label={getLanguages(checklanguage, "new_soul_writing_accept_terms_text")}
                tooltipLink={"/terms"}
                
              />
          } */}
            </div>

            <div className="step-right">
              <div className="right_warp">
                {Object.keys(getCompanion)?.length > 0 ? (
                  <>
                    <div className="getCompanion">
                      <p className="p text-grey-5 fw500">
                        {getLanguages(checklanguage, "myCompanion")}:
                      </p>
                      <div className="d-flex align-center profile-card">
                        <div className="comp-img">
                          <img
                            src={
                              getCompanion?.UserProfile?.attachment?.path
                                ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${getCompanion?.UserProfile?.attachment?.path}`
                                : "/images/userplace_holder.png"
                            }
                            alt="companion image"
                            className="cover-img"
                          />
                        </div>
                        <div className="text-left companion-details">
                          <div className="d-flex align-center comp-name-wrpr">
                            <h6 className="h6 fw600 comp-name">
                              {getCompanion?.UserProfile?.firstName}{" "}
                              {getCompanion?.UserProfile?.lastName}
                            </h6>
                          </div>

                          <p className="text-grey-5 comp-desc">
                            {getCompanion?.UserProfile?.gender === 1
                              ? getLanguages(checklanguage, "male")
                              : getLanguages(checklanguage, "female")}{" "}
                            ·{" "}
                            <span className="text-grey-1">
                              {getLanguages(checklanguage, "professional")}
                            </span>
                          </p>
                          <div className="f-start rating-stars">
                           
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        // onClick={(e) => {
                        //   e.preventDefault();
                        //   getComPanionList(1);
                        // }}

                        onClick={(e) => {
                          setShowCompanionCost("step1");
                          setshow(11);
                        }}
                        className={`mt-10 btn-accent w-100 ${
                          getValues("reason")?.length < 0 && "disabled"
                        }`}
                      >
                        <span className="icon add-user-icon"></span>
                        {getLanguages(checklanguage, "changeCompanion")}
                      </button>
                      {projectId && (
                        <div className="sendToCompanionMobile">
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              if (!checkValidation(categoryListForm)) return;
                              if (companionId == null) {
                                getComPanionList(null, null, "step1");
                              } else {
                                submit();
                                setshow(10);
                              }
                            }}
                            className={`btn-accent footer-btn  mobile-only ${
                              memberstatusInfo?.customerStatus == 2
                                ? "disabled"
                                : ""
                            }`}
                          >
                            {getLanguages(checklanguage, "sendToCompanion")}
                          </button>
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        getComPanionList(1, null, "step1");
                      }}
                      className={`btn-accent w-100 ${
                        getValues("reason")?.length < 0 && "disabled"
                      }`}
                    >
                      <span className="icon add-user-icon"></span>
                      {getLanguages(checklanguage, "selectCompanion")}
                    </button>
                    {projectId && (
                      <div className="sendToCompanionMobile">
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            if (!checkValidation(categoryListForm)) return;
                            if (companionId == null) {
                              getComPanionList(null, null, "step1");
                            } else {
                              submit();
                              // setshow(3);
                              setshow(10);
                            }
                          }}
                          className={`btn-accent footer-btn  mobile-only ${
                            memberstatusInfo?.customerStatus == 2
                              ? "disabled"
                              : ""
                          }`}
                        >
                          {getLanguages(checklanguage, "sendToCompanion")}
                        </button>
                      </div>
                    )}
                  </>
                )}
                <CommentCardFixedStages
          projectInfo={projectInfo}
          stage="reason"
        />
              </div>

              {/* {bridgeArrayChar?.length > 0 && (
            <div className={`${"bridge_wrp"} ${Object.keys(getCompanion)?.length > 0 ? "companion-selected" : ""}`} >
              {bridgeArrayChar?.map((item, i) => (
                <>
                  <div className="relative bridge-list-item">
                    <p
                      onClick={() =>
                        document
                          .querySelector(`#texthtml${item?.lineNumber}`)
                          ?.scrollIntoView({
                            behavior: "smooth",
                            block: "center",
                            inline: "nearest",
                          })
                      }
                      key={i}
                      className="bridge_chars"
                    >
                      {item?.selectionText}
                    </p>
                    {queryData?.bridge && (
                      <button
                        className="remove_bridge"
                        onClick={(e) => removeBridge(e, item, i)}
                      >
                        X
                      </button>
                    )}
                  </div>
                </>
              ))}
            </div>
          )} */}
              {/* <button className="d-flex align-center fw500 swap-comp"><span className="icon swap-icon"></span>Swap Companion</button>
                <div className="d-flex justify-between align-center comment-section-header">
                    <p className="p text-grey-5 fw500 d-flex align-center"><span className="icon soul-comment-icon"></span>Comments</p>
                    <button className="btn-secondary cmnt-btn disable">Add</button>
                </div> */}
            </div>
          </>
        )}
        </div>
      </div>
    </>
  );
};

export default SoulWriteStep1;
