import React, { Fragment, useEffect } from "react";
import { getLanguages, checklanguage } from "../../../constant";

const CharcterComponent = ({
  setshow,
  character,
  queryData,
  memberstatusInfo,
  setSingleCharacter,
}) => {
  return (
    <>
      <div className="d-flex align-center person-wrpr">
        {/* {
                    !queryData?.bridge && <button onClick={(e) => { e.preventDefault(); setshow(1); setSingleCharacter(null) }} className={`btn add-btn ${memberstatusInfo?.customerStatus == 2 && 'disabled'}`}><span className="icon plus-icon"></span>{getLanguages(checklanguage, 'add')}</button>
                } */}

        <div className="person-cards">
          {character?.map((item, i) => {
            let colorStyle = {};
            if (item?.color?.backgroundColor) {
              colorStyle = {
                background: item.color.backgroundColor,
                color: item.color.fontColor,
              };
            }
            return (
              <Fragment key={i}>
                {/* {
                                    i < 3 && <h6 className="h6 fw500 person-card">{item?.name?.split(' ')?.length > 1 ? item?.name?.split(' ')?.[0]?.charAt(0)?.toUpperCase() + item?.name?.split(' ')?.[1]?.charAt(0)?.toUpperCase() : item?.name?.split(' ')?.[0]?.charAt(0)?.toUpperCase()}</h6>
                                } */}
                {item?.Acronym
                  ? i < 3 && (
                      <h6 className="h6 fw500 person-card" style={colorStyle}>
                        {item?.Acronym?.substr(0, 3)?.toUpperCase() == "I"
                          ? getLanguages(checklanguage, "i")
                          : item?.Acronym?.substr(0, 3)?.toUpperCase()}
                      </h6>
                    )
                  : i < 3 && (
                      <h6 className="h6 fw500 person-card" style={colorStyle}>
                        {item?.name?.split(" ")?.length > 1
                          ? item?.name
                              ?.split(" ")?.[0]
                              ?.charAt(0)
                              ?.toUpperCase() +
                            item?.name
                              ?.split(" ")?.[1]
                              ?.charAt(0)
                              ?.toUpperCase()
                          : item?.name
                              ?.split(" ")?.[0]
                              ?.charAt(0)
                              ?.toUpperCase()}
                      </h6>
                    )}

                {character?.length > 3 && i === character?.length - 1 && (
                  <h6
                    className="h6 text-white fw500 person-card-count"
                    style={colorStyle}
                  >
                    +{character?.slice(3, character?.length)?.length}
                  </h6>
                )}
              </Fragment>
            );
          })}
        </div>
      </div>
      <div className="d-flex w-100 scene-row title">
        <p className="p text-grey-6 fw500 d-flex align-center step-subtitle first-col">
          {getLanguages(checklanguage, "line")}
        </p>
        <p className="p text-grey-6 fw500 d-flex align-center step-subtitle second-col">
          {getLanguages(checklanguage, "person")}
        </p>
        <p className="p text-grey-6 fw500 d-flex align-center step-subtitle third-col">
          {getLanguages(checklanguage, "scene")}
        </p>
      </div>
    </>
  );
};

export default CharcterComponent;
