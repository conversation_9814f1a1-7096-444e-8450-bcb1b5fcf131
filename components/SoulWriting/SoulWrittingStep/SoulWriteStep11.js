import React, { useEffect, useState } from "react";
import CharcterComponent from "./CharcterComponent";
import CkEditorCompnonent from "./CkEditorCompnonent";
import { getLanguages, checklanguage } from "../../../constant";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";

const SoulWriteStep11 = ({
  setqueryData,
  queryData,
  setUnsavedChanges,
  setshow,
  categoryListForm,
  projectId,
  character,
  setcharacterIndex,
  setcategoryListForm,
  setchooseStep,
  memberstatusInfo,
  setSingleCharacter,
  categoryList,
}) => {
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [description, setDescription] = useState(null);

  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[10]?.videoLink);
    }
    setDescription(categoryList[10]?.description);
  }, [categoryList]);
  return (
    <div className="d-flex soul-step-outer soul-step11" id="projection-11">
      {vimeoOpen === 1 && vimeoSrc != null ? (
        <SoulwritingVimeoModal
          vimeoOpen={vimeoOpen === 1}
          vimeoSrc={vimeoSrc}
          onHideVimeo={() => {
            if (vimeoSrc != "") {
              setvimeoOpen(0);
            } else {
              // setIsModalShow(1);
              setvimeoOpen(0);
            }
          }}
          description={description}
        />
      ) : (
        <div className="soul-step-inner">
          <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
            {getLanguages(checklanguage, "projection")}{" "}
            <span
              className="icon q-mark-icon playSvg"
              onClick={() => setvimeoOpen(1)}
            ></span>
          </h5>
          {/* <CharcterComponent
            setshow={setshow}
            character={character}
            memberstatusInfo={memberstatusInfo}
            setSingleCharacter={setSingleCharacter}
          /> */}
          <CkEditorCompnonent
            setUnsavedChanges={setUnsavedChanges}
            step={9}
            projectId={projectId}
            setshow={setshow}
            setcharacterIndex={setcharacterIndex}
            categoryListForm={categoryListForm}
            setcategoryListForm={setcategoryListForm}
            setchooseStep={setchooseStep}
            memberstatusInfo={memberstatusInfo}
            character={character}
          />
        </div>
      )}
    </div>
  );
};

export default SoulWriteStep11;
