import React, { useEffect, useState } from "react";
import { checklanguage, getLanguages } from "../../../constant";
import ReactSelectField from "../../FormFields/SelectField";
import { updateProject } from "../../../redux/action/soul-writing";
import { components } from "react-select";
import InputField from "../../FormFields/InputField";
import { useForm, useFieldArray } from "react-hook-form";
import { addProjectList } from "../../../redux/action/soul-writing";
import ReactVimeoModal from "../../Common/Modals/ReactVimeoModal";
import SoulwritingVimeoModal from "../../Common/Modals/SoulwritingVimeoModal";
import CommentCardFixedStages from "./CommentCardFixedStages";
const SoulWriteStep2 = ({
  projectId,
  setValue,
  data,
  control,
  queryData,
  getValues,
  createBridgetextSelection,
  selectedTextWrraperCategory1,
  setqueryData,
  categoryListForm,
  projectInfo,
  projectList,
  setProjectList,
  setshow,
  memberstatusInfo,
  categoryList,
  setNextClick,
}) => {
  //const { handleSubmit } = useForm();
  const [vimeoOpen, setvimeoOpen] = useState(0);
  const [vimeoSrc, setVimeoSrc] = useState(null);
  const [newProjectName, setNewProjectName] = useState("");
  const [description, setDescription] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  useEffect(() => {
    if (categoryList?.length && vimeoSrc == null) {
      setVimeoSrc(categoryList[0]?.videoLink);
    }
    setDescription(categoryList[1]?.description);
  }, [categoryList]);
  const SelectMenuButton = (props) => {
    return (
      <components.MenuList {...props}>
        {props.children}
        <a
          href="javascript:;"
          onClick={(e) => {
            e.preventDefault();
            setshow(6);
          }}
          className="d-flex align-center justify-center link w-100 new-proj"
        >
          <span className="icon plus-icon"></span>{" "}
          {getLanguages(checklanguage, "addProject")}{" "}
        </a>
      </components.MenuList>
    );
  };

  // const onsubmit = async () => {
  //   setSubmitting(true);
  //   const projectName = newProjectName;

  //   try {
  //     let response = await addProjectList({ projectList: [projectName] });

  //     setNewProjectName("");
  //     setProjectList(response?.data?.responseData);
  //     let dd = response?.data?.responseData?.[0];
  //     setValue("title", dd?.id);

  //     setSubmitting(false);
  //     setcomponentWrapper((prev) => {
  //       let findId = _.find(prev, ["categoryId", categoryList[1]?.id]);
  //       if (findId != undefined) {
  //         return prev;
  //       } else {
  //         return [...prev, { categoryId: categoryList[1]?.id }];
  //       }
  //     });
  //   } catch (err) {}
  //   return false;
  // };
  useEffect(() => {
    // setValue('title', 378);
    //     setValue('title',{
    //         "id": 378,
    //         "label": "This is test project",
    //         value: 378
    //       })
    //    console.log(getValues('title'), 'ttttt')
    if(projectInfo?.title){
      setValue("title", projectInfo?.title);
    }
    
  }, [projectInfo?.title]);
  const updateProjectValue = () => {};

  const updateSoulwritingProject = async () => {
    let payload = getValues();
    let data = {
      reason: payload?.reason,
      id: memberstatusInfo?.id,
      title: payload?.title,
    };
    try {
      setSubmitting(true);
      let response = await updateProject(data);
      setSubmitting(false);
    } catch (err) {
      setSubmitting(false);
    }
  };

  return (
    <>
      <div className="d-flex soul-step-outer soul-step2" id="project-2">
      <div className="soul-step-comment-col col-Project">
        {vimeoOpen === 1 && vimeoSrc != null ? (
          <SoulwritingVimeoModal
            vimeoOpen={vimeoOpen === 1}
            vimeoSrc={vimeoSrc}
            onHideVimeo={() => {
              if (vimeoSrc != "") {
                setvimeoOpen(0);
              } else {
                // setIsModalShow(1);
                setvimeoOpen(0);
              }
            }}
            description={description}
          />
        ) : (
          <>
            <div className="step-left">
              <h5 className="h5 d-flex align-center fw500 text-grey-3 step-title">
                {getLanguages(checklanguage, "project")}
                <span
                  className="icon q-mark-icon playSvg"
                  onClick={() => setvimeoOpen(1)}
                ></span>
              </h5>
              {/* {
                        queryData?.bridge ? < p id={`texthtml0`} onMouseUp={() => createBridgetextSelection(undefined, undefined, 'title')} dangerouslySetInnerHTML={{ __html: selectedTextWrraperCategory1?.title != null ? selectedTextWrraperCategory1?.title : getValues('title') }} /> : */}

              <div className="d-flex align-end project-field-wrpr">
                {queryData?.bridge && queryData.type == "present" ? (
                  <p
                    id={`texthtml_project`}
                    onMouseUp={() =>
                      createBridgetextSelection(
                        null,
                        0,
                        "project",
                        null,
                        queryData.type,
                        queryData
                      )
                    }
                    dangerouslySetInnerHTML={{
                      __html: memberstatusInfo?.title,
                    }}
                  ></p>
                ) : (
                  <InputField
                    control={control}
                    autoComplete="off"
                    // label={getLanguages(checklanguage, "title") + " *"}
                    label={""}
                    name="title"
                    labelClass="f-label"
                    formInClass="w-100"
                    type="text"
                    readOnly={(memberstatusInfo?.customerStatus == 2) ? true : false}
                    placeholder={getLanguages(checklanguage, "projectName")}
                    className="form-control projectInput"
                    inputClassName="f-in w-100"
                    rules={{
                      required: {
                        value: true,
                        message: getLanguages(
                          checklanguage,
                          "projectNameRequiredMessage"
                        ),
                      },
                    }}
                    onBlur={
                      (memberstatusInfo?.customerStatus != 2) && projectId
                        ? updateSoulwritingProject
                        : null
                    }
                  />
                )}
              </div>
              <div className="d-flex align-end project-field-wrpr"></div>

              
             
            </div>

            {/* {

                        Object.keys(projectInfo)?.length <= 0 &&
                        <>
                            <p className="or-wrpr">
                                <span className="or-content">Or create new</span>
                            </p>

                            <div>
                                <div className="d-flex project-field-wrpr ">
                                    <div className="w-50">
                                        <input
                                            type="text"
                                            autoComplete="off"
                                            name={`projectName`}
                                            className=" form-control sm"
                                            value={newProjectName}
                                            onChange={(e) => setNewProjectName(e.target.value)}
                                            placeholder={getLanguages(checklanguage, "projectName")}
                                        // rules={{
                                        //     required: {
                                        //         value: true,
                                        //         message: getLanguages(checklanguage, "projectnamereq"),
                                        //     },
                                        // }}
                                        />
                                    </div>
                                    <button
                                        type="button"
                                        className={`${"d-flex align-center justify-center btn sm "} ${submitting && 'btn-loader'}`}
                                        onClick={(e) => onsubmit()}
                                    >
                                        <span className="icon plus-icon"></span> Add project
                                    </button>
                                </div>
                            </div>
                        </>
                    } */}

            {/* } */}
            {/* </div> */}
          </>
        )}
        <div className="step-right">
          <CommentCardFixedStages
            projectInfo={projectInfo}
            stage="project"
          />
        </div>
        </div>
      </div>
    </>
  );
};

export default SoulWriteStep2;
