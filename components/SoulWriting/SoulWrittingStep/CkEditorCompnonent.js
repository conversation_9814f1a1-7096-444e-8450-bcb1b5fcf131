import React, { useEffect, useMemo, useState } from "react";
import { checklanguage, getLanguages, removeHtmlTags, stripTagsExceptMark } from "../../../constant";
import CommentCard from "./CommentCard";
import dynamic from "next/dynamic";
import _ from "lodash";
let CKEditor;
if (typeof window !== "undefined") {
  CKEditor = dynamic(() => import("../../FormFields/CKEditor"), {
    ssr: false,
  });
}
const CkEditorCompnonent = ({
  setUnsavedChanges,
  step,
  setshow,
  chooseCharacter,
  setcharacterIndex,
  categoryListForm,
  setcategoryListForm,
  setchooseStep,
  SoulWriteStep8,
  queryData,
  handleDragStart,
  handleDrop,
  handleDragLeave,
  handleDragEnter,
  onDragOver,
  createBridgetextSelection,
  setqueryData,
  memberstatusInfo,
  character,
  isBridge,
  bridgeType,
  addRow,
  fromOldProjectDeatils,
}) => {
  const [countdown, setCountdown] = useState(0);
  useEffect(() => {
    let countdownTimer;
    if (countdown > 0) {
      countdownTimer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      clearTimeout(countdownTimer);
    }
    return () => clearInterval(countdownTimer);
  }, [countdown]);
  useEffect(() => {}, [CKEditor]);

  useEffect(() => {
    setTimeout(() => {
      let btn = window?.document?.getElementById("collapse_pain_picture_button");
      if(btn){
        btn.click();
      }
    }, 500)
  }, [])

  const onAdd = async (e, index) => {
    e.preventDefault();
    if (memberstatusInfo?.customerStatus == 2) return;
    let isRedLineCase = 0;
    setcategoryListForm((prev) => {
      let currentLines = { ...prev };
      if (step == 4) {
        if (
          currentLines[`categoryForms${step}`]?.[index - 1] &&
          currentLines[`categoryForms${step}`]?.[index - 1].isRedLine == 1
        ) {
          currentLines[`categoryForms${step}`][index - 1] = Object.assign(
            currentLines[`categoryForms${step}`][index - 1],
            { isRedLine: 0, painpictureCollapse: 0 }
          );
          isRedLineCase = 1;
        }
      }
      const insert = (arr, index, newItem) => [
        ...arr.slice(0, index),
        newItem,
        ...arr.slice(index),
      ];
      return {
        ...currentLines,
        [`categoryForms${step}`]: insert(
          currentLines[`categoryForms${step}`],
          index,
          {
            content: "",
            categoryId: currentLines[`categoryForms${step}`][0]["categoryId"],
            character: character[0],
            lineNumber: 1,
            isRedLine: isRedLineCase,
            errorMessage: "",
            characterId: character?.[0]?.id,
            painpictureCollapse: isRedLineCase,
          }
        ),
      };
    });
  };

  const makeLineIncreament = () => {
    let counter = 1;
    let updateLineNumber = categoryListForm;
    if (updateLineNumber != null) {
      // delete updateLineNumber['categoryForms4']
      // delete updateLineNumber['categoryForms5']
      // delete updateLineNumber['categoryForms6']
      delete updateLineNumber["categoryForms1"];
      delete updateLineNumber["categoryForms5"];
      delete updateLineNumber["categoryForms6"];
      for (let iterator in updateLineNumber) {
        for (let j = 0; j < updateLineNumber[iterator].length; j++) {
          updateLineNumber[iterator][j].lineNumber = counter;
          if (updateLineNumber[iterator][j]?.isDeleted != "permanent") {
            counter++;
          }
        }
      }
      setcategoryListForm(updateLineNumber);
    }
  };
  if (!fromOldProjectDeatils) {
    makeLineIncreament();
  }
  const onDelete = (e, Index) => {
    e.preventDefault();
    if (memberstatusInfo?.customerStatus === 2) return;
    setCountdown(3);
    const timer = setTimeout(() => {
      setcategoryListForm((prev) => {
        let data = { ...prev };
        data[`categoryForms${step}`][Index] = Object.assign(
          data[`categoryForms${step}`][Index],
          { isDeleted: "permanent" }
        );
        return data;
      });
      setCountdown(0);
    }, 3000);

    setcategoryListForm((prev) => {
      let data = { ...prev };
      data[`categoryForms${step}`][Index] = Object.assign(
        data[`categoryForms${step}`][Index],
        { isDeleted: "temp", timeoutId: timer }
      );
      return data;
    });
  };
  const handleUndo = (e, Index) => {
    setCountdown(0);
    setcategoryListForm((prev) => {
      let data = { ...prev };
      clearTimeout(data[`categoryForms${step}`][Index]?.timeoutId);
      data[`categoryForms${step}`][Index] = Object.assign(
        data[`categoryForms${step}`][Index],
        // { timeoutId: null }
        { isDeleted: "", timeoutId: null }
      );
      return data;
    });
  };

  const chooseSoulCharacter = (index) => {
    if (memberstatusInfo?.customerStatus == 2) return;
    setshow(4);
    setcharacterIndex(index);
    setchooseStep(step);
  };
  const updateValueCallback = React.useCallback(async (index, data) => {
    setUnsavedChanges(true);
    await setcategoryListForm((prev) => {
      let formValues = { ...prev };
      if (data) {
        formValues[`categoryForms${step}`][index]["errorMessage"] = "";
        formValues[`categoryForms${step}`][index]["content"] = data;
        if (removeHtmlTags(data)?.length > 1000) {
          formValues[`categoryForms${step}`][index]["errorMessage"] = getLanguages(checklanguage, "soulWritingTextLengthMessage");
            //"character cannot be greater than 1000";
        }
      } else {
        formValues[`categoryForms${step}`][index]["content"] = "";
        // formValues[`categoryForms${step}`][index]['errorMessage'] = "Field is required"
      }
      console.log(`formVafdasdfa`, formValues);

      return formValues;
    });
  }, []);

  const expandPainPicture = (e, index) => {
    e.preventDefault();
    setcategoryListForm((prev) => {
      let formValues = { ...prev };
      let arrayIndex = index + 1;
      for (var i = 0; i < formValues[`categoryForms${4}`].length; i++) {
        if (arrayIndex > i) {
          if (formValues[`categoryForms${4}`][arrayIndex] != undefined) {
            formValues[`categoryForms${4}`][arrayIndex].isRedLine = 0;
            formValues[`categoryForms${4}`][arrayIndex].painpictureCollapse = 0;
            arrayIndex++;
          }
        }
      }
      return formValues;
    });
    setqueryData((prevState) => ({
      collapsetoggle: !prevState.collapsetoggle,
    }));
  };

  const collapsePainPicture = (e, index, gg) => {
    e.preventDefault();
    setcategoryListForm((prev) => {
      let formValues = { ...prev };
      let arrayIndex = index + 1;
      for (var i = 0; i < formValues[`categoryForms${4}`].length; i++) {
        if (arrayIndex > i) {
          if (formValues[`categoryForms${4}`][arrayIndex] != undefined) {
            formValues[`categoryForms${4}`][arrayIndex].isRedLine = 0;
            formValues[`categoryForms${4}`][arrayIndex].painpictureCollapse = 1;
            arrayIndex++;
          }
        }
      }

      return formValues;
    });
    setqueryData((prevState) => ({
      collapsetoggle: !prevState.collapsetoggle,
    }));
  };
  if (step == 2) {
  }


  const handleTouchStart = (e, index) => {
    e.preventDefault();
    console.log(`here touch start`);
    // return;
    const targett = window.document.querySelector("#red-lineStep");
    console.log(`ddddssssd`, targett);
    
    console.log(`dfasew`, e.target);
    const touch = e.touches[0];
    console.log("Touch details:", touch);

    // targett.style.position = "relative";
    // targett.style.left = touch.clientX + "px";
    // targett.style.top = touch.clientY + "px";
    // targett.style.backgroundColor = "red";
    // targett.style.opacity = "1";
    targett.style.width = "100%";
  };

  const handleTouchEnd = (e, index) => {
    e.preventDefault();
    const targett = window.document.querySelector("#red-lineStep");
    const touch = e.changedTouches[0];

    // targett.style.position = "relative";
    // targett.style.clientX = touch.clientX + "px";
    // targett.style.clientY = touch.clientY + "px";
    // targett.style.backgroundColor = "yellow";
    // targett.style.opacity = "1";
    targett.style.width = "100%";
  };
  ///

  return (
    <>
      {/* {deletedRow && (
        <div className="scene-row">
          <div className="undo_btn">
            <button onClick={handleUndo}>Undo</button>
          </div>
        </div>
      )} */}
      {categoryListForm != null &&
        categoryListForm[`categoryForms${step}`]?.map((_field, index) => {
          if (_field && _field.isDeleted == "permanent") {
            return <></>;
          }
          // console.log("Deleted row index:", deletedRow?.Index);
          let colorStyle = {
            cursor: "pointer",
            visibility: step == 8 ? "hidden" : "visible",
          };
          if (_field?.character?.color?.backgroundColor) {
            colorStyle = Object.assign(colorStyle, {
              background: _field?.character?.color?.backgroundColor,
              color: _field?.character?.color?.fontColor,
            });
          }
          return (
            <>
              <div
                onDrop={(e) => step == 4 && handleDrop(e, index)}

                onDragLeave={(e) => step == 4 && handleDragLeave(e, index)}
                onDragEnter={(e) => step == 4 && handleDragEnter(e, index)}
                onDragOver={(e) => step == 4 && onDragOver(e, index)}

                
                onDragStart={(e) => step == 4 && handleDragStart(e, index)}
                className={`${
                  step == 4 &&
                  _field.isRedLine == 0 &&
                  _field.painpictureCollapse == 0
                    ? "d-flex w-100 scene-row "
                    : step == 4 &&
                      _field.isRedLine == 0 &&
                      _field.painpictureCollapse == 1
                    ? "d-none"
                    : "d-flex w-100 scene-row"
                } ${step == 4 ?  "scene-row-redline": ""}`}
                key={index + 1}
              >
                <p className="p text-grey-6 fw500 first-col">
                  {_field?.lineNumber}
                </p>
                <div className="second-col">
                  <h6
                    style={colorStyle}
                    onClick={() => chooseSoulCharacter(index)}
                    // className="h6 fw500 person-card-empty"
                    className="h6 fw500 person-card"
                  >
                    {/* {
                                    _field?.character?.name?.split(' ')?.length > 1
                                        ?
                                        _field?.character?.name?.split(' ')?.[0]?.charAt(0)?.toUpperCase() + _field?.character?.name?.split(' ')?.[1]?.charAt(0)?.toUpperCase()
                                        :
                                        _field?.character?.name?.split(' ')?.[0]?.charAt(0)?.toUpperCase() || "@"
                                } */}
                    {_field?.character?.Acronym
                      ? _field?.character?.Acronym?.substr(
                          0,
                          3
                        )?.toUpperCase() == "I"
                        ? getLanguages(checklanguage, "i")
                        : _field?.character?.Acronym?.substr(
                            0,
                            3
                          )?.toUpperCase()
                      : _field?.character?.name?.split(" ")?.length > 1
                      ? _field?.character?.name
                          ?.split(" ")?.[0]
                          ?.charAt(0)
                          ?.toUpperCase() +
                        _field?.character?.name
                          ?.split(" ")?.[1]
                          ?.charAt(0)
                          ?.toUpperCase()
                      : _field?.character?.name
                          ?.split(" ")?.[0]
                          ?.charAt(0)
                          ?.toUpperCase() || "@"}
                  </h6>
                </div>
                <div className=" scene-text-wrpr third-col">
                  {isBridge ? (
                    <p
                      id={`texthtml${_field?.lineNumber}`}
                      onMouseUp={() => {
                        if (!queryData?.isReadOnly) {
                          createBridgetextSelection(
                            index,
                            step,
                            undefined,
                            _field?.lineNumber,
                            queryData?.type,
                            queryData
                          );
                        }

                        setTimeout(() => {
                          setqueryData((prev) => {
                            return {
                              ...prev,
                              bridge: false,
                              type: "",
                              redline: false,
                              date: new Date(),
                              case: null,
                              bridgeIndex: null,
                            };
                          });
                        }, 400);
                        setTimeout(() => {
                          let bridgeTypeId = "";
                          if (bridgeType == "past") {
                            bridgeTypeId = "#clickToAddBridge_past";
                          } else if (bridgeType == "present") {
                            bridgeTypeId = "#clickToAddBridge_present";
                          } else {
                            bridgeTypeId = "#bridge-7";
                          }
                          document.querySelector(bridgeTypeId)?.scrollIntoView({
                            behavior: "smooth",
                            block: "center",
                            inline: "nearest",
                          });
                        }, 800);
                      }}
                      dangerouslySetInnerHTML={{ __html: stripTagsExceptMark(_field?.content) }}
                    ></p>
                  ) : (
                    <>
                      {CKEditor && (
                        <CKEditor
                          ckeditorValue={_field?.content || ""}
                          index={index}
                          fieldData={_field}
                          textAreaClassName="font-inter textarea"
                          handleChangeFormData={updateValueCallback}
                          memberstatusInfo={memberstatusInfo}
                          placeholder={getLanguages(
                            checklanguage,
                            "soulWritingReasonPlaceholder"
                          )}
                          hideControlsOnBlur={true}
                        />
                      )}
                      <div className="d-flex align-center justify-between">
                        <span className="text-grey-7 font-inter char-count">
                          {removeHtmlTags(_field?.content)?.length}/1000{" "}
                        </span>
                        <div className="d-flex align-center action-btns">
                          {/* {deletedRowInfo && deletedRowInfo.index === index && (
                            <div className="scene-row">
                              <div className="undo_btn">
                                <button onClick={handleUndo}>Undo</button>
                              </div>
                            </div>
                          )} */}
                          {_field?.isDeleted == "temp" ? (
                            <button
                              className="undo_btn btn add-btn"
                              onClick={(e) => handleUndo(e, index)}
                            >
                              {getLanguages(checklanguage, "undo")}({countdown})
                            </button>
                          ) : (
                            index > 0 && (
                              <>
                                <button
                                  onClick={(e) => onDelete(e, index)}
                                  className="delete-btn"
                                >
                                  <span className="icon delete-icon"></span>
                                </button>
                              </>
                            )
                          )}

                          {addRow == "false" ? (
                            <></>
                          ) : (
                            <button
                              type="button"
                              onClick={(e) => onAdd(e, index + 1)}
                              className="btn add-btn"
                            >
                              <span className="icon plus-icon"></span>{" "}
                              {getLanguages(checklanguage, "addRow")}
                            </button>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
                <CommentCard
                  step={step}
                  fieldData={_field}
                  setcategoryListForm={setcategoryListForm}
                  index={index}
                />
              </div>
              {step == 4 &&
                _field?.isRedLine == 1 &&
                _field.painpictureCollapse == 1 && (
                  <div
                    draggable={
                      memberstatusInfo?.customerStatus == 2 ? false : true
                    }
                    onDragStart={step == 4 && handleDragStart}
                    onTouchStart={(e) => handleTouchStart(e)}
                    
                    onTouchEnd={(e) => handleTouchEnd(e)}
                    className="d-flex soul-step-outer-redline soul-step8"
                    id="redline"
                    // onMouseDown={(e) => pickup(e)}
                    // onTouchStart={(e) => pickup(e)}
                    // onMouseMove={(e) => move(e)}
                    // onTouchMove={(e) => move(e)}
                    // onMouseUp={(e) => drop(e)}
                    // onTouchEnd={(e) => drop(e)}
                  >
                    <div className="step-left" id="red-lineStep">
                      <div className="d-flex align-center redline-wrpr">
                        <span className="icon drag-icon"></span>
                        <div className="red-line"></div>
                      </div>
                    </div>
                  </div>
                )}

              {step == 4 &&
                _field?.isRedLine == 1 &&
                _field.painpictureCollapse == 1 &&
                queryData?.collapsetoggle &&
                index < categoryListForm[`categoryForms${4}`]?.length - 1 && (
                  <div className="d-flex soul-step-outer">
                    <div className="step-left">
                      <button
                        id = "collapse_pain_picture_button"
                        className="collpase-btn"
                        onClick={(e) => expandPainPicture(e, index)}
                      >
                        {getLanguages(checklanguage, "viewYourExistingStory")}
                      </button>
                    </div>
                  </div>
                )}
              {step == 4 &&
                _.find(categoryListForm[`categoryForms${4}`], [
                  "isRedLine",
                  1,
                ]) != undefined &&
                _field.painpictureCollapse == 0 &&
                queryData?.collapsetoggle == false &&
                index == categoryListForm[`categoryForms${4}`]?.length - 1 && (
                  <div className="d-flex soul-step-outer">
                    <div className="step-left">
                      <button
                        className="collpase-btn"
                        onClick={(e) =>
                          collapsePainPicture(
                            e,
                            _.findIndex(categoryListForm[`categoryForms${4}`], [
                              "isRedLine",
                              1,
                            ])
                          )
                        }
                      >
                        {getLanguages(checklanguage, "collapse")}
                      </button>
                    </div>
                  </div>
                )}
            </>
          );
        })}

      {queryData?.redline &&
      step == 4 &&
      _.find(categoryListForm[`categoryForms${4}`], ["isRedLine", 1]) ==
        undefined ? (
        <div className="d-flex soul-step-outer-redline" id="redline">
          <div className="step-left">
            <div
              draggable={memberstatusInfo?.customerStatus == 2 ? false : true}
              onDragStart={handleDragStart}
              // onTouchStart={(e) => handleTouchStart(e)}
              // onTouchEnd={(e) => handleTouchEnd(e)}
              className="d-flex align-center redline-wrpr"
            >
              <span className="icon drag-icon"></span>
              <div className="red-line disabled"></div>
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default CkEditorCompnonent;
