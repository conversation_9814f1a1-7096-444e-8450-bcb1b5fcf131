import React from 'react'
import { checklanguage, getLanguages } from '../../../constant'

const SoulWritingTableData = ({ categoryListForm, step, type, language }) => {
    
    return (
        <table className="table view-table">
            {
                categoryListForm != null && categoryListForm[`categoryForms${step}`]?.map((_field, index) => (
                    <>
                        <tr className="tr" key={index}>
                            <td className={type == 1 ? "td line_td pdf_font" : "td line_td"}><p>{_field?.lineNumber}</p></td>
                            <td className={type == 1 ? "td character_td pdf_font" : "td character_td"}>
                                <h6 className="h6 fw500 view-person-card">
                                    {
                                    _field?.character?.Acronym
                                    ? 
                                    _field?.character?.Acronym?.substr(0, 3)?.toUpperCase()
                                    :
                                    _field?.character?.name?.substr(0, 3)?.toUpperCase()
                                    }
                                    
                                </h6>
                            </td>
                            <td className={type == 1 ? "td content_td pdf_font" : "td content_td"} dangerouslySetInnerHTML={{ __html: _field?.content }}></td>

                        </tr>
                        {
                            step == 4 && _field?.isRedLine == 1 && _field.painpictureCollapse == 1 ?
                                <tr > <td colSpan={3}  >   <div><div style={{ marginTop: "5px" }} className="red-line view"></div><div style={{textAlign: "center", fontWeight: "bold", marginTop: "-18px"}}>{getLanguages(language, "redline")}</div></div> </td></tr>
                                : null
                        }
                    </>
                )

                )

            }
        </table>
    )
}

export default SoulWritingTableData