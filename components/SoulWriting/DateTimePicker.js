import React, { useState } from "react";
import DateTimePicker from "react-datetime-picker/dist/entry.nostyle";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";
const DateTimePickerComponents = ({
  disableCalendar,
  setDateInfo,
  dateInfo,
  name,
  minDate,
  maxDate,
  memberstatusInfo,
  realization,
}) => {
  const [calendarOpen, setCalendarOpen] = useState(false);
  const onChangeDate = (value) => {
    if(setDateInfo){
      setDateInfo((prev) => ({ ...prev, [name]: value }));
    }
    
  };
  const openCalendar = (e) => {
    e.preventDefault();
    if (memberstatusInfo?.customerStatus == 2) return;
    setCalendarOpen(!calendarOpen);
  };

  return (
    <div className="d-flex align-center date-in-wrpr">
      <div className="form-in">
        <div
          className={`f-in left-icon ${
            memberstatusInfo?.customerStatus === 2 && "disabled"
          }`}
        >
          <DateTimePicker
            disableCalendar={disableCalendar || false}
            disableClock={false}
            dayPlaceholder={"dd"}
            monthPlaceholder={"mm"}
            yearPlaceholder={"yyyy"}
            calendarIcon={<img src="/images/calender-icon.svg"></img>}
            format={
              localStorage.getItem("language") == "en" ? "MM/dd/y" : "dd.MM.y"
            }
            clearIcon={false}
            clockClassName=""
            name={name}
            onChange={onChangeDate}
            maxDate={maxDate}
            minDate={minDate}
            className="form-control"
            isCalendarOpen={calendarOpen}
            value={dateInfo != null ? new Date(dateInfo) : new Date()}
            // value={
            //   dateInfo != null
            //     ? new Date(dateInfo)
            //     : (realization = "true"
            //         ? new Date(Date.now() + 1000 * 60 * 60 * 24 * 365)
            //         : null)
            // }
          />
        </div>
      </div>
      {/* <button onClick={(e) => openCalendar(e)} className="btn add-btn">
        Change
      </button> */}
    </div>
  );
};

export default DateTimePickerComponents;
