import React, { useEffect, useState } from "react";
import { checklanguage, getLanguages, removeHtmlTags } from "../../constant";
import { useRouter } from "next/router";
const NextStepButton = ({
  componentWrapper,
  categoryList,
  setcomponentWrapper,
  values,
  currentCategoryId,
  setCurrentCategoryId,
  setNextClick,
  setLastOpenedStage,
  lastOpenedStage,
}) => {
  const router = useRouter();
  const [isDisable, setIsDisable] = useState(false);
  const { reason, title } = values;

  useEffect(() => {
    if (lastOpenedStage >= 12 || lastOpenedStage < 2) {
      setIsDisable(true);
    } else {
      setIsDisable(false);
    }
  }, [window?.location?.href, lastOpenedStage]);

  useEffect(() => {
    const onHashChangeStart = (url) => {
      //console.log(`Path changing to ${url}`);
    };

    router.events.on("hashChangeStart", onHashChangeStart);

    return () => {
      router.events.off("hashChangeStart", onHashChangeStart);
    };
  }, [router.events]);

  const getCurrentCategoryId = () => {
    let catIdArray = window?.location?.href?.split("#");

    if (catIdArray?.length < 2) {
      setCurrentCategoryId(3);
      return 3;
    } else {
      catIdArray = catIdArray[catIdArray.length - 1]?.split("-");
      setCurrentCategoryId(catIdArray[catIdArray.length - 1]);
      console.log(`dfasdflkdja`, catIdArray[catIdArray.length - 1]);
      // setCurrentCategory(catIdArray[catIdArray.length - 1]);
      return catIdArray[catIdArray.length - 1];
    }
  };


  const handleNextClick = () => {
    let tag = window?.document?.getElementById(
      "soulwriting-tab-id-" + (parseInt(lastOpenedStage) + 1)
    );
    setLastOpenedStage((prev) => {
      return prev + 1;
    });
    setTimeout(() => {
      console.log(tag, "taggg");
      router.push(tag?.getAttribute("href"));
    });
    tag?.click();
    setNextClick(true);
  };
  const handleDelyedNextClick = () => {
    setTimeout(handleNextClick, 300);
  };
  return (
    <>
      {componentWrapper?.length > 2 && (
        <button
          type="button"
          onClick={(e) => {
            let tag = window?.document?.getElementById(
              "soulwriting-tab-id-" + (parseInt(lastOpenedStage) + 1)
            );
            setTimeout(() => {
              setLastOpenedStage((prev) => {

                return prev + 1;
              });
            }, 200);

            // router.push(tag?.getAttribute("step"));

            tag?.click();
            setTimeout(() => {
              setNextClick(true);
            }, 500)


            // setTimeout(() => {
            //   console.log(`ddt44t`, tag?.getAttribute("step"));
            //   router.push(tag?.getAttribute("step"));
            // }, 100);
          }}
          className={`btn-accent footer-btn ${!title ||
              !reason ||
              reason?.length > 500 ||
              isDisable ||
              componentWrapper?.length < 3
              ? "disabled"
              : ""
            }  `}
        >
          {getLanguages(checklanguage, "nextStage")}
        </button>
      )}
    </>
  );
};

export default NextStepButton;
