import React, { useEffect, useState } from "react";
import { removeHtmlTags, getLanguages, checklanguage } from "../../constant";
import { useRouter } from "next/router";
const SoleWritingSubmitButton = ({
  bridgeArrayChar,
  componentWrapper,
  moveToNextStep,
  getComPanionList,
  getValues,
  setshow,
  companionId,
  values,
  categoryListForm,
  submitting,
  memberstatusInfo,
  checkValidation,
  onsubmit,
  lastOpenedStage,
  setIsAutoSaveEnabled,
  isSoulwritingEnabledData
}) => {
  let vv = getValues();
  const { reason, title, consent } = vv;
  
  const [soulwritingCheckData, setSoulwritingCheckData] = useState(isSoulwritingEnabledData || null);
  const router = useRouter();
  

  const { asPath } = router;
  useEffect(() => {
    setSoulwritingCheckData(isSoulwritingEnabledData);
  }, [isSoulwritingEnabledData])
  // useEffect(() => {
  //   console.log(values, 'vvvvvvvvv')
  // },[values])

  return (
    <>
      {/* {
        componentWrapper?.length > 2
        &&
        <button
          type='submit'
          className={`d-flex align-center footer-btn draft-btn ${memberstatusInfo?.customerStatus == 1 ? "" : memberstatusInfo?.customerStatus == 2 ? "disabled" : memberstatusInfo?.customerStatus == 3 ? "" : ""} ${submitting && 'btn-loader'}`}
        >
          <span className="icon draft-icon"></span>Save as draft
        </button>
      } */}
      {
        // componentWrapper?.length < 2
        //   ?
        //   <button
        //     type="button"
        //     onClick={(e) => moveToNextStep(e)}
        //     className={`btn-accent footer-btn ${(!reason || reason?.length > 250) && 'disabled'} `}
        //   >
        //     <span className="icon send-icon"></span>Continue
        //   </button>
        //   :
        componentWrapper?.length < 3 ? (
          <button
            type="button"
            onClick={(e) => {
              
              
              if(isSoulwritingEnabledData?.soulWritingStatus){
                localStorage.removeItem('soulwriting_project');
                localStorage.removeItem('soulwriting_reason');
                moveToNextStep(e);
              }else{
                //store project and data in local storage and redirect to sales page of digistore page
                setshow(17);
                localStorage.setItem('soulwriting_project', title);
                localStorage.setItem('soulwriting_reason', reason);
                // console.log(isSoulwritingEnabledData, 'isSoulwritingEnabledData')
                // if(isSoulwritingEnabledData?.soulWritingProductId?.value){
                //   router.push(`https://www.checkout-ds24.com/product/${isSoulwritingEnabledData?.soulWritingProductId?.value}/`);
                // }else{

                // }
              }
              
            }}
            className={`btn-accent footer-btn createProject ${
              !title || !reason || reason?.length > 500 ? "disabled" : ""
            }  `}
          >
            <span className="icon send-icon"></span>
            {getLanguages(checklanguage, "createProject")}
          </button>
        ) : (
          <>
            {asPath.includes("#bridge-7") ||
            asPath.includes("#redline-8") ||
            asPath.includes("#rewrite-9") ||
            asPath.includes("#affirmation-10") ||
            asPath.includes("#projection-11") ||
            asPath.includes("#implementation-12") ||
            lastOpenedStage > 6 ? (
              <button
                className="btn-accent footer-btn saveLeave desktop-only"
                onClick={(e) => {
                  e.preventDefault();
                  setshow(16);
                }}
              >
                {getLanguages(checklanguage, "bridge")}
              </button>
            ) : (
              <></>
            )}

            <button
              className="btn-accent footer-btn saveLeave protagonists"
              onClick={(e) => {
                e.preventDefault();
                setshow(14);
              }}
            >
              {getLanguages(checklanguage, "protagonists")}
            </button>
            
            <button
              onClick={(e) => {
                e.preventDefault();
                setIsAutoSaveEnabled(false);
                if (!checkValidation(categoryListForm)) return;
                if (companionId == null) {
                  getComPanionList(null, null, "step1");
                } else {
                  onsubmit();
                  setTimeout(() => {
                    setshow(10);
                  }, 500)
                }
              }}
              className={`btn-accent footer-btn  SendCompanion${
                memberstatusInfo?.customerStatus == 2 ? "disabled" : ""
              }`}
            >
              {getLanguages(checklanguage, "sendToCompanion")}
            </button>
          </>
        )
      }
    </>
  );
};
export default SoleWritingSubmitButton;