function Loader({dynamicClass}) {
  return (
    <div className={'page-loader active'}>
      {/* add sm className to show in small size  */}
      <div className='loader sm'>
        <div className="loader-item loader1"></div>
        <div className="loader-item loader2"></div>
        <div className="loader-item loader3"></div>
        <div className="loader-item loader4"></div>
        <div className="loader-item loader5"></div>
        <div className="loader-item loader6"></div>
        <div className="loader-item loader7"></div>
        <div className="loader-item loader8"></div>
      </div>
    </div>
  );
}

export default Loader;
