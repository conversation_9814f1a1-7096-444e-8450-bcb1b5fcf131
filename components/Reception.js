import { useCookie } from "next-cookie";
import { Number, Currency } from "react-intl-number-format";
import Link from "next/link";
import React from "react";
import { useState } from "react";
import { useEffect } from "react";
import Slider from "react-slick";
import { SlickSettings } from "../constant";
import { getAllCompanions } from "../redux/action/kuby-companion";
import { getProductList, getUserProducts } from "../redux/action/product";
import ReactFlagComponent from "./Common/ReactFlagComponent";
import ReceptionSkeleton from "./Common/SkeletonLoader/ReceptionSkeleton";
import SeminarComponent from "./Common/Seminar";
import SeminarUpdatedComp from "./Common/SeminarUpdatedComp";
import { useRouter } from "next/router";
import { upcomingMeetings } from "../redux/action/kuby-companion";
import moment from "moment";
import _, { initial } from "lodash";
import { getLanguages, checklanguage } from "../constant";
const Reception = () => {
  const soulWriting = process.env.NEXT_PUBLIC_SOUL_WRITING;
  // use hooks
  const cookies = useCookie();
  const router = useRouter();
  const [CompanionList, setCompanionList] = useState(null);
  let [ProductLists, setProductLists] = useState(null);
  let [userProducts, setUserProducts] = useState(null);
  const [isFetching, setisFetching] = useState(false);
  const [UpcomingMeeting, setUpcomingMeeting] = useState(null);
  useEffect(() => {
    getCompanionList();
    getProducts();

    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      UpCommingMeeting();
      getUserBoughtProducts();
    }
  }, []);

  const getCompanionList = async () => {
    setisFetching(true);
    try {
      let response = await getAllCompanions({
        language: localStorage.getItem("language") ?? "de",
        meetingProductId: 1,
        showData: "companion",
        random: 1,
      });
      setisFetching(false);
      let data = response?.data?.responseData;
      if (data?.users && data?.users?.length > 0) {
        data.users.forEach((obj, index) => {
          data.users[index] = Object.assign(data.users[index], {
            userType: obj?.Roles?.[0]?.name,
          });
        });
      }
      setCompanionList(data);
      setTimeout(() => {
        if (data && data?.length) {
          var slickHeight = document.querySelector(".slick-track").offsetHeight;
          var allSlides = document.querySelectorAll(".slick-slide");
          var allSlideCards = document.querySelectorAll(".comp-card");
          if (allSlides && allSlides.length > 0) {
            allSlides.forEach((obj) => {
              obj.style.height = slickHeight + "px";
            });
          }
          if (allSlideCards && allSlideCards.length > 0) {
            allSlideCards.forEach((obj) => {
              obj.style.height = slickHeight + "px";
            });
          }
        }

        //var slideHeight = $(".slick-track").find(".slick-slide").outerHeight();
      }, 200);
    } catch (err) {}
  };
  const getProducts = async () => {
    try {
      let response = await getProductList({ isFeatured: 1 });
      setProductLists(response?.data?.responseData);
    } catch (err) {}
  };

  const getUserBoughtProducts = async () => {
    try {
      let response = await getUserProducts();
      let products = [];

      let data = response?.data?.responseData;

      if (data?.Seminars) {
        if (data?.Seminars?.topicList?.length) {
          data?.Seminars?.topicList?.forEach((obj, index) => {
            products.push(obj);
          });
        }
      }
      if (data?.shopProduct) {
        data?.shopProduct?.topicList?.forEach((obj, index) => {
          products.push(obj);
        });
      }
      if (data?.KUBYstudy) {
        data?.KUBYstudy?.topicList?.forEach((obj, index) => {
          products.push(obj);
        });
      }
      if (data?.Trainings) {
        data?.Trainings?.topicList?.forEach((obj, index) => {
          products.push(obj);
        });
      }
      setUserProducts(products);
      if (products?.length) {
        setTimeout(() => {
          const container = document.querySelector("#my-products-container");
          if (container) {
            const matches = container.querySelectorAll(".img-name-card");
            if (matches?.length) {
              let maxHeight = 0;
              matches?.forEach((obj, index) => {
                if (maxHeight < obj.offsetHeight) {
                  maxHeight = obj.offsetHeight;
                }
              });

              matches?.forEach((obj, index) => {
                obj.style.height = maxHeight + "px";
              });
            }
          }
        }, 200);
      }
    } catch (err) {}
  };

  const UpCommingMeeting = async () => {
    try {
      let response = await upcomingMeetings({ pageNumber: 1, limit: 1 });
      setUpcomingMeeting(response?.data?.responseData?.records);
    } catch (err) {}
  };
  let companionName = _.find(UpcomingMeeting?.[0]?.Participants, [
    "role",
    "companion",
  ]);

  const checkRedirection = (seminar) => {
    if (
      typeof cookies.get("jwtToken") !== "undefined" &&
      cookies.get("jwtToken") !== null
    ) {
      if (!seminar?.isPurchased) {
        if (seminar.salesPageLink) {
          //router.push(seminar.salesPageLink);
          window.location.href = seminar.salesPageLink;
        } else {
          router.push(
            `/dashboard/self-practice-sales?id=${seminar?.id}&paymentlink=${seminar?.purchaseLink}`
          );
        }
      } else if (seminar?.isPurchased == 1) {
        router.push(`/dashboard/self-practice-details/${seminar?.id}`);
      } else {
        router.push(`/dashboard/self-practice-sales`);
      }
    } else {
      router.push(
        `/signin?redirectTourl=/dashboard/self-practice-details/${seminar?.id}`
      );
    }
  };
  return (
    <>
      {userProducts?.length > 0 ? (
        <div className="d-flex w-100 comp-reception-row">
          <div className="w-100 companions-wrpr my-products-wrapper com-row-left">
            <div className="h-100 w-100 d-flex fd-col align-center justify-center h-100 center-panel-inner">
              <div className="w-100 d-flex align-center justify-between heading-row">
                <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                  {getLanguages(checklanguage, "myProducts")}
                </h4>
              </div>
              <div
                id="my-products-container"
                className="d-flex w-100 seminar-wrpr reception-card-block  comp_slider"
              >
                <Slider {...SlickSettings}>
                  {userProducts?.length > 0 &&
                    userProducts?.map((seminar, index) => {
                      return (
                        <Link
                          href={
                            "/dashboard/self-practice-details/" + seminar?.id
                          }
                          key={index}
                        >
                          {seminar.isPurchased ? (
                            <div className="img-name-card">
                              <div className="relative img-card ">
                                <img
                                  src={
                                    seminar?.posterImage?.attachmentUrl != null
                                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${seminar?.posterImage?.attachmentUrl}`
                                      : "/images/test.jpeg"
                                  }
                                  alt=""
                                  className="img-fluid cover-img"
                                />
                                {seminar?.isPurchased ? (
                                  <></>
                                ) : (
                                  <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr top">
                                    <span className="lock"></span>
                                  </div>
                                )}
                              </div>
                              <h6 className="h6 text-grey-3">
                                <span className="fw600">{seminar?.name} –</span>{" "}
                                {seminar?.subTitle}
                              </h6>
                            </div>
                          ) : (
                            <></>
                          )}
                        </Link>
                      );
                    })}
                </Slider>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
      <div className="d-flex w-100 comp-reception-row">
        <div className="w-100 companions-wrpr com-row-left">
          <div className="h-100 w-100 d-flex fd-col align-center justify-center h-100 center-panel-inner">
            <div className="w-100 d-flex align-center justify-between heading-row">
              <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                {getLanguages(checklanguage, "kubycompanion")}
              </h4>
              <Link href={"dashboard/kuby-companion?tabstatus=currentAppointments"}>
                <a className="link view-all-link">
                  {getLanguages(checklanguage, "viewAll")}
                </a>
              </Link>
            </div>
            <div className="comp_slider ">
              {isFetching ? (
                <ReceptionSkeleton listsToRender={5} />
              ) : (
                <Slider {...SlickSettings}>
                  {CompanionList?.users?.length > 0
                    ? CompanionList?.users?.map((list, i) => (
                        <Link
                          href={`dashboard/kuby-companion?tabstatus=professionals&companionId=${
                            list?.id
                          }&type=${2}`}
                          key={i}
                        >
                          <div className="comp-card" key={i}>
                            <div className="comp-card-top">
                              <div className="comp-img">
                                <img
                                  src={
                                    list?.UserProfile?.attachment?.path
                                      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/${list?.UserProfile?.attachment?.path}`
                                      : "images/user-image.jpg"
                                  }
                                  alt=""
                                  className="cover-img"
                                />
                              </div>
                              <h6 className="h6 fw500 comp-name">
                                {list?.UserProfile?.firstName}{" "}
                                {list?.UserProfile?.lastName}{" "}
                              </h6>
                              <div className="d-flex align-center justify-center comp-name-wrpr">
                                {list?.Languages?.length > 0 &&
                                  list?.Languages?.map((_lang, index) => {
                                    return (
                                      <ReactFlagComponent
                                        code={_lang?.mobileCode}
                                        height="16"
                                        key={index}
                                      />
                                    );
                                  })}
                              </div>
                              <p className="text-grey-5 comp-desc">
                                {list?.UserProfile?.gender === 1
                                  ? getLanguages(checklanguage, "male")
                                  : getLanguages(checklanguage, "female")}
                                ·{" "}
                                <span className="text-grey-1">
                                  {list.userType == "companion"
                                    ? getLanguages(
                                        checklanguage,
                                        "professional"
                                      )
                                    : getLanguages(checklanguage, "student")}
                                </span>
                              </p>
                              {/* <div className="rating-stars">
                              <img src="images/filled-star.svg" className="filled" />
                              <img src="images/filled-star.svg" className="filled" />
                              <img src="images/filled-star.svg" className="filled" />
                              <img src="images/filled-star.svg" className="filled" />
                              <img src="images/empty-star.svg" className="empty" />
                            </div> */}
                            </div>
                            <div className="comp-price-details">
                              {list?.UserProfile?.meetingPrice != null && (
                                <p className="comp-price">
                                  <span className="text-black-1 price-value fw600">
                                    {" "}
                                    {/* € {list?.UserProfile?.meetingPrice != null ? list?.UserProfile?.meetingPrice : 0}*/}
                                    <Currency
                                      locale={checklanguage}
                                      currency={"EUR"}
                                    >
                                      {list?.UserProfile?.meetingPrice != null
                                        ? list?.UserProfile?.meetingPrice
                                        : 0}
                                    </Currency>
                                  </span>
                                  /{getLanguages(checklanguage, "min")}
                                </p>
                              )}
                            </div>
                          </div>
                        </Link>
                      ))
                    : null}
                </Slider>
              )}
            </div>
          </div>
        </div>
        <div className="meeting-block com-row-right">
          <div
            className={`d-flex fd-col bg-white ${
              UpcomingMeeting?.length > 0 ? "meeting-card-wrpr" : ""
            }`}
          >
            {UpcomingMeeting?.length > 0 && (
              <div className="w-100 d-flex align-center justify-between heading-row">
                <p className="mb-0 fw500 text-dark-grey img-card-title">
                  {getLanguages(checklanguage, "calender")}
                </p>
              </div>
            )}

            {UpcomingMeeting?.length > 0 && (
              <Link
                href={
                  "/dashboard/calender?s=" + UpcomingMeeting?.[0]?.startDate
                }
              >
                <a>
                  <div className="d-flex align-center meeting-card">
                    <div className="meeting-date-wrpr">
                      <p className="fw500 text-white meeting-mnth">
                        {localStorage.getItem("language") == "en"
                          ? moment(UpcomingMeeting?.[0]?.startDate).format(
                              "MMM"
                            )
                          : moment(UpcomingMeeting?.[0]?.startDate)
                              .locale("de")
                              .format("MMM")}
                      </p>
                      <p className="fw600 text-white meeting-date">
                        {localStorage.getItem("language") == "en"
                          ? moment(UpcomingMeeting?.[0]?.startDate).format("D")
                          : moment(UpcomingMeeting?.[0]?.startDate)
                              .locale("de")
                              .format("D")}
                      </p>
                    </div>
                    <div className="meeting-title-card">
                      <p className="d-flex align-center text-grey-6 fw500">
                        <span className="icon clock-icon"></span>{" "}
                        {localStorage.getItem("language") == "en"
                          ? moment(UpcomingMeeting?.[0]?.startDate).format(
                              "hh:mm a"
                            )
                          : moment(UpcomingMeeting?.[0]?.startDate)
                              .locale("de")
                              .format("HH:mm [Uhr]")}
                        {}
                      </p>
                      <p className="p text-grey-3 fw500 meeting-name">
                        {companionName?.User?.firstName}{" "}
                        {companionName?.User?.lastName}
                      </p>
                      <p className="p text-grey-6 meeting-desc">
                        {getLanguages(
                          checklanguage,
                          `${UpcomingMeeting?.[0]?.title}`
                        )}
                        {/* {UpcomingMeeting?.[0]?.title} */}
                      </p>
                    </div>
                  </div>
                </a>
              </Link>
            )}

            {/* {cookies.get("jwtToken") != undefined ? null : (
              <div className="d-flex align-center justify-center w-100 h-100 lock-icon-wrpr ">
                <span className="lock"></span>
              </div>
            )} */}
          </div>
          <div className="w-100 center-btn-wrpr">
            {soulWriting == "show" ? (
              <a
                onClick={() => router.push("/dashboard/soulwriting")}
                href="javascript:void(0)"
                className="w-100 btn-accent-lite soulwriting-btn"
              >
                <span className="icon soul-icon"></span>
                {getLanguages(checklanguage, "startSoulwriting")}
              </a>
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>

      {/* <SeminarComponent /> */}
      <SeminarUpdatedComp />

      {ProductLists != null ? (
        <div className="d-flex w-100 reception-row">
          <div className="w-100 shop-wrpr">
            <div className="w-100 d-flex align-center justify-between shop-title-row">
              <h4 className="h4 mb-0 text-dark-grey fw500 img-card-title">
                {getLanguages(checklanguage, "kubyshop")}
              </h4>
              <a
                href="javascript:void(0)"
                onClick={() => router.push("/dashboard/shop")}
                className="link view-all-link"
              >
                {getLanguages(checklanguage, "viewAll")}
              </a>
            </div>
            <div className="d-flex shop-card-wrpr">
              {ProductLists == null ? (
                <></>
              ) : (
                ProductLists?.products?.slice(0, 6)?.map((product, index) => {
                  return (
                    <div
                      style={{ cursor: "pointer" }}
                      className="shop-card card1"
                      key={index}
                      onClick={() =>
                        router.push(
                          `/dashboard/shop/product-detail?pid=${product?.id}`
                        )
                      }
                    >
                      <div className="prod-img">
                        <img
                          src={`${process.env.NEXT_PUBLIC_API_BASE_URL}/${product?.attachment?.path}`}
                          alt=""
                          className="img-fluid cover-img"
                        />
                      </div>
                      <h5 className="prod-title">{product?.content?.name}</h5>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default Reception;
