import React from "react";
import { useRouter } from "next/router";
import Link from "next/link";
const NavLink = ({
  path,
  activeClassName,
  name,
  sideBarstatus,
  title,
  setToggleNavigation,
  sethamburgerMenu,
}) => {
  const router = useRouter();
  const closeNavigation = () => {
    // setToggleNavigation(true);
    // sethamburgerMenu(false);
    let element = document.getElementById("mobile_main_menu");
    if (element) {
      element.click();
    }
  };

  return (
    <>
      {sideBarstatus == 1 ? (
        <li className="nav-item">
          <Link
            legacyBehavior
            href={
              path.includes("self-practice")
                ? "/dashboard/self-practice"
                : path.includes("create-soulwriting")
                ? "/dashboard/soulwriting"
                : path.includes("product-detail") ||
                  path.includes("cart") ||
                  path.includes("shop")
                ? "/dashboard/shop"
                : path
            }
          >
            <a
              onClick={closeNavigation}
              title={title}
              className={`text-grey-5 d-flex align-center nav-link ${
                router.asPath == path ? "active" : ""
              }`}
            >
              <span className={`nav-icon  ${activeClassName}`}></span>
              <span className="nav-link-text">{name}</span>
            </a>
          </Link>
        </li>
      ) : (
        <Link legacyBehavior href={path}>
          <a
            className={`${activeClassName} ${
              router.pathname === path ? "active" : ""
            }`}
          >
            {name}
          </a>
        </Link>
      )}
    </>
  );
};
export default NavLink;
