import React, { Fragment } from "react";
import { useRouter } from "next/router";
import { useCookie } from "next-cookie";
import Link from "next/link";
const CommonNavigation = ({
  data,
  kubynavbarStatus,
  MeetingTab,
  setMeetingTab,
  SubVideosLesson,
  shwoMycontanct,
}) => {
  // use hooks
  const cookies = useCookie();
  const router = useRouter();
  const { query } = router;
  const handleTabNavigation = (url) => {
    let newQuery = { ...router.query };
    delete newQuery.companionId;
    delete newQuery.type;
    newQuery.tabstatus = url;
    router.push(
      {
        pathname: `${router?.pathname}`,
        query: newQuery,
      },
      undefined,
      { shallow: false }
    );
  };
  return (
    <>
      {data?.length > 0 && (
        <ul
          className={
            kubynavbarStatus === 3
              ? "tabs companion-tab gggg"
              : "d-flex tab-list-wrpr"
          }
        >
          {data?.map((_data, index) => {
            return (
              <Fragment key={index}>
                {
                  // _data?.type != "self_practice_tabs"
                  //   ?
                  kubynavbarStatus === 1 ? (
                    <li
                      title="1"
                      className={`${"tab-item "} ${
                        (typeof cookies.get("jwtToken") == "undefined" ||
                          cookies.get("jwtToken") == null) &&
                        _data.tab == 3
                          ? ""
                          : ""
                      } `}
                      key={index}
                      onClick={() => handleTabNavigation(_data?.query)}
                    >
                      <a
                        href="javascript:void(0);"
                        className={`tab-link ${
                          query.tabstatus == _data.query ? "active" : ""
                        }`}
                      >
                        {_data?.name}
                      </a>
                    </li>
                  ) : kubynavbarStatus === 2 ? (
                    <li
                      title="2"
                      className={`${"tab-item"} ${
                        typeof cookies.get("jwtToken") !== "undefined" &&
                        cookies.get("jwtToken") !== null
                          ? ""
                          : ""
                      } `}
                      key={index}
                      onClick={() => setMeetingTab(_data?.tab)}
                    >
                      <a
                        className={`tab-link ${
                          _data?.tab === MeetingTab ? "active" : ""
                        }`}
                      >
                        {_data?.name}
                      </a>
                    </li>
                  ) : kubynavbarStatus === 3 ? ( //Self practice tabs
                    (_data?.query == "overview" &&
                      SubVideosLesson?.description) ||
                    (_data?.query == "downloadfiles" &&
                      SubVideosLesson?.attachments?.length) ? (
                      <li
                        title="3"
                        className={`${"tab-item"} ${
                          typeof cookies.get("jwtToken") !== "undefined" &&
                          cookies.get("jwtToken") !== null
                            ? ""
                            : ""
                        } `}
                        key={index}
                        onClick={() => setMeetingTab(_data?.tab)}
                      >
                        <a
                          href="javascript:void(0);"
                          className={`fw500 tab-link  ${
                            _data?.tab === MeetingTab ? "active" : ""
                          }`}
                        >
                          <span className={`icon ${_data?.icon}`}></span>
                          {_data?.name}
                        </a>
                      </li>
                    ) : (
                      <></>
                    )
                  ) : (
                    <li
                      title="4"
                      className={`${"tab-item"} ${
                        typeof cookies.get("jwtToken") !== "undefined" &&
                        cookies.get("jwtToken") !== null
                          ? ""
                          : ""
                      } `}
                      key={index}
                    >
                      <Link legacyBehavior href={_data?.url}>
                        <a
                          className={`tab-link ${
                            router?.pathname.includes(_data?.url)
                              ? "active"
                              : ""
                          }`}
                        >
                          {_data?.name}
                        </a>
                      </Link>
                    </li>
                  )
                  // :

                  // ((_data?.query == "overview" && SubVideosLesson?.description) || (_data?.query == "downloadfiles" && SubVideosLesson?.attachments?.length))
                  //   ?
                  //   <li title='1' className={`${"tab-item "} ${((typeof cookies.get('jwtToken') == 'undefined' || cookies.get('jwtToken') == null) && _data.tab == 3) ? "" : ""} `} key={index} onClick={() => handleTabNavigation(_data?.query)}>
                  //     <a href="javascript:void(0);" className={`tab-link ${query.tabstatus == _data.query ? 'active' : ''}`}>{_data?.name}</a>
                  //   </li>
                  //   :
                  //   <></>
                }
              </Fragment>
            );
          })}
        </ul>
      )}
    </>
  );
};
export default CommonNavigation;
