import moment from "moment";
import { useCookie } from "next-cookie";
import { NumericFormat } from "react-number-format";
import _, { indexOf } from "lodash";
export const errorType = [
  "manual",
  "required",
  "pattern",
  "validate",
  "minLength",
  "maxLength",
  "max",
  "min",
  "positive",
  "lessThanTen",
  "greaterThan",
  "checkUrl",
];

export const checklanguage =
  typeof window !== "undefined" && localStorage.getItem("language")
    ? localStorage.getItem("language")
    : "de";

export const getLanguages = (lng, key, replaceArray, replaceWith, isHtml) => {

  if (!lng || lng == null || typeof lng == 'undefined' || lng == "undefined") {
    lng = 'de';
  }
  //myString = myString.split('{' + replaceArray[i] + '}').join(replaceWith[i]);
  const loadedLanguage = require(`./languageFolder/${lng}.json`);
  if (loadedLanguage[key] != undefined && loadedLanguage[key] != "") {
    let myString = loadedLanguage[key];
    if (replaceArray && replaceWith) {
      for (var i = 0; i < replaceArray.length; i++) {
        myString = myString
          .split("{" + replaceArray[i] + "}")
          .join(replaceWith[i]);
      }
    }
    if (isHtml) {
      return <span dangerouslySetInnerHTML={{ __html: myString }} />;
    } else {
      return myString;
    }
  } else {
    return key;
  }
};

export const COMPANION_TYPE = {
  students: 3,
  professionals: 4,
};

export const TitleArray = {
  en: [
    {
      value: "1",
      label: getLanguages(checklanguage, "mr"),
      name: getLanguages(checklanguage, "mr"),
    },
    {
      value: "2",
      label: getLanguages(checklanguage, "miss"),
      name: getLanguages(checklanguage, "miss"),
    },
  ],
  de: [
    {
      value: "1",
      label: getLanguages(checklanguage, "mr"),
      name: getLanguages(checklanguage, "mr"),
    },
    {
      value: "2",
      label: getLanguages(checklanguage, "miss"),
      name: getLanguages(checklanguage, "miss"),
    },
  ],
  cz: [
    {
      value: "1",
      label: getLanguages(checklanguage, "mr"),
      name: getLanguages(checklanguage, "mr"),
    },
    {
      value: "2",
      label: getLanguages(checklanguage, "miss"),
      name: getLanguages(checklanguage, "miss"),
    },
  ],
};

export const GENDER_TYPES = {
  en: [
    { id: 1, name: getLanguages(checklanguage, "male") },
    { id: 2, name: getLanguages(checklanguage, "female") },
  ],
  de: [
    { id: 1, name: getLanguages(checklanguage, "male") },
    { id: 2, name: getLanguages(checklanguage, "female") },
  ],
  cz: [
    { id: 1, name: getLanguages(checklanguage, "male") },
    { id: 2, name: getLanguages(checklanguage, "female") },
  ],
};

export const BACKGROUND_COLORS = [
  "#1f77b4",
  "#aec7e8",
  "#ff7f0e",
  "#ffbb78",
  "#2ca02c",
  "#98df8a",
  "#ff9896",
  "#9467bd",
  "#bcbd22",
  "#17becf",
  "#dbdb8d",
  "#e377c2",
];

export const LANGUAGEA_ARRAY = [
  { id: 1, label: "French", value: "French" },
  { id: 2, label: "English", value: "English" },
];

export const PROFILE_NAV_LABELS = [
  {
    name: getLanguages(checklanguage, "profiledetail"),
    url: "/user/profile",
  },
  // {
  //   name: getLanguages(checklanguage, "addresses"),
  //   url: "/user/addresses",
  // },
  {
    name: getLanguages(checklanguage, "upcomingMeeting"),
    url: "/user/upcoming-meeting",
  },
  {
    name: getLanguages(checklanguage, "meetinghistory"),
    url: "/user/meeting-history",
  },
  // {
  //   name: getLanguages(checklanguage, "soulwritinghistory"),
  //   url: "/user/soulwriting-history",
  // },
  {
    name: getLanguages(checklanguage, "orderhistory"),
    url: "/user/order-history",
  },
  // {
  //   name: getLanguages(checklanguage, "payments"),
  //   url: "/user/payments",
  // },
];

export const COMPANION_NAV_LABELS = [
  {
    name: getLanguages(checklanguage, "currentAppointments"),
    query: "currentAppointments",
    tab: 1,
  },
  {
    name: getLanguages(checklanguage, "allCompanions"),
    query: "professionals",
    tab: 1,
  },
  {
    name: getLanguages(checklanguage, "allStudents"),
    query: "students",
    tab: 2,
  },
  {
    name: getLanguages(checklanguage, "mycompanion"),
    query: "companions",
    tab: 3,
  },
];

export const SELF_PRACTICE_NAV_LABELS = [
  {
    type: "self_practice_tabs",
    name: getLanguages(checklanguage, "overview"),
    query: "overview",
    icon: "overview-icon",
    tab: 1,
  },
  {
    type: "self_practice_tabs",
    name: getLanguages(checklanguage, "downloadfiles"),
    query: "downloadfiles",
    icon: "download-icon",
    tab: 2,
  },
  // {
  //   type: "self_practice_tabs",
  //   name: getLanguages(checklanguage, "comments&&rating"),
  //   query: "comments",
  //   icon: "comment-icon",
  //   tab: 3
  // },
];

export const PRODUCT_DETAILS_LABELS = [
  {
    name: getLanguages(checklanguage, "description"),
    tab: 1,
  },
  {
    name: getLanguages(checklanguage, "additionalInfo"),
    tab: 2,
  },
];
// export const STEPS_DATA = [
//   { step: 1, title: "Confirm date & time" },
//   { step: 2, title: "Confirm your appointment" },
// ];
export const STEPS_DATA = [
  { step: 1, title: getLanguages(checklanguage, "confirmDateAndTime") },
  { step: 2, title: getLanguages(checklanguage, "confirmYourAppointment") },
];

export const BOOK_MEETING_STEPS_DATA = [
  { step: 1, title: getLanguages(checklanguage, "selectADateAndTime") },
  { step: 2, title: getLanguages(checklanguage, "selectCompanion") },
  { step: 3, title: getLanguages(checklanguage, "confirmYourAppointment") },
];

function SampleNextArrow(props) {
  const { className, style, onClick } = props;
  return (
    <button className="slick_next cstm_slick_arrow " onClick={onClick}>
      <img src="/images/slide-arrow.svg" />
    </button>
  );
}

function SamplePrevArrow(props) {
  const { className, style, onClick } = props;
  return (
    <button className="slick_prev cstm_slick_arrow" onClick={onClick}>
      <img src="/images/slide-arrow.svg" />
    </button>
  );
}

export const SlickSettings = {
  slidesToShow: 4,
  slidesToScroll: 1,
  arrows: true,
  infinite: false,
  dots: false,
  centerMode: false,
  swipeToSlide: true,
  touchMove: true,
  nextArrow: <SampleNextArrow />,
  prevArrow: <SamplePrevArrow />,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
      },
    },
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 2,
      },
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
      },
    },
  ],
};

function AppointmentNextArrow(props) {
  const { className, style, onClick } = props;
  return (
    <button className="slick_next cstm_slick_arrow" onClick={onClick}>
      <img src="/images/chevron-left.svg" />
    </button>
  );
}

function AppointmentPrevArrow(props) {
  const { className, style, onClick } = props;
  return (
    <button className="slick_prev cstm_slick_arrow" onClick={onClick}>
      <img src="/images/chevron-left.svg" />
    </button>
  );
}

export const APPOINTMENT_SCHEDULE_SETTINGS = {
  slidesToShow: 7,
  slidesToScroll: 1,
  arrows: true,
  dots: false,
  infinite: false,
  prevArrow: <AppointmentPrevArrow />,
  nextArrow: <AppointmentNextArrow />,
};

export function dynamicSort(property) {
  var sortOrder = 1;
  if (property[0] === "-") {
    sortOrder = -1;

    property = property.substr(1);
  }

  return function (a, b) {
    /* next line works with strings and numbers,

     * and you may want to customize it to your needs

     */
    var result =
      a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;

    return result * sortOrder;
  };
}

export function filterSlotArray(array, dictBooking = {}) {
  var arrayFinal = [];
  for (let i = 0; i < array.length; i++) {
    let item = array[i];
    if (i == 0) {
      arrayFinal.push(item);
      continue;
    }
    let previous = arrayFinal[arrayFinal.length - 1];
    if (
      item.start >= previous.start &&
      item.end > previous.start &&
      item.start <= previous.end
    ) {
      //Time in between or grater
      if (item.end > previous.end) {
        //Update previous
        previous.end = item.end;
        arrayFinal[arrayFinal.length - 1] = previous;
      } else {

      }
    } else {
      // console.log("Next :", item);
      //diff Slot
      arrayFinal.push(item);
    }
  }
  ///Create Slots for full Day
  let arraySlots = [];
  for (let i = 0; i < arrayFinal.length; i++) {
    let item = arrayFinal[i];
    let start = item.start;
    let startTime = item.startTime;
    let endTime = item.endTime;
    let end = item.end;
    let duration = item.duration;
    let current = start;
    while (true) {
      let endDate = moment(current).add(duration, "m").toDate();
      let sDateStr = current.toISOString();
      if (endDate <= end) {
        // var count = 0;
        // arrayBooking.filter((item) => {
        //   let date = moment(item).utc().toDate();
        //   if (date == nil) {
        //     return false;
        //   }
        //   return date;
        // });
        let isEnable = true;
        if (dictBooking[sDateStr]) {
          isEnable = false;
        }
        // for (let i = 0; i < arrayBooking.length; i++) { }
        arraySlots.push({
          start: current,
          end: endDate,
          startTime: startTime,
          endTime: endTime,
          isEnable,
        });
        current = endDate;
      } else {
        // arraySlots.push({
        //   start: current,
        //   end: endDate,
        //   startTime: startTime,
        //   endTime: endTime,
        //   isEnable: false,
        // });
        break;
      }
    }
  }
  return arraySlots;
}

export function getYearExperience(dateString) {
  const date = moment(dateString, "YYYY-MM-DD");
  const years = moment().diff(date, "years");
  const days = moment().diff(date.add(years, "years"), "days", false);
  return years;
}

export const clearSession = async () => {
  const cookies = useCookie();
  await cookies.remove("jwtToken");
  // Redirect on root (Home Page)
  window.location = "/";
};

export const checkRescheduleTime = (startTime, showEventInfo) => {
  var scheduleTime = moment(startTime);
  var now = moment();
  const diff = scheduleTime.diff(now);
  const duration = moment.duration(diff);
  const hours = duration.asHours();
  if (hours > showEventInfo?.User.reScheduleTime) {
    return false;
  } else {
    return true;
  }
};

export const checkCancelTime = (startTime, showEventInfo) => {
  var scheduleTime = moment(startTime);
  var now = moment();
  const diff = scheduleTime.diff(now);
  const duration = moment.duration(diff);
  const hours = duration.asHours();
  if (hours > showEventInfo?.User.cancelTime) {
    return false;
  } else {
    return true;
  }
};

export const checkCourseCommingSoon = (courseStartTime) => {
  var start = moment(courseStartTime);
  var now = moment();
  const diff = start.diff(now);
  if (diff > 0) {
    return true;
  } else {
    return false;
  }
};
export const nFormatter = (num) => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1).replace(/\.0$/, "") + "B";
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, "") + "K";
  }
  return num;
};

export const currencyFormat = (value, currency, manualClass) => {
  if (currency != false)
    return (
      <NumericFormat
        displayType="text"
        className={manualClass ? manualClass : ""}
        value={value}
        thousandSeparator={true}
        prefix={`${currency} `}
      />
    );
};
export const SIDBAR_ROUTES = (link, Arr) => {
  return Arr.some((url) => link.includes(url));
};
export const SIDE_BAR_ARR = [
  "self-practice-details",
  "self-practice-sales",
  "product-detail",
  "cart",
  "create-soulwriting",
  "/dashboard/soulwriting",
];

export const SIDE_BAR_WITHOUCT_CLOSED = [
  "/dashboard/self-practice",
  "product-detail",
  "cart",
  "shop",
  "/dashboard/shop",
];
export const KINSHIPARRAY = {
  en: [
    { id: 1, label: "Father", value: "father" },
    { id: 2, label: "Mother", value: "mother" },
    { id: 3, label: "Brother", value: "brother" },
    { id: 4, label: "Daughter", value: "daughter" },
    { id: 5, label: "Sister", value: "sister" },
    { id: 6, label: "Uncle", value: "uncle" },
    { id: 7, label: "Son", value: "son" },
    { id: 8, label: "Other", value: "other" },
  ],

  de: [
    { id: 1, label: "Father", value: "father" },
    { id: 2, label: "Mother", value: "mother" },
    { id: 3, label: "Brother", value: "brother" },
    { id: 4, label: "Daughter", value: "daughter" },
    { id: 5, label: "Sister", value: "sister" },
    { id: 6, label: "Uncle", value: "uncle" },
    { id: 7, label: "Son", value: "son" },
    { id: 8, label: "Other", value: "other" },
  ],
};

export const LIFE_STATUS = {
  en: [
    { name: "Alive", value: 1 },
    { name: "Passed away", value: 0 },
  ],
  de: [
    { name: "Alive", value: 1 },
    { name: "Passed away", value: 0 },
  ],
};

export function removeHtmlTags(html) {
  if (html != undefined) {
    var html = html.replaceAll("<br>", "||br||");
    var tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    html = tmp.textContent || tmp.innerText;
    return html.replaceAll("||br||", "");
  }
}

export const collapseElement = (redline, arrayObject, step) => {
  if (
    redline &&
    _.find(arrayObject[`categoryForms${step}`], ["isRedLine", 1]) != undefined
  ) {
    return true;
  } else {
    return false;
  }
};

export const SOUL_WRITING_STATUS = {
  1: getLanguages(checklanguage, "draft"),
  2: getLanguages(checklanguage, "submitted"),
  3: getLanguages(checklanguage, "received"),
  4: getLanguages(checklanguage, "completed"),
};

export const SOUL_WRITING_CLASS = {
  1: "Draft",
  2: "submitted",
  3: "recieved",
  4: "Completed",
};

// export const SEMINAR_DATA = {
//     title: "Gesund ohne Medizin (GoM)",
//     smallDescription: "Wolltest du schon immer die Kompetenz haben, dich selbst zu heilenund deine Probleme zu lösen?",
//     videoLink: "https://player.vimeo.com/video/226053498?h=a1599a8ee9",
//     description: "In this free webinar, learn how Clemens healed from his spinal cord injury - contrary to the diagnosis of 40 doctors. What symptoms and problems Clemens Kuby has to deal with in his everyday seminar life and how others have already worked successfully with KUBY. why expanding consciousness is the key to self-healing and how you can implement it.​​​​​​​ How to activate your self-healing powers. what everyone can do directly to take personal responsibility for their problems and symptoms."
// }

export const TESTIMONIALS = [
  {
    name: "",
    rating: 5,
    place: "Vienna",
    description:
      "Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.",
  },
  {
    name: "",
    rating: 5,
    place: "Vienna",
    description:
      "Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.",
  },
  {
    name: "",
    rating: 5,
    place: "Vienna",
    description:
      "Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.",
  },
  {
    name: "",
    rating: 5,
    place: "Vienna",
    description:
      "Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.",
  },
  {
    name: "",
    rating: 5,
    place: "Vienna",
    description:
      "Good morning dears, that was amazing last night, so much energy, so much interest, that was masterful. Then I sat down and wrote. Now I have the feeling that everything is solved, the healing process can proceed in giant strides. I am already looking forward to the next online seminar.",
  },
];

export const capitalizeAndReplace = (sentence) => {
  if (!sentence || sentence == "") {
    return "";
  } else {
    sentence = sentence.toString();
  }
  // Replace underscores with spaces
  const modifiedSentence = sentence.replace(/_/g, " ");

  // Capitalize the first character of the sentence
  const capitalizedSentence =
    modifiedSentence.charAt(0).toUpperCase() + modifiedSentence.slice(1);

  return capitalizedSentence;
};

export const Hilitor = (el, tag) => {
  // Original JavaScript code by Chirp Internet: www.chirpinternet.eu
  // Please acknowledge use of this code by including this header.

  // private variables
  //var targetNode = document.getElementById(id) || document.body;
  var targetNode = el;
  var hiliteTag = tag || "MARK";
  var skipTags = new RegExp("^(?:" + hiliteTag + "|SCRIPT|FORM|SPAN)$");
  var colors = ["#ff6", "#a0ffff", "#9f9", "#f99", "#f6f"];
  var wordColor = [];
  var colorIdx = 0;
  var matchRegExp = "";
  var openLeft = false;
  var openRight = false;

  // characters to strip from start and end of the input string
  var endRegExp = new RegExp("^[^\\w]+|[^\\w]+$", "g");

  // characters used to break up the input string into words
  var breakRegExp = new RegExp("[^\\w'-]+", "g");

  this.setEndRegExp = function (regex) {
    endRegExp = regex;
    return endRegExp;
  };

  this.setBreakRegExp = function (regex) {
    breakRegExp = regex;
    return breakRegExp;
  };

  this.setMatchType = function (type) {
    switch (type) {
      case "left":
        this.openLeft = false;
        this.openRight = true;
        break;

      case "right":
        this.openLeft = true;
        this.openRight = false;
        break;

      case "open":
        this.openLeft = this.openRight = true;
        break;

      default:
        this.openLeft = this.openRight = false;
    }
  };

  this.setRegex = function (input) {
    input = input.replace(endRegExp, "");
    input = input.replace(breakRegExp, "|");
    input = input.replace(/^\||\|$/g, "");
    if (input) {
      var re = "(" + input + ")";
      if (!this.openLeft) {
        re = "\\b" + re;
      }
      if (!this.openRight) {
        re = re + "\\b";
      }
      matchRegExp = new RegExp(re, "i");
      return matchRegExp;
    }
    return false;
  };

  this.getRegex = function () {
    var retval = matchRegExp.toString();
    retval = retval.replace(/(^\/(\\b)?|\(|\)|(\\b)?\/i$)/g, "");
    retval = retval.replace(/\|/g, " ");
    return retval;
  };

  // recursively apply word highlighting
  this.hiliteWords = function (node) {
    if (node === undefined || !node) return;
    if (!matchRegExp) return;
    if (skipTags.test(node.nodeName)) return;

    if (node.hasChildNodes()) {
      for (var i = 0; i < node.childNodes.length; i++)
        this.hiliteWords(node.childNodes[i]);
    }
    if (node.nodeType == 3) {
      // NODE_TEXT

      var nv, regs;

      if ((nv = node.nodeValue) && (regs = matchRegExp.exec(nv))) {
        if (!wordColor[regs[0].toLowerCase()]) {
          wordColor[regs[0].toLowerCase()] = colors[colorIdx++ % colors.length];
        }

        var match = document.createElement(hiliteTag);
        match.appendChild(document.createTextNode(regs[0]));
        match.classList.add("bridgeHighlight");
        match.style.backgroundColor = wordColor[regs[0].toLowerCase()];
        match.style.color = "#000";

        var after = node.splitText(regs.index);
        after.nodeValue = after.nodeValue.substring(regs[0].length);
        node.parentNode.insertBefore(match, after);
      }
    }
  };

  // remove highlighting
  this.remove = function () {
    var arr = document.getElementsByTagName(hiliteTag),
      el;
    while (arr.length && (el = arr[0])) {
      var parent = el.parentNode;
      parent.replaceChild(el.firstChild, el);
      parent.normalize();
    }
  };

  // start highlighting at target node
  this.apply = function (input) {
    this.remove();
    if (input === undefined || !(input = input.replace(/(^\s+|\s+$)/g, ""))) {
      return;
    }
    if (this.setRegex(input)) {
      this.hiliteWords(targetNode);
    }
    return matchRegExp;
  };
};

export const MEETING_STATUSES = {
  NOT_BOOKED: 0,
  BOOKED: 1,
  RESCHEDULED: 2,
  CANCELLED_WITH_REFUND: 3,
  CANCELLED_WITHOUT_REFUND: 4,
  NOT_JOINED: 5,
  COMPLETED_WITH_COMPLETED_PAYMENT: 6,
  COMPLETED_WITH_PENDING_PAYMENT: 7,
};

export const HIDE_PANELS = {
  en: ["NOTIFICATIONS"],
  cz: [
    "RECEPTION",
    "MEETING_HISTORY",
    "UPCOMING_MEETINGS",
    "CALENDAR",
    "KUBY_COMPANIONS",
    "SOUL_WRITING",
    "KUBY_SHOP",
    "NOTIFICATIONS",
  ],
  de: ["NOTIFICATIONS"],
};

export const LANDING_PAGE = {
  en: "/",
  cz: "/dashboard/self-practice",
  de: "/",
};

export const getLandingPageUrl = (lang = "") => {

  if (lang && LANDING_PAGE?.[lang]) {
    return LANDING_PAGE?.[lang];
  } else if (LANDING_PAGE?.[checklanguage]) {
    return LANDING_PAGE?.[checklanguage];
  }

  return "/";
};

export const showHideNavigationLink = (link_tag) => {
  if (HIDE_PANELS?.[checklanguage]?.length > 0) {
    if (HIDE_PANELS?.[checklanguage]?.indexOf(link_tag) !== -1) {
      return false;
    } else {
      return true;
    }
  } else {
    return true;
  }
};

export const URLify = (string) => {
  const urls = string.match(
    /((((ftp|https?):\/\/)|(w{3}\.))[\-\w@:%_\+.~#?,&\/\/=]+)/g
  );
  if (urls) {
    urls.forEach(function (url) {
      string = string.replace(
        url,
        '<a target="_blank" href="' + url + '">' + url + "</a>"
      );
    });
  }
  return string.replace("(", "<br/>(");
};

export const convertTextLinksToClickableLinks = (htmlString) => {
  // Regular expression to find text links (http, https, ftp, or ftps)
  var regex = /(https?:\/\/[^\s]+)/g;

  // Replace text links with clickable HTML links
  var updatedHtml = htmlString.replace(regex, function (match) {
    return '<a href="' + match + '" target="_blank">' + match + "</a>";
  });

  return updatedHtml;
};

export const formatStatus = (eventStatus) => {
  if (eventStatus == 0) {
    return getLanguages(checklanguage, "notBooked");
  } else if (eventStatus == 1) {
    return getLanguages(checklanguage, "booked");
  } else if (eventStatus == 2) {
    return getLanguages(checklanguage, "rescheduled");
  } else if (eventStatus == 3) {
    return getLanguages(checklanguage, "cancelledWithRefund");
  } else if (eventStatus == 4) {
    return getLanguages(checklanguage, "cancelledWithoutRefund");
  } else if (eventStatus == 5) {
    return getLanguages(checklanguage, "notJoined");
  } else if (eventStatus == 6) {
    return getLanguages(checklanguage, "completedWithCompletedPayment");
  } else if (eventStatus == 7) {
    return getLanguages(checklanguage, "completedWithPendingPayment");
  } else {
    return "";
  }
};

export const convertToUTCMinutes = (minutes, timezone) => {
  // Convert the given minutes to hours and minutes
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  // Create a moment object at midnight, adding the given time
  const localTime = moment.tz({ hour: hours, minute: mins }, timezone);

  // Get the UTC offset in minutes
  const utcOffsetMinutes = localTime.utcOffset();

  // Calculate UTC time in minutes
  const utcMinutes = minutes - utcOffsetMinutes;

  return utcMinutes;
};

export const convertUTCToLocalMinutes = (utcMinutes, timezone) => {
  // Convert UTC minutes to hours and minutes
  const utcHours = Math.floor(utcMinutes / 60);
  const utcMins = utcMinutes % 60;

  // Create a UTC-based moment object
  const utcTime = moment.utc({ hour: utcHours, minute: utcMins });

  // Convert the UTC moment to the desired timezone
  const localTime = utcTime.tz(timezone);

  // Calculate total minutes in the local timezone
  const localMinutes = localTime.hours() * 60 + localTime.minutes();

  return localMinutes;
};

export const getDayDescription = (inputDate) => {
  const givenDate = moment(inputDate);
  const today = moment();
  const tomorrow = moment().add(1, 'day');

  if (givenDate.isSame(today, 'day')) {
    //return 'today';
    return getLanguages(checklanguage, 'today');
  } else if (givenDate.isSame(tomorrow, 'day')) {
    //return 'tomorrow';
    return getLanguages(checklanguage, 'tomorrow');
  } else {
    return givenDate.format('dddd'); // Day of the week (e.g., Monday, Tuesday)
  }
}


export const timeZoneConversion = (inputDate, timeZone, onlyDate = false) => {
  // Input: UTC time string
  //const inputDate = "2025-01-20T18:30:00.000Z";

  // Step 1: Create a Date object from the UTC input
  const dateInUTC = new Date(inputDate);

  // Step 2: Convert the date to Asia/Kolkata timezone by adjusting the time
  const kolkataTime = dateInUTC.toLocaleString('en-US', {
    timeZone: timeZone,
    hour12: false
  });

  // Step 3: Extract the components from the formatted date string (DD/MM/YYYY, HH:MM:SS)
  const [datePart, timePart] = kolkataTime.split(', ');

  // Split the date part (DD/MM/YYYY)
  const [month, day, year] = datePart.split('/');

  // Split the time part (HH:MM:SS)
  const [hours, minutes, seconds] = timePart.split(':');

  // Step 4: Set the time to midnight (00:00:00) in Asia/Kolkata
  const kolkataMidnight = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));

  // Step 5: Convert the midnight date back to UTC
  if (onlyDate) {
    let dd = kolkataMidnight.toISOString();
    dd = dd.split("T");
    return dd[0]
  } else {
    return kolkataMidnight.toISOString();
  }

}

export const stripTagsExceptMark = (html) => {
  // First, temporarily replace allowed tags with placeholders
  html = html.replace(/<mark class="bridgeHighlight">/g, '[[[MARK_OPEN]]]');
  html = html.replace(/<\/mark>/g, '[[[MARK_CLOSE]]]');

  // Remove all remaining tags
  html = html.replace(/<\/?[^>]+(>|$)/g, '');

  // Restore the allowed tags
  html = html.replace(/\[\[\[MARK_OPEN\]\]\]/g, '<mark class="bridgeHighlight">');
  html = html.replace(/\[\[\[MARK_CLOSE\]\]\]/g, '</mark>');

  return "<p>"+html+"</p>";
}
