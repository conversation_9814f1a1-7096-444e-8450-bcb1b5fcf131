{"name": "kuby-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3005", "build": "next build", "start": "next start  -p 3005", "lint": "next lint"}, "dependencies": {"@babel/preset-react": "^7.18.6", "@ckeditor/ckeditor5-font": "^36.0.1", "@ckeditor/ckeditor5-react": "^5.1.0", "@fullcalendar/common": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@madzadev/audio-player": "^1.1.11", "@progress/kendo-drawing": "^1.17.5", "@progress/kendo-licensing": "^1.3.0", "@progress/kendo-react-pdf": "^5.12.0", "@react-oauth/google": "^0.11.0", "@u-wave/react-vimeo": "^0.9.10", "@vimeo/player": "^2.18.0", "@wojtekmaj/react-daterange-picker": "^4.2.1", "axios": "^1.1.3", "base-64": "^1.0.0", "ckeditor5-custom-build": "file:ckeditor5", "cookies-next": "^2.1.1", "docx": "^9.1.1", "eslint": "8.27.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "next": "^12.2.4", "next-cookie": "^2.8.0", "next-ga": "^2.3.4", "next-images": "^1.8.4", "next-transpile-modules": "^9.1.0", "query-string": "^8.1.0", "react": "18.2.0", "react-animated-show-more": "^1.0.2", "react-audio-player": "^0.17.0", "react-bootstrap": "^2.5.0", "react-calendar": "^4.2.1", "react-circular-progressbar": "^2.1.0", "react-color": "^2.19.3", "react-cookie-consent": "^8.0.1", "react-date-picker": "^9.2.0", "react-datetime-picker": "^4.2.0", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-facebook-pixel": "^1.0.4", "react-google-login": "^5.2.2", "react-hook-form": "^7.39.1", "react-hot-toast": "^2.4.0", "react-infinite-scroll-component": "^6.1.0", "react-intl-number-format": "^1.1.1", "react-js-pagination": "^3.0.3", "react-loader-spinner": "^5.3.4", "react-loading-overlay": "^1.0.1", "react-loading-skeleton": "^3.1.0", "react-lottie-player": "^1.5.4", "react-number-format": "^5.1.3", "react-player": "^2.16.0", "react-redux": "^8.0.5", "react-select": "^5.6.1", "react-simple-star-rating": "^5.1.7", "react-slick": "^0.29.0", "react-spinners": "^0.13.8", "react-tooltip": "^5.26.3", "react-use-intercom": "^5.1.4", "react-world-flags": "^1.5.1", "redux": "^4.2.0", "redux-persist": "^6.0.0", "save": "^2.9.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.6.1", "use-query-params": "^2.2.1"}, "devDependencies": {"eslint": "8.21.0", "eslint-config-next": "12.2.4"}}