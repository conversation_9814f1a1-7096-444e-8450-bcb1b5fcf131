import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  checklanguage,
  getLanguages,
  TitleArray,
  KINSHIPARRAY,
  LIFE_STATUS,
  getYearExperience,
  GENDER_TYPES,
} from "../../../constant";
import InputField from "../../FormFields/InputField";
import MultiRadioBtnField from "../../FormFields/RadioBtnField";
import ReactTimePickerField from "../../FormFields/ReactTimePickerField";
import ReactSelectField from "../../FormFields/SelectField";
import {
  addCharacters,
  updateCharacters,
  getProtagonistQuestions,
} from "../../../redux/action/soul-writing";
import _ from "lodash";

const ProtoganistMoreInfoModal = ({
  showCharacterPopup,
  setShowCharacterPopup,
  setshow,
  show,
  onHide,
  projectId,
  set<PERSON>haracter,
  SingleCharacter,
  setcategoryListForm,
  set<PERSON>ingleCharacter,
  setProtagonistSurveyData,
  protagonistSurveyData,
  setFethcCharForS3,
  fetchCharForS3,
  isAddProtagonistOpenFromS3,
}) => {
  const {
    register,
    handleSubmit,
    control,
    setValue,
    getValues,
    clearErrors,
    reset,
    watch,
    formState: { errors },
  } = useForm({ shouldValidate: true });
  watch("isAlive");
  watch("protagonistDistance");
  watch("meetings");
  const [isFetching, setIsFetching] = useState(false);
  const [nameOfProtagonist, setNameOfProtagonist] = useState(null);
  const [activeForm, setActiveForm] = useState(1);
  const [arrayDisplayedQuestions, setArrayDisplayedQuestions] = useState();
  const [surveyData, setSurveyData] = useState([]);

  useEffect(() => {
    setNameOfProtagonist(protagonistSurveyData?.[0]?.text);
  }, []);

  const nextForm = () => {
    if (activeForm < 2) {
      setActiveForm(activeForm + 1);
    }
  };

  const prevForm = () => {
    if (activeForm > 1) {
      setActiveForm(activeForm - 1);
    }
  };
  const availableColors = [
    { backgroundColor: "#1f77b4", fontColor: "White" },
    { backgroundColor: "#aec7e8", fontColor: "Black" },
    { backgroundColor: "#ff7f0e", fontColor: "Black" },
    { backgroundColor: "#ffbb78", fontColor: "Black" },
    { backgroundColor: "#2ca02c", fontColor: "White" },
    { backgroundColor: "#98df8a", fontColor: "Black" },
    { backgroundColor: "#ff9896", fontColor: "Black" },
    { backgroundColor: "#9467bd", fontColor: "Black" },
    { backgroundColor: "#bcbd22", fontColor: "Black" },
    { backgroundColor: "#17becf", fontColor: "Black" },
    { backgroundColor: "#dbdb8d", fontColor: "Black" },
    { backgroundColor: "#e377c2", fontColor: "Black" },
  ];

  function getRandomColor() {
    return availableColors[Math.floor(Math.random() * availableColors.length)];
  }
  const onSubmit = (formvalues) => {
    if (activeForm < 2) {
      setActiveForm(activeForm + 1);
      return;
    } else {

      let payload = {
        surveyData: protagonistSurveyData,
        moreInfo: formvalues,
        defaultCharacter: true,
      };

      let kinnship = "";

      protagonistSurveyData?.forEach((question, index) => {
        if (question.forKinnship) {
          kinnship +=
            question.text === "" ? question.q + " - " : question.text + " - ";
        }
      });
      kinnship = kinnship.trim();

      if (kinnship.endsWith("-")) {
        kinnship = kinnship.slice(0, -1);
      }
      let name = protagonistSurveyData?.[0]?.text;
      let Acronym = name.replaceAll(" ", "").toUpperCase().substring(0, 3);
      let contact = formvalues?.meetings;
      let noOfMeetings = formvalues?.[contact];
      let formattedContact = "";
      if (contact == "only_at") {
        formattedContact = noOfMeetings;
      } else if (contact == "daily") {
        formattedContact = contact;
      } else {
        formattedContact = contact + " (" + noOfMeetings + ")";
      }
      let gender =
        protagonistSurveyData[protagonistSurveyData.length - 1]?.gender;
      if (
        protagonistSurveyData[protagonistSurveyData.length - 1]?.gender == ""
      ) {
        gender = protagonistSurveyData[protagonistSurveyData.length - 1]?.q;
      } else {
        gender =
          protagonistSurveyData[protagonistSurveyData.length - 1]?.gender;
      }
      if (gender.trim().toLowerCase() == "male") {
        gender = "1";
      } else {
        gender = "2";
      }
      let distance = formvalues?.protagonistDistance;
      if (distance == "address") {
        distance = formvalues?.address;
      } else if (distance == "far_away") {
        distance = formvalues?.far_away;
      }
      let finalPayload = {
        projectId: projectId,
        age: formvalues?.ageInTheScene,
        lifeStatus: formvalues?.isAlive == "yes" ? 1 : 0,
        // passAwayDate: formvalues?.yearOfDeath,
        title: gender,
        name: name,
        distance: distance,
        degreeOfKinship: kinnship,
        Acronym: Acronym,
        contact: formattedContact,
        noOfMeetings: noOfMeetings,
        sympethetic: formvalues?.sympatheticValue,
        protogonistObject: payload,
        // color: {
        //   backgroundColor: "#CBECEA",
        //   fontColor: "#262626",
        // },
        color: getRandomColor(),
      };

      try {
        setIsFetching(true);
        let response = addCharacters(finalPayload);
        // if (response) {
        //   setFethcCharForS3(!fetchCharForS3);
        // }
        setIsFetching(false);
        if (showCharacterPopup) {
          setTimeout(() => {
            setShowCharacterPopup(false);
            setshow(4);
          }, 1500);
        } else {
          setTimeout(() => {
            onHide();
          }, 2000);
        }
      } catch (error) {
        setIsFetching(false);
      }
    }
  };

  return (
    <div
      className={`modal protagonist-modal new ${show ? "show" : ""}`}
      id="protagonist"
    >
      <div className="overlay-div" id="modalOverlay"></div>
      <div className="modal-content">
        <div className="modal-dialog">
          <div className="text-center modal-header">
            <a
              href="javascript:;"
              className="close-btn"
              id="closeModal"
              onClick={(e) => {
                e.preventDefault();
                if (!isAddProtagonistOpenFromS3) {
                  setshow(14);
                } else {
                  setshow(0);
                  // setFethcCharForS3(!fetchCharForS3);
                }
              }}
            >
              <span className="icon close-icon"></span>
            </a>
            <h4 className="h4 text-dark-grey fw500">
              {getLanguages(checklanguage, "moreInfoAboutProtagonist")}
            </h4>
          </div>
          <div className="modal-body">
            <form
              className="protagonist-form-wrpr"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="protagonist-form-wrpr">
                <div className="form-inner protogonist-form-inner">
                  {activeForm == 1 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 mb-0">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "sympatheticValueQuestion",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div
                          className="protogonist-opt-list"
                          onClick={() =>
                            setTimeout(() => {
                              document
                                .querySelector("#submit_from_radioBtn")
                                .click();
                            }, 300)
                          }
                        >
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="very_trustworthy"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(
                                    checklanguage,
                                    "veryTrustWorthy"
                                  )}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="sympathetic"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "sympathetic")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="neutral"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "neutral")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="unsympathetic"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "unsympathetic")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="repulsive"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "repulsive")}
                                </span>
                              </div>
                            </label>
                          </div>
                          <div className="protogonist-opt-item">
                            <label className="protogonist-opt">
                              <input
                                type="radio"
                                value="fear_occupied"
                                {...register("sympatheticValue", {
                                  required: true,
                                })}
                              />
                              <div className="protogonist-content">
                                <span>
                                  {getLanguages(checklanguage, "fearOccupied")}
                                </span>
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  {activeForm == 2 && (
                    <div className="more-info-form-wrpr">
                      <div className="form-in w-100 ">
                        <label className="fw700 h6 f-label">
                          {getLanguages(
                            checklanguage,
                            "protagonistMoreInfoHowOld",
                            ["nameOfProtagonist"],
                            [nameOfProtagonist]
                          )}
                        </label>
                        <div
                          className={`${
                            errors.ageInTheScene ? "f-error" : ""
                          } ${"f-in w-100"}`}
                        >
                          <input
                            placeholder="e.g. 22"
                            type="text"
                            className="form-control sm"
                            {...register("ageInTheScene", { required: true })}
                          />
                          {errors?.ageInTheScene && (
                            <p className="error d-flex align-center">
                              <span className="icon info-icon"></span>
                              {getLanguages(checklanguage, "required")}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="d-flex align-center justify-end bt-0 modal-footer">
                  <div className="d-flex align-center modal-btn-wrpr">
                    {activeForm > 1 ? (
                      <button
                        onClick={() => prevForm()}
                        type="button"
                        className={`btn-tertiary sm w-100`}
                      >
                        {getLanguages(checklanguage, "back")}
                      </button>
                    ) : (
                      <></>
                    )}

                    <button
                      type="submit"
                      id="submit_from_radioBtn"
                      className={`btn-accent sm w-100 ${
                        isFetching && "btn-loader"
                      }`}
                    >
                      {activeForm < 2
                        ? getLanguages(checklanguage, "next")
                        : getLanguages(checklanguage, "save")}
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ProtoganistMoreInfoModal;

////

// const handleTouchStart = (e) => {
//   e.preventDefault();
//   const targetElement = window.document.querySelector("#red-lineStep");
//   const touch = e.touches[0];
//   const rect = targetElement.getBoundingClientRect();
//   const offsetX = touch.clientX - rect.left;
//   const offsetY = touch.clientY - rect.top;
//   targetElement.style.position = "absolute";
//   targetElement.style.left = touch.clientX - offsetX + "px";
//   targetElement.style.top = touch.clientY - offsetY + "px";
//   targetElement.style.backgroundColor = "red";
//   targetElement.style.opacity = "1";
//   targetElement.style.width = "100%";

//   // Attach touchmove and touchend event listeners directly to the target element
//   targetElement.addEventListener("touchmove", handleTouchMove, false);
//   targetElement.addEventListener("touchend", handleTouchEnd, false);
// };

// const handleTouchMove = (e) => {
//   e.preventDefault();
//   const targetElement = window.document.querySelector("#red-lineStep");
//   const touch = e.touches[0];
//   const rect = targetElement.getBoundingClientRect();
//   const offsetX = touch.clientX - rect.left;
//   const offsetY = touch.clientY - rect.top;
//   targetElement.style.left = touch.clientX - offsetX + "px";
//   targetElement.style.top = touch.clientY - offsetY + "px";
// };

// const handleTouchEnd = (e) => {
//   e.preventDefault();
//   const targetElement = window.document.querySelector("#red-lineStep");
//   targetElement.style.backgroundColor = "yellow";
//   targetElement.style.opacity = "1";
//   targetElement.style.width = "100%";

//   // Remove touchmove and touchend event listeners
//   targetElement.removeEventListener("touchmove", handleTouchMove);
//   targetElement.removeEventListener("touchend", handleTouchEnd);
// };
// let moving = null;
// const pickup = (e) => {
//   moving = e.target;
//   console.log(`movinggg`, moving.clientHeight);
//   moving.style.height = moving.clientHeight;
//   moving.style.width = moving.clientWidth;
//   moving.style.position = "absolute";
// };
// const move = (event) => {
//   if (moving) {
//     if (event.clientX) {
//       // mousemove
//       moving.style.left = event.clientX - moving.clientWidth / 2;
//       moving.style.top = event.clientY - moving.clientHeight / 2;
//     } else {
//       // touchmove - assuming a single touchpoint
//       moving.style.left =
//         event.changedTouches[0].clientX - moving.clientWidth / 2;
//       moving.style.top =
//         event.changedTouches[0].clientY - moving.clientHeight / 2;
//     }
//   }
// };
// function drop(event) {
//   if (moving) {
//     // reset our element
//     moving.style.left = "";
//     moving.style.top = "";
//     moving.style.height = "";
//     moving.style.width = "";
//     moving.style.position = "";
//     moving.style.zIndex = "";

//     moving = null;
//   }
// }

///
{
  /* <>
                          {getLanguages(checklanguage, "read")}{" "}
                          <span onClick={(e) => e.stopPropagation()}>
                            {" "}
                            <a href="/terms" target="_blank">
                              {" "}
                              {getLanguages(
                                checklanguage,
                                "termsAndConditionConsent"
                              )}{" "}
                            </a>
                            {getLanguages(checklanguage, "and")}{" "}
                            <a href="/data-protection" target="_blank">
                              {getLanguages(
                                checklanguage,
                                "dataProtectionDeclaration"
                              )}
                            </a>{" "}
                          </span>{" "}
                          {getLanguages(checklanguage, "accept")}
                        </> */
}
{
  /* <span className="icon q-mark-icon"></span> */
}
